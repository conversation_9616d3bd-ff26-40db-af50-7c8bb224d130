/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.autoscaling.v2;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v2";

// ContainerResourceMetricSource indicates how to scale on a resource metric known to
// Kubernetes, as specified in requests and limits, describing each pod in the
// current scale target (e.g. CPU or memory).  The values will be averaged
// together before being compared to the target.  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.  Only one "target" type
// should be set.
message ContainerResourceMetricSource {
  // name is the name of the resource in question.
  optional string name = 1;

  // target specifies the target value for the given metric
  optional MetricTarget target = 2;

  // container is the name of the container in the pods of the scaling target
  optional string container = 3;
}

// ContainerResourceMetricStatus indicates the current value of a resource metric known to
// Kubernetes, as specified in requests and limits, describing a single container in each pod in the
// current scale target (e.g. CPU or memory).  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.
message ContainerResourceMetricStatus {
  // Name is the name of the resource in question.
  optional string name = 1;

  // current contains the current value for the given metric
  optional MetricValueStatus current = 2;

  // Container is the name of the container in the pods of the scaling target
  optional string container = 3;
}

// CrossVersionObjectReference contains enough information to let you identify the referred resource.
message CrossVersionObjectReference {
  // Kind of the referent; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds"
  optional string kind = 1;

  // Name of the referent; More info: http://kubernetes.io/docs/user-guide/identifiers#names
  optional string name = 2;

  // API version of the referent
  // +optional
  optional string apiVersion = 3;
}

// ExternalMetricSource indicates how to scale on a metric not associated with
// any Kubernetes object (for example length of queue in cloud
// messaging service, or QPS from loadbalancer running outside of cluster).
message ExternalMetricSource {
  // metric identifies the target metric by name and selector
  optional MetricIdentifier metric = 1;

  // target specifies the target value for the given metric
  optional MetricTarget target = 2;
}

// ExternalMetricStatus indicates the current value of a global metric
// not associated with any Kubernetes object.
message ExternalMetricStatus {
  // metric identifies the target metric by name and selector
  optional MetricIdentifier metric = 1;

  // current contains the current value for the given metric
  optional MetricValueStatus current = 2;
}

// HPAScalingPolicy is a single policy which must hold true for a specified past interval.
message HPAScalingPolicy {
  // Type is used to specify the scaling policy.
  optional string type = 1;

  // Value contains the amount of change which is permitted by the policy.
  // It must be greater than zero
  optional int32 value = 2;

  // PeriodSeconds specifies the window of time for which the policy should hold true.
  // PeriodSeconds must be greater than zero and less than or equal to 1800 (30 min).
  optional int32 periodSeconds = 3;
}

// HPAScalingRules configures the scaling behavior for one direction.
// These Rules are applied after calculating DesiredReplicas from metrics for the HPA.
// They can limit the scaling velocity by specifying scaling policies.
// They can prevent flapping by specifying the stabilization window, so that the
// number of replicas is not set instantly, instead, the safest value from the stabilization
// window is chosen.
message HPAScalingRules {
  // StabilizationWindowSeconds is the number of seconds for which past recommendations should be
  // considered while scaling up or scaling down.
  // StabilizationWindowSeconds must be greater than or equal to zero and less than or equal to 3600 (one hour).
  // If not set, use the default values:
  // - For scale up: 0 (i.e. no stabilization is done).
  // - For scale down: 300 (i.e. the stabilization window is 300 seconds long).
  // +optional
  optional int32 stabilizationWindowSeconds = 3;

  // selectPolicy is used to specify which policy should be used.
  // If not set, the default value Max is used.
  // +optional
  optional string selectPolicy = 1;

  // policies is a list of potential scaling polices which can be used during scaling.
  // At least one policy must be specified, otherwise the HPAScalingRules will be discarded as invalid
  // +listType=atomic
  // +optional
  repeated HPAScalingPolicy policies = 2;
}

// HorizontalPodAutoscaler is the configuration for a horizontal pod
// autoscaler, which automatically manages the replica count of any resource
// implementing the scale subresource based on the metrics specified.
message HorizontalPodAutoscaler {
  // metadata is the standard object metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the specification for the behaviour of the autoscaler.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status.
  // +optional
  optional HorizontalPodAutoscalerSpec spec = 2;

  // status is the current information about the autoscaler.
  // +optional
  optional HorizontalPodAutoscalerStatus status = 3;
}

// HorizontalPodAutoscalerBehavior configures the scaling behavior of the target
// in both Up and Down directions (scaleUp and scaleDown fields respectively).
message HorizontalPodAutoscalerBehavior {
  // scaleUp is scaling policy for scaling Up.
  // If not set, the default value is the higher of:
  //   * increase no more than 4 pods per 60 seconds
  //   * double the number of pods per 60 seconds
  // No stabilization is used.
  // +optional
  optional HPAScalingRules scaleUp = 1;

  // scaleDown is scaling policy for scaling Down.
  // If not set, the default value is to allow to scale down to minReplicas pods, with a
  // 300 second stabilization window (i.e., the highest recommendation for
  // the last 300sec is used).
  // +optional
  optional HPAScalingRules scaleDown = 2;
}

// HorizontalPodAutoscalerCondition describes the state of
// a HorizontalPodAutoscaler at a certain point.
message HorizontalPodAutoscalerCondition {
  // type describes the current condition
  optional string type = 1;

  // status is the status of the condition (True, False, Unknown)
  optional string status = 2;

  // lastTransitionTime is the last time the condition transitioned from
  // one status to another
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // reason is the reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // message is a human-readable explanation containing details about
  // the transition
  // +optional
  optional string message = 5;
}

// HorizontalPodAutoscalerList is a list of horizontal pod autoscaler objects.
message HorizontalPodAutoscalerList {
  // metadata is the standard list metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of horizontal pod autoscaler objects.
  repeated HorizontalPodAutoscaler items = 2;
}

// HorizontalPodAutoscalerSpec describes the desired functionality of the HorizontalPodAutoscaler.
message HorizontalPodAutoscalerSpec {
  // scaleTargetRef points to the target resource to scale, and is used to the pods for which metrics
  // should be collected, as well as to actually change the replica count.
  optional CrossVersionObjectReference scaleTargetRef = 1;

  // minReplicas is the lower limit for the number of replicas to which the autoscaler
  // can scale down.  It defaults to 1 pod.  minReplicas is allowed to be 0 if the
  // alpha feature gate HPAScaleToZero is enabled and at least one Object or External
  // metric is configured.  Scaling is active as long as at least one metric value is
  // available.
  // +optional
  optional int32 minReplicas = 2;

  // maxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up.
  // It cannot be less that minReplicas.
  optional int32 maxReplicas = 3;

  // metrics contains the specifications for which to use to calculate the
  // desired replica count (the maximum replica count across all metrics will
  // be used).  The desired replica count is calculated multiplying the
  // ratio between the target value and the current value by the current
  // number of pods.  Ergo, metrics used must decrease as the pod count is
  // increased, and vice-versa.  See the individual metric source types for
  // more information about how each type of metric must respond.
  // If not set, the default metric will be set to 80% average CPU utilization.
  // +listType=atomic
  // +optional
  repeated MetricSpec metrics = 4;

  // behavior configures the scaling behavior of the target
  // in both Up and Down directions (scaleUp and scaleDown fields respectively).
  // If not set, the default HPAScalingRules for scale up and scale down are used.
  // +optional
  optional HorizontalPodAutoscalerBehavior behavior = 5;
}

// HorizontalPodAutoscalerStatus describes the current status of a horizontal pod autoscaler.
message HorizontalPodAutoscalerStatus {
  // observedGeneration is the most recent generation observed by this autoscaler.
  // +optional
  optional int64 observedGeneration = 1;

  // lastScaleTime is the last time the HorizontalPodAutoscaler scaled the number of pods,
  // used by the autoscaler to control how often the number of pods is changed.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastScaleTime = 2;

  // currentReplicas is current number of replicas of pods managed by this autoscaler,
  // as last seen by the autoscaler.
  // +optional
  optional int32 currentReplicas = 3;

  // desiredReplicas is the desired number of replicas of pods managed by this autoscaler,
  // as last calculated by the autoscaler.
  optional int32 desiredReplicas = 4;

  // currentMetrics is the last read state of the metrics used by this autoscaler.
  // +listType=atomic
  // +optional
  repeated MetricStatus currentMetrics = 5;

  // conditions is the set of conditions required for this autoscaler to scale its target,
  // and indicates whether or not those conditions are met.
  // +patchMergeKey=type
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=type
  // +optional
  repeated HorizontalPodAutoscalerCondition conditions = 6;
}

// MetricIdentifier defines the name and optionally selector for a metric
message MetricIdentifier {
  // name is the name of the given metric
  optional string name = 1;

  // selector is the string-encoded form of a standard kubernetes label selector for the given metric
  // When set, it is passed as an additional parameter to the metrics server for more specific metrics scoping.
  // When unset, just the metricName will be used to gather metrics.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;
}

// MetricSpec specifies how to scale based on a single metric
// (only `type` and one other matching field should be set at once).
message MetricSpec {
  // type is the type of metric source.  It should be one of "ContainerResource", "External",
  // "Object", "Pods" or "Resource", each mapping to a matching field in the object.
  // Note: "ContainerResource" type is available on when the feature-gate
  // HPAContainerMetrics is enabled
  optional string type = 1;

  // object refers to a metric describing a single kubernetes object
  // (for example, hits-per-second on an Ingress object).
  // +optional
  optional ObjectMetricSource object = 2;

  // pods refers to a metric describing each pod in the current scale target
  // (for example, transactions-processed-per-second).  The values will be
  // averaged together before being compared to the target value.
  // +optional
  optional PodsMetricSource pods = 3;

  // resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing each pod in the
  // current scale target (e.g. CPU or memory). Such metrics are built in to
  // Kubernetes, and have special scaling options on top of those available
  // to normal per-pod metrics using the "pods" source.
  // +optional
  optional ResourceMetricSource resource = 4;

  // containerResource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing a single container in
  // each pod of the current scale target (e.g. CPU or memory). Such metrics are
  // built in to Kubernetes, and have special scaling options on top of those
  // available to normal per-pod metrics using the "pods" source.
  // This is an alpha feature and can be enabled by the HPAContainerMetrics feature flag.
  // +optional
  optional ContainerResourceMetricSource containerResource = 7;

  // external refers to a global metric that is not associated
  // with any Kubernetes object. It allows autoscaling based on information
  // coming from components running outside of cluster
  // (for example length of queue in cloud messaging service, or
  // QPS from loadbalancer running outside of cluster).
  // +optional
  optional ExternalMetricSource external = 5;
}

// MetricStatus describes the last-read state of a single metric.
message MetricStatus {
  // type is the type of metric source.  It will be one of "ContainerResource", "External",
  // "Object", "Pods" or "Resource", each corresponds to a matching field in the object.
  // Note: "ContainerResource" type is available on when the feature-gate
  // HPAContainerMetrics is enabled
  optional string type = 1;

  // object refers to a metric describing a single kubernetes object
  // (for example, hits-per-second on an Ingress object).
  // +optional
  optional ObjectMetricStatus object = 2;

  // pods refers to a metric describing each pod in the current scale target
  // (for example, transactions-processed-per-second).  The values will be
  // averaged together before being compared to the target value.
  // +optional
  optional PodsMetricStatus pods = 3;

  // resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing each pod in the
  // current scale target (e.g. CPU or memory). Such metrics are built in to
  // Kubernetes, and have special scaling options on top of those available
  // to normal per-pod metrics using the "pods" source.
  // +optional
  optional ResourceMetricStatus resource = 4;

  // container resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing a single container in each pod in the
  // current scale target (e.g. CPU or memory). Such metrics are built in to
  // Kubernetes, and have special scaling options on top of those available
  // to normal per-pod metrics using the "pods" source.
  // +optional
  optional ContainerResourceMetricStatus containerResource = 7;

  // external refers to a global metric that is not associated
  // with any Kubernetes object. It allows autoscaling based on information
  // coming from components running outside of cluster
  // (for example length of queue in cloud messaging service, or
  // QPS from loadbalancer running outside of cluster).
  // +optional
  optional ExternalMetricStatus external = 5;
}

// MetricTarget defines the target value, average value, or average utilization of a specific metric
message MetricTarget {
  // type represents whether the metric type is Utilization, Value, or AverageValue
  optional string type = 1;

  // value is the target value of the metric (as a quantity).
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity value = 2;

  // averageValue is the target value of the average of the
  // metric across all relevant pods (as a quantity)
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity averageValue = 3;

  // averageUtilization is the target value of the average of the
  // resource metric across all relevant pods, represented as a percentage of
  // the requested value of the resource for the pods.
  // Currently only valid for Resource metric source type
  // +optional
  optional int32 averageUtilization = 4;
}

// MetricValueStatus holds the current value for a metric
message MetricValueStatus {
  // value is the current value of the metric (as a quantity).
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity value = 1;

  // averageValue is the current value of the average of the
  // metric across all relevant pods (as a quantity)
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity averageValue = 2;

  // currentAverageUtilization is the current value of the average of the
  // resource metric across all relevant pods, represented as a percentage of
  // the requested value of the resource for the pods.
  // +optional
  optional int32 averageUtilization = 3;
}

// ObjectMetricSource indicates how to scale on a metric describing a
// kubernetes object (for example, hits-per-second on an Ingress object).
message ObjectMetricSource {
  // describedObject specifies the descriptions of a object,such as kind,name apiVersion
  optional CrossVersionObjectReference describedObject = 1;

  // target specifies the target value for the given metric
  optional MetricTarget target = 2;

  // metric identifies the target metric by name and selector
  optional MetricIdentifier metric = 3;
}

// ObjectMetricStatus indicates the current value of a metric describing a
// kubernetes object (for example, hits-per-second on an Ingress object).
message ObjectMetricStatus {
  // metric identifies the target metric by name and selector
  optional MetricIdentifier metric = 1;

  // current contains the current value for the given metric
  optional MetricValueStatus current = 2;

  // DescribedObject specifies the descriptions of a object,such as kind,name apiVersion
  optional CrossVersionObjectReference describedObject = 3;
}

// PodsMetricSource indicates how to scale on a metric describing each pod in
// the current scale target (for example, transactions-processed-per-second).
// The values will be averaged together before being compared to the target
// value.
message PodsMetricSource {
  // metric identifies the target metric by name and selector
  optional MetricIdentifier metric = 1;

  // target specifies the target value for the given metric
  optional MetricTarget target = 2;
}

// PodsMetricStatus indicates the current value of a metric describing each pod in
// the current scale target (for example, transactions-processed-per-second).
message PodsMetricStatus {
  // metric identifies the target metric by name and selector
  optional MetricIdentifier metric = 1;

  // current contains the current value for the given metric
  optional MetricValueStatus current = 2;
}

// ResourceMetricSource indicates how to scale on a resource metric known to
// Kubernetes, as specified in requests and limits, describing each pod in the
// current scale target (e.g. CPU or memory).  The values will be averaged
// together before being compared to the target.  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.  Only one "target" type
// should be set.
message ResourceMetricSource {
  // name is the name of the resource in question.
  optional string name = 1;

  // target specifies the target value for the given metric
  optional MetricTarget target = 2;
}

// ResourceMetricStatus indicates the current value of a resource metric known to
// Kubernetes, as specified in requests and limits, describing each pod in the
// current scale target (e.g. CPU or memory).  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.
message ResourceMetricStatus {
  // Name is the name of the resource in question.
  optional string name = 1;

  // current contains the current value for the given metric
  optional MetricValueStatus current = 2;
}

