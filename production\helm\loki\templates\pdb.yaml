{{- if .Values.podDisruptionBudget -}}
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: {{ template "loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "loki.name" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    chart: {{ template "loki.chart" . }}
spec:
  selector:
    matchLabels:
      app: {{ template "loki.name" . }}
{{ toYaml .Values.podDisruptionBudget | indent 2 }}
{{- end }}
