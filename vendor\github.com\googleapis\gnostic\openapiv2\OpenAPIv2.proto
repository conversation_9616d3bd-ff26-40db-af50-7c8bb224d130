// Copyright 2020 Google LLC. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// THIS FILE IS AUTOMATICALLY GENERATED.

syntax = "proto3";

package openapi.v2;

import "google/protobuf/any.proto";

// This option lets the proto compiler generate Java code inside the package
// name (see below) instead of inside an outer class. It creates a simpler
// developer experience by reducing one-level of name nesting and be
// consistent with most programming languages that don't support outer classes.
option java_multiple_files = true;

// The Java outer classname should be the filename in UpperCamelCase. This
// class is only used to hold proto descriptor, so developers don't need to
// work with it directly.
option java_outer_classname = "OpenAPIProto";

// The Java package name must be proto package name with proper prefix.
option java_package = "org.openapi_v2";

// A reasonable prefix for the Objective-C symbols generated from the package.
// It should at a minimum be 3 characters long, all uppercase, and convention
// is to use an abbreviation of the package name. Something short, but
// hopefully unique enough to not conflict with things that may come along in
// the future. 'GPB' is reserved for the protocol buffer implementation itself.
option objc_class_prefix = "OAS";

// The Go package name.
option go_package = "./openapiv2;openapi_v2";

message AdditionalPropertiesItem {
  oneof oneof {
    Schema schema = 1;
    bool boolean = 2;
  }
}

message Any {
  google.protobuf.Any value = 1;
  string yaml = 2;
}

message ApiKeySecurity {
  string type = 1;
  string name = 2;
  string in = 3;
  string description = 4;
  repeated NamedAny vendor_extension = 5;
}

message BasicAuthenticationSecurity {
  string type = 1;
  string description = 2;
  repeated NamedAny vendor_extension = 3;
}

message BodyParameter {
  // A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
  string description = 1;
  // The name of the parameter.
  string name = 2;
  // Determines the location of the parameter.
  string in = 3;
  // Determines whether or not this parameter is required or optional.
  bool required = 4;
  Schema schema = 5;
  repeated NamedAny vendor_extension = 6;
}

// Contact information for the owners of the API.
message Contact {
  // The identifying name of the contact person/organization.
  string name = 1;
  // The URL pointing to the contact information.
  string url = 2;
  // The email address of the contact person/organization.
  string email = 3;
  repeated NamedAny vendor_extension = 4;
}

message Default {
  repeated NamedAny additional_properties = 1;
}

// One or more JSON objects describing the schemas being consumed and produced by the API.
message Definitions {
  repeated NamedSchema additional_properties = 1;
}

message Document {
  // The Swagger version of this document.
  string swagger = 1;
  Info info = 2;
  // The host (name or ip) of the API. Example: 'swagger.io'
  string host = 3;
  // The base path to the API. Example: '/api'.
  string base_path = 4;
  // The transfer protocol of the API.
  repeated string schemes = 5;
  // A list of MIME types accepted by the API.
  repeated string consumes = 6;
  // A list of MIME types the API can produce.
  repeated string produces = 7;
  Paths paths = 8;
  Definitions definitions = 9;
  ParameterDefinitions parameters = 10;
  ResponseDefinitions responses = 11;
  repeated SecurityRequirement security = 12;
  SecurityDefinitions security_definitions = 13;
  repeated Tag tags = 14;
  ExternalDocs external_docs = 15;
  repeated NamedAny vendor_extension = 16;
}

message Examples {
  repeated NamedAny additional_properties = 1;
}

// information about external documentation
message ExternalDocs {
  string description = 1;
  string url = 2;
  repeated NamedAny vendor_extension = 3;
}

// A deterministic version of a JSON Schema object.
message FileSchema {
  string format = 1;
  string title = 2;
  string description = 3;
  Any default = 4;
  repeated string required = 5;
  string type = 6;
  bool read_only = 7;
  ExternalDocs external_docs = 8;
  Any example = 9;
  repeated NamedAny vendor_extension = 10;
}

message FormDataParameterSubSchema {
  // Determines whether or not this parameter is required or optional.
  bool required = 1;
  // Determines the location of the parameter.
  string in = 2;
  // A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
  string description = 3;
  // The name of the parameter.
  string name = 4;
  // allows sending a parameter by name only or with an empty value.
  bool allow_empty_value = 5;
  string type = 6;
  string format = 7;
  PrimitivesItems items = 8;
  string collection_format = 9;
  Any default = 10;
  double maximum = 11;
  bool exclusive_maximum = 12;
  double minimum = 13;
  bool exclusive_minimum = 14;
  int64 max_length = 15;
  int64 min_length = 16;
  string pattern = 17;
  int64 max_items = 18;
  int64 min_items = 19;
  bool unique_items = 20;
  repeated Any enum = 21;
  double multiple_of = 22;
  repeated NamedAny vendor_extension = 23;
}

message Header {
  string type = 1;
  string format = 2;
  PrimitivesItems items = 3;
  string collection_format = 4;
  Any default = 5;
  double maximum = 6;
  bool exclusive_maximum = 7;
  double minimum = 8;
  bool exclusive_minimum = 9;
  int64 max_length = 10;
  int64 min_length = 11;
  string pattern = 12;
  int64 max_items = 13;
  int64 min_items = 14;
  bool unique_items = 15;
  repeated Any enum = 16;
  double multiple_of = 17;
  string description = 18;
  repeated NamedAny vendor_extension = 19;
}

message HeaderParameterSubSchema {
  // Determines whether or not this parameter is required or optional.
  bool required = 1;
  // Determines the location of the parameter.
  string in = 2;
  // A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
  string description = 3;
  // The name of the parameter.
  string name = 4;
  string type = 5;
  string format = 6;
  PrimitivesItems items = 7;
  string collection_format = 8;
  Any default = 9;
  double maximum = 10;
  bool exclusive_maximum = 11;
  double minimum = 12;
  bool exclusive_minimum = 13;
  int64 max_length = 14;
  int64 min_length = 15;
  string pattern = 16;
  int64 max_items = 17;
  int64 min_items = 18;
  bool unique_items = 19;
  repeated Any enum = 20;
  double multiple_of = 21;
  repeated NamedAny vendor_extension = 22;
}

message Headers {
  repeated NamedHeader additional_properties = 1;
}

// General information about the API.
message Info {
  // A unique and precise title of the API.
  string title = 1;
  // A semantic version number of the API.
  string version = 2;
  // A longer description of the API. Should be different from the title.  GitHub Flavored Markdown is allowed.
  string description = 3;
  // The terms of service for the API.
  string terms_of_service = 4;
  Contact contact = 5;
  License license = 6;
  repeated NamedAny vendor_extension = 7;
}

message ItemsItem {
  repeated Schema schema = 1;
}

message JsonReference {
  string _ref = 1;
  string description = 2;
}

message License {
  // The name of the license type. It's encouraged to use an OSI compatible license.
  string name = 1;
  // The URL pointing to the license.
  string url = 2;
  repeated NamedAny vendor_extension = 3;
}

// Automatically-generated message used to represent maps of Any as ordered (name,value) pairs.
message NamedAny {
  // Map key
  string name = 1;
  // Mapped value
  Any value = 2;
}

// Automatically-generated message used to represent maps of Header as ordered (name,value) pairs.
message NamedHeader {
  // Map key
  string name = 1;
  // Mapped value
  Header value = 2;
}

// Automatically-generated message used to represent maps of Parameter as ordered (name,value) pairs.
message NamedParameter {
  // Map key
  string name = 1;
  // Mapped value
  Parameter value = 2;
}

// Automatically-generated message used to represent maps of PathItem as ordered (name,value) pairs.
message NamedPathItem {
  // Map key
  string name = 1;
  // Mapped value
  PathItem value = 2;
}

// Automatically-generated message used to represent maps of Response as ordered (name,value) pairs.
message NamedResponse {
  // Map key
  string name = 1;
  // Mapped value
  Response value = 2;
}

// Automatically-generated message used to represent maps of ResponseValue as ordered (name,value) pairs.
message NamedResponseValue {
  // Map key
  string name = 1;
  // Mapped value
  ResponseValue value = 2;
}

// Automatically-generated message used to represent maps of Schema as ordered (name,value) pairs.
message NamedSchema {
  // Map key
  string name = 1;
  // Mapped value
  Schema value = 2;
}

// Automatically-generated message used to represent maps of SecurityDefinitionsItem as ordered (name,value) pairs.
message NamedSecurityDefinitionsItem {
  // Map key
  string name = 1;
  // Mapped value
  SecurityDefinitionsItem value = 2;
}

// Automatically-generated message used to represent maps of string as ordered (name,value) pairs.
message NamedString {
  // Map key
  string name = 1;
  // Mapped value
  string value = 2;
}

// Automatically-generated message used to represent maps of StringArray as ordered (name,value) pairs.
message NamedStringArray {
  // Map key
  string name = 1;
  // Mapped value
  StringArray value = 2;
}

message NonBodyParameter {
  oneof oneof {
    HeaderParameterSubSchema header_parameter_sub_schema = 1;
    FormDataParameterSubSchema form_data_parameter_sub_schema = 2;
    QueryParameterSubSchema query_parameter_sub_schema = 3;
    PathParameterSubSchema path_parameter_sub_schema = 4;
  }
}

message Oauth2AccessCodeSecurity {
  string type = 1;
  string flow = 2;
  Oauth2Scopes scopes = 3;
  string authorization_url = 4;
  string token_url = 5;
  string description = 6;
  repeated NamedAny vendor_extension = 7;
}

message Oauth2ApplicationSecurity {
  string type = 1;
  string flow = 2;
  Oauth2Scopes scopes = 3;
  string token_url = 4;
  string description = 5;
  repeated NamedAny vendor_extension = 6;
}

message Oauth2ImplicitSecurity {
  string type = 1;
  string flow = 2;
  Oauth2Scopes scopes = 3;
  string authorization_url = 4;
  string description = 5;
  repeated NamedAny vendor_extension = 6;
}

message Oauth2PasswordSecurity {
  string type = 1;
  string flow = 2;
  Oauth2Scopes scopes = 3;
  string token_url = 4;
  string description = 5;
  repeated NamedAny vendor_extension = 6;
}

message Oauth2Scopes {
  repeated NamedString additional_properties = 1;
}

message Operation {
  repeated string tags = 1;
  // A brief summary of the operation.
  string summary = 2;
  // A longer description of the operation, GitHub Flavored Markdown is allowed.
  string description = 3;
  ExternalDocs external_docs = 4;
  // A unique identifier of the operation.
  string operation_id = 5;
  // A list of MIME types the API can produce.
  repeated string produces = 6;
  // A list of MIME types the API can consume.
  repeated string consumes = 7;
  // The parameters needed to send a valid API call.
  repeated ParametersItem parameters = 8;
  Responses responses = 9;
  // The transfer protocol of the API.
  repeated string schemes = 10;
  bool deprecated = 11;
  repeated SecurityRequirement security = 12;
  repeated NamedAny vendor_extension = 13;
}

message Parameter {
  oneof oneof {
    BodyParameter body_parameter = 1;
    NonBodyParameter non_body_parameter = 2;
  }
}

// One or more JSON representations for parameters
message ParameterDefinitions {
  repeated NamedParameter additional_properties = 1;
}

message ParametersItem {
  oneof oneof {
    Parameter parameter = 1;
    JsonReference json_reference = 2;
  }
}

message PathItem {
  string _ref = 1;
  Operation get = 2;
  Operation put = 3;
  Operation post = 4;
  Operation delete = 5;
  Operation options = 6;
  Operation head = 7;
  Operation patch = 8;
  // The parameters needed to send a valid API call.
  repeated ParametersItem parameters = 9;
  repeated NamedAny vendor_extension = 10;
}

message PathParameterSubSchema {
  // Determines whether or not this parameter is required or optional.
  bool required = 1;
  // Determines the location of the parameter.
  string in = 2;
  // A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
  string description = 3;
  // The name of the parameter.
  string name = 4;
  string type = 5;
  string format = 6;
  PrimitivesItems items = 7;
  string collection_format = 8;
  Any default = 9;
  double maximum = 10;
  bool exclusive_maximum = 11;
  double minimum = 12;
  bool exclusive_minimum = 13;
  int64 max_length = 14;
  int64 min_length = 15;
  string pattern = 16;
  int64 max_items = 17;
  int64 min_items = 18;
  bool unique_items = 19;
  repeated Any enum = 20;
  double multiple_of = 21;
  repeated NamedAny vendor_extension = 22;
}

// Relative paths to the individual endpoints. They must be relative to the 'basePath'.
message Paths {
  repeated NamedAny vendor_extension = 1;
  repeated NamedPathItem path = 2;
}

message PrimitivesItems {
  string type = 1;
  string format = 2;
  PrimitivesItems items = 3;
  string collection_format = 4;
  Any default = 5;
  double maximum = 6;
  bool exclusive_maximum = 7;
  double minimum = 8;
  bool exclusive_minimum = 9;
  int64 max_length = 10;
  int64 min_length = 11;
  string pattern = 12;
  int64 max_items = 13;
  int64 min_items = 14;
  bool unique_items = 15;
  repeated Any enum = 16;
  double multiple_of = 17;
  repeated NamedAny vendor_extension = 18;
}

message Properties {
  repeated NamedSchema additional_properties = 1;
}

message QueryParameterSubSchema {
  // Determines whether or not this parameter is required or optional.
  bool required = 1;
  // Determines the location of the parameter.
  string in = 2;
  // A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
  string description = 3;
  // The name of the parameter.
  string name = 4;
  // allows sending a parameter by name only or with an empty value.
  bool allow_empty_value = 5;
  string type = 6;
  string format = 7;
  PrimitivesItems items = 8;
  string collection_format = 9;
  Any default = 10;
  double maximum = 11;
  bool exclusive_maximum = 12;
  double minimum = 13;
  bool exclusive_minimum = 14;
  int64 max_length = 15;
  int64 min_length = 16;
  string pattern = 17;
  int64 max_items = 18;
  int64 min_items = 19;
  bool unique_items = 20;
  repeated Any enum = 21;
  double multiple_of = 22;
  repeated NamedAny vendor_extension = 23;
}

message Response {
  string description = 1;
  SchemaItem schema = 2;
  Headers headers = 3;
  Examples examples = 4;
  repeated NamedAny vendor_extension = 5;
}

// One or more JSON representations for responses
message ResponseDefinitions {
  repeated NamedResponse additional_properties = 1;
}

message ResponseValue {
  oneof oneof {
    Response response = 1;
    JsonReference json_reference = 2;
  }
}

// Response objects names can either be any valid HTTP status code or 'default'.
message Responses {
  repeated NamedResponseValue response_code = 1;
  repeated NamedAny vendor_extension = 2;
}

// A deterministic version of a JSON Schema object.
message Schema {
  string _ref = 1;
  string format = 2;
  string title = 3;
  string description = 4;
  Any default = 5;
  double multiple_of = 6;
  double maximum = 7;
  bool exclusive_maximum = 8;
  double minimum = 9;
  bool exclusive_minimum = 10;
  int64 max_length = 11;
  int64 min_length = 12;
  string pattern = 13;
  int64 max_items = 14;
  int64 min_items = 15;
  bool unique_items = 16;
  int64 max_properties = 17;
  int64 min_properties = 18;
  repeated string required = 19;
  repeated Any enum = 20;
  AdditionalPropertiesItem additional_properties = 21;
  TypeItem type = 22;
  ItemsItem items = 23;
  repeated Schema all_of = 24;
  Properties properties = 25;
  string discriminator = 26;
  bool read_only = 27;
  Xml xml = 28;
  ExternalDocs external_docs = 29;
  Any example = 30;
  repeated NamedAny vendor_extension = 31;
}

message SchemaItem {
  oneof oneof {
    Schema schema = 1;
    FileSchema file_schema = 2;
  }
}

message SecurityDefinitions {
  repeated NamedSecurityDefinitionsItem additional_properties = 1;
}

message SecurityDefinitionsItem {
  oneof oneof {
    BasicAuthenticationSecurity basic_authentication_security = 1;
    ApiKeySecurity api_key_security = 2;
    Oauth2ImplicitSecurity oauth2_implicit_security = 3;
    Oauth2PasswordSecurity oauth2_password_security = 4;
    Oauth2ApplicationSecurity oauth2_application_security = 5;
    Oauth2AccessCodeSecurity oauth2_access_code_security = 6;
  }
}

message SecurityRequirement {
  repeated NamedStringArray additional_properties = 1;
}

message StringArray {
  repeated string value = 1;
}

message Tag {
  string name = 1;
  string description = 2;
  ExternalDocs external_docs = 3;
  repeated NamedAny vendor_extension = 4;
}

message TypeItem {
  repeated string value = 1;
}

// Any property starting with x- is valid.
message VendorExtension {
  repeated NamedAny additional_properties = 1;
}

message Xml {
  string name = 1;
  string namespace = 2;
  string prefix = 3;
  bool attribute = 4;
  bool wrapped = 5;
  repeated NamedAny vendor_extension = 6;
}

