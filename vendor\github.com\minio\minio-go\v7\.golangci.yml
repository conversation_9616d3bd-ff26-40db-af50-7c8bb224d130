linters-settings:
  misspell:
    locale: US

linters:
  disable-all: true
  enable:
    - typecheck
    - goimports
    - misspell
    - revive
    - govet
    - ineffassign
    - gosimple
    - deadcode
    - structcheck
    - gocritic

issues:
  exclude-use-default: false
  exclude:
      # todo fix these when we get enough time.
      - "singleCaseSwitch: should rewrite switch statement to if statement"
      - "unlambda: replace"
      - "captLocal:"
      - "ifElse<PERSON>hain:"
      - "elseif:"
