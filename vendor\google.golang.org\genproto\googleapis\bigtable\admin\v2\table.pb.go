// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.13.0
// source: google/bigtable/admin/v2/table.proto

package admin

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	status "google.golang.org/genproto/googleapis/rpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Indicates the type of the restore source.
type RestoreSourceType int32

const (
	// No restore associated.
	RestoreSourceType_RESTORE_SOURCE_TYPE_UNSPECIFIED RestoreSourceType = 0
	// A backup was used as the source of the restore.
	RestoreSourceType_BACKUP RestoreSourceType = 1
)

// Enum value maps for RestoreSourceType.
var (
	RestoreSourceType_name = map[int32]string{
		0: "RESTORE_SOURCE_TYPE_UNSPECIFIED",
		1: "BACKUP",
	}
	RestoreSourceType_value = map[string]int32{
		"RESTORE_SOURCE_TYPE_UNSPECIFIED": 0,
		"BACKUP":                          1,
	}
)

func (x RestoreSourceType) Enum() *RestoreSourceType {
	p := new(RestoreSourceType)
	*p = x
	return p
}

func (x RestoreSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RestoreSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[0].Descriptor()
}

func (RestoreSourceType) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[0]
}

func (x RestoreSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RestoreSourceType.Descriptor instead.
func (RestoreSourceType) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{0}
}

// Possible timestamp granularities to use when keeping multiple versions
// of data in a table.
type Table_TimestampGranularity int32

const (
	// The user did not specify a granularity. Should not be returned.
	// When specified during table creation, MILLIS will be used.
	Table_TIMESTAMP_GRANULARITY_UNSPECIFIED Table_TimestampGranularity = 0
	// The table keeps data versioned at a granularity of 1ms.
	Table_MILLIS Table_TimestampGranularity = 1
)

// Enum value maps for Table_TimestampGranularity.
var (
	Table_TimestampGranularity_name = map[int32]string{
		0: "TIMESTAMP_GRANULARITY_UNSPECIFIED",
		1: "MILLIS",
	}
	Table_TimestampGranularity_value = map[string]int32{
		"TIMESTAMP_GRANULARITY_UNSPECIFIED": 0,
		"MILLIS":                            1,
	}
)

func (x Table_TimestampGranularity) Enum() *Table_TimestampGranularity {
	p := new(Table_TimestampGranularity)
	*p = x
	return p
}

func (x Table_TimestampGranularity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Table_TimestampGranularity) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[1].Descriptor()
}

func (Table_TimestampGranularity) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[1]
}

func (x Table_TimestampGranularity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Table_TimestampGranularity.Descriptor instead.
func (Table_TimestampGranularity) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{1, 0}
}

// Defines a view over a table's fields.
type Table_View int32

const (
	// Uses the default view for each method as documented in its request.
	Table_VIEW_UNSPECIFIED Table_View = 0
	// Only populates `name`.
	Table_NAME_ONLY Table_View = 1
	// Only populates `name` and fields related to the table's schema.
	Table_SCHEMA_VIEW Table_View = 2
	// Only populates `name` and fields related to the table's replication
	// state.
	Table_REPLICATION_VIEW Table_View = 3
	// Only populates 'name' and fields related to the table's encryption state.
	Table_ENCRYPTION_VIEW Table_View = 5
	// Populates all fields.
	Table_FULL Table_View = 4
)

// Enum value maps for Table_View.
var (
	Table_View_name = map[int32]string{
		0: "VIEW_UNSPECIFIED",
		1: "NAME_ONLY",
		2: "SCHEMA_VIEW",
		3: "REPLICATION_VIEW",
		5: "ENCRYPTION_VIEW",
		4: "FULL",
	}
	Table_View_value = map[string]int32{
		"VIEW_UNSPECIFIED": 0,
		"NAME_ONLY":        1,
		"SCHEMA_VIEW":      2,
		"REPLICATION_VIEW": 3,
		"ENCRYPTION_VIEW":  5,
		"FULL":             4,
	}
)

func (x Table_View) Enum() *Table_View {
	p := new(Table_View)
	*p = x
	return p
}

func (x Table_View) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Table_View) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[2].Descriptor()
}

func (Table_View) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[2]
}

func (x Table_View) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Table_View.Descriptor instead.
func (Table_View) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{1, 1}
}

// Table replication states.
type Table_ClusterState_ReplicationState int32

const (
	// The replication state of the table is unknown in this cluster.
	Table_ClusterState_STATE_NOT_KNOWN Table_ClusterState_ReplicationState = 0
	// The cluster was recently created, and the table must finish copying
	// over pre-existing data from other clusters before it can begin
	// receiving live replication updates and serving Data API requests.
	Table_ClusterState_INITIALIZING Table_ClusterState_ReplicationState = 1
	// The table is temporarily unable to serve Data API requests from this
	// cluster due to planned internal maintenance.
	Table_ClusterState_PLANNED_MAINTENANCE Table_ClusterState_ReplicationState = 2
	// The table is temporarily unable to serve Data API requests from this
	// cluster due to unplanned or emergency maintenance.
	Table_ClusterState_UNPLANNED_MAINTENANCE Table_ClusterState_ReplicationState = 3
	// The table can serve Data API requests from this cluster. Depending on
	// replication delay, reads may not immediately reflect the state of the
	// table in other clusters.
	Table_ClusterState_READY Table_ClusterState_ReplicationState = 4
	// The table is fully created and ready for use after a restore, and is
	// being optimized for performance. When optimizations are complete, the
	// table will transition to `READY` state.
	Table_ClusterState_READY_OPTIMIZING Table_ClusterState_ReplicationState = 5
)

// Enum value maps for Table_ClusterState_ReplicationState.
var (
	Table_ClusterState_ReplicationState_name = map[int32]string{
		0: "STATE_NOT_KNOWN",
		1: "INITIALIZING",
		2: "PLANNED_MAINTENANCE",
		3: "UNPLANNED_MAINTENANCE",
		4: "READY",
		5: "READY_OPTIMIZING",
	}
	Table_ClusterState_ReplicationState_value = map[string]int32{
		"STATE_NOT_KNOWN":       0,
		"INITIALIZING":          1,
		"PLANNED_MAINTENANCE":   2,
		"UNPLANNED_MAINTENANCE": 3,
		"READY":                 4,
		"READY_OPTIMIZING":      5,
	}
)

func (x Table_ClusterState_ReplicationState) Enum() *Table_ClusterState_ReplicationState {
	p := new(Table_ClusterState_ReplicationState)
	*p = x
	return p
}

func (x Table_ClusterState_ReplicationState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Table_ClusterState_ReplicationState) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[3].Descriptor()
}

func (Table_ClusterState_ReplicationState) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[3]
}

func (x Table_ClusterState_ReplicationState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Table_ClusterState_ReplicationState.Descriptor instead.
func (Table_ClusterState_ReplicationState) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{1, 0, 0}
}

// Possible encryption types for a resource.
type EncryptionInfo_EncryptionType int32

const (
	// Encryption type was not specified, though data at rest remains encrypted.
	EncryptionInfo_ENCRYPTION_TYPE_UNSPECIFIED EncryptionInfo_EncryptionType = 0
	// The data backing this resource is encrypted at rest with a key that is
	// fully managed by Google. No key version or status will be populated.
	// This is the default state.
	EncryptionInfo_GOOGLE_DEFAULT_ENCRYPTION EncryptionInfo_EncryptionType = 1
	// The data backing this resource is encrypted at rest with a key that is
	// managed by the customer.
	// The in-use version of the key and its status are populated for
	// CMEK-protected tables.
	// CMEK-protected backups are pinned to the key version that was in use at
	// the time the backup was taken. This key version is populated but its
	// status is not tracked and is reported as `UNKNOWN`.
	EncryptionInfo_CUSTOMER_MANAGED_ENCRYPTION EncryptionInfo_EncryptionType = 2
)

// Enum value maps for EncryptionInfo_EncryptionType.
var (
	EncryptionInfo_EncryptionType_name = map[int32]string{
		0: "ENCRYPTION_TYPE_UNSPECIFIED",
		1: "GOOGLE_DEFAULT_ENCRYPTION",
		2: "CUSTOMER_MANAGED_ENCRYPTION",
	}
	EncryptionInfo_EncryptionType_value = map[string]int32{
		"ENCRYPTION_TYPE_UNSPECIFIED": 0,
		"GOOGLE_DEFAULT_ENCRYPTION":   1,
		"CUSTOMER_MANAGED_ENCRYPTION": 2,
	}
)

func (x EncryptionInfo_EncryptionType) Enum() *EncryptionInfo_EncryptionType {
	p := new(EncryptionInfo_EncryptionType)
	*p = x
	return p
}

func (x EncryptionInfo_EncryptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EncryptionInfo_EncryptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[4].Descriptor()
}

func (EncryptionInfo_EncryptionType) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[4]
}

func (x EncryptionInfo_EncryptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EncryptionInfo_EncryptionType.Descriptor instead.
func (EncryptionInfo_EncryptionType) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{4, 0}
}

// Possible states of a snapshot.
type Snapshot_State int32

const (
	// The state of the snapshot could not be determined.
	Snapshot_STATE_NOT_KNOWN Snapshot_State = 0
	// The snapshot has been successfully created and can serve all requests.
	Snapshot_READY Snapshot_State = 1
	// The snapshot is currently being created, and may be destroyed if the
	// creation process encounters an error. A snapshot may not be restored to a
	// table while it is being created.
	Snapshot_CREATING Snapshot_State = 2
)

// Enum value maps for Snapshot_State.
var (
	Snapshot_State_name = map[int32]string{
		0: "STATE_NOT_KNOWN",
		1: "READY",
		2: "CREATING",
	}
	Snapshot_State_value = map[string]int32{
		"STATE_NOT_KNOWN": 0,
		"READY":           1,
		"CREATING":        2,
	}
)

func (x Snapshot_State) Enum() *Snapshot_State {
	p := new(Snapshot_State)
	*p = x
	return p
}

func (x Snapshot_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Snapshot_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[5].Descriptor()
}

func (Snapshot_State) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[5]
}

func (x Snapshot_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Snapshot_State.Descriptor instead.
func (Snapshot_State) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{5, 0}
}

// Indicates the current state of the backup.
type Backup_State int32

const (
	// Not specified.
	Backup_STATE_UNSPECIFIED Backup_State = 0
	// The pending backup is still being created. Operations on the
	// backup may fail with `FAILED_PRECONDITION` in this state.
	Backup_CREATING Backup_State = 1
	// The backup is complete and ready for use.
	Backup_READY Backup_State = 2
)

// Enum value maps for Backup_State.
var (
	Backup_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "CREATING",
		2: "READY",
	}
	Backup_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"CREATING":          1,
		"READY":             2,
	}
)

func (x Backup_State) Enum() *Backup_State {
	p := new(Backup_State)
	*p = x
	return p
}

func (x Backup_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Backup_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_table_proto_enumTypes[6].Descriptor()
}

func (Backup_State) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_table_proto_enumTypes[6]
}

func (x Backup_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Backup_State.Descriptor instead.
func (Backup_State) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{6, 0}
}

// Information about a table restore.
type RestoreInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of the restore source.
	SourceType RestoreSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=google.bigtable.admin.v2.RestoreSourceType" json:"source_type,omitempty"`
	// Information about the source used to restore the table.
	//
	// Types that are assignable to SourceInfo:
	//	*RestoreInfo_BackupInfo
	SourceInfo isRestoreInfo_SourceInfo `protobuf_oneof:"source_info"`
}

func (x *RestoreInfo) Reset() {
	*x = RestoreInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestoreInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreInfo) ProtoMessage() {}

func (x *RestoreInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreInfo.ProtoReflect.Descriptor instead.
func (*RestoreInfo) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{0}
}

func (x *RestoreInfo) GetSourceType() RestoreSourceType {
	if x != nil {
		return x.SourceType
	}
	return RestoreSourceType_RESTORE_SOURCE_TYPE_UNSPECIFIED
}

func (m *RestoreInfo) GetSourceInfo() isRestoreInfo_SourceInfo {
	if m != nil {
		return m.SourceInfo
	}
	return nil
}

func (x *RestoreInfo) GetBackupInfo() *BackupInfo {
	if x, ok := x.GetSourceInfo().(*RestoreInfo_BackupInfo); ok {
		return x.BackupInfo
	}
	return nil
}

type isRestoreInfo_SourceInfo interface {
	isRestoreInfo_SourceInfo()
}

type RestoreInfo_BackupInfo struct {
	// Information about the backup used to restore the table. The backup
	// may no longer exist.
	BackupInfo *BackupInfo `protobuf:"bytes,2,opt,name=backup_info,json=backupInfo,proto3,oneof"`
}

func (*RestoreInfo_BackupInfo) isRestoreInfo_SourceInfo() {}

// A collection of user data indexed by row, column, and timestamp.
// Each table is served using the resources of its parent cluster.
type Table struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique name of the table. Values are of the form
	// `projects/{project}/instances/{instance}/tables/[_a-zA-Z0-9][-_.a-zA-Z0-9]*`.
	// Views: `NAME_ONLY`, `SCHEMA_VIEW`, `REPLICATION_VIEW`, `FULL`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Output only. Map from cluster ID to per-cluster table state.
	// If it could not be determined whether or not the table has data in a
	// particular cluster (for example, if its zone is unavailable), then
	// there will be an entry for the cluster with UNKNOWN `replication_status`.
	// Views: `REPLICATION_VIEW`, `ENCRYPTION_VIEW`, `FULL`
	ClusterStates map[string]*Table_ClusterState `protobuf:"bytes,2,rep,name=cluster_states,json=clusterStates,proto3" json:"cluster_states,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// (`CreationOnly`)
	// The column families configured for this table, mapped by column family ID.
	// Views: `SCHEMA_VIEW`, `FULL`
	ColumnFamilies map[string]*ColumnFamily `protobuf:"bytes,3,rep,name=column_families,json=columnFamilies,proto3" json:"column_families,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// (`CreationOnly`)
	// The granularity (i.e. `MILLIS`) at which timestamps are stored in
	// this table. Timestamps not matching the granularity will be rejected.
	// If unspecified at creation time, the value will be set to `MILLIS`.
	// Views: `SCHEMA_VIEW`, `FULL`.
	Granularity Table_TimestampGranularity `protobuf:"varint,4,opt,name=granularity,proto3,enum=google.bigtable.admin.v2.Table_TimestampGranularity" json:"granularity,omitempty"`
	// Output only. If this table was restored from another data source (e.g. a
	// backup), this field will be populated with information about the restore.
	RestoreInfo *RestoreInfo `protobuf:"bytes,6,opt,name=restore_info,json=restoreInfo,proto3" json:"restore_info,omitempty"`
}

func (x *Table) Reset() {
	*x = Table{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Table) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Table) ProtoMessage() {}

func (x *Table) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Table.ProtoReflect.Descriptor instead.
func (*Table) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{1}
}

func (x *Table) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Table) GetClusterStates() map[string]*Table_ClusterState {
	if x != nil {
		return x.ClusterStates
	}
	return nil
}

func (x *Table) GetColumnFamilies() map[string]*ColumnFamily {
	if x != nil {
		return x.ColumnFamilies
	}
	return nil
}

func (x *Table) GetGranularity() Table_TimestampGranularity {
	if x != nil {
		return x.Granularity
	}
	return Table_TIMESTAMP_GRANULARITY_UNSPECIFIED
}

func (x *Table) GetRestoreInfo() *RestoreInfo {
	if x != nil {
		return x.RestoreInfo
	}
	return nil
}

// A set of columns within a table which share a common configuration.
type ColumnFamily struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Garbage collection rule specified as a protobuf.
	// Must serialize to at most 500 bytes.
	//
	// NOTE: Garbage collection executes opportunistically in the background, and
	// so it's possible for reads to return a cell even if it matches the active
	// GC expression for its family.
	GcRule *GcRule `protobuf:"bytes,1,opt,name=gc_rule,json=gcRule,proto3" json:"gc_rule,omitempty"`
}

func (x *ColumnFamily) Reset() {
	*x = ColumnFamily{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ColumnFamily) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ColumnFamily) ProtoMessage() {}

func (x *ColumnFamily) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ColumnFamily.ProtoReflect.Descriptor instead.
func (*ColumnFamily) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{2}
}

func (x *ColumnFamily) GetGcRule() *GcRule {
	if x != nil {
		return x.GcRule
	}
	return nil
}

// Rule for determining which cells to delete during garbage collection.
type GcRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Garbage collection rules.
	//
	// Types that are assignable to Rule:
	//	*GcRule_MaxNumVersions
	//	*GcRule_MaxAge
	//	*GcRule_Intersection_
	//	*GcRule_Union_
	Rule isGcRule_Rule `protobuf_oneof:"rule"`
}

func (x *GcRule) Reset() {
	*x = GcRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GcRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GcRule) ProtoMessage() {}

func (x *GcRule) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GcRule.ProtoReflect.Descriptor instead.
func (*GcRule) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{3}
}

func (m *GcRule) GetRule() isGcRule_Rule {
	if m != nil {
		return m.Rule
	}
	return nil
}

func (x *GcRule) GetMaxNumVersions() int32 {
	if x, ok := x.GetRule().(*GcRule_MaxNumVersions); ok {
		return x.MaxNumVersions
	}
	return 0
}

func (x *GcRule) GetMaxAge() *durationpb.Duration {
	if x, ok := x.GetRule().(*GcRule_MaxAge); ok {
		return x.MaxAge
	}
	return nil
}

func (x *GcRule) GetIntersection() *GcRule_Intersection {
	if x, ok := x.GetRule().(*GcRule_Intersection_); ok {
		return x.Intersection
	}
	return nil
}

func (x *GcRule) GetUnion() *GcRule_Union {
	if x, ok := x.GetRule().(*GcRule_Union_); ok {
		return x.Union
	}
	return nil
}

type isGcRule_Rule interface {
	isGcRule_Rule()
}

type GcRule_MaxNumVersions struct {
	// Delete all cells in a column except the most recent N.
	MaxNumVersions int32 `protobuf:"varint,1,opt,name=max_num_versions,json=maxNumVersions,proto3,oneof"`
}

type GcRule_MaxAge struct {
	// Delete cells in a column older than the given age.
	// Values must be at least one millisecond, and will be truncated to
	// microsecond granularity.
	MaxAge *durationpb.Duration `protobuf:"bytes,2,opt,name=max_age,json=maxAge,proto3,oneof"`
}

type GcRule_Intersection_ struct {
	// Delete cells that would be deleted by every nested rule.
	Intersection *GcRule_Intersection `protobuf:"bytes,3,opt,name=intersection,proto3,oneof"`
}

type GcRule_Union_ struct {
	// Delete cells that would be deleted by any nested rule.
	Union *GcRule_Union `protobuf:"bytes,4,opt,name=union,proto3,oneof"`
}

func (*GcRule_MaxNumVersions) isGcRule_Rule() {}

func (*GcRule_MaxAge) isGcRule_Rule() {}

func (*GcRule_Intersection_) isGcRule_Rule() {}

func (*GcRule_Union_) isGcRule_Rule() {}

// Encryption information for a given resource.
// If this resource is protected with customer managed encryption, the in-use
// Cloud Key Management Service (Cloud KMS) key version is specified along with
// its status.
type EncryptionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The type of encryption used to protect this resource.
	EncryptionType EncryptionInfo_EncryptionType `protobuf:"varint,3,opt,name=encryption_type,json=encryptionType,proto3,enum=google.bigtable.admin.v2.EncryptionInfo_EncryptionType" json:"encryption_type,omitempty"`
	// Output only. The status of encrypt/decrypt calls on underlying data for
	// this resource. Regardless of status, the existing data is always encrypted
	// at rest.
	EncryptionStatus *status.Status `protobuf:"bytes,4,opt,name=encryption_status,json=encryptionStatus,proto3" json:"encryption_status,omitempty"`
	// Output only. The version of the Cloud KMS key specified in the parent
	// cluster that is in use for the data underlying this table.
	KmsKeyVersion string `protobuf:"bytes,2,opt,name=kms_key_version,json=kmsKeyVersion,proto3" json:"kms_key_version,omitempty"`
}

func (x *EncryptionInfo) Reset() {
	*x = EncryptionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EncryptionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncryptionInfo) ProtoMessage() {}

func (x *EncryptionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncryptionInfo.ProtoReflect.Descriptor instead.
func (*EncryptionInfo) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{4}
}

func (x *EncryptionInfo) GetEncryptionType() EncryptionInfo_EncryptionType {
	if x != nil {
		return x.EncryptionType
	}
	return EncryptionInfo_ENCRYPTION_TYPE_UNSPECIFIED
}

func (x *EncryptionInfo) GetEncryptionStatus() *status.Status {
	if x != nil {
		return x.EncryptionStatus
	}
	return nil
}

func (x *EncryptionInfo) GetKmsKeyVersion() string {
	if x != nil {
		return x.KmsKeyVersion
	}
	return ""
}

// A snapshot of a table at a particular time. A snapshot can be used as a
// checkpoint for data restoration or a data source for a new table.
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
type Snapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The unique name of the snapshot.
	// Values are of the form
	// `projects/{project}/instances/{instance}/clusters/{cluster}/snapshots/{snapshot}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Output only. The source table at the time the snapshot was taken.
	SourceTable *Table `protobuf:"bytes,2,opt,name=source_table,json=sourceTable,proto3" json:"source_table,omitempty"`
	// Output only. The size of the data in the source table at the time the
	// snapshot was taken. In some cases, this value may be computed
	// asynchronously via a background process and a placeholder of 0 will be used
	// in the meantime.
	DataSizeBytes int64 `protobuf:"varint,3,opt,name=data_size_bytes,json=dataSizeBytes,proto3" json:"data_size_bytes,omitempty"`
	// Output only. The time when the snapshot is created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. The time when the snapshot will be deleted. The maximum amount
	// of time a snapshot can stay active is 365 days. If 'ttl' is not specified,
	// the default maximum of 365 days will be used.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	// Output only. The current state of the snapshot.
	State Snapshot_State `protobuf:"varint,6,opt,name=state,proto3,enum=google.bigtable.admin.v2.Snapshot_State" json:"state,omitempty"`
	// Output only. Description of the snapshot.
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Snapshot) Reset() {
	*x = Snapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Snapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Snapshot) ProtoMessage() {}

func (x *Snapshot) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Snapshot.ProtoReflect.Descriptor instead.
func (*Snapshot) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{5}
}

func (x *Snapshot) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Snapshot) GetSourceTable() *Table {
	if x != nil {
		return x.SourceTable
	}
	return nil
}

func (x *Snapshot) GetDataSizeBytes() int64 {
	if x != nil {
		return x.DataSizeBytes
	}
	return 0
}

func (x *Snapshot) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Snapshot) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Snapshot) GetState() Snapshot_State {
	if x != nil {
		return x.State
	}
	return Snapshot_STATE_NOT_KNOWN
}

func (x *Snapshot) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// A backup of a Cloud Bigtable table.
type Backup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. A globally unique identifier for the backup which cannot be
	// changed. Values are of the form
	// `projects/{project}/instances/{instance}/clusters/{cluster}/
	//    backups/[_a-zA-Z0-9][-_.a-zA-Z0-9]*`
	// The final segment of the name must be between 1 and 50 characters
	// in length.
	//
	// The backup is stored in the cluster identified by the prefix of the backup
	// name of the form
	// `projects/{project}/instances/{instance}/clusters/{cluster}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. Immutable. Name of the table from which this backup was created.
	// This needs to be in the same instance as the backup. Values are of the form
	// `projects/{project}/instances/{instance}/tables/{source_table}`.
	SourceTable string `protobuf:"bytes,2,opt,name=source_table,json=sourceTable,proto3" json:"source_table,omitempty"`
	// Required. The expiration time of the backup, with microseconds
	// granularity that must be at least 6 hours and at most 30 days
	// from the time the request is received. Once the `expire_time`
	// has passed, Cloud Bigtable will delete the backup and free the
	// resources used by the backup.
	ExpireTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// Output only. `start_time` is the time that the backup was started
	// (i.e. approximately the time the
	// [CreateBackup][google.bigtable.admin.v2.BigtableTableAdmin.CreateBackup]
	// request is received).  The row data in this backup will be no older than
	// this timestamp.
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// Output only. `end_time` is the time that the backup was finished. The row
	// data in the backup will be no newer than this timestamp.
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// Output only. Size of the backup in bytes.
	SizeBytes int64 `protobuf:"varint,6,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	// Output only. The current state of the backup.
	State Backup_State `protobuf:"varint,7,opt,name=state,proto3,enum=google.bigtable.admin.v2.Backup_State" json:"state,omitempty"`
	// Output only. The encryption information for the backup.
	EncryptionInfo *EncryptionInfo `protobuf:"bytes,9,opt,name=encryption_info,json=encryptionInfo,proto3" json:"encryption_info,omitempty"`
}

func (x *Backup) Reset() {
	*x = Backup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Backup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Backup) ProtoMessage() {}

func (x *Backup) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Backup.ProtoReflect.Descriptor instead.
func (*Backup) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{6}
}

func (x *Backup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Backup) GetSourceTable() string {
	if x != nil {
		return x.SourceTable
	}
	return ""
}

func (x *Backup) GetExpireTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireTime
	}
	return nil
}

func (x *Backup) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Backup) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Backup) GetSizeBytes() int64 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *Backup) GetState() Backup_State {
	if x != nil {
		return x.State
	}
	return Backup_STATE_UNSPECIFIED
}

func (x *Backup) GetEncryptionInfo() *EncryptionInfo {
	if x != nil {
		return x.EncryptionInfo
	}
	return nil
}

// Information about a backup.
type BackupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. Name of the backup.
	Backup string `protobuf:"bytes,1,opt,name=backup,proto3" json:"backup,omitempty"`
	// Output only. The time that the backup was started. Row data in the backup
	// will be no older than this timestamp.
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// Output only. This time that the backup was finished. Row data in the
	// backup will be no newer than this timestamp.
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// Output only. Name of the table the backup was created from.
	SourceTable string `protobuf:"bytes,4,opt,name=source_table,json=sourceTable,proto3" json:"source_table,omitempty"`
}

func (x *BackupInfo) Reset() {
	*x = BackupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BackupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupInfo) ProtoMessage() {}

func (x *BackupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupInfo.ProtoReflect.Descriptor instead.
func (*BackupInfo) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{7}
}

func (x *BackupInfo) GetBackup() string {
	if x != nil {
		return x.Backup
	}
	return ""
}

func (x *BackupInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *BackupInfo) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *BackupInfo) GetSourceTable() string {
	if x != nil {
		return x.SourceTable
	}
	return ""
}

// The state of a table's data in a particular cluster.
type Table_ClusterState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The state of replication for the table in this cluster.
	ReplicationState Table_ClusterState_ReplicationState `protobuf:"varint,1,opt,name=replication_state,json=replicationState,proto3,enum=google.bigtable.admin.v2.Table_ClusterState_ReplicationState" json:"replication_state,omitempty"`
	// Output only. The encryption information for the table in this cluster.
	// If the encryption key protecting this resource is customer managed, then
	// its version can be rotated in Cloud Key Management Service (Cloud KMS).
	// The primary version of the key and its status will be reflected here when
	// changes propagate from Cloud KMS.
	EncryptionInfo []*EncryptionInfo `protobuf:"bytes,2,rep,name=encryption_info,json=encryptionInfo,proto3" json:"encryption_info,omitempty"`
}

func (x *Table_ClusterState) Reset() {
	*x = Table_ClusterState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Table_ClusterState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Table_ClusterState) ProtoMessage() {}

func (x *Table_ClusterState) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Table_ClusterState.ProtoReflect.Descriptor instead.
func (*Table_ClusterState) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Table_ClusterState) GetReplicationState() Table_ClusterState_ReplicationState {
	if x != nil {
		return x.ReplicationState
	}
	return Table_ClusterState_STATE_NOT_KNOWN
}

func (x *Table_ClusterState) GetEncryptionInfo() []*EncryptionInfo {
	if x != nil {
		return x.EncryptionInfo
	}
	return nil
}

// A GcRule which deletes cells matching all of the given rules.
type GcRule_Intersection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Only delete cells which would be deleted by every element of `rules`.
	Rules []*GcRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *GcRule_Intersection) Reset() {
	*x = GcRule_Intersection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GcRule_Intersection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GcRule_Intersection) ProtoMessage() {}

func (x *GcRule_Intersection) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GcRule_Intersection.ProtoReflect.Descriptor instead.
func (*GcRule_Intersection) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GcRule_Intersection) GetRules() []*GcRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

// A GcRule which deletes cells matching any of the given rules.
type GcRule_Union struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Delete cells which would be deleted by any element of `rules`.
	Rules []*GcRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *GcRule_Union) Reset() {
	*x = GcRule_Union{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GcRule_Union) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GcRule_Union) ProtoMessage() {}

func (x *GcRule_Union) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_table_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GcRule_Union.ProtoReflect.Descriptor instead.
func (*GcRule_Union) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_table_proto_rawDescGZIP(), []int{3, 1}
}

func (x *GcRule_Union) GetRules() []*GcRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

var File_google_bigtable_admin_v2_table_proto protoreflect.FileDescriptor

var file_google_bigtable_admin_v2_table_proto_rawDesc = []byte{
	0x0a, 0x24, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x32, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0d, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0xd6, 0x09, 0x0a,
	0x05, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x0e, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x0f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f,
	0x66, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x46, 0x61, 0x6d, 0x69, 0x6c,
	0x69, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x47, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x0b,
	0x67, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x48, 0x0a, 0x0c, 0x72,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xe3, 0x02, 0x0a, 0x0c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x6a, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x10, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0e, 0x65, 0x6e, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8e, 0x01, 0x0a, 0x10, 0x52,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49,
	0x5a, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4c, 0x41, 0x4e, 0x4e, 0x45,
	0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x55, 0x4e, 0x50, 0x4c, 0x41, 0x4e, 0x4e, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x49,
	0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x4f,
	0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x1a, 0x6e, 0x0a, 0x12, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x69, 0x0a, 0x13, 0x43,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x49, 0x0a, 0x14, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x47, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x25,
	0x0a, 0x21, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x5f, 0x47, 0x52, 0x41, 0x4e,
	0x55, 0x4c, 0x41, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x49, 0x4c, 0x4c, 0x49, 0x53, 0x10,
	0x01, 0x22, 0x71, 0x0a, 0x04, 0x56, 0x69, 0x65, 0x77, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x0f,
	0x0a, 0x0b, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x41, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12,
	0x14, 0x0a, 0x10, 0x52, 0x45, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56,
	0x49, 0x45, 0x57, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x55,
	0x4c, 0x4c, 0x10, 0x04, 0x3a, 0x5f, 0xea, 0x41, 0x5c, 0x0a, 0x22, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x36, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x7d, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x7d, 0x22, 0x49, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x46,
	0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x07, 0x67, 0x63, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x63, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x67, 0x63, 0x52, 0x75, 0x6c, 0x65,
	0x22, 0x90, 0x03, 0x0a, 0x06, 0x47, 0x63, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x6d,
	0x61, 0x78, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x12, 0x53, 0x0a,
	0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x47,
	0x63, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x63, 0x52,
	0x75, 0x6c, 0x65, 0x2e, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x05, 0x75, 0x6e, 0x69,
	0x6f, 0x6e, 0x1a, 0x46, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x63, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x1a, 0x3f, 0x0a, 0x05, 0x55, 0x6e,
	0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x63,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x42, 0x06, 0x0a, 0x04, 0x72,
	0x75, 0x6c, 0x65, 0x22, 0x8a, 0x03, 0x0a, 0x0e, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x65, 0x0a, 0x0f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x37, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0e, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a,
	0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x10, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x58, 0x0a, 0x0f, 0x6b, 0x6d, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0xe0, 0x41,
	0x03, 0xfa, 0x41, 0x2a, 0x0a, 0x28, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6b, 0x6d, 0x73, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x43, 0x72,
	0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d,
	0x6b, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x71, 0x0a,
	0x0e, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x1b, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55,
	0x4c, 0x54, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12,
	0x1f, 0x0a, 0x1b, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x41,
	0x47, 0x45, 0x44, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02,
	0x22, 0x9a, 0x04, 0x0a, 0x08, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x42, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x64, 0x61, 0x74, 0x61, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76,
	0x32, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02,
	0x3a, 0x7b, 0xea, 0x41, 0x78, 0x0a, 0x25, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x4f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d,
	0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x73, 0x2f, 0x7b, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x7d, 0x22, 0xf9, 0x04,
	0x0a, 0x06, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xe0, 0x41, 0x05, 0xe0, 0x41, 0x02, 0x52,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x41,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x56, 0x0a, 0x0f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x37, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x10, 0x02, 0x3a, 0x75, 0xea, 0x41, 0x72, 0x0a, 0x23, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x4b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d,
	0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x7d, 0x22, 0xcd, 0x01, 0x0a, 0x0a, 0x42, 0x61,
	0x63, 0x6b, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x06, 0x62, 0x61, 0x63, 0x6b,
	0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x06, 0x62,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0b, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x2a, 0x44, 0x0a, 0x11, 0x52, 0x65, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x1f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x41, 0x43, 0x4b, 0x55, 0x50, 0x10, 0x01, 0x42,
	0xfc, 0x02, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32,
	0x42, 0x0a, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3d,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x32, 0x3b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0xaa, 0x02, 0x1e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x42, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x56, 0x32, 0xca, 0x02,
	0x1e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x42, 0x69,
	0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x32, 0xea,
	0x02, 0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a,
	0x3a, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x3a, 0x3a, 0x56, 0x32, 0xea, 0x41, 0xa6, 0x01, 0x0a, 0x28, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6b,
	0x6d, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x7a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x6b, 0x65, 0x79, 0x52,
	0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x6b, 0x65, 0x79, 0x5f, 0x72, 0x69, 0x6e, 0x67, 0x7d, 0x2f,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79, 0x73, 0x2f, 0x7b, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x6f, 0x5f, 0x6b, 0x65, 0x79, 0x7d, 0x2f, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65,
	0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x6f, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_bigtable_admin_v2_table_proto_rawDescOnce sync.Once
	file_google_bigtable_admin_v2_table_proto_rawDescData = file_google_bigtable_admin_v2_table_proto_rawDesc
)

func file_google_bigtable_admin_v2_table_proto_rawDescGZIP() []byte {
	file_google_bigtable_admin_v2_table_proto_rawDescOnce.Do(func() {
		file_google_bigtable_admin_v2_table_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_bigtable_admin_v2_table_proto_rawDescData)
	})
	return file_google_bigtable_admin_v2_table_proto_rawDescData
}

var file_google_bigtable_admin_v2_table_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_google_bigtable_admin_v2_table_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_google_bigtable_admin_v2_table_proto_goTypes = []interface{}{
	(RestoreSourceType)(0),                   // 0: google.bigtable.admin.v2.RestoreSourceType
	(Table_TimestampGranularity)(0),          // 1: google.bigtable.admin.v2.Table.TimestampGranularity
	(Table_View)(0),                          // 2: google.bigtable.admin.v2.Table.View
	(Table_ClusterState_ReplicationState)(0), // 3: google.bigtable.admin.v2.Table.ClusterState.ReplicationState
	(EncryptionInfo_EncryptionType)(0),       // 4: google.bigtable.admin.v2.EncryptionInfo.EncryptionType
	(Snapshot_State)(0),                      // 5: google.bigtable.admin.v2.Snapshot.State
	(Backup_State)(0),                        // 6: google.bigtable.admin.v2.Backup.State
	(*RestoreInfo)(nil),                      // 7: google.bigtable.admin.v2.RestoreInfo
	(*Table)(nil),                            // 8: google.bigtable.admin.v2.Table
	(*ColumnFamily)(nil),                     // 9: google.bigtable.admin.v2.ColumnFamily
	(*GcRule)(nil),                           // 10: google.bigtable.admin.v2.GcRule
	(*EncryptionInfo)(nil),                   // 11: google.bigtable.admin.v2.EncryptionInfo
	(*Snapshot)(nil),                         // 12: google.bigtable.admin.v2.Snapshot
	(*Backup)(nil),                           // 13: google.bigtable.admin.v2.Backup
	(*BackupInfo)(nil),                       // 14: google.bigtable.admin.v2.BackupInfo
	(*Table_ClusterState)(nil),               // 15: google.bigtable.admin.v2.Table.ClusterState
	nil,                                      // 16: google.bigtable.admin.v2.Table.ClusterStatesEntry
	nil,                                      // 17: google.bigtable.admin.v2.Table.ColumnFamiliesEntry
	(*GcRule_Intersection)(nil),              // 18: google.bigtable.admin.v2.GcRule.Intersection
	(*GcRule_Union)(nil),                     // 19: google.bigtable.admin.v2.GcRule.Union
	(*durationpb.Duration)(nil),              // 20: google.protobuf.Duration
	(*status.Status)(nil),                    // 21: google.rpc.Status
	(*timestamppb.Timestamp)(nil),            // 22: google.protobuf.Timestamp
}
var file_google_bigtable_admin_v2_table_proto_depIdxs = []int32{
	0,  // 0: google.bigtable.admin.v2.RestoreInfo.source_type:type_name -> google.bigtable.admin.v2.RestoreSourceType
	14, // 1: google.bigtable.admin.v2.RestoreInfo.backup_info:type_name -> google.bigtable.admin.v2.BackupInfo
	16, // 2: google.bigtable.admin.v2.Table.cluster_states:type_name -> google.bigtable.admin.v2.Table.ClusterStatesEntry
	17, // 3: google.bigtable.admin.v2.Table.column_families:type_name -> google.bigtable.admin.v2.Table.ColumnFamiliesEntry
	1,  // 4: google.bigtable.admin.v2.Table.granularity:type_name -> google.bigtable.admin.v2.Table.TimestampGranularity
	7,  // 5: google.bigtable.admin.v2.Table.restore_info:type_name -> google.bigtable.admin.v2.RestoreInfo
	10, // 6: google.bigtable.admin.v2.ColumnFamily.gc_rule:type_name -> google.bigtable.admin.v2.GcRule
	20, // 7: google.bigtable.admin.v2.GcRule.max_age:type_name -> google.protobuf.Duration
	18, // 8: google.bigtable.admin.v2.GcRule.intersection:type_name -> google.bigtable.admin.v2.GcRule.Intersection
	19, // 9: google.bigtable.admin.v2.GcRule.union:type_name -> google.bigtable.admin.v2.GcRule.Union
	4,  // 10: google.bigtable.admin.v2.EncryptionInfo.encryption_type:type_name -> google.bigtable.admin.v2.EncryptionInfo.EncryptionType
	21, // 11: google.bigtable.admin.v2.EncryptionInfo.encryption_status:type_name -> google.rpc.Status
	8,  // 12: google.bigtable.admin.v2.Snapshot.source_table:type_name -> google.bigtable.admin.v2.Table
	22, // 13: google.bigtable.admin.v2.Snapshot.create_time:type_name -> google.protobuf.Timestamp
	22, // 14: google.bigtable.admin.v2.Snapshot.delete_time:type_name -> google.protobuf.Timestamp
	5,  // 15: google.bigtable.admin.v2.Snapshot.state:type_name -> google.bigtable.admin.v2.Snapshot.State
	22, // 16: google.bigtable.admin.v2.Backup.expire_time:type_name -> google.protobuf.Timestamp
	22, // 17: google.bigtable.admin.v2.Backup.start_time:type_name -> google.protobuf.Timestamp
	22, // 18: google.bigtable.admin.v2.Backup.end_time:type_name -> google.protobuf.Timestamp
	6,  // 19: google.bigtable.admin.v2.Backup.state:type_name -> google.bigtable.admin.v2.Backup.State
	11, // 20: google.bigtable.admin.v2.Backup.encryption_info:type_name -> google.bigtable.admin.v2.EncryptionInfo
	22, // 21: google.bigtable.admin.v2.BackupInfo.start_time:type_name -> google.protobuf.Timestamp
	22, // 22: google.bigtable.admin.v2.BackupInfo.end_time:type_name -> google.protobuf.Timestamp
	3,  // 23: google.bigtable.admin.v2.Table.ClusterState.replication_state:type_name -> google.bigtable.admin.v2.Table.ClusterState.ReplicationState
	11, // 24: google.bigtable.admin.v2.Table.ClusterState.encryption_info:type_name -> google.bigtable.admin.v2.EncryptionInfo
	15, // 25: google.bigtable.admin.v2.Table.ClusterStatesEntry.value:type_name -> google.bigtable.admin.v2.Table.ClusterState
	9,  // 26: google.bigtable.admin.v2.Table.ColumnFamiliesEntry.value:type_name -> google.bigtable.admin.v2.ColumnFamily
	10, // 27: google.bigtable.admin.v2.GcRule.Intersection.rules:type_name -> google.bigtable.admin.v2.GcRule
	10, // 28: google.bigtable.admin.v2.GcRule.Union.rules:type_name -> google.bigtable.admin.v2.GcRule
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_google_bigtable_admin_v2_table_proto_init() }
func file_google_bigtable_admin_v2_table_proto_init() {
	if File_google_bigtable_admin_v2_table_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_bigtable_admin_v2_table_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RestoreInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Table); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ColumnFamily); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GcRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EncryptionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Snapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Backup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BackupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Table_ClusterState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GcRule_Intersection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_table_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GcRule_Union); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_bigtable_admin_v2_table_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RestoreInfo_BackupInfo)(nil),
	}
	file_google_bigtable_admin_v2_table_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*GcRule_MaxNumVersions)(nil),
		(*GcRule_MaxAge)(nil),
		(*GcRule_Intersection_)(nil),
		(*GcRule_Union_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_bigtable_admin_v2_table_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_bigtable_admin_v2_table_proto_goTypes,
		DependencyIndexes: file_google_bigtable_admin_v2_table_proto_depIdxs,
		EnumInfos:         file_google_bigtable_admin_v2_table_proto_enumTypes,
		MessageInfos:      file_google_bigtable_admin_v2_table_proto_msgTypes,
	}.Build()
	File_google_bigtable_admin_v2_table_proto = out.File
	file_google_bigtable_admin_v2_table_proto_rawDesc = nil
	file_google_bigtable_admin_v2_table_proto_goTypes = nil
	file_google_bigtable_admin_v2_table_proto_depIdxs = nil
}
