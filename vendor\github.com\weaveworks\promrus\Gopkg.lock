# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  branch = "master"
  name = "github.com/beorn7/perks"
  packages = ["quantile"]
  revision = "4c0e84591b9aa9e6dcfdf3e020114cd81f89d5f9"

[[projects]]
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  revision = "346938d642f2ec3594ed81d874461961cd0faa76"
  version = "v1.1.0"

[[projects]]
  branch = "master"
  name = "github.com/golang/protobuf"
  packages = ["proto"]
  revision = "130e6b02ab059e7b717a096f397c5b60111cae74"

[[projects]]
  name = "github.com/matttproud/golang_protobuf_extensions"
  packages = ["pbutil"]
  revision = "3247c84500bff8d9fb6d579d800f20b3e091582c"
  version = "v1.0.0"

[[projects]]
  name = "github.com/pmezard/go-difflib"
  packages = ["difflib"]
  revision = "792786c7400a136282c1664665ae0a8db921c6c2"
  version = "v1.0.0"

[[projects]]
  name = "github.com/prometheus/client_golang"
  packages = ["prometheus"]
  revision = "c5b7fccd204277076155f10851dad72b76a49317"
  version = "v0.8.0"

[[projects]]
  branch = "master"
  name = "github.com/prometheus/client_model"
  packages = ["go"]
  revision = "6f3806018612930941127f2a7c6c453ba2c527d2"

[[projects]]
  branch = "master"
  name = "github.com/prometheus/common"
  packages = ["expfmt","internal/bitbucket.org/ww/goautoneg","model"]
  revision = "2f17f4a9d485bf34b4bfaccc273805040e4f86c8"

[[projects]]
  branch = "master"
  name = "github.com/prometheus/procfs"
  packages = [".","xfs"]
  revision = "e645f4e5aaa8506fc71d6edbc5c4ff02c04c46f2"

[[projects]]
  name = "github.com/sirupsen/logrus"
  packages = ["."]
  revision = "f006c2ac4710855cf0f916dd6b77acf6b048dc6e"
  version = "v1.0.3"

[[projects]]
  name = "github.com/stretchr/testify"
  packages = ["assert"]
  revision = "69483b4bd14f5845b5a1e55bca19e954e827f1d0"
  version = "v1.1.4"

[[projects]]
  branch = "master"
  name = "golang.org/x/crypto"
  packages = ["ssh/terminal"]
  revision = "9419663f5a44be8b34ca85f08abc5fe1be11f8a3"

[[projects]]
  branch = "master"
  name = "golang.org/x/sys"
  packages = ["unix","windows"]
  revision = "314a259e304ff91bd6985da2a7149bbf91237993"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  inputs-digest = "36d94413e5fdab027b84850df466089880bec5abaff8f69c5ad07c1532758f3d"
  solver-name = "gps-cdcl"
  solver-version = 1
