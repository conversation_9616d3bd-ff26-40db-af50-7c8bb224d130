{{- if .Values.rbac.create }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "promtail.fullname" . }}-clusterrolebinding
  labels:
    app: {{ template "promtail.name" . }}
    chart: {{ template "promtail.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
subjects:
  - kind: ServiceAccount
    name: {{ template "promtail.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ template "promtail.fullname" . }}-clusterrole
  apiGroup: rbac.authorization.k8s.io
{{- end }}
