language: go

os:
  - linux
  - osx
  - windows

arch:
  - amd64
  - arm64

go:
  - 1.12.x
  - 1.13.x
  - 1.14.x
  - master

script:
  - go vet ./...
  - go test -race ./...
  - go test -tags=noasm ./...

stages:
  - gofmt
  - test

matrix:
  allow_failures:
    - go: 'master'
  fast_finish: true
  include:
    - stage: gofmt
      go: 1.14.x
      os: linux
      arch: amd64
      script:
        - diff <(gofmt -d .) <(printf "")
        - diff <(gofmt -d ./private) <(printf "")
        - go install github.com/klauspost/asmfmt/cmd/asmfmt
        - diff <(asmfmt -d .) <(printf "")
    - stage: i386
      go: 1.14.x
      os: linux
      arch: amd64
      script:
        - GOOS=linux GOARCH=386 go test .
