auth_enabled: false

server:
  log_level: info
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

common:
  replication_factor: 1
  # Tell Loki which address to advertise
  instance_addr: {{ env "NOMAD_IP_grpc" }}
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  wal:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/wal
    flush_on_shutdown: true
    replay_memory_ceiling: "1G"

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v12
    index:
      prefix: index_
      period: 24h

storage_config:
  boltdb_shipper:
    # Nomad ephemeral disk is used to store index and cache
    # it will try to preserve /alloc/data between job updates
    active_index_directory: {{ env "NOMAD_ALLOC_DIR" }}/data/index
    cache_location: {{ env "NOMAD_ALLOC_DIR" }}/data/index-cache
    shared_store: s3

  aws:
    endpoint: https://minio.service.consul
    bucketnames: loki
    region: us-west-1
    access_key_id: ${S3_ACCESS_KEY_ID}
    secret_access_key: ${S3_SECRET_ACCESS_KEY}
    s3forcepathstyle: true

limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h

compactor:
  working_directory: {{ env "NOMAD_ALLOC_DIR" }}/compactor
  shared_store: s3
  compaction_interval: 5m
  retention_enabled: true

ruler:
  alertmanager_url: https://alertmanager.service.consul
  enable_alertmanager_v2: true
  enable_api: true
  external_url: https://loki.service.consul
  rule_path: {{ env "NOMAD_ALLOC_DIR" }}/tmp/rules
  storage:
    type: local
    local:
      directory: {{ env "NOMAD_TASK_DIR" }}/rules
  wal:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/ruler
