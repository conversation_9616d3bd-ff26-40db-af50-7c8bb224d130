/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.policy.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";
import "k8s.io/apimachinery/pkg/util/intstr/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// Eviction evicts a pod from its node subject to certain policies and safety constraints.
// This is a subresource of Pod.  A request to cause such an eviction is
// created by POSTing to .../pods/<pod name>/evictions.
message Eviction {
  // ObjectMeta describes the pod that is being evicted.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // DeleteOptions may be provided
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.DeleteOptions deleteOptions = 2;
}

// PodDisruptionBudget is an object to define the max disruption that can be caused to a collection of pods
message PodDisruptionBudget {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the PodDisruptionBudget.
  // +optional
  optional PodDisruptionBudgetSpec spec = 2;

  // Most recently observed status of the PodDisruptionBudget.
  // +optional
  optional PodDisruptionBudgetStatus status = 3;
}

// PodDisruptionBudgetList is a collection of PodDisruptionBudgets.
message PodDisruptionBudgetList {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of PodDisruptionBudgets
  repeated PodDisruptionBudget items = 2;
}

// PodDisruptionBudgetSpec is a description of a PodDisruptionBudget.
message PodDisruptionBudgetSpec {
  // An eviction is allowed if at least "minAvailable" pods selected by
  // "selector" will still be available after the eviction, i.e. even in the
  // absence of the evicted pod.  So for example you can prevent all voluntary
  // evictions by specifying "100%".
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString minAvailable = 1;

  // Label query over pods whose evictions are managed by the disruption
  // budget.
  // A null selector will match no pods, while an empty ({}) selector will select
  // all pods within the namespace.
  // +patchStrategy=replace
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // An eviction is allowed if at most "maxUnavailable" pods selected by
  // "selector" are unavailable after the eviction, i.e. even in absence of
  // the evicted pod. For example, one can prevent all voluntary evictions
  // by specifying 0. This is a mutually exclusive setting with "minAvailable".
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnavailable = 3;
}

// PodDisruptionBudgetStatus represents information about the status of a
// PodDisruptionBudget. Status may trail the actual state of a system.
message PodDisruptionBudgetStatus {
  // Most recent generation observed when updating this PDB status. DisruptionsAllowed and other
  // status information is valid only if observedGeneration equals to PDB's object generation.
  // +optional
  optional int64 observedGeneration = 1;

  // DisruptedPods contains information about pods whose eviction was
  // processed by the API server eviction subresource handler but has not
  // yet been observed by the PodDisruptionBudget controller.
  // A pod will be in this map from the time when the API server processed the
  // eviction request to the time when the pod is seen by PDB controller
  // as having been marked for deletion (or after a timeout). The key in the map is the name of the pod
  // and the value is the time when the API server processed the eviction request. If
  // the deletion didn't occur and a pod is still there it will be removed from
  // the list automatically by PodDisruptionBudget controller after some time.
  // If everything goes smooth this map should be empty for the most of the time.
  // Large number of entries in the map may indicate problems with pod deletions.
  // +optional
  map<string, k8s.io.apimachinery.pkg.apis.meta.v1.Time> disruptedPods = 2;

  // Number of pod disruptions that are currently allowed.
  optional int32 disruptionsAllowed = 3;

  // current number of healthy pods
  optional int32 currentHealthy = 4;

  // minimum desired number of healthy pods
  optional int32 desiredHealthy = 5;

  // total number of pods counted by this disruption budget
  optional int32 expectedPods = 6;

  // Conditions contain conditions for PDB. The disruption controller sets the
  // DisruptionAllowed condition. The following are known values for the reason field
  // (additional reasons could be added in the future):
  // - SyncFailed: The controller encountered an error and wasn't able to compute
  //               the number of allowed disruptions. Therefore no disruptions are
  //               allowed and the status of the condition will be False.
  // - InsufficientPods: The number of pods are either at or below the number
  //                     required by the PodDisruptionBudget. No disruptions are
  //                     allowed and the status of the condition will be False.
  // - SufficientPods: There are more pods than required by the PodDisruptionBudget.
  //                   The condition will be True, and the number of allowed
  //                   disruptions are provided by the disruptionsAllowed property.
  //
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=type
  repeated k8s.io.apimachinery.pkg.apis.meta.v1.Condition conditions = 7;
}

