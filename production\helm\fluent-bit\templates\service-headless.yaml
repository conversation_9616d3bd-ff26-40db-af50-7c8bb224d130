{{- if .Values.serviceMonitor.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "fluent-bit-loki.fullname" . }}-headless
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "fluent-bit-loki.name" . }}
    chart: {{ template "fluent-bit-loki.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.config.port }}
      protocol: TCP
      name: http-metrics
      targetPort: http-metrics
  selector:
    app: {{ template "fluent-bit-loki.name" . }}
    release: {{ .Release.Name }}
{{- end }}
