apiVersion: v1
kind: Pod
metadata:
  annotations:
    "helm.sh/hook": test-success
  labels:
    app: {{ template "loki-stack.name" . }}
    chart: {{ template "loki-stack.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  name: {{ template "loki-stack.fullname" . }}-test
spec:
  containers:
  - name: test
    image: bats/bats:v1.1.0
    args:
    - /var/lib/loki/test.sh
    env:
    - name: LOKI_SERVICE
      value: {{ template "loki.serviceName" . }}
    - name: LOKI_PORT
      value: "{{ .Values.loki.service.port }}"
    volumeMounts:
    - name: tests
      mountPath: /var/lib/loki
  restartPolicy: Never
  volumes:
  - name: tests
    configMap:
      name: {{ template "loki-stack.fullname" . }}-test
