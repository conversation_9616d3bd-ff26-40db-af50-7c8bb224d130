{{- if .Values.rbac.create }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "fluent-bit-loki.fullname" . }}-clusterrolebinding
  labels:
    app: {{ template "fluent-bit-loki.name" . }}
    chart: {{ template "fluent-bit-loki.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
subjects:
  - kind: ServiceAccount
    name: {{ template "fluent-bit-loki.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ template "fluent-bit-loki.fullname" . }}-clusterrole
  apiGroup: rbac.authorization.k8s.io
{{- end }}
