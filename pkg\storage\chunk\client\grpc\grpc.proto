syntax = "proto3";

package grpc;

import "google/protobuf/empty.proto";

service grpc_store {
  /// index-client

  /// WriteIndex writes batch of indexes to the index tables.
  rpc WriteIndex(WriteIndexRequest) returns (google.protobuf.Empty);
  /// QueryIndex reads the indexes required for given query & sends back the batch of rows
  /// in rpc streams
  rpc QueryIndex(QueryIndexRequest) returns (stream QueryIndexResponse);
  /// DeleteIndex deletes the batch of index entries from the index tables
  rpc DeleteIndex(DeleteIndexRequest) returns (google.protobuf.Empty);

  /// storage-client

  /// PutChunks saves the batch of chunks into the chunk tables.
  rpc PutChunks(PutChunksRequest) returns (google.protobuf.Empty);
  /// GetChunks requests for batch of chunks and the batch of chunks are sent back in rpc streams
  /// batching needs to be performed at server level as per requirement instead of sending single chunk per stream.
  /// In GetChunks rpc request send buf as nil
  rpc GetChunks(GetChunksRequest) returns (stream GetChunksResponse);
  /// DeleteChunks deletes the chunks based on chunkID.
  rpc DeleteChunks(ChunkID) returns (google.protobuf.Empty);

  /// table-client

  /// Lists all the tables that exists in the database.
  rpc ListTables(google.protobuf.Empty) returns (ListTablesResponse);
  /// Creates a table with provided name & attributes.
  rpc CreateTable(CreateTableRequest) returns (google.protobuf.Empty);
  // Deletes a table using table name provided.
  rpc DeleteTable(DeleteTableRequest) returns (google.protobuf.Empty);
  // Describes a table information for the provided table.
  rpc DescribeTable(DescribeTableRequest) returns (DescribeTableResponse);
  // Update a table with newly provided table information.
  rpc UpdateTable(UpdateTableRequest) returns (google.protobuf.Empty);
}

message PutChunksRequest {
  repeated Chunk chunks = 1;
}

message GetChunksRequest {
  repeated Chunk chunks = 1;
}

message GetChunksResponse {
  repeated Chunk chunks = 1;
}

message Chunk {
  bytes encoded = 1;
  string key = 2;
  string tableName = 3;
}

message ChunkID {
  string chunkID = 1;
}

message DeleteTableRequest {
  string tableName = 1;
}

message DescribeTableRequest {
  string tableName = 1;
}

message WriteBatch {
  repeated IndexEntry writes = 1;
  repeated IndexEntry deletes = 2;
}

message WriteIndexRequest {
  repeated IndexEntry writes = 1;
}

message DeleteIndexRequest {
  repeated IndexEntry deletes = 1;
}

message QueryIndexResponse {
  repeated Row rows = 1;
}

message Row {
  bytes rangeValue = 1;
  bytes value = 2;
}

message IndexEntry {
  string tableName = 1;
  string hashValue = 2;
  bytes rangeValue = 3;
  bytes value = 4;
}

message QueryIndexRequest {
  string tableName = 1;
  string hashValue = 2;
  bytes rangeValuePrefix = 3;
  bytes rangeValueStart = 4;
  bytes valueEqual = 5;
  bool immutable = 6;
}

message UpdateTableRequest {
  TableDesc current = 1;
  TableDesc expected = 2;
}

message DescribeTableResponse {
  TableDesc desc = 1;
  bool isActive = 2;
}

message CreateTableRequest {
  TableDesc desc = 1;
}

message TableDesc {
  string name = 1;
  bool useOnDemandIOMode = 2;
  int64 provisionedRead = 3;
  int64 provisionedWrite = 4;
  map<string, string> tags = 5;
}

message ListTablesResponse {
  repeated string tableNames = 1;
}

message Labels {
  string name = 1;
  string value = 2;
}
