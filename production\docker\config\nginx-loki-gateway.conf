error_log  /dev/stderr;
pid        /tmp/nginx.pid;
worker_rlimit_nofile 8192;

events {
    worker_connections  4096;  ## Default: 1024
}

http {

  default_type application/octet-stream;
  log_format   main '$remote_addr - $remote_user [$time_local]  $status '
    '"$request" $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';
  access_log   /dev/stderr  main;
  sendfile     on;
  tcp_nopush   on;

  upstream distributor {
    server loki-1:3100;
    server loki-2:3100;
    server loki-3:3100;
  }

  upstream querier {
    server loki-1:3100;
    server loki-2:3100;
    server loki-3:3100;
  }

  upstream query-frontend {
    server loki-frontend:3100;
  }

  server {
    listen 80;
    proxy_set_header     X-Scope-OrgID docker-ha;

    location = /loki/api/v1/push {
        proxy_pass       http://distributor$request_uri;
    }
    
    location = /ring {
        proxy_pass       http://distributor$request_uri;
    }

    location = /loki/api/v1/tail {
        proxy_pass       http://querier$request_uri;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ~ /loki/api/.* {
        proxy_pass       http://query-frontend$request_uri;
    }
  }

  server {
    listen 3100;
    proxy_set_header     X-Scope-OrgID docker-ha;

    location ~ /loki/api/.* {
        proxy_pass       http://querier$request_uri;
    }
    
  }
}
