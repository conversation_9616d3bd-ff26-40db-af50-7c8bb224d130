# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  digest = "1:892e39e5c083d0943f1e80ab8351690f183c6a5ab24e1d280adcad424c26255e"
  name = "contrib.go.opencensus.io/exporter/ocagent"
  packages = ["."]
  pruneopts = "UT"
  revision = "a8a6f458bbc1d5042322ad1f9b65eeb0b69be9ea"
  version = "v0.6.0"

[[projects]]
  digest = "1:8f5acd4d4462b5136af644d25101f0968a7a94ee90fcb2059cec5b7cc42e0b20"
  name = "github.com/census-instrumentation/opencensus-proto"
  packages = [
    "gen-go/agent/common/v1",
    "gen-go/agent/metrics/v1",
    "gen-go/agent/trace/v1",
    "gen-go/metrics/v1",
    "gen-go/resource/v1",
    "gen-go/trace/v1",
  ]
  pruneopts = "UT"
  revision = "d89fa54de508111353cb0b06403c00569be780d8"
  version = "v0.2.1"

[[projects]]
  digest = "1:ffe9824d294da03b391f44e1ae8281281b4afc1bdaa9588c9097785e3af10cec"
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  pruneopts = "UT"
  revision = "8991bc29aa16c548c550c7ff78260e27b9ab7c73"
  version = "v1.1.1"

[[projects]]
  digest = "1:76dc72490af7174349349838f2fe118996381b31ea83243812a97e5a0fd5ed55"
  name = "github.com/dgrijalva/jwt-go"
  packages = ["."]
  pruneopts = "UT"
  revision = "06ea1031745cb8b3dab3f6a236daf2b0aa468b7e"
  version = "v3.2.0"

[[projects]]
  digest = "1:cf0d2e435fd4ce45b789e93ef24b5f08e86be0e9807a16beb3694e2d8c9af965"
  name = "github.com/dimchansky/utfbom"
  packages = ["."]
  pruneopts = "UT"
  revision = "d2133a1ce379ef6fa992b0514a77146c60db9d1c"
  version = "v1.1.0"

[[projects]]
  branch = "master"
  digest = "1:b7cb6054d3dff43b38ad2e92492f220f57ae6087ee797dca298139776749ace8"
  name = "github.com/golang/groupcache"
  packages = ["lru"]
  pruneopts = "UT"
  revision = "611e8accdfc92c4187d399e95ce826046d4c8d73"

[[projects]]
  digest = "1:e3839df32927e8d3403cd5aa7253d966e8ff80fc8f10e2e35d146461cd83fcfa"
  name = "github.com/golang/protobuf"
  packages = [
    "descriptor",
    "jsonpb",
    "proto",
    "protoc-gen-go/descriptor",
    "ptypes",
    "ptypes/any",
    "ptypes/duration",
    "ptypes/struct",
    "ptypes/timestamp",
    "ptypes/wrappers",
  ]
  pruneopts = "UT"
  revision = "6c65a5562fc06764971b7c5d05c76c75e84bdbf7"
  version = "v1.3.2"

[[projects]]
  digest = "1:c560cd79300fac84f124b96225181a637a70b60155919a3c36db50b7cca6b806"
  name = "github.com/grpc-ecosystem/grpc-gateway"
  packages = [
    "internal",
    "runtime",
    "utilities",
  ]
  pruneopts = "UT"
  revision = "f7120437bb4f6c71f7f5076ad65a45310de2c009"
  version = "v1.12.1"

[[projects]]
  digest = "1:5d231480e1c64a726869bc4142d270184c419749d34f167646baa21008eb0a79"
  name = "github.com/mitchellh/go-homedir"
  packages = ["."]
  pruneopts = "UT"
  revision = "af06845cf3004701891bf4fdb884bfe4920b3727"
  version = "v1.1.0"

[[projects]]
  digest = "1:0028cb19b2e4c3112225cd871870f2d9cf49b9b4276531f03438a88e94be86fe"
  name = "github.com/pmezard/go-difflib"
  packages = ["difflib"]
  pruneopts = "UT"
  revision = "792786c7400a136282c1664665ae0a8db921c6c2"
  version = "v1.0.0"

[[projects]]
  digest = "1:99d32780e5238c2621fff621123997c3e3cca96db8be13179013aea77dfab551"
  name = "github.com/stretchr/testify"
  packages = [
    "assert",
    "require",
  ]
  pruneopts = "UT"
  revision = "221dbe5ed46703ee255b1da0dec05086f5035f62"
  version = "v1.4.0"

[[projects]]
  digest = "1:7c5e00383399fe13de0b4b65c9fdde16275407ce8ac02d867eafeaa916edcc71"
  name = "go.opencensus.io"
  packages = [
    ".",
    "internal",
    "internal/tagencoding",
    "metric/metricdata",
    "metric/metricproducer",
    "plugin/ocgrpc",
    "plugin/ochttp",
    "plugin/ochttp/propagation/b3",
    "plugin/ochttp/propagation/tracecontext",
    "resource",
    "stats",
    "stats/internal",
    "stats/view",
    "tag",
    "trace",
    "trace/internal",
    "trace/propagation",
    "trace/tracestate",
  ]
  pruneopts = "UT"
  revision = "aad2c527c5defcf89b5afab7f37274304195a6b2"
  version = "v0.22.2"

[[projects]]
  branch = "master"
  digest = "1:f604f5e2ee721b6757d962dfe7bab4f28aae50c456e39cfb2f3819762a44a6ae"
  name = "golang.org/x/crypto"
  packages = [
    "pkcs12",
    "pkcs12/internal/rc2",
  ]
  pruneopts = "UT"
  revision = "e9b2fee46413994441b28dfca259d911d963dfed"

[[projects]]
  branch = "master"
  digest = "1:334b27eac455cb6567ea28cd424230b07b1a64334a2f861a8075ac26ce10af43"
  name = "golang.org/x/lint"
  packages = [
    ".",
    "golint",
  ]
  pruneopts = "UT"
  revision = "fdd1cda4f05fd1fd86124f0ef9ce31a0b72c8448"

[[projects]]
  branch = "master"
  digest = "1:257a75d024975428ab9192bfc334c3490882f8cb21322ea5784ca8eca000a910"
  name = "golang.org/x/net"
  packages = [
    "http/httpguts",
    "http2",
    "http2/hpack",
    "idna",
    "internal/timeseries",
    "trace",
  ]
  pruneopts = "UT"
  revision = "1ddd1de85cb0337b623b740a609d35817d516a8d"

[[projects]]
  branch = "master"
  digest = "1:382bb5a7fb4034db3b6a2d19e5a4a6bcf52f4750530603c01ca18a172fa3089b"
  name = "golang.org/x/sync"
  packages = ["semaphore"]
  pruneopts = "UT"
  revision = "cd5d95a43a6e21273425c7ae415d3df9ea832eeb"

[[projects]]
  branch = "master"
  digest = "1:4da420ceda5f68e8d748aa2169d0ed44ffadb1bbd6537cf778a49563104189b8"
  name = "golang.org/x/sys"
  packages = ["unix"]
  pruneopts = "UT"
  revision = "ce4227a45e2eb77e5c847278dcc6a626742e2945"

[[projects]]
  digest = "1:8d8faad6b12a3a4c819a3f9618cb6ee1fa1cfc33253abeeea8b55336721e3405"
  name = "golang.org/x/text"
  packages = [
    "collate",
    "collate/build",
    "internal/colltab",
    "internal/gen",
    "internal/language",
    "internal/language/compact",
    "internal/tag",
    "internal/triegen",
    "internal/ucd",
    "language",
    "secure/bidirule",
    "transform",
    "unicode/bidi",
    "unicode/cldr",
    "unicode/norm",
    "unicode/rangetable",
  ]
  pruneopts = "UT"
  revision = "342b2e1fbaa52c93f31447ad2c6abc048c63e475"
  version = "v0.3.2"

[[projects]]
  branch = "master"
  digest = "1:4eb5ea8395fb60212dd58b92c9db80bab59d5e99c7435f9a6a0a528c373b60e7"
  name = "golang.org/x/tools"
  packages = [
    "go/ast/astutil",
    "go/gcexportdata",
    "go/internal/gcimporter",
    "go/types/typeutil",
  ]
  pruneopts = "UT"
  revision = "259af5ff87bdcd4abf2ecda8edc3f13f04f26a42"

[[projects]]
  digest = "1:964bb30febc27fabfbec4759fa530c6ec35e77a7c85fed90b9317ea39a054877"
  name = "google.golang.org/api"
  packages = ["support/bundler"]
  pruneopts = "UT"
  revision = "8a410c21381766a810817fd6200fce8838ecb277"
  version = "v0.14.0"

[[projects]]
  branch = "master"
  digest = "1:a8d5c2c6e746b3485e36908ab2a9e3d77b86b81f8156d88403c7d2b462431dfd"
  name = "google.golang.org/genproto"
  packages = [
    "googleapis/api/httpbody",
    "googleapis/rpc/status",
    "protobuf/field_mask",
  ]
  pruneopts = "UT"
  revision = "51378566eb590fa106d1025ea12835a4416dda84"

[[projects]]
  digest = "1:b59ce3ddb11daeeccccc9cb3183b58ebf8e9a779f1c853308cd91612e817a301"
  name = "google.golang.org/grpc"
  packages = [
    ".",
    "backoff",
    "balancer",
    "balancer/base",
    "balancer/roundrobin",
    "binarylog/grpc_binarylog_v1",
    "codes",
    "connectivity",
    "credentials",
    "credentials/internal",
    "encoding",
    "encoding/proto",
    "grpclog",
    "internal",
    "internal/backoff",
    "internal/balancerload",
    "internal/binarylog",
    "internal/buffer",
    "internal/channelz",
    "internal/envconfig",
    "internal/grpcrand",
    "internal/grpcsync",
    "internal/resolver/dns",
    "internal/resolver/passthrough",
    "internal/syscall",
    "internal/transport",
    "keepalive",
    "metadata",
    "naming",
    "peer",
    "resolver",
    "serviceconfig",
    "stats",
    "status",
    "tap",
  ]
  pruneopts = "UT"
  revision = "1a3960e4bd028ac0cec0a2afd27d7d8e67c11514"
  version = "v1.25.1"

[[projects]]
  digest = "1:b75b3deb2bce8bc079e16bb2aecfe01eb80098f5650f9e93e5643ca8b7b73737"
  name = "gopkg.in/yaml.v2"
  packages = ["."]
  pruneopts = "UT"
  revision = "1f64d6156d11335c3f22d9330b0ad14fc1e789ce"
  version = "v2.2.7"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  input-imports = [
    "contrib.go.opencensus.io/exporter/ocagent",
    "github.com/dgrijalva/jwt-go",
    "github.com/dimchansky/utfbom",
    "github.com/mitchellh/go-homedir",
    "github.com/stretchr/testify/require",
    "go.opencensus.io/plugin/ochttp",
    "go.opencensus.io/plugin/ochttp/propagation/tracecontext",
    "go.opencensus.io/stats/view",
    "go.opencensus.io/trace",
    "golang.org/x/crypto/pkcs12",
    "golang.org/x/lint/golint",
  ]
  solver-name = "gps-cdcl"
  solver-version = 1
