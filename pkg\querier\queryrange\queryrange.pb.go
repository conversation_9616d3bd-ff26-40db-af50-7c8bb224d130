// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/querier/queryrange/queryrange.proto

package queryrange

import (
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	github_com_grafana_loki_pkg_logproto "github.com/grafana/loki/pkg/logproto"
	logproto "github.com/grafana/loki/pkg/logproto"
	stats "github.com/grafana/loki/pkg/logqlmodel/stats"
	github_com_grafana_loki_pkg_querier_queryrange_queryrangebase "github.com/grafana/loki/pkg/querier/queryrange/queryrangebase"
	queryrangebase "github.com/grafana/loki/pkg/querier/queryrange/queryrangebase"
	_ "github.com/grafana/loki/pkg/storage/stores/shipper/indexgateway/indexgatewaypb"
	github_com_grafana_loki_pkg_storage_stores_shipper_indexgateway_indexgatewaypb "github.com/grafana/loki/pkg/storage/stores/shipper/indexgateway/indexgatewaypb"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type LokiRequest struct {
	Query     string             `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Limit     uint32             `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Step      int64              `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`
	Interval  int64              `protobuf:"varint,9,opt,name=interval,proto3" json:"interval,omitempty"`
	StartTs   time.Time          `protobuf:"bytes,4,opt,name=startTs,proto3,stdtime" json:"startTs"`
	EndTs     time.Time          `protobuf:"bytes,5,opt,name=endTs,proto3,stdtime" json:"endTs"`
	Direction logproto.Direction `protobuf:"varint,6,opt,name=direction,proto3,enum=logproto.Direction" json:"direction,omitempty"`
	Path      string             `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`
	Shards    []string           `protobuf:"bytes,8,rep,name=shards,proto3" json:"shards"`
}

func (m *LokiRequest) Reset()      { *m = LokiRequest{} }
func (*LokiRequest) ProtoMessage() {}
func (*LokiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{0}
}
func (m *LokiRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiRequest.Merge(m, src)
}
func (m *LokiRequest) XXX_Size() int {
	return m.Size()
}
func (m *LokiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LokiRequest proto.InternalMessageInfo

func (m *LokiRequest) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *LokiRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *LokiRequest) GetStep() int64 {
	if m != nil {
		return m.Step
	}
	return 0
}

func (m *LokiRequest) GetInterval() int64 {
	if m != nil {
		return m.Interval
	}
	return 0
}

func (m *LokiRequest) GetStartTs() time.Time {
	if m != nil {
		return m.StartTs
	}
	return time.Time{}
}

func (m *LokiRequest) GetEndTs() time.Time {
	if m != nil {
		return m.EndTs
	}
	return time.Time{}
}

func (m *LokiRequest) GetDirection() logproto.Direction {
	if m != nil {
		return m.Direction
	}
	return logproto.FORWARD
}

func (m *LokiRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *LokiRequest) GetShards() []string {
	if m != nil {
		return m.Shards
	}
	return nil
}

type LokiInstantRequest struct {
	Query     string             `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Limit     uint32             `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TimeTs    time.Time          `protobuf:"bytes,3,opt,name=timeTs,proto3,stdtime" json:"timeTs"`
	Direction logproto.Direction `protobuf:"varint,4,opt,name=direction,proto3,enum=logproto.Direction" json:"direction,omitempty"`
	Path      string             `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`
	Shards    []string           `protobuf:"bytes,6,rep,name=shards,proto3" json:"shards"`
}

func (m *LokiInstantRequest) Reset()      { *m = LokiInstantRequest{} }
func (*LokiInstantRequest) ProtoMessage() {}
func (*LokiInstantRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{1}
}
func (m *LokiInstantRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiInstantRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiInstantRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiInstantRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiInstantRequest.Merge(m, src)
}
func (m *LokiInstantRequest) XXX_Size() int {
	return m.Size()
}
func (m *LokiInstantRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiInstantRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LokiInstantRequest proto.InternalMessageInfo

func (m *LokiInstantRequest) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *LokiInstantRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *LokiInstantRequest) GetTimeTs() time.Time {
	if m != nil {
		return m.TimeTs
	}
	return time.Time{}
}

func (m *LokiInstantRequest) GetDirection() logproto.Direction {
	if m != nil {
		return m.Direction
	}
	return logproto.FORWARD
}

func (m *LokiInstantRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *LokiInstantRequest) GetShards() []string {
	if m != nil {
		return m.Shards
	}
	return nil
}

type LokiResponse struct {
	Status     string                                                                                   `protobuf:"bytes,1,opt,name=Status,proto3" json:"status"`
	Data       LokiData                                                                                 `protobuf:"bytes,2,opt,name=Data,proto3" json:"data,omitempty"`
	ErrorType  string                                                                                   `protobuf:"bytes,3,opt,name=ErrorType,proto3" json:"errorType,omitempty"`
	Error      string                                                                                   `protobuf:"bytes,4,opt,name=Error,proto3" json:"error,omitempty"`
	Direction  logproto.Direction                                                                       `protobuf:"varint,5,opt,name=direction,proto3,enum=logproto.Direction" json:"direction,omitempty"`
	Limit      uint32                                                                                   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	Version    uint32                                                                                   `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`
	Statistics stats.Result                                                                             `protobuf:"bytes,8,opt,name=statistics,proto3" json:"statistics"`
	Headers    []github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader `protobuf:"bytes,9,rep,name=Headers,proto3,customtype=github.com/grafana/loki/pkg/querier/queryrange/queryrangebase.PrometheusResponseHeader" json:"-"`
}

func (m *LokiResponse) Reset()      { *m = LokiResponse{} }
func (*LokiResponse) ProtoMessage() {}
func (*LokiResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{2}
}
func (m *LokiResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiResponse.Merge(m, src)
}
func (m *LokiResponse) XXX_Size() int {
	return m.Size()
}
func (m *LokiResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LokiResponse proto.InternalMessageInfo

func (m *LokiResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *LokiResponse) GetData() LokiData {
	if m != nil {
		return m.Data
	}
	return LokiData{}
}

func (m *LokiResponse) GetErrorType() string {
	if m != nil {
		return m.ErrorType
	}
	return ""
}

func (m *LokiResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *LokiResponse) GetDirection() logproto.Direction {
	if m != nil {
		return m.Direction
	}
	return logproto.FORWARD
}

func (m *LokiResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *LokiResponse) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *LokiResponse) GetStatistics() stats.Result {
	if m != nil {
		return m.Statistics
	}
	return stats.Result{}
}

type LokiSeriesRequest struct {
	Match   []string  `protobuf:"bytes,1,rep,name=match,proto3" json:"match,omitempty"`
	StartTs time.Time `protobuf:"bytes,2,opt,name=startTs,proto3,stdtime" json:"startTs"`
	EndTs   time.Time `protobuf:"bytes,3,opt,name=endTs,proto3,stdtime" json:"endTs"`
	Path    string    `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	Shards  []string  `protobuf:"bytes,5,rep,name=shards,proto3" json:"shards"`
}

func (m *LokiSeriesRequest) Reset()      { *m = LokiSeriesRequest{} }
func (*LokiSeriesRequest) ProtoMessage() {}
func (*LokiSeriesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{3}
}
func (m *LokiSeriesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiSeriesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiSeriesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiSeriesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiSeriesRequest.Merge(m, src)
}
func (m *LokiSeriesRequest) XXX_Size() int {
	return m.Size()
}
func (m *LokiSeriesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiSeriesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LokiSeriesRequest proto.InternalMessageInfo

func (m *LokiSeriesRequest) GetMatch() []string {
	if m != nil {
		return m.Match
	}
	return nil
}

func (m *LokiSeriesRequest) GetStartTs() time.Time {
	if m != nil {
		return m.StartTs
	}
	return time.Time{}
}

func (m *LokiSeriesRequest) GetEndTs() time.Time {
	if m != nil {
		return m.EndTs
	}
	return time.Time{}
}

func (m *LokiSeriesRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *LokiSeriesRequest) GetShards() []string {
	if m != nil {
		return m.Shards
	}
	return nil
}

type LokiSeriesResponse struct {
	Status     string                                                                                   `protobuf:"bytes,1,opt,name=Status,proto3" json:"status"`
	Data       []logproto.SeriesIdentifier                                                              `protobuf:"bytes,2,rep,name=Data,proto3" json:"data,omitempty"`
	Version    uint32                                                                                   `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	Headers    []github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader `protobuf:"bytes,4,rep,name=Headers,proto3,customtype=github.com/grafana/loki/pkg/querier/queryrange/queryrangebase.PrometheusResponseHeader" json:"-"`
	Statistics stats.Result                                                                             `protobuf:"bytes,5,opt,name=statistics,proto3" json:"statistics"`
}

func (m *LokiSeriesResponse) Reset()      { *m = LokiSeriesResponse{} }
func (*LokiSeriesResponse) ProtoMessage() {}
func (*LokiSeriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{4}
}
func (m *LokiSeriesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiSeriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiSeriesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiSeriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiSeriesResponse.Merge(m, src)
}
func (m *LokiSeriesResponse) XXX_Size() int {
	return m.Size()
}
func (m *LokiSeriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiSeriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LokiSeriesResponse proto.InternalMessageInfo

func (m *LokiSeriesResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *LokiSeriesResponse) GetData() []logproto.SeriesIdentifier {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *LokiSeriesResponse) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *LokiSeriesResponse) GetStatistics() stats.Result {
	if m != nil {
		return m.Statistics
	}
	return stats.Result{}
}

type LokiLabelNamesRequest struct {
	StartTs time.Time `protobuf:"bytes,1,opt,name=startTs,proto3,stdtime" json:"startTs"`
	EndTs   time.Time `protobuf:"bytes,2,opt,name=endTs,proto3,stdtime" json:"endTs"`
	Path    string    `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
}

func (m *LokiLabelNamesRequest) Reset()      { *m = LokiLabelNamesRequest{} }
func (*LokiLabelNamesRequest) ProtoMessage() {}
func (*LokiLabelNamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{5}
}
func (m *LokiLabelNamesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiLabelNamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiLabelNamesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiLabelNamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiLabelNamesRequest.Merge(m, src)
}
func (m *LokiLabelNamesRequest) XXX_Size() int {
	return m.Size()
}
func (m *LokiLabelNamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiLabelNamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LokiLabelNamesRequest proto.InternalMessageInfo

func (m *LokiLabelNamesRequest) GetStartTs() time.Time {
	if m != nil {
		return m.StartTs
	}
	return time.Time{}
}

func (m *LokiLabelNamesRequest) GetEndTs() time.Time {
	if m != nil {
		return m.EndTs
	}
	return time.Time{}
}

func (m *LokiLabelNamesRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

type LokiLabelNamesResponse struct {
	Status     string                                                                                   `protobuf:"bytes,1,opt,name=Status,proto3" json:"status"`
	Data       []string                                                                                 `protobuf:"bytes,2,rep,name=Data,proto3" json:"data,omitempty"`
	Version    uint32                                                                                   `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	Headers    []github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader `protobuf:"bytes,4,rep,name=Headers,proto3,customtype=github.com/grafana/loki/pkg/querier/queryrange/queryrangebase.PrometheusResponseHeader" json:"-"`
	Statistics stats.Result                                                                             `protobuf:"bytes,5,opt,name=statistics,proto3" json:"statistics"`
}

func (m *LokiLabelNamesResponse) Reset()      { *m = LokiLabelNamesResponse{} }
func (*LokiLabelNamesResponse) ProtoMessage() {}
func (*LokiLabelNamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{6}
}
func (m *LokiLabelNamesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiLabelNamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiLabelNamesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiLabelNamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiLabelNamesResponse.Merge(m, src)
}
func (m *LokiLabelNamesResponse) XXX_Size() int {
	return m.Size()
}
func (m *LokiLabelNamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiLabelNamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LokiLabelNamesResponse proto.InternalMessageInfo

func (m *LokiLabelNamesResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *LokiLabelNamesResponse) GetData() []string {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *LokiLabelNamesResponse) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *LokiLabelNamesResponse) GetStatistics() stats.Result {
	if m != nil {
		return m.Statistics
	}
	return stats.Result{}
}

type LokiData struct {
	ResultType string                                        `protobuf:"bytes,1,opt,name=ResultType,proto3" json:"resultType"`
	Result     []github_com_grafana_loki_pkg_logproto.Stream `protobuf:"bytes,2,rep,name=Result,proto3,customtype=github.com/grafana/loki/pkg/logproto.Stream" json:"result"`
}

func (m *LokiData) Reset()      { *m = LokiData{} }
func (*LokiData) ProtoMessage() {}
func (*LokiData) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{7}
}
func (m *LokiData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiData.Merge(m, src)
}
func (m *LokiData) XXX_Size() int {
	return m.Size()
}
func (m *LokiData) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiData.DiscardUnknown(m)
}

var xxx_messageInfo_LokiData proto.InternalMessageInfo

func (m *LokiData) GetResultType() string {
	if m != nil {
		return m.ResultType
	}
	return ""
}

// LokiPromResponse wraps a Prometheus response with statistics.
type LokiPromResponse struct {
	Response   *queryrangebase.PrometheusResponse `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	Statistics stats.Result                       `protobuf:"bytes,2,opt,name=statistics,proto3" json:"statistics"`
}

func (m *LokiPromResponse) Reset()      { *m = LokiPromResponse{} }
func (*LokiPromResponse) ProtoMessage() {}
func (*LokiPromResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{8}
}
func (m *LokiPromResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LokiPromResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LokiPromResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LokiPromResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LokiPromResponse.Merge(m, src)
}
func (m *LokiPromResponse) XXX_Size() int {
	return m.Size()
}
func (m *LokiPromResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LokiPromResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LokiPromResponse proto.InternalMessageInfo

func (m *LokiPromResponse) GetResponse() *queryrangebase.PrometheusResponse {
	if m != nil {
		return m.Response
	}
	return nil
}

func (m *LokiPromResponse) GetStatistics() stats.Result {
	if m != nil {
		return m.Statistics
	}
	return stats.Result{}
}

type IndexStatsResponse struct {
	Response *github_com_grafana_loki_pkg_storage_stores_shipper_indexgateway_indexgatewaypb.IndexStatsResponse `protobuf:"bytes,1,opt,name=response,proto3,customtype=github.com/grafana/loki/pkg/storage/stores/shipper/indexgateway/indexgatewaypb.IndexStatsResponse" json:"response,omitempty"`
	Headers  []github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader           `protobuf:"bytes,2,rep,name=Headers,proto3,customtype=github.com/grafana/loki/pkg/querier/queryrange/queryrangebase.PrometheusResponseHeader" json:"-"`
}

func (m *IndexStatsResponse) Reset()      { *m = IndexStatsResponse{} }
func (*IndexStatsResponse) ProtoMessage() {}
func (*IndexStatsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_51b9d53b40d11902, []int{9}
}
func (m *IndexStatsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IndexStatsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IndexStatsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IndexStatsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexStatsResponse.Merge(m, src)
}
func (m *IndexStatsResponse) XXX_Size() int {
	return m.Size()
}
func (m *IndexStatsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexStatsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IndexStatsResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*LokiRequest)(nil), "queryrange.LokiRequest")
	proto.RegisterType((*LokiInstantRequest)(nil), "queryrange.LokiInstantRequest")
	proto.RegisterType((*LokiResponse)(nil), "queryrange.LokiResponse")
	proto.RegisterType((*LokiSeriesRequest)(nil), "queryrange.LokiSeriesRequest")
	proto.RegisterType((*LokiSeriesResponse)(nil), "queryrange.LokiSeriesResponse")
	proto.RegisterType((*LokiLabelNamesRequest)(nil), "queryrange.LokiLabelNamesRequest")
	proto.RegisterType((*LokiLabelNamesResponse)(nil), "queryrange.LokiLabelNamesResponse")
	proto.RegisterType((*LokiData)(nil), "queryrange.LokiData")
	proto.RegisterType((*LokiPromResponse)(nil), "queryrange.LokiPromResponse")
	proto.RegisterType((*IndexStatsResponse)(nil), "queryrange.IndexStatsResponse")
}

func init() {
	proto.RegisterFile("pkg/querier/queryrange/queryrange.proto", fileDescriptor_51b9d53b40d11902)
}

var fileDescriptor_51b9d53b40d11902 = []byte{
	// 1003 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x56, 0x4f, 0x6f, 0x1b, 0x45,
	0x14, 0xf7, 0xf8, 0x5f, 0xec, 0x09, 0x0d, 0x30, 0x29, 0xed, 0xca, 0x48, 0xbb, 0x96, 0x0f, 0x60,
	0x04, 0xdd, 0x15, 0x29, 0x70, 0x40, 0x80, 0xe8, 0x2a, 0x20, 0x22, 0x55, 0x08, 0x6d, 0x2d, 0xae,
	0x68, 0x9c, 0x9d, 0xac, 0x57, 0xd9, 0x7f, 0x99, 0x19, 0x17, 0x72, 0x43, 0xe2, 0x8c, 0xd4, 0xcf,
	0x00, 0x42, 0x20, 0x3e, 0x49, 0x8e, 0x39, 0x56, 0x91, 0x58, 0x88, 0x73, 0x00, 0x7c, 0xea, 0x47,
	0x40, 0x33, 0xb3, 0x6b, 0x8f, 0x53, 0xa7, 0x89, 0xd3, 0x4b, 0x0f, 0x5c, 0xbc, 0xef, 0xbd, 0x79,
	0xbf, 0xd9, 0x79, 0xbf, 0xf7, 0x7b, 0xe3, 0x85, 0x6f, 0x66, 0xfb, 0x81, 0x73, 0x30, 0x26, 0x34,
	0x24, 0x54, 0x3e, 0x0f, 0x29, 0x4e, 0x02, 0xa2, 0x99, 0x76, 0x46, 0x53, 0x9e, 0x22, 0x38, 0x8f,
	0x74, 0xee, 0x04, 0x21, 0x1f, 0x8d, 0x87, 0xf6, 0x6e, 0x1a, 0x3b, 0x41, 0x1a, 0xa4, 0x8e, 0x4c,
	0x19, 0x8e, 0xf7, 0xa4, 0x27, 0x1d, 0x69, 0x29, 0x68, 0xc7, 0x0a, 0xd2, 0x34, 0x88, 0xc8, 0x3c,
	0x8b, 0x87, 0x31, 0x61, 0x1c, 0xc7, 0x59, 0x91, 0xf0, 0xba, 0x38, 0x44, 0x94, 0x06, 0x0a, 0x59,
	0x1a, 0xc5, 0x62, 0xb7, 0x58, 0x3c, 0x88, 0xe2, 0xd4, 0x27, 0x91, 0xc3, 0x38, 0xe6, 0x4c, 0xfd,
	0x16, 0x19, 0x1f, 0x5c, 0x5a, 0xc3, 0x10, 0xb3, 0xa7, 0x4b, 0xea, 0x6c, 0x0b, 0x1c, 0xe3, 0x29,
	0xc5, 0x01, 0x91, 0x4f, 0xc2, 0x1c, 0x36, 0x0a, 0xb3, 0x8c, 0x50, 0x27, 0x4c, 0x7c, 0xf2, 0x5d,
	0x80, 0x39, 0xf9, 0x16, 0x1f, 0x2e, 0x38, 0xd9, 0xd0, 0x29, 0x2c, 0xb5, 0x4b, 0xef, 0xb8, 0x0a,
	0xd7, 0xef, 0xa7, 0xfb, 0xa1, 0x47, 0x0e, 0xc6, 0x84, 0x71, 0x74, 0x13, 0x36, 0xe4, 0x9b, 0x0c,
	0xd0, 0x05, 0xfd, 0xb6, 0xa7, 0x1c, 0x11, 0x8d, 0xc2, 0x38, 0xe4, 0x46, 0xb5, 0x0b, 0xfa, 0x37,
	0x3c, 0xe5, 0x20, 0x04, 0xeb, 0x8c, 0x93, 0xcc, 0xa8, 0x75, 0x41, 0xbf, 0xe6, 0x49, 0x1b, 0x75,
	0x60, 0x2b, 0x4c, 0x38, 0xa1, 0x0f, 0x71, 0x64, 0xb4, 0x65, 0x7c, 0xe6, 0xa3, 0x4f, 0xe0, 0x1a,
	0xe3, 0x98, 0xf2, 0x01, 0x33, 0xea, 0x5d, 0xd0, 0x5f, 0xdf, 0xea, 0xd8, 0x8a, 0x5b, 0xbb, 0xe4,
	0xd6, 0x1e, 0x94, 0xdc, 0xba, 0xad, 0xa3, 0xdc, 0xaa, 0x3c, 0xfa, 0xd3, 0x02, 0x5e, 0x09, 0x42,
	0x1f, 0xc2, 0x06, 0x49, 0xfc, 0x01, 0x33, 0x1a, 0x2b, 0xa0, 0x15, 0x04, 0xbd, 0x0b, 0xdb, 0x7e,
	0x48, 0xc9, 0x2e, 0x0f, 0xd3, 0xc4, 0x68, 0x76, 0x41, 0x7f, 0x63, 0x6b, 0xd3, 0x9e, 0xf5, 0x6a,
	0xbb, 0x5c, 0xf2, 0xe6, 0x59, 0xa2, 0xbc, 0x0c, 0xf3, 0x91, 0xb1, 0x26, 0x99, 0x90, 0x36, 0xea,
	0xc1, 0x26, 0x1b, 0x61, 0xea, 0x33, 0xa3, 0xd5, 0xad, 0xf5, 0xdb, 0x2e, 0x9c, 0xe6, 0x56, 0x11,
	0xf1, 0x8a, 0x67, 0xef, 0x5f, 0x00, 0x91, 0xa0, 0x74, 0x27, 0x61, 0x1c, 0x27, 0xfc, 0x3a, 0xcc,
	0x7e, 0x04, 0x9b, 0x42, 0x65, 0x03, 0x26, 0xb9, 0xbd, 0x6a, 0xa9, 0x05, 0x66, 0xb1, 0xd6, 0xfa,
	0x4a, 0xb5, 0x36, 0x96, 0xd6, 0xda, 0xbc, 0xb0, 0xd6, 0x9f, 0xea, 0xf0, 0x25, 0x25, 0x1f, 0x96,
	0xa5, 0x09, 0x23, 0x02, 0xf4, 0x80, 0x63, 0x3e, 0x66, 0xaa, 0xcc, 0x02, 0x24, 0x23, 0x5e, 0xb1,
	0x82, 0x3e, 0x85, 0xf5, 0x6d, 0xcc, 0xb1, 0x2c, 0x79, 0x7d, 0xeb, 0xa6, 0xad, 0x49, 0x5b, 0xec,
	0x25, 0xd6, 0xdc, 0x5b, 0xa2, 0xaa, 0x69, 0x6e, 0x6d, 0xf8, 0x98, 0xe3, 0x77, 0xd2, 0x38, 0xe4,
	0x24, 0xce, 0xf8, 0xa1, 0x27, 0x91, 0xe8, 0x7d, 0xd8, 0xfe, 0x8c, 0xd2, 0x94, 0x0e, 0x0e, 0x33,
	0x22, 0x29, 0x6a, 0xbb, 0xb7, 0xa7, 0xb9, 0xb5, 0x49, 0xca, 0xa0, 0x86, 0x98, 0x67, 0xa2, 0xb7,
	0x60, 0x43, 0x3a, 0x92, 0x94, 0xb6, 0xbb, 0x39, 0xcd, 0xad, 0x97, 0x25, 0x44, 0x4b, 0x57, 0x19,
	0x8b, 0x1c, 0x36, 0xae, 0xc4, 0xe1, 0xac, 0x95, 0x4d, 0xbd, 0x95, 0x06, 0x5c, 0x7b, 0x48, 0x28,
	0x13, 0xdb, 0xac, 0xc9, 0x78, 0xe9, 0xa2, 0x7b, 0x10, 0x0a, 0x62, 0x42, 0xc6, 0xc3, 0x5d, 0xa1,
	0x27, 0x41, 0xc6, 0x0d, 0x5b, 0x5d, 0x0d, 0x1e, 0x61, 0xe3, 0x88, 0xbb, 0xa8, 0x60, 0x41, 0x4b,
	0xf4, 0x34, 0x1b, 0xfd, 0x0c, 0xe0, 0xda, 0x17, 0x04, 0xfb, 0x84, 0x32, 0xa3, 0xdd, 0xad, 0xf5,
	0xd7, 0xb7, 0xfa, 0xf6, 0xe2, 0xbd, 0x61, 0x7f, 0x45, 0xd3, 0x98, 0xf0, 0x11, 0x19, 0xb3, 0xb2,
	0x47, 0x0a, 0xe0, 0x7e, 0x73, 0x92, 0x5b, 0x5f, 0xeb, 0x57, 0x21, 0xc5, 0x7b, 0x38, 0xc1, 0x4e,
	0x94, 0xee, 0x87, 0xce, 0x95, 0xee, 0xa4, 0x0b, 0xf7, 0x9e, 0xe6, 0x16, 0xb8, 0xe3, 0x95, 0x27,
	0xeb, 0xfd, 0x01, 0xe0, 0xab, 0xa2, 0xb1, 0x0f, 0xc4, 0x7e, 0x4c, 0x9b, 0x87, 0x18, 0xf3, 0xdd,
	0x91, 0x01, 0x84, 0xba, 0x3c, 0xe5, 0xe8, 0x77, 0x44, 0xf5, 0xb9, 0xee, 0x88, 0xda, 0xea, 0x77,
	0x44, 0x39, 0x04, 0xf5, 0xa5, 0x43, 0xd0, 0xb8, 0x70, 0x08, 0x7e, 0xa8, 0xa9, 0x81, 0x2f, 0xeb,
	0x5b, 0x61, 0x14, 0x3e, 0x9f, 0x8d, 0x42, 0x4d, 0x9e, 0x76, 0xa6, 0x30, 0xb5, 0xd7, 0x8e, 0x4f,
	0x12, 0x1e, 0xee, 0x85, 0x84, 0x5e, 0x32, 0x10, 0x9a, 0xca, 0x6a, 0x8b, 0x2a, 0xd3, 0x25, 0x52,
	0x7f, 0x51, 0x25, 0x72, 0x6e, 0x16, 0x1a, 0xd7, 0x98, 0x85, 0xde, 0xaf, 0x00, 0xbe, 0x26, 0xba,
	0x70, 0x1f, 0x0f, 0x49, 0xf4, 0x25, 0x8e, 0xe7, 0x4a, 0xd3, 0x34, 0x05, 0x9e, 0x4b, 0x53, 0xd5,
	0xeb, 0x6b, 0xaa, 0x36, 0xd7, 0x54, 0xef, 0xef, 0x2a, 0xbc, 0x75, 0xfe, 0xa4, 0x2b, 0x68, 0xe6,
	0x0d, 0x4d, 0x33, 0x6d, 0x17, 0xfd, 0xaf, 0x89, 0x8b, 0x35, 0xf1, 0x3b, 0x80, 0xad, 0xf2, 0x2f,
	0x05, 0xd9, 0x10, 0x2a, 0x98, 0xfc, 0xd7, 0x50, 0xfc, 0x6e, 0x08, 0x30, 0x9d, 0x45, 0x3d, 0x2d,
	0x03, 0x25, 0xb0, 0xa9, 0xbc, 0x62, 0x3a, 0x6f, 0x6b, 0xd3, 0xc9, 0x29, 0xc1, 0xf1, 0x3d, 0x1f,
	0x67, 0x9c, 0x50, 0xf7, 0x63, 0x71, 0x8a, 0x93, 0xdc, 0x7a, 0xfb, 0x59, 0xb4, 0x9c, 0xc3, 0x8a,
	0xbe, 0xaa, 0xf7, 0x7a, 0xc5, 0x5b, 0x7a, 0x3f, 0x02, 0xf8, 0x8a, 0x38, 0xac, 0xa0, 0x67, 0x26,
	0x88, 0x6d, 0xd8, 0xa2, 0x85, 0x5d, 0x88, 0xb7, 0x77, 0x79, 0xab, 0xdc, 0xfa, 0x51, 0x6e, 0x01,
	0x6f, 0x86, 0x44, 0x77, 0x17, 0xa8, 0xac, 0x2e, 0xa3, 0x52, 0x40, 0x2a, 0x0b, 0xe4, 0x4d, 0xaa,
	0x10, 0xed, 0x88, 0x6f, 0x47, 0xa1, 0xbb, 0xb9, 0x44, 0x7f, 0x01, 0x4b, 0x8e, 0xb4, 0xf8, 0x8d,
	0x69, 0x3f, 0x0d, 0x73, 0xc9, 0x49, 0x6e, 0xe1, 0x67, 0x11, 0xb4, 0xfa, 0xa7, 0xec, 0x92, 0xd7,
	0x68, 0x35, 0xeb, 0x22, 0xaf, 0xbe, 0xa8, 0x22, 0x77, 0xdf, 0x3b, 0x3e, 0x35, 0x2b, 0x8f, 0x4f,
	0xcd, 0xca, 0x93, 0x53, 0x13, 0x7c, 0x3f, 0x31, 0xc1, 0x6f, 0x13, 0x13, 0x1c, 0x4d, 0x4c, 0x70,
	0x3c, 0x31, 0xc1, 0x5f, 0x13, 0x13, 0xfc, 0x33, 0x31, 0x2b, 0x4f, 0x26, 0x26, 0x78, 0x74, 0x66,
	0x56, 0x8e, 0xcf, 0xcc, 0xca, 0xe3, 0x33, 0xb3, 0x32, 0x6c, 0x4a, 0x29, 0xdd, 0xfd, 0x2f, 0x00,
	0x00, 0xff, 0xff, 0x56, 0x94, 0x8e, 0x4e, 0x00, 0x0d, 0x00, 0x00,
}

func (this *LokiRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiRequest)
	if !ok {
		that2, ok := that.(LokiRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Query != that1.Query {
		return false
	}
	if this.Limit != that1.Limit {
		return false
	}
	if this.Step != that1.Step {
		return false
	}
	if this.Interval != that1.Interval {
		return false
	}
	if !this.StartTs.Equal(that1.StartTs) {
		return false
	}
	if !this.EndTs.Equal(that1.EndTs) {
		return false
	}
	if this.Direction != that1.Direction {
		return false
	}
	if this.Path != that1.Path {
		return false
	}
	if len(this.Shards) != len(that1.Shards) {
		return false
	}
	for i := range this.Shards {
		if this.Shards[i] != that1.Shards[i] {
			return false
		}
	}
	return true
}
func (this *LokiInstantRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiInstantRequest)
	if !ok {
		that2, ok := that.(LokiInstantRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Query != that1.Query {
		return false
	}
	if this.Limit != that1.Limit {
		return false
	}
	if !this.TimeTs.Equal(that1.TimeTs) {
		return false
	}
	if this.Direction != that1.Direction {
		return false
	}
	if this.Path != that1.Path {
		return false
	}
	if len(this.Shards) != len(that1.Shards) {
		return false
	}
	for i := range this.Shards {
		if this.Shards[i] != that1.Shards[i] {
			return false
		}
	}
	return true
}
func (this *LokiResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiResponse)
	if !ok {
		that2, ok := that.(LokiResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Status != that1.Status {
		return false
	}
	if !this.Data.Equal(&that1.Data) {
		return false
	}
	if this.ErrorType != that1.ErrorType {
		return false
	}
	if this.Error != that1.Error {
		return false
	}
	if this.Direction != that1.Direction {
		return false
	}
	if this.Limit != that1.Limit {
		return false
	}
	if this.Version != that1.Version {
		return false
	}
	if !this.Statistics.Equal(&that1.Statistics) {
		return false
	}
	if len(this.Headers) != len(that1.Headers) {
		return false
	}
	for i := range this.Headers {
		if !this.Headers[i].Equal(that1.Headers[i]) {
			return false
		}
	}
	return true
}
func (this *LokiSeriesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiSeriesRequest)
	if !ok {
		that2, ok := that.(LokiSeriesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Match) != len(that1.Match) {
		return false
	}
	for i := range this.Match {
		if this.Match[i] != that1.Match[i] {
			return false
		}
	}
	if !this.StartTs.Equal(that1.StartTs) {
		return false
	}
	if !this.EndTs.Equal(that1.EndTs) {
		return false
	}
	if this.Path != that1.Path {
		return false
	}
	if len(this.Shards) != len(that1.Shards) {
		return false
	}
	for i := range this.Shards {
		if this.Shards[i] != that1.Shards[i] {
			return false
		}
	}
	return true
}
func (this *LokiSeriesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiSeriesResponse)
	if !ok {
		that2, ok := that.(LokiSeriesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Status != that1.Status {
		return false
	}
	if len(this.Data) != len(that1.Data) {
		return false
	}
	for i := range this.Data {
		if !this.Data[i].Equal(&that1.Data[i]) {
			return false
		}
	}
	if this.Version != that1.Version {
		return false
	}
	if len(this.Headers) != len(that1.Headers) {
		return false
	}
	for i := range this.Headers {
		if !this.Headers[i].Equal(that1.Headers[i]) {
			return false
		}
	}
	if !this.Statistics.Equal(&that1.Statistics) {
		return false
	}
	return true
}
func (this *LokiLabelNamesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiLabelNamesRequest)
	if !ok {
		that2, ok := that.(LokiLabelNamesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.StartTs.Equal(that1.StartTs) {
		return false
	}
	if !this.EndTs.Equal(that1.EndTs) {
		return false
	}
	if this.Path != that1.Path {
		return false
	}
	return true
}
func (this *LokiLabelNamesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiLabelNamesResponse)
	if !ok {
		that2, ok := that.(LokiLabelNamesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Status != that1.Status {
		return false
	}
	if len(this.Data) != len(that1.Data) {
		return false
	}
	for i := range this.Data {
		if this.Data[i] != that1.Data[i] {
			return false
		}
	}
	if this.Version != that1.Version {
		return false
	}
	if len(this.Headers) != len(that1.Headers) {
		return false
	}
	for i := range this.Headers {
		if !this.Headers[i].Equal(that1.Headers[i]) {
			return false
		}
	}
	if !this.Statistics.Equal(&that1.Statistics) {
		return false
	}
	return true
}
func (this *LokiData) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiData)
	if !ok {
		that2, ok := that.(LokiData)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ResultType != that1.ResultType {
		return false
	}
	if len(this.Result) != len(that1.Result) {
		return false
	}
	for i := range this.Result {
		if !this.Result[i].Equal(that1.Result[i]) {
			return false
		}
	}
	return true
}
func (this *LokiPromResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LokiPromResponse)
	if !ok {
		that2, ok := that.(LokiPromResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Response.Equal(that1.Response) {
		return false
	}
	if !this.Statistics.Equal(&that1.Statistics) {
		return false
	}
	return true
}
func (this *IndexStatsResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*IndexStatsResponse)
	if !ok {
		that2, ok := that.(IndexStatsResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if that1.Response == nil {
		if this.Response != nil {
			return false
		}
	} else if !this.Response.Equal(*that1.Response) {
		return false
	}
	if len(this.Headers) != len(that1.Headers) {
		return false
	}
	for i := range this.Headers {
		if !this.Headers[i].Equal(that1.Headers[i]) {
			return false
		}
	}
	return true
}
func (this *LokiRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 13)
	s = append(s, "&queryrange.LokiRequest{")
	s = append(s, "Query: "+fmt.Sprintf("%#v", this.Query)+",\n")
	s = append(s, "Limit: "+fmt.Sprintf("%#v", this.Limit)+",\n")
	s = append(s, "Step: "+fmt.Sprintf("%#v", this.Step)+",\n")
	s = append(s, "Interval: "+fmt.Sprintf("%#v", this.Interval)+",\n")
	s = append(s, "StartTs: "+fmt.Sprintf("%#v", this.StartTs)+",\n")
	s = append(s, "EndTs: "+fmt.Sprintf("%#v", this.EndTs)+",\n")
	s = append(s, "Direction: "+fmt.Sprintf("%#v", this.Direction)+",\n")
	s = append(s, "Path: "+fmt.Sprintf("%#v", this.Path)+",\n")
	s = append(s, "Shards: "+fmt.Sprintf("%#v", this.Shards)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiInstantRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 10)
	s = append(s, "&queryrange.LokiInstantRequest{")
	s = append(s, "Query: "+fmt.Sprintf("%#v", this.Query)+",\n")
	s = append(s, "Limit: "+fmt.Sprintf("%#v", this.Limit)+",\n")
	s = append(s, "TimeTs: "+fmt.Sprintf("%#v", this.TimeTs)+",\n")
	s = append(s, "Direction: "+fmt.Sprintf("%#v", this.Direction)+",\n")
	s = append(s, "Path: "+fmt.Sprintf("%#v", this.Path)+",\n")
	s = append(s, "Shards: "+fmt.Sprintf("%#v", this.Shards)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 13)
	s = append(s, "&queryrange.LokiResponse{")
	s = append(s, "Status: "+fmt.Sprintf("%#v", this.Status)+",\n")
	s = append(s, "Data: "+strings.Replace(this.Data.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "ErrorType: "+fmt.Sprintf("%#v", this.ErrorType)+",\n")
	s = append(s, "Error: "+fmt.Sprintf("%#v", this.Error)+",\n")
	s = append(s, "Direction: "+fmt.Sprintf("%#v", this.Direction)+",\n")
	s = append(s, "Limit: "+fmt.Sprintf("%#v", this.Limit)+",\n")
	s = append(s, "Version: "+fmt.Sprintf("%#v", this.Version)+",\n")
	s = append(s, "Statistics: "+strings.Replace(this.Statistics.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "Headers: "+fmt.Sprintf("%#v", this.Headers)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiSeriesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&queryrange.LokiSeriesRequest{")
	s = append(s, "Match: "+fmt.Sprintf("%#v", this.Match)+",\n")
	s = append(s, "StartTs: "+fmt.Sprintf("%#v", this.StartTs)+",\n")
	s = append(s, "EndTs: "+fmt.Sprintf("%#v", this.EndTs)+",\n")
	s = append(s, "Path: "+fmt.Sprintf("%#v", this.Path)+",\n")
	s = append(s, "Shards: "+fmt.Sprintf("%#v", this.Shards)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiSeriesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&queryrange.LokiSeriesResponse{")
	s = append(s, "Status: "+fmt.Sprintf("%#v", this.Status)+",\n")
	if this.Data != nil {
		vs := make([]*logproto.SeriesIdentifier, len(this.Data))
		for i := range vs {
			vs[i] = &this.Data[i]
		}
		s = append(s, "Data: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "Version: "+fmt.Sprintf("%#v", this.Version)+",\n")
	s = append(s, "Headers: "+fmt.Sprintf("%#v", this.Headers)+",\n")
	s = append(s, "Statistics: "+strings.Replace(this.Statistics.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiLabelNamesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&queryrange.LokiLabelNamesRequest{")
	s = append(s, "StartTs: "+fmt.Sprintf("%#v", this.StartTs)+",\n")
	s = append(s, "EndTs: "+fmt.Sprintf("%#v", this.EndTs)+",\n")
	s = append(s, "Path: "+fmt.Sprintf("%#v", this.Path)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiLabelNamesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&queryrange.LokiLabelNamesResponse{")
	s = append(s, "Status: "+fmt.Sprintf("%#v", this.Status)+",\n")
	s = append(s, "Data: "+fmt.Sprintf("%#v", this.Data)+",\n")
	s = append(s, "Version: "+fmt.Sprintf("%#v", this.Version)+",\n")
	s = append(s, "Headers: "+fmt.Sprintf("%#v", this.Headers)+",\n")
	s = append(s, "Statistics: "+strings.Replace(this.Statistics.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiData) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrange.LokiData{")
	s = append(s, "ResultType: "+fmt.Sprintf("%#v", this.ResultType)+",\n")
	s = append(s, "Result: "+fmt.Sprintf("%#v", this.Result)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LokiPromResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrange.LokiPromResponse{")
	if this.Response != nil {
		s = append(s, "Response: "+fmt.Sprintf("%#v", this.Response)+",\n")
	}
	s = append(s, "Statistics: "+strings.Replace(this.Statistics.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *IndexStatsResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrange.IndexStatsResponse{")
	s = append(s, "Response: "+fmt.Sprintf("%#v", this.Response)+",\n")
	s = append(s, "Headers: "+fmt.Sprintf("%#v", this.Headers)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringQueryrange(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *LokiRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Interval != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Interval))
		i--
		dAtA[i] = 0x48
	}
	if len(m.Shards) > 0 {
		for iNdEx := len(m.Shards) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Shards[iNdEx])
			copy(dAtA[i:], m.Shards[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Shards[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0x3a
	}
	if m.Direction != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Direction))
		i--
		dAtA[i] = 0x30
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTs):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintQueryrange(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x2a
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTs):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintQueryrange(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x22
	if m.Step != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Step))
		i--
		dAtA[i] = 0x18
	}
	if m.Limit != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LokiInstantRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiInstantRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiInstantRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Shards) > 0 {
		for iNdEx := len(m.Shards) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Shards[iNdEx])
			copy(dAtA[i:], m.Shards[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Shards[iNdEx])))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Direction != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Direction))
		i--
		dAtA[i] = 0x20
	}
	n3, err3 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.TimeTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.TimeTs):])
	if err3 != nil {
		return 0, err3
	}
	i -= n3
	i = encodeVarintQueryrange(dAtA, i, uint64(n3))
	i--
	dAtA[i] = 0x1a
	if m.Limit != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LokiResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Headers) > 0 {
		for iNdEx := len(m.Headers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Headers[iNdEx].Size()
				i -= size
				if _, err := m.Headers[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x4a
		}
	}
	{
		size, err := m.Statistics.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x42
	if m.Version != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x38
	}
	if m.Limit != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x30
	}
	if m.Direction != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Direction))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ErrorType) > 0 {
		i -= len(m.ErrorType)
		copy(dAtA[i:], m.ErrorType)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.ErrorType)))
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Data.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LokiSeriesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiSeriesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiSeriesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Shards) > 0 {
		for iNdEx := len(m.Shards) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Shards[iNdEx])
			copy(dAtA[i:], m.Shards[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Shards[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0x22
	}
	n6, err6 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTs):])
	if err6 != nil {
		return 0, err6
	}
	i -= n6
	i = encodeVarintQueryrange(dAtA, i, uint64(n6))
	i--
	dAtA[i] = 0x1a
	n7, err7 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTs):])
	if err7 != nil {
		return 0, err7
	}
	i -= n7
	i = encodeVarintQueryrange(dAtA, i, uint64(n7))
	i--
	dAtA[i] = 0x12
	if len(m.Match) > 0 {
		for iNdEx := len(m.Match) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Match[iNdEx])
			copy(dAtA[i:], m.Match[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Match[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *LokiSeriesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiSeriesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiSeriesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Statistics.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x2a
	if len(m.Headers) > 0 {
		for iNdEx := len(m.Headers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Headers[iNdEx].Size()
				i -= size
				if _, err := m.Headers[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.Version != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Data) > 0 {
		for iNdEx := len(m.Data) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Data[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LokiLabelNamesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiLabelNamesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiLabelNamesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0x1a
	}
	n9, err9 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTs):])
	if err9 != nil {
		return 0, err9
	}
	i -= n9
	i = encodeVarintQueryrange(dAtA, i, uint64(n9))
	i--
	dAtA[i] = 0x12
	n10, err10 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTs):])
	if err10 != nil {
		return 0, err10
	}
	i -= n10
	i = encodeVarintQueryrange(dAtA, i, uint64(n10))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *LokiLabelNamesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiLabelNamesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiLabelNamesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Statistics.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x2a
	if len(m.Headers) > 0 {
		for iNdEx := len(m.Headers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Headers[iNdEx].Size()
				i -= size
				if _, err := m.Headers[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.Version != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Data) > 0 {
		for iNdEx := len(m.Data) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Data[iNdEx])
			copy(dAtA[i:], m.Data[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Data[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LokiData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Result) > 0 {
		for iNdEx := len(m.Result) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Result[iNdEx].Size()
				i -= size
				if _, err := m.Result[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ResultType) > 0 {
		i -= len(m.ResultType)
		copy(dAtA[i:], m.ResultType)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.ResultType)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LokiPromResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LokiPromResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LokiPromResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Statistics.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.Response != nil {
		{
			size, err := m.Response.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintQueryrange(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IndexStatsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexStatsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IndexStatsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Headers) > 0 {
		for iNdEx := len(m.Headers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Headers[iNdEx].Size()
				i -= size
				if _, err := m.Headers[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Response != nil {
		{
			size := m.Response.Size()
			i -= size
			if _, err := m.Response.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintQueryrange(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintQueryrange(dAtA []byte, offset int, v uint64) int {
	offset -= sovQueryrange(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *LokiRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if m.Limit != 0 {
		n += 1 + sovQueryrange(uint64(m.Limit))
	}
	if m.Step != 0 {
		n += 1 + sovQueryrange(uint64(m.Step))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTs)
	n += 1 + l + sovQueryrange(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTs)
	n += 1 + l + sovQueryrange(uint64(l))
	if m.Direction != 0 {
		n += 1 + sovQueryrange(uint64(m.Direction))
	}
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Shards) > 0 {
		for _, s := range m.Shards {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	if m.Interval != 0 {
		n += 1 + sovQueryrange(uint64(m.Interval))
	}
	return n
}

func (m *LokiInstantRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if m.Limit != 0 {
		n += 1 + sovQueryrange(uint64(m.Limit))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.TimeTs)
	n += 1 + l + sovQueryrange(uint64(l))
	if m.Direction != 0 {
		n += 1 + sovQueryrange(uint64(m.Direction))
	}
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Shards) > 0 {
		for _, s := range m.Shards {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *LokiResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	l = m.Data.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	l = len(m.ErrorType)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if m.Direction != 0 {
		n += 1 + sovQueryrange(uint64(m.Direction))
	}
	if m.Limit != 0 {
		n += 1 + sovQueryrange(uint64(m.Limit))
	}
	if m.Version != 0 {
		n += 1 + sovQueryrange(uint64(m.Version))
	}
	l = m.Statistics.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	if len(m.Headers) > 0 {
		for _, e := range m.Headers {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *LokiSeriesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Match) > 0 {
		for _, s := range m.Match {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTs)
	n += 1 + l + sovQueryrange(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTs)
	n += 1 + l + sovQueryrange(uint64(l))
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Shards) > 0 {
		for _, s := range m.Shards {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *LokiSeriesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Data) > 0 {
		for _, e := range m.Data {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	if m.Version != 0 {
		n += 1 + sovQueryrange(uint64(m.Version))
	}
	if len(m.Headers) > 0 {
		for _, e := range m.Headers {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	l = m.Statistics.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	return n
}

func (m *LokiLabelNamesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTs)
	n += 1 + l + sovQueryrange(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTs)
	n += 1 + l + sovQueryrange(uint64(l))
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	return n
}

func (m *LokiLabelNamesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Data) > 0 {
		for _, s := range m.Data {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	if m.Version != 0 {
		n += 1 + sovQueryrange(uint64(m.Version))
	}
	if len(m.Headers) > 0 {
		for _, e := range m.Headers {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	l = m.Statistics.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	return n
}

func (m *LokiData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResultType)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Result) > 0 {
		for _, e := range m.Result {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *LokiPromResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Response != nil {
		l = m.Response.Size()
		n += 1 + l + sovQueryrange(uint64(l))
	}
	l = m.Statistics.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	return n
}

func (m *IndexStatsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Response != nil {
		l = m.Response.Size()
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Headers) > 0 {
		for _, e := range m.Headers {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func sovQueryrange(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozQueryrange(x uint64) (n int) {
	return sovQueryrange(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *LokiRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiRequest{`,
		`Query:` + fmt.Sprintf("%v", this.Query) + `,`,
		`Limit:` + fmt.Sprintf("%v", this.Limit) + `,`,
		`Step:` + fmt.Sprintf("%v", this.Step) + `,`,
		`StartTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.StartTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`EndTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EndTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Direction:` + fmt.Sprintf("%v", this.Direction) + `,`,
		`Path:` + fmt.Sprintf("%v", this.Path) + `,`,
		`Shards:` + fmt.Sprintf("%v", this.Shards) + `,`,
		`Interval:` + fmt.Sprintf("%v", this.Interval) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiInstantRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiInstantRequest{`,
		`Query:` + fmt.Sprintf("%v", this.Query) + `,`,
		`Limit:` + fmt.Sprintf("%v", this.Limit) + `,`,
		`TimeTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.TimeTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Direction:` + fmt.Sprintf("%v", this.Direction) + `,`,
		`Path:` + fmt.Sprintf("%v", this.Path) + `,`,
		`Shards:` + fmt.Sprintf("%v", this.Shards) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiResponse{`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`Data:` + strings.Replace(strings.Replace(this.Data.String(), "LokiData", "LokiData", 1), `&`, ``, 1) + `,`,
		`ErrorType:` + fmt.Sprintf("%v", this.ErrorType) + `,`,
		`Error:` + fmt.Sprintf("%v", this.Error) + `,`,
		`Direction:` + fmt.Sprintf("%v", this.Direction) + `,`,
		`Limit:` + fmt.Sprintf("%v", this.Limit) + `,`,
		`Version:` + fmt.Sprintf("%v", this.Version) + `,`,
		`Statistics:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Statistics), "Result", "stats.Result", 1), `&`, ``, 1) + `,`,
		`Headers:` + fmt.Sprintf("%v", this.Headers) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiSeriesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiSeriesRequest{`,
		`Match:` + fmt.Sprintf("%v", this.Match) + `,`,
		`StartTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.StartTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`EndTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EndTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Path:` + fmt.Sprintf("%v", this.Path) + `,`,
		`Shards:` + fmt.Sprintf("%v", this.Shards) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiSeriesResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForData := "[]SeriesIdentifier{"
	for _, f := range this.Data {
		repeatedStringForData += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForData += "}"
	s := strings.Join([]string{`&LokiSeriesResponse{`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`Data:` + repeatedStringForData + `,`,
		`Version:` + fmt.Sprintf("%v", this.Version) + `,`,
		`Headers:` + fmt.Sprintf("%v", this.Headers) + `,`,
		`Statistics:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Statistics), "Result", "stats.Result", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiLabelNamesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiLabelNamesRequest{`,
		`StartTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.StartTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`EndTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EndTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Path:` + fmt.Sprintf("%v", this.Path) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiLabelNamesResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiLabelNamesResponse{`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`Data:` + fmt.Sprintf("%v", this.Data) + `,`,
		`Version:` + fmt.Sprintf("%v", this.Version) + `,`,
		`Headers:` + fmt.Sprintf("%v", this.Headers) + `,`,
		`Statistics:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Statistics), "Result", "stats.Result", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiData) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiData{`,
		`ResultType:` + fmt.Sprintf("%v", this.ResultType) + `,`,
		`Result:` + fmt.Sprintf("%v", this.Result) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LokiPromResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LokiPromResponse{`,
		`Response:` + strings.Replace(fmt.Sprintf("%v", this.Response), "PrometheusResponse", "queryrangebase.PrometheusResponse", 1) + `,`,
		`Statistics:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Statistics), "Result", "stats.Result", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *IndexStatsResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IndexStatsResponse{`,
		`Response:` + fmt.Sprintf("%v", this.Response) + `,`,
		`Headers:` + fmt.Sprintf("%v", this.Headers) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringQueryrange(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *LokiRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Step", wireType)
			}
			m.Step = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Step |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Direction", wireType)
			}
			m.Direction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Direction |= logproto.Direction(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shards", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Shards = append(m.Shards, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Interval", wireType)
			}
			m.Interval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Interval |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiInstantRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiInstantRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiInstantRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.TimeTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Direction", wireType)
			}
			m.Direction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Direction |= logproto.Direction(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shards", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Shards = append(m.Shards, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Direction", wireType)
			}
			m.Direction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Direction |= logproto.Direction(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Statistics", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Statistics.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Headers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Headers = append(m.Headers, github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader{})
			if err := m.Headers[len(m.Headers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiSeriesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiSeriesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiSeriesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Match", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Match = append(m.Match, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shards", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Shards = append(m.Shards, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiSeriesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiSeriesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiSeriesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data, logproto.SeriesIdentifier{})
			if err := m.Data[len(m.Data)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Headers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Headers = append(m.Headers, github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader{})
			if err := m.Headers[len(m.Headers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Statistics", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Statistics.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiLabelNamesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiLabelNamesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiLabelNamesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiLabelNamesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiLabelNamesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiLabelNamesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Headers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Headers = append(m.Headers, github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader{})
			if err := m.Headers[len(m.Headers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Statistics", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Statistics.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResultType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResultType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Result = append(m.Result, github_com_grafana_loki_pkg_logproto.Stream{})
			if err := m.Result[len(m.Result)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LokiPromResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LokiPromResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LokiPromResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Response", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Response == nil {
				m.Response = &queryrangebase.PrometheusResponse{}
			}
			if err := m.Response.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Statistics", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Statistics.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IndexStatsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IndexStatsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IndexStatsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Response", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Response == nil {
				m.Response = &github_com_grafana_loki_pkg_storage_stores_shipper_indexgateway_indexgatewaypb.IndexStatsResponse{}
			}
			if err := m.Response.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Headers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Headers = append(m.Headers, github_com_grafana_loki_pkg_querier_queryrange_queryrangebase.PrometheusResponseHeader{})
			if err := m.Headers[len(m.Headers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipQueryrange(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthQueryrange
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthQueryrange
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowQueryrange
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipQueryrange(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthQueryrange
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthQueryrange = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowQueryrange   = fmt.Errorf("proto: integer overflow")
)
