# Internal

This directory contains internal code for cloud.google.com/go packages.

## .repo-metadata-full.json

`.repo-metadata-full.json` contains metadata about the packages in this repo. It
is generated by `internal/gapicgen/generator`. It's processed by external tools
to build lists of all of the packages.

Don't make breaking changes to the format without consulting with the external
tools.

One day, we may want to create individual `.repo-metadata.json` files next to
each package, which is the pattern followed by some other languages. External
tools would then talk to pkg.go.dev or some other service to get the overall
list of packages and use the `.repo-metadata.json` files to get the additional
metadata required. For now, `.repo-metadata-full.json` includes everything.