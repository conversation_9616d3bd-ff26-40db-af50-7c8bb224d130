/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// ContainerImageApplyConfiguration represents an declarative configuration of the ContainerImage type for use
// with apply.
type ContainerImageApplyConfiguration struct {
	Names     []string `json:"names,omitempty"`
	SizeBytes *int64   `json:"sizeBytes,omitempty"`
}

// ContainerImageApplyConfiguration constructs an declarative configuration of the ContainerImage type for use with
// apply.
func ContainerImage() *ContainerImageApplyConfiguration {
	return &ContainerImageApplyConfiguration{}
}

// WithN<PERSON>s adds the given value to the Names field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Names field.
func (b *ContainerImageApplyConfiguration) WithNames(values ...string) *ContainerImageApplyConfiguration {
	for i := range values {
		b.Names = append(b.Names, values[i])
	}
	return b
}

// WithSizeBytes sets the SizeBytes field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SizeBytes field is set to the value of the last call.
func (b *ContainerImageApplyConfiguration) WithSizeBytes(value int64) *ContainerImageApplyConfiguration {
	b.SizeBytes = &value
	return b
}
