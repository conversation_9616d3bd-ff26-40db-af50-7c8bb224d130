/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.node.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1beta1";

// Overhead structure represents the resource overhead associated with running a pod.
message Overhead {
  // PodFixed represents the fixed resource overhead associated with running a pod.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> podFixed = 1;
}

// RuntimeClass defines a class of container runtime supported in the cluster.
// The RuntimeClass is used to determine which container runtime is used to run
// all containers in a pod. RuntimeClasses are (currently) manually defined by a
// user or cluster provisioner, and referenced in the PodSpec. The Kubelet is
// responsible for resolving the RuntimeClassName reference before running the
// pod.  For more details, see
// https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class
message RuntimeClass {
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Handler specifies the underlying runtime and configuration that the CRI
  // implementation will use to handle pods of this class. The possible values
  // are specific to the node & CRI configuration.  It is assumed that all
  // handlers are available on every node, and handlers of the same name are
  // equivalent on every node.
  // For example, a handler called "runc" might specify that the runc OCI
  // runtime (using native Linux containers) will be used to run the containers
  // in a pod.
  // The Handler must be lowercase, conform to the DNS Label (RFC 1123) requirements,
  // and is immutable.
  optional string handler = 2;

  // Overhead represents the resource overhead associated with running a pod for a
  // given RuntimeClass. For more details, see
  // https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md
  // This field is beta-level as of Kubernetes v1.18, and is only honored by servers that enable the PodOverhead feature.
  // +optional
  optional Overhead overhead = 3;

  // Scheduling holds the scheduling constraints to ensure that pods running
  // with this RuntimeClass are scheduled to nodes that support it.
  // If scheduling is nil, this RuntimeClass is assumed to be supported by all
  // nodes.
  // +optional
  optional Scheduling scheduling = 4;
}

// RuntimeClassList is a list of RuntimeClass objects.
message RuntimeClassList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of schema objects.
  repeated RuntimeClass items = 2;
}

// Scheduling specifies the scheduling constraints for nodes supporting a
// RuntimeClass.
message Scheduling {
  // nodeSelector lists labels that must be present on nodes that support this
  // RuntimeClass. Pods using this RuntimeClass can only be scheduled to a
  // node matched by this selector. The RuntimeClass nodeSelector is merged
  // with a pod's existing nodeSelector. Any conflicts will cause the pod to
  // be rejected in admission.
  // +optional
  // +mapType=atomic
  map<string, string> nodeSelector = 1;

  // tolerations are appended (excluding duplicates) to pods running with this
  // RuntimeClass during admission, effectively unioning the set of nodes
  // tolerated by the pod and the RuntimeClass.
  // +optional
  // +listType=atomic
  repeated k8s.io.api.core.v1.Toleration tolerations = 2;
}

