// Code generated by goyacc -o promql/parser/generated_parser.y.go promql/parser/generated_parser.y. DO NOT EDIT.

//line promql/parser/generated_parser.y:15
package parser

import __yyfmt__ "fmt"

//line promql/parser/generated_parser.y:15

import (
	"math"
	"sort"
	"strconv"
	"time"

	"github.com/prometheus/prometheus/model/labels"
	"github.com/prometheus/prometheus/model/value"
)

//line promql/parser/generated_parser.y:28
type yySymType struct {
	yys      int
	node     Node
	item     Item
	matchers []*labels.Matcher
	matcher  *labels.Matcher
	label    labels.Label
	labels   labels.Labels
	strings  []string
	series   []SequenceValue
	uint     uint64
	float    float64
	duration time.Duration
}

const (
	EQL                      = 57346
	BLANK                    = 57347
	COLON                    = 57348
	COMMA                    = 57349
	COMMENT                  = 57350
	DURATION                 = 57351
	EOF                      = 57352
	ERROR                    = 57353
	IDENTIFIER               = 57354
	LEFT_BRACE               = 57355
	LEFT_BRACKET             = 57356
	LEFT_PAREN               = 57357
	METRIC_IDENTIFIER        = 57358
	NUMBER                   = 57359
	RIGHT_BRACE              = 57360
	RIGHT_BRACKET            = 57361
	RIGHT_PAREN              = 57362
	SEMICOLON                = 57363
	SPACE                    = 57364
	STRING                   = 57365
	TIMES                    = 57366
	operatorsStart           = 57367
	ADD                      = 57368
	DIV                      = 57369
	EQLC                     = 57370
	EQL_REGEX                = 57371
	GTE                      = 57372
	GTR                      = 57373
	LAND                     = 57374
	LOR                      = 57375
	LSS                      = 57376
	LTE                      = 57377
	LUNLESS                  = 57378
	MOD                      = 57379
	MUL                      = 57380
	NEQ                      = 57381
	NEQ_REGEX                = 57382
	POW                      = 57383
	SUB                      = 57384
	AT                       = 57385
	ATAN2                    = 57386
	operatorsEnd             = 57387
	aggregatorsStart         = 57388
	AVG                      = 57389
	BOTTOMK                  = 57390
	COUNT                    = 57391
	COUNT_VALUES             = 57392
	GROUP                    = 57393
	MAX                      = 57394
	MIN                      = 57395
	QUANTILE                 = 57396
	STDDEV                   = 57397
	STDVAR                   = 57398
	SUM                      = 57399
	TOPK                     = 57400
	aggregatorsEnd           = 57401
	keywordsStart            = 57402
	BOOL                     = 57403
	BY                       = 57404
	GROUP_LEFT               = 57405
	GROUP_RIGHT              = 57406
	IGNORING                 = 57407
	OFFSET                   = 57408
	ON                       = 57409
	WITHOUT                  = 57410
	keywordsEnd              = 57411
	preprocessorStart        = 57412
	START                    = 57413
	END                      = 57414
	preprocessorEnd          = 57415
	startSymbolsStart        = 57416
	START_METRIC             = 57417
	START_SERIES_DESCRIPTION = 57418
	START_EXPRESSION         = 57419
	START_METRIC_SELECTOR    = 57420
	startSymbolsEnd          = 57421
)

var yyToknames = [...]string{
	"$end",
	"error",
	"$unk",
	"EQL",
	"BLANK",
	"COLON",
	"COMMA",
	"COMMENT",
	"DURATION",
	"EOF",
	"ERROR",
	"IDENTIFIER",
	"LEFT_BRACE",
	"LEFT_BRACKET",
	"LEFT_PAREN",
	"METRIC_IDENTIFIER",
	"NUMBER",
	"RIGHT_BRACE",
	"RIGHT_BRACKET",
	"RIGHT_PAREN",
	"SEMICOLON",
	"SPACE",
	"STRING",
	"TIMES",
	"operatorsStart",
	"ADD",
	"DIV",
	"EQLC",
	"EQL_REGEX",
	"GTE",
	"GTR",
	"LAND",
	"LOR",
	"LSS",
	"LTE",
	"LUNLESS",
	"MOD",
	"MUL",
	"NEQ",
	"NEQ_REGEX",
	"POW",
	"SUB",
	"AT",
	"ATAN2",
	"operatorsEnd",
	"aggregatorsStart",
	"AVG",
	"BOTTOMK",
	"COUNT",
	"COUNT_VALUES",
	"GROUP",
	"MAX",
	"MIN",
	"QUANTILE",
	"STDDEV",
	"STDVAR",
	"SUM",
	"TOPK",
	"aggregatorsEnd",
	"keywordsStart",
	"BOOL",
	"BY",
	"GROUP_LEFT",
	"GROUP_RIGHT",
	"IGNORING",
	"OFFSET",
	"ON",
	"WITHOUT",
	"keywordsEnd",
	"preprocessorStart",
	"START",
	"END",
	"preprocessorEnd",
	"startSymbolsStart",
	"START_METRIC",
	"START_SERIES_DESCRIPTION",
	"START_EXPRESSION",
	"START_METRIC_SELECTOR",
	"startSymbolsEnd",
}

var yyStatenames = [...]string{}

const (
	yyEofCode          = 1
	yyErrCode          = 2
	yyInitialStackSize = 16
)

//line promql/parser/generated_parser.y:749

//line yacctab:1
var yyExca = [...]int{
	-1, 1,
	1, -1,
	-2, 0,
	-1, 35,
	1, 131,
	10, 131,
	22, 131,
	-2, 0,
	-1, 58,
	2, 143,
	15, 143,
	62, 143,
	68, 143,
	-2, 97,
	-1, 59,
	2, 144,
	15, 144,
	62, 144,
	68, 144,
	-2, 98,
	-1, 60,
	2, 145,
	15, 145,
	62, 145,
	68, 145,
	-2, 100,
	-1, 61,
	2, 146,
	15, 146,
	62, 146,
	68, 146,
	-2, 101,
	-1, 62,
	2, 147,
	15, 147,
	62, 147,
	68, 147,
	-2, 102,
	-1, 63,
	2, 148,
	15, 148,
	62, 148,
	68, 148,
	-2, 107,
	-1, 64,
	2, 149,
	15, 149,
	62, 149,
	68, 149,
	-2, 109,
	-1, 65,
	2, 150,
	15, 150,
	62, 150,
	68, 150,
	-2, 111,
	-1, 66,
	2, 151,
	15, 151,
	62, 151,
	68, 151,
	-2, 112,
	-1, 67,
	2, 152,
	15, 152,
	62, 152,
	68, 152,
	-2, 113,
	-1, 68,
	2, 153,
	15, 153,
	62, 153,
	68, 153,
	-2, 114,
	-1, 69,
	2, 154,
	15, 154,
	62, 154,
	68, 154,
	-2, 115,
	-1, 190,
	12, 199,
	13, 199,
	16, 199,
	17, 199,
	23, 199,
	26, 199,
	32, 199,
	33, 199,
	36, 199,
	42, 199,
	47, 199,
	48, 199,
	49, 199,
	50, 199,
	51, 199,
	52, 199,
	53, 199,
	54, 199,
	55, 199,
	56, 199,
	57, 199,
	58, 199,
	62, 199,
	66, 199,
	68, 199,
	71, 199,
	72, 199,
	-2, 0,
	-1, 191,
	12, 199,
	13, 199,
	16, 199,
	17, 199,
	23, 199,
	26, 199,
	32, 199,
	33, 199,
	36, 199,
	42, 199,
	47, 199,
	48, 199,
	49, 199,
	50, 199,
	51, 199,
	52, 199,
	53, 199,
	54, 199,
	55, 199,
	56, 199,
	57, 199,
	58, 199,
	62, 199,
	66, 199,
	68, 199,
	71, 199,
	72, 199,
	-2, 0,
	-1, 212,
	19, 197,
	-2, 0,
	-1, 262,
	19, 198,
	-2, 0,
}

const yyPrivate = 57344

const yyLast = 659

var yyAct = [...]int{
	268, 37, 216, 142, 258, 257, 150, 113, 77, 102,
	101, 104, 188, 271, 189, 190, 191, 105, 6, 126,
	218, 57, 253, 149, 154, 252, 251, 266, 180, 121,
	228, 260, 265, 272, 234, 103, 269, 144, 274, 247,
	155, 72, 213, 162, 145, 264, 212, 250, 106, 179,
	230, 231, 246, 153, 232, 108, 161, 109, 208, 211,
	106, 107, 245, 33, 122, 219, 221, 223, 224, 225,
	233, 235, 238, 239, 240, 241, 242, 143, 110, 220,
	222, 226, 227, 229, 236, 237, 115, 79, 7, 243,
	244, 2, 3, 4, 5, 104, 114, 78, 145, 263,
	170, 105, 248, 177, 156, 169, 145, 118, 166, 160,
	163, 158, 117, 159, 157, 10, 168, 100, 120, 273,
	119, 145, 81, 116, 187, 74, 178, 34, 186, 192,
	193, 194, 195, 196, 197, 198, 199, 200, 201, 202,
	203, 204, 205, 206, 96, 185, 99, 207, 127, 128,
	129, 130, 131, 132, 133, 134, 135, 136, 137, 138,
	139, 140, 141, 182, 56, 1, 115, 9, 9, 98,
	184, 148, 172, 218, 173, 153, 114, 249, 209, 210,
	261, 8, 112, 228, 154, 35, 153, 234, 47, 46,
	254, 215, 79, 255, 256, 154, 45, 259, 44, 175,
	155, 125, 78, 230, 231, 43, 48, 232, 76, 174,
	176, 155, 73, 42, 41, 245, 262, 123, 219, 221,
	223, 224, 225, 233, 235, 238, 239, 240, 241, 242,
	164, 40, 220, 222, 226, 227, 229, 236, 237, 124,
	151, 152, 243, 244, 39, 38, 49, 146, 183, 267,
	80, 181, 214, 75, 270, 51, 72, 147, 53, 22,
	52, 55, 217, 165, 171, 50, 54, 111, 275, 70,
	0, 0, 276, 0, 0, 18, 19, 0, 0, 20,
	0, 0, 0, 0, 0, 71, 0, 0, 0, 0,
	58, 59, 60, 61, 62, 63, 64, 65, 66, 67,
	68, 69, 0, 0, 0, 13, 0, 0, 0, 24,
	0, 30, 0, 0, 31, 32, 36, 100, 51, 72,
	0, 53, 22, 52, 0, 0, 0, 0, 0, 54,
	84, 0, 70, 0, 0, 0, 0, 0, 18, 19,
	93, 94, 20, 0, 96, 0, 99, 83, 71, 0,
	0, 0, 0, 58, 59, 60, 61, 62, 63, 64,
	65, 66, 67, 68, 69, 0, 0, 0, 13, 98,
	0, 0, 24, 0, 30, 0, 0, 31, 32, 51,
	72, 0, 53, 22, 52, 0, 0, 0, 0, 0,
	54, 0, 0, 70, 0, 0, 0, 0, 0, 18,
	19, 0, 0, 20, 0, 0, 17, 72, 0, 71,
	22, 0, 0, 0, 58, 59, 60, 61, 62, 63,
	64, 65, 66, 67, 68, 69, 18, 19, 0, 13,
	20, 0, 0, 24, 0, 30, 0, 0, 31, 32,
	0, 11, 12, 14, 15, 16, 21, 23, 25, 26,
	27, 28, 29, 17, 33, 0, 13, 22, 0, 0,
	24, 0, 30, 0, 0, 31, 32, 0, 0, 0,
	0, 0, 0, 18, 19, 0, 0, 20, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 11, 12,
	14, 15, 16, 21, 23, 25, 26, 27, 28, 29,
	0, 0, 100, 13, 0, 0, 0, 24, 167, 30,
	0, 0, 31, 32, 82, 84, 85, 0, 86, 87,
	88, 89, 90, 91, 92, 93, 94, 95, 100, 96,
	97, 99, 83, 0, 0, 0, 0, 0, 0, 0,
	82, 84, 85, 0, 86, 87, 88, 89, 90, 91,
	92, 93, 94, 95, 98, 96, 97, 99, 83, 0,
	0, 100, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 82, 84, 85, 0, 86, 87, 88,
	98, 90, 91, 92, 93, 94, 95, 100, 96, 97,
	99, 83, 0, 0, 0, 0, 0, 0, 0, 82,
	84, 85, 0, 86, 87, 0, 100, 90, 91, 0,
	93, 94, 95, 98, 96, 97, 99, 83, 82, 84,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 93,
	94, 0, 0, 96, 97, 99, 83, 0, 0, 98,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 98,
}

var yyPact = [...]int{
	16, 78, 441, 441, 306, 394, -1000, -1000, -1000, 50,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, 190, -1000, 120, -1000, 514, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	33, 45, -1000, 367, -1000, 367, 28, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, 164, -1000, -1000, 105, -1000, -1000, 116, -1000,
	7, -1000, -42, -42, -42, -42, -42, -42, -42, -42,
	-42, -42, -42, -42, -42, -42, -42, -42, 35, 169,
	112, 45, -51, -1000, 41, 41, 243, -1000, 488, 103,
	-1000, 98, -1000, -1000, 170, -1000, -1000, 85, -1000, 26,
	-1000, 158, 367, -1000, -53, -48, -1000, 367, 367, 367,
	367, 367, 367, 367, 367, 367, 367, 367, 367, 367,
	367, 367, -1000, 89, -1000, -1000, -1000, 43, -1000, -1000,
	-1000, -1000, -1000, -1000, 36, 36, 40, -1000, -1000, -1000,
	-1000, 171, -1000, -1000, 32, -1000, 514, -1000, -1000, 84,
	-1000, 24, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, 1, -2, -1000, -1000, -1000, 303, 41, 41,
	41, 41, 103, 103, 592, 592, 592, 573, 547, 592,
	592, 573, 103, 103, 592, 103, 303, -1000, 11, -1000,
	-1000, -1000, 97, -1000, 25, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, 367, -1000, -1000,
	-1000, -1000, 19, 19, -11, -1000, -1000, -1000, -1000, -1000,
	-1000, 14, 117, -1000, -1000, 18, -1000, 514, -1000, -1000,
	-1000, 19, -1000, -1000, -1000, -1000, -1000,
}

var yyPgo = [...]int{
	0, 267, 7, 265, 2, 264, 262, 164, 261, 257,
	115, 253, 181, 8, 252, 4, 5, 251, 250, 0,
	23, 248, 6, 247, 246, 245, 10, 64, 244, 239,
	1, 231, 230, 9, 217, 21, 214, 213, 205, 201,
	198, 196, 189, 188, 206, 3, 180, 165, 127,
}

var yyR1 = [...]int{
	0, 47, 47, 47, 47, 47, 47, 47, 30, 30,
	30, 30, 30, 30, 30, 30, 30, 30, 30, 30,
	25, 25, 25, 25, 26, 26, 28, 28, 28, 28,
	28, 28, 28, 28, 28, 28, 28, 28, 28, 28,
	28, 28, 27, 29, 29, 39, 39, 34, 34, 34,
	34, 15, 15, 15, 15, 14, 14, 14, 4, 4,
	31, 33, 33, 32, 32, 32, 40, 38, 38, 38,
	24, 24, 24, 9, 9, 36, 42, 42, 42, 42,
	42, 43, 44, 44, 44, 35, 35, 35, 1, 1,
	1, 2, 2, 2, 2, 12, 12, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 10,
	10, 10, 10, 11, 11, 11, 13, 13, 13, 13,
	48, 18, 18, 18, 18, 17, 17, 17, 17, 17,
	21, 21, 21, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 6, 6, 6, 6, 6,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	6, 6, 8, 8, 5, 5, 5, 5, 37, 20,
	22, 22, 23, 23, 19, 45, 41, 46, 46, 16,
	16,
}

var yyR2 = [...]int{
	0, 2, 2, 2, 2, 2, 2, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	3, 3, 2, 2, 2, 2, 4, 4, 4, 4,
	4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
	4, 4, 1, 0, 1, 3, 3, 1, 1, 3,
	3, 3, 4, 2, 1, 3, 1, 2, 1, 1,
	2, 3, 2, 3, 1, 2, 3, 3, 4, 3,
	3, 5, 3, 1, 1, 4, 6, 6, 5, 4,
	3, 2, 2, 1, 1, 3, 4, 2, 3, 1,
	2, 3, 3, 2, 1, 2, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 3,
	4, 2, 0, 3, 1, 2, 3, 3, 2, 1,
	2, 0, 3, 2, 1, 1, 3, 1, 3, 4,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	2, 2, 1, 1, 1, 1, 1, 0, 1, 0,
	1,
}

var yyChk = [...]int{
	-1000, -47, 75, 76, 77, 78, 2, 10, -12, -7,
	-10, 47, 48, 62, 49, 50, 51, 12, 32, 33,
	36, 52, 16, 53, 66, 54, 55, 56, 57, 58,
	68, 71, 72, 13, -48, -12, 10, -30, -25, -28,
	-31, -36, -37, -38, -40, -41, -42, -43, -44, -24,
	-3, 12, 17, 15, 23, -8, -7, -35, 47, 48,
	49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
	26, 42, 13, -44, -10, -11, 18, -13, 12, 2,
	-18, 2, 26, 44, 27, 28, 30, 31, 32, 33,
	34, 35, 36, 37, 38, 39, 41, 42, 66, 43,
	14, -26, -33, 2, 62, 68, 15, -33, -30, -30,
	-35, -1, 18, -2, 12, 2, 18, 7, 2, 4,
	2, 22, -27, -34, -29, -39, 61, -27, -27, -27,
	-27, -27, -27, -27, -27, -27, -27, -27, -27, -27,
	-27, -27, -45, 42, 2, 9, -23, -9, 2, -20,
	-22, 71, 72, 17, 26, 42, -45, 2, -33, -26,
	-15, 15, 2, -15, -32, 20, -30, 20, 18, 7,
	2, -5, 2, 4, 39, 29, 40, 18, -13, 23,
	2, -17, 5, -21, 12, -20, -22, -30, 65, 67,
	63, 64, -30, -30, -30, -30, -30, -30, -30, -30,
	-30, -30, -30, -30, -30, -30, -30, -45, 15, -20,
	-20, 19, 6, 2, -14, 20, -4, -6, 2, 47,
	61, 48, 62, 49, 50, 51, 63, 64, 12, 65,
	32, 33, 36, 52, 16, 53, 66, 67, 54, 55,
	56, 57, 58, 71, 72, 44, 20, 7, 18, -2,
	23, 2, 24, 24, -22, -15, -15, -16, -15, -16,
	20, -46, -45, 2, 20, 7, 2, -30, -19, 17,
	-19, 24, 19, 2, 20, -4, -19,
}

var yyDef = [...]int{
	0, -2, 122, 122, 0, 0, 7, 6, 1, 122,
	96, 97, 98, 99, 100, 101, 102, 103, 104, 105,
	106, 107, 108, 109, 110, 111, 112, 113, 114, 115,
	116, 117, 118, 0, 2, -2, 3, 4, 8, 9,
	10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
	0, 103, 188, 0, 196, 0, 83, 84, -2, -2,
	-2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
	182, 183, 0, 5, 95, 0, 121, 124, 0, 129,
	130, 134, 43, 43, 43, 43, 43, 43, 43, 43,
	43, 43, 43, 43, 43, 43, 43, 43, 0, 0,
	0, 0, 22, 23, 0, 0, 0, 60, 0, 81,
	82, 0, 87, 89, 0, 94, 119, 0, 125, 0,
	128, 133, 0, 42, 47, 48, 44, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 67, 0, 69, 195, 70, 0, 72, 192,
	193, 73, 74, 189, 0, 0, 0, 80, 20, 21,
	24, 0, 54, 25, 0, 62, 64, 66, 85, 0,
	90, 0, 93, 184, 185, 186, 187, 120, 123, 126,
	127, 132, 135, 137, 140, 141, 142, 26, 0, 0,
	-2, -2, 27, 28, 29, 30, 31, 32, 33, 34,
	35, 36, 37, 38, 39, 40, 41, 68, 0, 190,
	191, 75, -2, 79, 0, 53, 56, 58, 59, 155,
	156, 157, 158, 159, 160, 161, 162, 163, 164, 165,
	166, 167, 168, 169, 170, 171, 172, 173, 174, 175,
	176, 177, 178, 179, 180, 181, 61, 65, 86, 88,
	91, 92, 0, 0, 0, 45, 46, 49, 200, 50,
	71, 0, -2, 78, 51, 0, 57, 63, 136, 194,
	138, 0, 76, 77, 52, 55, 139,
}

var yyTok1 = [...]int{
	1,
}

var yyTok2 = [...]int{
	2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
	12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
	22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
	32, 33, 34, 35, 36, 37, 38, 39, 40, 41,
	42, 43, 44, 45, 46, 47, 48, 49, 50, 51,
	52, 53, 54, 55, 56, 57, 58, 59, 60, 61,
	62, 63, 64, 65, 66, 67, 68, 69, 70, 71,
	72, 73, 74, 75, 76, 77, 78, 79,
}

var yyTok3 = [...]int{
	0,
}

var yyErrorMessages = [...]struct {
	state int
	token int
	msg   string
}{}

//line yaccpar:1

/*	parser for yacc output	*/

var (
	yyDebug        = 0
	yyErrorVerbose = false
)

type yyLexer interface {
	Lex(lval *yySymType) int
	Error(s string)
}

type yyParser interface {
	Parse(yyLexer) int
	Lookahead() int
}

type yyParserImpl struct {
	lval  yySymType
	stack [yyInitialStackSize]yySymType
	char  int
}

func (p *yyParserImpl) Lookahead() int {
	return p.char
}

func yyNewParser() yyParser {
	return &yyParserImpl{}
}

const yyFlag = -1000

func yyTokname(c int) string {
	if c >= 1 && c-1 < len(yyToknames) {
		if yyToknames[c-1] != "" {
			return yyToknames[c-1]
		}
	}
	return __yyfmt__.Sprintf("tok-%v", c)
}

func yyStatname(s int) string {
	if s >= 0 && s < len(yyStatenames) {
		if yyStatenames[s] != "" {
			return yyStatenames[s]
		}
	}
	return __yyfmt__.Sprintf("state-%v", s)
}

func yyErrorMessage(state, lookAhead int) string {
	const TOKSTART = 4

	if !yyErrorVerbose {
		return "syntax error"
	}

	for _, e := range yyErrorMessages {
		if e.state == state && e.token == lookAhead {
			return "syntax error: " + e.msg
		}
	}

	res := "syntax error: unexpected " + yyTokname(lookAhead)

	// To match Bison, suggest at most four expected tokens.
	expected := make([]int, 0, 4)

	// Look for shiftable tokens.
	base := yyPact[state]
	for tok := TOKSTART; tok-1 < len(yyToknames); tok++ {
		if n := base + tok; n >= 0 && n < yyLast && yyChk[yyAct[n]] == tok {
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}
	}

	if yyDef[state] == -2 {
		i := 0
		for yyExca[i] != -1 || yyExca[i+1] != state {
			i += 2
		}

		// Look for tokens that we accept or reduce.
		for i += 2; yyExca[i] >= 0; i += 2 {
			tok := yyExca[i]
			if tok < TOKSTART || yyExca[i+1] == 0 {
				continue
			}
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}

		// If the default action is to accept or reduce, give up.
		if yyExca[i+1] != 0 {
			return res
		}
	}

	for i, tok := range expected {
		if i == 0 {
			res += ", expecting "
		} else {
			res += " or "
		}
		res += yyTokname(tok)
	}
	return res
}

func yylex1(lex yyLexer, lval *yySymType) (char, token int) {
	token = 0
	char = lex.Lex(lval)
	if char <= 0 {
		token = yyTok1[0]
		goto out
	}
	if char < len(yyTok1) {
		token = yyTok1[char]
		goto out
	}
	if char >= yyPrivate {
		if char < yyPrivate+len(yyTok2) {
			token = yyTok2[char-yyPrivate]
			goto out
		}
	}
	for i := 0; i < len(yyTok3); i += 2 {
		token = yyTok3[i+0]
		if token == char {
			token = yyTok3[i+1]
			goto out
		}
	}

out:
	if token == 0 {
		token = yyTok2[1] /* unknown char */
	}
	if yyDebug >= 3 {
		__yyfmt__.Printf("lex %s(%d)\n", yyTokname(token), uint(char))
	}
	return char, token
}

func yyParse(yylex yyLexer) int {
	return yyNewParser().Parse(yylex)
}

func (yyrcvr *yyParserImpl) Parse(yylex yyLexer) int {
	var yyn int
	var yyVAL yySymType
	var yyDollar []yySymType
	_ = yyDollar // silence set and not used
	yyS := yyrcvr.stack[:]

	Nerrs := 0   /* number of errors */
	Errflag := 0 /* error recovery flag */
	yystate := 0
	yyrcvr.char = -1
	yytoken := -1 // yyrcvr.char translated into internal numbering
	defer func() {
		// Make sure we report no lookahead when not parsing.
		yystate = -1
		yyrcvr.char = -1
		yytoken = -1
	}()
	yyp := -1
	goto yystack

ret0:
	return 0

ret1:
	return 1

yystack:
	/* put a state and value onto the stack */
	if yyDebug >= 4 {
		__yyfmt__.Printf("char %v in %v\n", yyTokname(yytoken), yyStatname(yystate))
	}

	yyp++
	if yyp >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyS[yyp] = yyVAL
	yyS[yyp].yys = yystate

yynewstate:
	yyn = yyPact[yystate]
	if yyn <= yyFlag {
		goto yydefault /* simple state */
	}
	if yyrcvr.char < 0 {
		yyrcvr.char, yytoken = yylex1(yylex, &yyrcvr.lval)
	}
	yyn += yytoken
	if yyn < 0 || yyn >= yyLast {
		goto yydefault
	}
	yyn = yyAct[yyn]
	if yyChk[yyn] == yytoken { /* valid shift */
		yyrcvr.char = -1
		yytoken = -1
		yyVAL = yyrcvr.lval
		yystate = yyn
		if Errflag > 0 {
			Errflag--
		}
		goto yystack
	}

yydefault:
	/* default state action */
	yyn = yyDef[yystate]
	if yyn == -2 {
		if yyrcvr.char < 0 {
			yyrcvr.char, yytoken = yylex1(yylex, &yyrcvr.lval)
		}

		/* look through exception table */
		xi := 0
		for {
			if yyExca[xi+0] == -1 && yyExca[xi+1] == yystate {
				break
			}
			xi += 2
		}
		for xi += 2; ; xi += 2 {
			yyn = yyExca[xi+0]
			if yyn < 0 || yyn == yytoken {
				break
			}
		}
		yyn = yyExca[xi+1]
		if yyn < 0 {
			goto ret0
		}
	}
	if yyn == 0 {
		/* error ... attempt to resume parsing */
		switch Errflag {
		case 0: /* brand new error */
			yylex.Error(yyErrorMessage(yystate, yytoken))
			Nerrs++
			if yyDebug >= 1 {
				__yyfmt__.Printf("%s", yyStatname(yystate))
				__yyfmt__.Printf(" saw %s\n", yyTokname(yytoken))
			}
			fallthrough

		case 1, 2: /* incompletely recovered error ... try again */
			Errflag = 3

			/* find a state where "error" is a legal shift action */
			for yyp >= 0 {
				yyn = yyPact[yyS[yyp].yys] + yyErrCode
				if yyn >= 0 && yyn < yyLast {
					yystate = yyAct[yyn] /* simulate a shift of "error" */
					if yyChk[yystate] == yyErrCode {
						goto yystack
					}
				}

				/* the current p has no shift on "error", pop stack */
				if yyDebug >= 2 {
					__yyfmt__.Printf("error recovery pops state %d\n", yyS[yyp].yys)
				}
				yyp--
			}
			/* there is no state on the stack with an error shift ... abort */
			goto ret1

		case 3: /* no shift yet; clobber input char */
			if yyDebug >= 2 {
				__yyfmt__.Printf("error recovery discards %s\n", yyTokname(yytoken))
			}
			if yytoken == yyEofCode {
				goto ret1
			}
			yyrcvr.char = -1
			yytoken = -1
			goto yynewstate /* try again in the same state */
		}
	}

	/* reduction by production yyn */
	if yyDebug >= 2 {
		__yyfmt__.Printf("reduce %v in:\n\t%v\n", yyn, yyStatname(yystate))
	}

	yynt := yyn
	yypt := yyp
	_ = yypt // guard against "declared and not used"

	yyp -= yyR2[yyn]
	// yyp is now the index of $0. Perform the default action. Iff the
	// reduced production is ε, $1 is possibly out of range.
	if yyp+1 >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyVAL = yyS[yyp+1]

	/* consult goto table to find next state */
	yyn = yyR1[yyn]
	yyg := yyPgo[yyn]
	yyj := yyg + yyS[yyp].yys + 1

	if yyj >= yyLast {
		yystate = yyAct[yyg]
	} else {
		yystate = yyAct[yyj]
		if yyChk[yystate] != -yyn {
			yystate = yyAct[yyg]
		}
	}
	// dummy call; replaced with literal code
	switch yynt {

	case 1:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:174
		{
			yylex.(*parser).generatedParserResult = yyDollar[2].labels
		}
	case 3:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:177
		{
			yylex.(*parser).addParseErrf(PositionRange{}, "no expression found in input")
		}
	case 4:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:179
		{
			yylex.(*parser).generatedParserResult = yyDollar[2].node
		}
	case 5:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:181
		{
			yylex.(*parser).generatedParserResult = yyDollar[2].node
		}
	case 7:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:184
		{
			yylex.(*parser).unexpected("", "")
		}
	case 20:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:207
		{
			yyVAL.node = yylex.(*parser).newAggregateExpr(yyDollar[1].item, yyDollar[2].node, yyDollar[3].node)
		}
	case 21:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:209
		{
			yyVAL.node = yylex.(*parser).newAggregateExpr(yyDollar[1].item, yyDollar[3].node, yyDollar[2].node)
		}
	case 22:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:211
		{
			yyVAL.node = yylex.(*parser).newAggregateExpr(yyDollar[1].item, &AggregateExpr{}, yyDollar[2].node)
		}
	case 23:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:213
		{
			yylex.(*parser).unexpected("aggregation", "")
			yyVAL.node = yylex.(*parser).newAggregateExpr(yyDollar[1].item, &AggregateExpr{}, Expressions{})
		}
	case 24:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:221
		{
			yyVAL.node = &AggregateExpr{
				Grouping: yyDollar[2].strings,
			}
		}
	case 25:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:227
		{
			yyVAL.node = &AggregateExpr{
				Grouping: yyDollar[2].strings,
				Without:  true,
			}
		}
	case 26:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:240
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 27:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:241
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 28:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:242
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 29:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:243
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 30:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:244
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 31:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:245
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 32:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:246
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 33:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:247
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 34:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:248
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 35:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:249
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 36:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:250
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 37:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:251
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 38:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:252
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 39:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:253
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 40:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:254
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 41:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:255
		{
			yyVAL.node = yylex.(*parser).newBinaryExpression(yyDollar[1].node, yyDollar[2].item, yyDollar[3].node, yyDollar[4].node)
		}
	case 43:
		yyDollar = yyS[yypt-0 : yypt+1]
//line promql/parser/generated_parser.y:263
		{
			yyVAL.node = &BinaryExpr{
				VectorMatching: &VectorMatching{Card: CardOneToOne},
			}
		}
	case 44:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:268
		{
			yyVAL.node = &BinaryExpr{
				VectorMatching: &VectorMatching{Card: CardOneToOne},
				ReturnBool:     true,
			}
		}
	case 45:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:276
		{
			yyVAL.node = yyDollar[1].node
			yyVAL.node.(*BinaryExpr).VectorMatching.MatchingLabels = yyDollar[3].strings
		}
	case 46:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:281
		{
			yyVAL.node = yyDollar[1].node
			yyVAL.node.(*BinaryExpr).VectorMatching.MatchingLabels = yyDollar[3].strings
			yyVAL.node.(*BinaryExpr).VectorMatching.On = true
		}
	case 49:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:291
		{
			yyVAL.node = yyDollar[1].node
			yyVAL.node.(*BinaryExpr).VectorMatching.Card = CardManyToOne
			yyVAL.node.(*BinaryExpr).VectorMatching.Include = yyDollar[3].strings
		}
	case 50:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:297
		{
			yyVAL.node = yyDollar[1].node
			yyVAL.node.(*BinaryExpr).VectorMatching.Card = CardOneToMany
			yyVAL.node.(*BinaryExpr).VectorMatching.Include = yyDollar[3].strings
		}
	case 51:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:306
		{
			yyVAL.strings = yyDollar[2].strings
		}
	case 52:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:308
		{
			yyVAL.strings = yyDollar[2].strings
		}
	case 53:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:310
		{
			yyVAL.strings = []string{}
		}
	case 54:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:312
		{
			yylex.(*parser).unexpected("grouping opts", "\"(\"")
			yyVAL.strings = nil
		}
	case 55:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:318
		{
			yyVAL.strings = append(yyDollar[1].strings, yyDollar[3].item.Val)
		}
	case 56:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:320
		{
			yyVAL.strings = []string{yyDollar[1].item.Val}
		}
	case 57:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:322
		{
			yylex.(*parser).unexpected("grouping opts", "\",\" or \")\"")
			yyVAL.strings = yyDollar[1].strings
		}
	case 58:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:326
		{
			if !isLabel(yyDollar[1].item.Val) {
				yylex.(*parser).unexpected("grouping opts", "label")
			}
			yyVAL.item = yyDollar[1].item
		}
	case 59:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:333
		{
			yylex.(*parser).unexpected("grouping opts", "label")
			yyVAL.item = Item{}
		}
	case 60:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:341
		{
			fn, exist := getFunction(yyDollar[1].item.Val)
			if !exist {
				yylex.(*parser).addParseErrf(yyDollar[1].item.PositionRange(), "unknown function with name %q", yyDollar[1].item.Val)
			}
			yyVAL.node = &Call{
				Func: fn,
				Args: yyDollar[2].node.(Expressions),
				PosRange: PositionRange{
					Start: yyDollar[1].item.Pos,
					End:   yylex.(*parser).lastClosing,
				},
			}
		}
	case 61:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:358
		{
			yyVAL.node = yyDollar[2].node
		}
	case 62:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:360
		{
			yyVAL.node = Expressions{}
		}
	case 63:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:364
		{
			yyVAL.node = append(yyDollar[1].node.(Expressions), yyDollar[3].node.(Expr))
		}
	case 64:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:366
		{
			yyVAL.node = Expressions{yyDollar[1].node.(Expr)}
		}
	case 65:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:368
		{
			yylex.(*parser).addParseErrf(yyDollar[2].item.PositionRange(), "trailing commas not allowed in function call args")
			yyVAL.node = yyDollar[1].node
		}
	case 66:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:379
		{
			yyVAL.node = &ParenExpr{Expr: yyDollar[2].node.(Expr), PosRange: mergeRanges(&yyDollar[1].item, &yyDollar[3].item)}
		}
	case 67:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:387
		{
			yylex.(*parser).addOffset(yyDollar[1].node, yyDollar[3].duration)
			yyVAL.node = yyDollar[1].node
		}
	case 68:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:392
		{
			yylex.(*parser).addOffset(yyDollar[1].node, -yyDollar[4].duration)
			yyVAL.node = yyDollar[1].node
		}
	case 69:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:397
		{
			yylex.(*parser).unexpected("offset", "duration")
			yyVAL.node = yyDollar[1].node
		}
	case 70:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:404
		{
			yylex.(*parser).setTimestamp(yyDollar[1].node, yyDollar[3].float)
			yyVAL.node = yyDollar[1].node
		}
	case 71:
		yyDollar = yyS[yypt-5 : yypt+1]
//line promql/parser/generated_parser.y:409
		{
			yylex.(*parser).setAtModifierPreprocessor(yyDollar[1].node, yyDollar[3].item)
			yyVAL.node = yyDollar[1].node
		}
	case 72:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:414
		{
			yylex.(*parser).unexpected("@", "timestamp")
			yyVAL.node = yyDollar[1].node
		}
	case 75:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:424
		{
			var errMsg string
			vs, ok := yyDollar[1].node.(*VectorSelector)
			if !ok {
				errMsg = "ranges only allowed for vector selectors"
			} else if vs.OriginalOffset != 0 {
				errMsg = "no offset modifiers allowed before range"
			} else if vs.Timestamp != nil {
				errMsg = "no @ modifiers allowed before range"
			}

			if errMsg != "" {
				errRange := mergeRanges(&yyDollar[2].item, &yyDollar[4].item)
				yylex.(*parser).addParseErrf(errRange, errMsg)
			}

			yyVAL.node = &MatrixSelector{
				VectorSelector: yyDollar[1].node.(Expr),
				Range:          yyDollar[3].duration,
				EndPos:         yylex.(*parser).lastClosing,
			}
		}
	case 76:
		yyDollar = yyS[yypt-6 : yypt+1]
//line promql/parser/generated_parser.y:449
		{
			yyVAL.node = &SubqueryExpr{
				Expr:  yyDollar[1].node.(Expr),
				Range: yyDollar[3].duration,
				Step:  yyDollar[5].duration,

				EndPos: yyDollar[6].item.Pos + 1,
			}
		}
	case 77:
		yyDollar = yyS[yypt-6 : yypt+1]
//line promql/parser/generated_parser.y:459
		{
			yylex.(*parser).unexpected("subquery selector", "\"]\"")
			yyVAL.node = yyDollar[1].node
		}
	case 78:
		yyDollar = yyS[yypt-5 : yypt+1]
//line promql/parser/generated_parser.y:461
		{
			yylex.(*parser).unexpected("subquery selector", "duration or \"]\"")
			yyVAL.node = yyDollar[1].node
		}
	case 79:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:463
		{
			yylex.(*parser).unexpected("subquery or range", "\":\" or \"]\"")
			yyVAL.node = yyDollar[1].node
		}
	case 80:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:465
		{
			yylex.(*parser).unexpected("subquery selector", "duration")
			yyVAL.node = yyDollar[1].node
		}
	case 81:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:475
		{
			if nl, ok := yyDollar[2].node.(*NumberLiteral); ok {
				if yyDollar[1].item.Typ == SUB {
					nl.Val *= -1
				}
				nl.PosRange.Start = yyDollar[1].item.Pos
				yyVAL.node = nl
			} else {
				yyVAL.node = &UnaryExpr{Op: yyDollar[1].item.Typ, Expr: yyDollar[2].node.(Expr), StartPos: yyDollar[1].item.Pos}
			}
		}
	case 82:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:493
		{
			vs := yyDollar[2].node.(*VectorSelector)
			vs.PosRange = mergeRanges(&yyDollar[1].item, vs)
			vs.Name = yyDollar[1].item.Val
			yylex.(*parser).assembleVectorSelector(vs)
			yyVAL.node = vs
		}
	case 83:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:501
		{
			vs := &VectorSelector{
				Name:          yyDollar[1].item.Val,
				LabelMatchers: []*labels.Matcher{},
				PosRange:      yyDollar[1].item.PositionRange(),
			}
			yylex.(*parser).assembleVectorSelector(vs)
			yyVAL.node = vs
		}
	case 84:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:511
		{
			vs := yyDollar[1].node.(*VectorSelector)
			yylex.(*parser).assembleVectorSelector(vs)
			yyVAL.node = vs
		}
	case 85:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:519
		{
			yyVAL.node = &VectorSelector{
				LabelMatchers: yyDollar[2].matchers,
				PosRange:      mergeRanges(&yyDollar[1].item, &yyDollar[3].item),
			}
		}
	case 86:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:526
		{
			yyVAL.node = &VectorSelector{
				LabelMatchers: yyDollar[2].matchers,
				PosRange:      mergeRanges(&yyDollar[1].item, &yyDollar[4].item),
			}
		}
	case 87:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:533
		{
			yyVAL.node = &VectorSelector{
				LabelMatchers: []*labels.Matcher{},
				PosRange:      mergeRanges(&yyDollar[1].item, &yyDollar[2].item),
			}
		}
	case 88:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:542
		{
			if yyDollar[1].matchers != nil {
				yyVAL.matchers = append(yyDollar[1].matchers, yyDollar[3].matcher)
			} else {
				yyVAL.matchers = yyDollar[1].matchers
			}
		}
	case 89:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:550
		{
			yyVAL.matchers = []*labels.Matcher{yyDollar[1].matcher}
		}
	case 90:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:552
		{
			yylex.(*parser).unexpected("label matching", "\",\" or \"}\"")
			yyVAL.matchers = yyDollar[1].matchers
		}
	case 91:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:556
		{
			yyVAL.matcher = yylex.(*parser).newLabelMatcher(yyDollar[1].item, yyDollar[2].item, yyDollar[3].item)
		}
	case 92:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:558
		{
			yylex.(*parser).unexpected("label matching", "string")
			yyVAL.matcher = nil
		}
	case 93:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:560
		{
			yylex.(*parser).unexpected("label matching", "label matching operator")
			yyVAL.matcher = nil
		}
	case 94:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:562
		{
			yylex.(*parser).unexpected("label matching", "identifier or \"}\"")
			yyVAL.matcher = nil
		}
	case 95:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:570
		{
			yyVAL.labels = append(yyDollar[2].labels, labels.Label{Name: labels.MetricName, Value: yyDollar[1].item.Val})
			sort.Sort(yyVAL.labels)
		}
	case 96:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:572
		{
			yyVAL.labels = yyDollar[1].labels
		}
	case 119:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:579
		{
			yyVAL.labels = labels.New(yyDollar[2].labels...)
		}
	case 120:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:581
		{
			yyVAL.labels = labels.New(yyDollar[2].labels...)
		}
	case 121:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:583
		{
			yyVAL.labels = labels.New()
		}
	case 122:
		yyDollar = yyS[yypt-0 : yypt+1]
//line promql/parser/generated_parser.y:585
		{
			yyVAL.labels = labels.New()
		}
	case 123:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:589
		{
			yyVAL.labels = append(yyDollar[1].labels, yyDollar[3].label)
		}
	case 124:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:591
		{
			yyVAL.labels = []labels.Label{yyDollar[1].label}
		}
	case 125:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:593
		{
			yylex.(*parser).unexpected("label set", "\",\" or \"}\"")
			yyVAL.labels = yyDollar[1].labels
		}
	case 126:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:598
		{
			yyVAL.label = labels.Label{Name: yyDollar[1].item.Val, Value: yylex.(*parser).unquoteString(yyDollar[3].item.Val)}
		}
	case 127:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:600
		{
			yylex.(*parser).unexpected("label set", "string")
			yyVAL.label = labels.Label{}
		}
	case 128:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:602
		{
			yylex.(*parser).unexpected("label set", "\"=\"")
			yyVAL.label = labels.Label{}
		}
	case 129:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:604
		{
			yylex.(*parser).unexpected("label set", "identifier or \"}\"")
			yyVAL.label = labels.Label{}
		}
	case 130:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:612
		{
			yylex.(*parser).generatedParserResult = &seriesDescription{
				labels: yyDollar[1].labels,
				values: yyDollar[2].series,
			}
		}
	case 131:
		yyDollar = yyS[yypt-0 : yypt+1]
//line promql/parser/generated_parser.y:621
		{
			yyVAL.series = []SequenceValue{}
		}
	case 132:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:623
		{
			yyVAL.series = append(yyDollar[1].series, yyDollar[3].series...)
		}
	case 133:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:625
		{
			yyVAL.series = yyDollar[1].series
		}
	case 134:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:627
		{
			yylex.(*parser).unexpected("series values", "")
			yyVAL.series = nil
		}
	case 135:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:631
		{
			yyVAL.series = []SequenceValue{{Omitted: true}}
		}
	case 136:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:633
		{
			yyVAL.series = []SequenceValue{}
			for i := uint64(0); i < yyDollar[3].uint; i++ {
				yyVAL.series = append(yyVAL.series, SequenceValue{Omitted: true})
			}
		}
	case 137:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:640
		{
			yyVAL.series = []SequenceValue{{Value: yyDollar[1].float}}
		}
	case 138:
		yyDollar = yyS[yypt-3 : yypt+1]
//line promql/parser/generated_parser.y:642
		{
			yyVAL.series = []SequenceValue{}
			for i := uint64(0); i <= yyDollar[3].uint; i++ {
				yyVAL.series = append(yyVAL.series, SequenceValue{Value: yyDollar[1].float})
			}
		}
	case 139:
		yyDollar = yyS[yypt-4 : yypt+1]
//line promql/parser/generated_parser.y:649
		{
			yyVAL.series = []SequenceValue{}
			for i := uint64(0); i <= yyDollar[4].uint; i++ {
				yyVAL.series = append(yyVAL.series, SequenceValue{Value: yyDollar[1].float})
				yyDollar[1].float += yyDollar[2].float
			}
		}
	case 140:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:659
		{
			if yyDollar[1].item.Val != "stale" {
				yylex.(*parser).unexpected("series values", "number or \"stale\"")
			}
			yyVAL.float = math.Float64frombits(value.StaleNaN)
		}
	case 188:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:690
		{
			yyVAL.node = &NumberLiteral{
				Val:      yylex.(*parser).number(yyDollar[1].item.Val),
				PosRange: yyDollar[1].item.PositionRange(),
			}
		}
	case 189:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:698
		{
			yyVAL.float = yylex.(*parser).number(yyDollar[1].item.Val)
		}
	case 190:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:700
		{
			yyVAL.float = yyDollar[2].float
		}
	case 191:
		yyDollar = yyS[yypt-2 : yypt+1]
//line promql/parser/generated_parser.y:701
		{
			yyVAL.float = -yyDollar[2].float
		}
	case 194:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:707
		{
			var err error
			yyVAL.uint, err = strconv.ParseUint(yyDollar[1].item.Val, 10, 64)
			if err != nil {
				yylex.(*parser).addParseErrf(yyDollar[1].item.PositionRange(), "invalid repetition in series values: %s", err)
			}
		}
	case 195:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:717
		{
			var err error
			yyVAL.duration, err = parseDuration(yyDollar[1].item.Val)
			if err != nil {
				yylex.(*parser).addParseErr(yyDollar[1].item.PositionRange(), err)
			}
		}
	case 196:
		yyDollar = yyS[yypt-1 : yypt+1]
//line promql/parser/generated_parser.y:728
		{
			yyVAL.node = &StringLiteral{
				Val:      yylex.(*parser).unquoteString(yyDollar[1].item.Val),
				PosRange: yyDollar[1].item.PositionRange(),
			}
		}
	case 197:
		yyDollar = yyS[yypt-0 : yypt+1]
//line promql/parser/generated_parser.y:741
		{
			yyVAL.duration = 0
		}
	case 199:
		yyDollar = yyS[yypt-0 : yypt+1]
//line promql/parser/generated_parser.y:745
		{
			yyVAL.strings = nil
		}
	}
	goto yystack /* stack new state and value */
}
