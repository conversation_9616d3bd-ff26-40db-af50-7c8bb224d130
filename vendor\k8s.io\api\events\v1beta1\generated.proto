/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.events.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1beta1";

// Event is a report of an event somewhere in the cluster. It generally denotes some state change in the system.
// Events have a limited retention time and triggers and messages may evolve
// with time.  Event consumers should not rely on the timing of an event
// with a given Reason reflecting a consistent underlying trigger, or the
// continued existence of events with that Reason.  Events should be
// treated as informative, best-effort, supplemental data.
message Event {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // eventTime is the time when this Event was first observed. It is required.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.MicroTime eventTime = 2;

  // series is data about the Event series this event represents or nil if it's a singleton Event.
  // +optional
  optional EventSeries series = 3;

  // reportingController is the name of the controller that emitted this Event, e.g. `kubernetes.io/kubelet`.
  // This field cannot be empty for new Events.
  // +optional
  optional string reportingController = 4;

  // reportingInstance is the ID of the controller instance, e.g. `kubelet-xyzf`.
  // This field cannot be empty for new Events and it can have at most 128 characters.
  // +optional
  optional string reportingInstance = 5;

  // action is what action was taken/failed regarding to the regarding object. It is machine-readable.
  // This field can have at most 128 characters.
  // +optional
  optional string action = 6;

  // reason is why the action was taken. It is human-readable.
  // This field can have at most 128 characters.
  // +optional
  optional string reason = 7;

  // regarding contains the object this Event is about. In most cases it's an Object reporting controller
  // implements, e.g. ReplicaSetController implements ReplicaSets and this event is emitted because
  // it acts on some changes in a ReplicaSet object.
  // +optional
  optional k8s.io.api.core.v1.ObjectReference regarding = 8;

  // related is the optional secondary object for more complex actions. E.g. when regarding object triggers
  // a creation or deletion of related object.
  // +optional
  optional k8s.io.api.core.v1.ObjectReference related = 9;

  // note is a human-readable description of the status of this operation.
  // Maximal length of the note is 1kB, but libraries should be prepared to
  // handle values up to 64kB.
  // +optional
  optional string note = 10;

  // type is the type of this event (Normal, Warning), new types could be added in the future.
  // It is machine-readable.
  // +optional
  optional string type = 11;

  // deprecatedSource is the deprecated field assuring backward compatibility with core.v1 Event type.
  // +optional
  optional k8s.io.api.core.v1.EventSource deprecatedSource = 12;

  // deprecatedFirstTimestamp is the deprecated field assuring backward compatibility with core.v1 Event type.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time deprecatedFirstTimestamp = 13;

  // deprecatedLastTimestamp is the deprecated field assuring backward compatibility with core.v1 Event type.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time deprecatedLastTimestamp = 14;

  // deprecatedCount is the deprecated field assuring backward compatibility with core.v1 Event type.
  // +optional
  optional int32 deprecatedCount = 15;
}

// EventList is a list of Event objects.
message EventList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is a list of schema objects.
  repeated Event items = 2;
}

// EventSeries contain information on series of events, i.e. thing that was/is happening
// continuously for some time.
message EventSeries {
  // count is the number of occurrences in this series up to the last heartbeat time.
  optional int32 count = 1;

  // lastObservedTime is the time when last Event from the series was seen before last heartbeat.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.MicroTime lastObservedTime = 2;
}

