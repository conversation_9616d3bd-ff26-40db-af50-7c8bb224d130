/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.apiserverinternal.v1alpha1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1alpha1";

// An API server instance reports the version it can decode and the version it
// encodes objects to when persisting objects in the backend.
message ServerStorageVersion {
  // The ID of the reporting API server.
  optional string apiServerID = 1;

  // The API server encodes the object to this version when persisting it in
  // the backend (e.g., etcd).
  optional string encodingVersion = 2;

  // The API server can decode objects encoded in these versions.
  // The encodingVersion must be included in the decodableVersions.
  // +listType=set
  repeated string decodableVersions = 3;
}

//  Storage version of a specific resource.
message StorageVersion {
  // The name is <group>.<resource>.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec is an empty spec. It is here to comply with Kubernetes API style.
  optional StorageVersionSpec spec = 2;

  // API server instances report the version they can decode and the version they
  // encode objects to when persisting objects in the backend.
  optional StorageVersionStatus status = 3;
}

// Describes the state of the storageVersion at a certain point.
message StorageVersionCondition {
  // Type of the condition.
  // +required
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  // +required
  optional string status = 2;

  // If set, this represents the .metadata.generation that the condition was set based upon.
  // +optional
  optional int64 observedGeneration = 3;

  // Last time the condition transitioned from one status to another.
  // +required
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // The reason for the condition's last transition.
  // +required
  optional string reason = 5;

  // A human readable message indicating details about the transition.
  // +required
  optional string message = 6;
}

// A list of StorageVersions.
message StorageVersionList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items holds a list of StorageVersion
  repeated StorageVersion items = 2;
}

// StorageVersionSpec is an empty spec.
message StorageVersionSpec {
}

// API server instances report the versions they can decode and the version they
// encode objects to when persisting objects in the backend.
message StorageVersionStatus {
  // The reported versions per API server instance.
  // +optional
  // +listType=map
  // +listMapKey=apiServerID
  repeated ServerStorageVersion storageVersions = 1;

  // If all API server instances agree on the same encoding storage version,
  // then this field is set to that version. Otherwise this field is left empty.
  // API servers should finish updating its storageVersionStatus entry before
  // serving write operations, so that this field will be in sync with the reality.
  // +optional
  optional string commonEncodingVersion = 2;

  // The latest available observations of the storageVersion's state.
  // +optional
  // +listType=map
  // +listMapKey=type
  repeated StorageVersionCondition conditions = 3;
}

