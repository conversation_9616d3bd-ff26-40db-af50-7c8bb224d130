#!/bin/bash

#  This script was sourced here: https://github.com/fmahnke/shell-semver and had the following license:

#  The MIT License (MIT)
#
#  Copyright (c) 2014 <PERSON>
#
#  Permission is hereby granted, free of charge, to any person obtaining a copy
#  of this software and associated documentation files (the "Software"), to deal
#  in the Software without restriction, including without limitation the rights
#  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
#  copies of the Software, and to permit persons to whom the Software is
#  furnished to do so, subject to the following conditions:
#
#  The above copyright notice and this permission notice shall be included in all
#  copies or substantial portions of the Software.
#
#  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
#  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
#  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
#  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
#  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARIS<PERSON> FROM,
#  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
#  SOFTWARE.


# Increment a version string using Semantic Versioning (SemVer) terminology.

# Parse command line options.

while getopts ":Mmp" Option
do
  case "${Option}" in
    M ) major=true;;
    m ) minor=true;;
    p ) patch=true;;
    * )
        echo "unknown option: ${Option}"
        exit 1
  esac
done

shift $((OPTIND - 1))

version=$1

# Build array from version string.

a=( "${version//./ }" )

# If version string is missing or has the wrong number of members, show usage message.

if [[ ${#a[@]} -ne 3 ]]
then
  echo "usage: $(basename "$0") [-Mmp] major.minor.patch"
  exit 1
fi

# Increment version numbers as requested.

if [[ -n "${major}" ]]
then
  ((a[0]++))
  a[1]=0
  a[2]=0
fi

if [[ -n "${minor}" ]]
then
  ((a[1]++))
  a[2]=0
fi

if [[ -n "${patch}" ]]
then
  ((a[2]++))
fi

echo "${a[0]}.${a[1]}.${a[2]}"
