package compute

// Copyright (c) Microsoft and contributors.  All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"context"
	"encoding/json"
	"github.com/Azure/go-autorest/autorest"
	"github.com/Azure/go-autorest/autorest/azure"
	"github.com/Azure/go-autorest/autorest/date"
	"github.com/Azure/go-autorest/autorest/to"
	"github.com/Azure/go-autorest/tracing"
	"net/http"
)

// The package's fully qualified name.
const fqdn = "github.com/Azure/azure-sdk-for-go/services/compute/mgmt/2018-10-01/compute"

// AccessLevel enumerates the values for access level.
type AccessLevel string

const (
	// None ...
	None AccessLevel = "None"
	// Read ...
	Read AccessLevel = "Read"
)

// PossibleAccessLevelValues returns an array of possible values for the AccessLevel const type.
func PossibleAccessLevelValues() []AccessLevel {
	return []AccessLevel{None, Read}
}

// AggregatedReplicationState enumerates the values for aggregated replication state.
type AggregatedReplicationState string

const (
	// Completed ...
	Completed AggregatedReplicationState = "Completed"
	// Failed ...
	Failed AggregatedReplicationState = "Failed"
	// InProgress ...
	InProgress AggregatedReplicationState = "InProgress"
	// Unknown ...
	Unknown AggregatedReplicationState = "Unknown"
)

// PossibleAggregatedReplicationStateValues returns an array of possible values for the AggregatedReplicationState const type.
func PossibleAggregatedReplicationStateValues() []AggregatedReplicationState {
	return []AggregatedReplicationState{Completed, Failed, InProgress, Unknown}
}

// AvailabilitySetSkuTypes enumerates the values for availability set sku types.
type AvailabilitySetSkuTypes string

const (
	// Aligned ...
	Aligned AvailabilitySetSkuTypes = "Aligned"
	// Classic ...
	Classic AvailabilitySetSkuTypes = "Classic"
)

// PossibleAvailabilitySetSkuTypesValues returns an array of possible values for the AvailabilitySetSkuTypes const type.
func PossibleAvailabilitySetSkuTypesValues() []AvailabilitySetSkuTypes {
	return []AvailabilitySetSkuTypes{Aligned, Classic}
}

// CachingTypes enumerates the values for caching types.
type CachingTypes string

const (
	// CachingTypesNone ...
	CachingTypesNone CachingTypes = "None"
	// CachingTypesReadOnly ...
	CachingTypesReadOnly CachingTypes = "ReadOnly"
	// CachingTypesReadWrite ...
	CachingTypesReadWrite CachingTypes = "ReadWrite"
)

// PossibleCachingTypesValues returns an array of possible values for the CachingTypes const type.
func PossibleCachingTypesValues() []CachingTypes {
	return []CachingTypes{CachingTypesNone, CachingTypesReadOnly, CachingTypesReadWrite}
}

// ComponentNames enumerates the values for component names.
type ComponentNames string

const (
	// MicrosoftWindowsShellSetup ...
	MicrosoftWindowsShellSetup ComponentNames = "Microsoft-Windows-Shell-Setup"
)

// PossibleComponentNamesValues returns an array of possible values for the ComponentNames const type.
func PossibleComponentNamesValues() []ComponentNames {
	return []ComponentNames{MicrosoftWindowsShellSetup}
}

// ContainerServiceOrchestratorTypes enumerates the values for container service orchestrator types.
type ContainerServiceOrchestratorTypes string

const (
	// Custom ...
	Custom ContainerServiceOrchestratorTypes = "Custom"
	// DCOS ...
	DCOS ContainerServiceOrchestratorTypes = "DCOS"
	// Kubernetes ...
	Kubernetes ContainerServiceOrchestratorTypes = "Kubernetes"
	// Swarm ...
	Swarm ContainerServiceOrchestratorTypes = "Swarm"
)

// PossibleContainerServiceOrchestratorTypesValues returns an array of possible values for the ContainerServiceOrchestratorTypes const type.
func PossibleContainerServiceOrchestratorTypesValues() []ContainerServiceOrchestratorTypes {
	return []ContainerServiceOrchestratorTypes{Custom, DCOS, Kubernetes, Swarm}
}

// ContainerServiceVMSizeTypes enumerates the values for container service vm size types.
type ContainerServiceVMSizeTypes string

const (
	// StandardA0 ...
	StandardA0 ContainerServiceVMSizeTypes = "Standard_A0"
	// StandardA1 ...
	StandardA1 ContainerServiceVMSizeTypes = "Standard_A1"
	// StandardA10 ...
	StandardA10 ContainerServiceVMSizeTypes = "Standard_A10"
	// StandardA11 ...
	StandardA11 ContainerServiceVMSizeTypes = "Standard_A11"
	// StandardA2 ...
	StandardA2 ContainerServiceVMSizeTypes = "Standard_A2"
	// StandardA3 ...
	StandardA3 ContainerServiceVMSizeTypes = "Standard_A3"
	// StandardA4 ...
	StandardA4 ContainerServiceVMSizeTypes = "Standard_A4"
	// StandardA5 ...
	StandardA5 ContainerServiceVMSizeTypes = "Standard_A5"
	// StandardA6 ...
	StandardA6 ContainerServiceVMSizeTypes = "Standard_A6"
	// StandardA7 ...
	StandardA7 ContainerServiceVMSizeTypes = "Standard_A7"
	// StandardA8 ...
	StandardA8 ContainerServiceVMSizeTypes = "Standard_A8"
	// StandardA9 ...
	StandardA9 ContainerServiceVMSizeTypes = "Standard_A9"
	// StandardD1 ...
	StandardD1 ContainerServiceVMSizeTypes = "Standard_D1"
	// StandardD11 ...
	StandardD11 ContainerServiceVMSizeTypes = "Standard_D11"
	// StandardD11V2 ...
	StandardD11V2 ContainerServiceVMSizeTypes = "Standard_D11_v2"
	// StandardD12 ...
	StandardD12 ContainerServiceVMSizeTypes = "Standard_D12"
	// StandardD12V2 ...
	StandardD12V2 ContainerServiceVMSizeTypes = "Standard_D12_v2"
	// StandardD13 ...
	StandardD13 ContainerServiceVMSizeTypes = "Standard_D13"
	// StandardD13V2 ...
	StandardD13V2 ContainerServiceVMSizeTypes = "Standard_D13_v2"
	// StandardD14 ...
	StandardD14 ContainerServiceVMSizeTypes = "Standard_D14"
	// StandardD14V2 ...
	StandardD14V2 ContainerServiceVMSizeTypes = "Standard_D14_v2"
	// StandardD1V2 ...
	StandardD1V2 ContainerServiceVMSizeTypes = "Standard_D1_v2"
	// StandardD2 ...
	StandardD2 ContainerServiceVMSizeTypes = "Standard_D2"
	// StandardD2V2 ...
	StandardD2V2 ContainerServiceVMSizeTypes = "Standard_D2_v2"
	// StandardD3 ...
	StandardD3 ContainerServiceVMSizeTypes = "Standard_D3"
	// StandardD3V2 ...
	StandardD3V2 ContainerServiceVMSizeTypes = "Standard_D3_v2"
	// StandardD4 ...
	StandardD4 ContainerServiceVMSizeTypes = "Standard_D4"
	// StandardD4V2 ...
	StandardD4V2 ContainerServiceVMSizeTypes = "Standard_D4_v2"
	// StandardD5V2 ...
	StandardD5V2 ContainerServiceVMSizeTypes = "Standard_D5_v2"
	// StandardDS1 ...
	StandardDS1 ContainerServiceVMSizeTypes = "Standard_DS1"
	// StandardDS11 ...
	StandardDS11 ContainerServiceVMSizeTypes = "Standard_DS11"
	// StandardDS12 ...
	StandardDS12 ContainerServiceVMSizeTypes = "Standard_DS12"
	// StandardDS13 ...
	StandardDS13 ContainerServiceVMSizeTypes = "Standard_DS13"
	// StandardDS14 ...
	StandardDS14 ContainerServiceVMSizeTypes = "Standard_DS14"
	// StandardDS2 ...
	StandardDS2 ContainerServiceVMSizeTypes = "Standard_DS2"
	// StandardDS3 ...
	StandardDS3 ContainerServiceVMSizeTypes = "Standard_DS3"
	// StandardDS4 ...
	StandardDS4 ContainerServiceVMSizeTypes = "Standard_DS4"
	// StandardG1 ...
	StandardG1 ContainerServiceVMSizeTypes = "Standard_G1"
	// StandardG2 ...
	StandardG2 ContainerServiceVMSizeTypes = "Standard_G2"
	// StandardG3 ...
	StandardG3 ContainerServiceVMSizeTypes = "Standard_G3"
	// StandardG4 ...
	StandardG4 ContainerServiceVMSizeTypes = "Standard_G4"
	// StandardG5 ...
	StandardG5 ContainerServiceVMSizeTypes = "Standard_G5"
	// StandardGS1 ...
	StandardGS1 ContainerServiceVMSizeTypes = "Standard_GS1"
	// StandardGS2 ...
	StandardGS2 ContainerServiceVMSizeTypes = "Standard_GS2"
	// StandardGS3 ...
	StandardGS3 ContainerServiceVMSizeTypes = "Standard_GS3"
	// StandardGS4 ...
	StandardGS4 ContainerServiceVMSizeTypes = "Standard_GS4"
	// StandardGS5 ...
	StandardGS5 ContainerServiceVMSizeTypes = "Standard_GS5"
)

// PossibleContainerServiceVMSizeTypesValues returns an array of possible values for the ContainerServiceVMSizeTypes const type.
func PossibleContainerServiceVMSizeTypesValues() []ContainerServiceVMSizeTypes {
	return []ContainerServiceVMSizeTypes{StandardA0, StandardA1, StandardA10, StandardA11, StandardA2, StandardA3, StandardA4, StandardA5, StandardA6, StandardA7, StandardA8, StandardA9, StandardD1, StandardD11, StandardD11V2, StandardD12, StandardD12V2, StandardD13, StandardD13V2, StandardD14, StandardD14V2, StandardD1V2, StandardD2, StandardD2V2, StandardD3, StandardD3V2, StandardD4, StandardD4V2, StandardD5V2, StandardDS1, StandardDS11, StandardDS12, StandardDS13, StandardDS14, StandardDS2, StandardDS3, StandardDS4, StandardG1, StandardG2, StandardG3, StandardG4, StandardG5, StandardGS1, StandardGS2, StandardGS3, StandardGS4, StandardGS5}
}

// DiffDiskOptions enumerates the values for diff disk options.
type DiffDiskOptions string

const (
	// Local ...
	Local DiffDiskOptions = "Local"
)

// PossibleDiffDiskOptionsValues returns an array of possible values for the DiffDiskOptions const type.
func PossibleDiffDiskOptionsValues() []DiffDiskOptions {
	return []DiffDiskOptions{Local}
}

// DiskCreateOption enumerates the values for disk create option.
type DiskCreateOption string

const (
	// Attach ...
	Attach DiskCreateOption = "Attach"
	// Copy ...
	Copy DiskCreateOption = "Copy"
	// Empty ...
	Empty DiskCreateOption = "Empty"
	// FromImage ...
	FromImage DiskCreateOption = "FromImage"
	// Import ...
	Import DiskCreateOption = "Import"
	// Restore ...
	Restore DiskCreateOption = "Restore"
)

// PossibleDiskCreateOptionValues returns an array of possible values for the DiskCreateOption const type.
func PossibleDiskCreateOptionValues() []DiskCreateOption {
	return []DiskCreateOption{Attach, Copy, Empty, FromImage, Import, Restore}
}

// DiskCreateOptionTypes enumerates the values for disk create option types.
type DiskCreateOptionTypes string

const (
	// DiskCreateOptionTypesAttach ...
	DiskCreateOptionTypesAttach DiskCreateOptionTypes = "Attach"
	// DiskCreateOptionTypesEmpty ...
	DiskCreateOptionTypesEmpty DiskCreateOptionTypes = "Empty"
	// DiskCreateOptionTypesFromImage ...
	DiskCreateOptionTypesFromImage DiskCreateOptionTypes = "FromImage"
)

// PossibleDiskCreateOptionTypesValues returns an array of possible values for the DiskCreateOptionTypes const type.
func PossibleDiskCreateOptionTypesValues() []DiskCreateOptionTypes {
	return []DiskCreateOptionTypes{DiskCreateOptionTypesAttach, DiskCreateOptionTypesEmpty, DiskCreateOptionTypesFromImage}
}

// DiskStorageAccountTypes enumerates the values for disk storage account types.
type DiskStorageAccountTypes string

const (
	// PremiumLRS ...
	PremiumLRS DiskStorageAccountTypes = "Premium_LRS"
	// StandardLRS ...
	StandardLRS DiskStorageAccountTypes = "Standard_LRS"
	// StandardSSDLRS ...
	StandardSSDLRS DiskStorageAccountTypes = "StandardSSD_LRS"
	// UltraSSDLRS ...
	UltraSSDLRS DiskStorageAccountTypes = "UltraSSD_LRS"
)

// PossibleDiskStorageAccountTypesValues returns an array of possible values for the DiskStorageAccountTypes const type.
func PossibleDiskStorageAccountTypesValues() []DiskStorageAccountTypes {
	return []DiskStorageAccountTypes{PremiumLRS, StandardLRS, StandardSSDLRS, UltraSSDLRS}
}

// HostCaching enumerates the values for host caching.
type HostCaching string

const (
	// HostCachingNone ...
	HostCachingNone HostCaching = "None"
	// HostCachingReadOnly ...
	HostCachingReadOnly HostCaching = "ReadOnly"
	// HostCachingReadWrite ...
	HostCachingReadWrite HostCaching = "ReadWrite"
)

// PossibleHostCachingValues returns an array of possible values for the HostCaching const type.
func PossibleHostCachingValues() []HostCaching {
	return []HostCaching{HostCachingNone, HostCachingReadOnly, HostCachingReadWrite}
}

// InstanceViewTypes enumerates the values for instance view types.
type InstanceViewTypes string

const (
	// InstanceView ...
	InstanceView InstanceViewTypes = "instanceView"
)

// PossibleInstanceViewTypesValues returns an array of possible values for the InstanceViewTypes const type.
func PossibleInstanceViewTypesValues() []InstanceViewTypes {
	return []InstanceViewTypes{InstanceView}
}

// IntervalInMins enumerates the values for interval in mins.
type IntervalInMins string

const (
	// FiveMins ...
	FiveMins IntervalInMins = "FiveMins"
	// SixtyMins ...
	SixtyMins IntervalInMins = "SixtyMins"
	// ThirtyMins ...
	ThirtyMins IntervalInMins = "ThirtyMins"
	// ThreeMins ...
	ThreeMins IntervalInMins = "ThreeMins"
)

// PossibleIntervalInMinsValues returns an array of possible values for the IntervalInMins const type.
func PossibleIntervalInMinsValues() []IntervalInMins {
	return []IntervalInMins{FiveMins, SixtyMins, ThirtyMins, ThreeMins}
}

// IPVersion enumerates the values for ip version.
type IPVersion string

const (
	// IPv4 ...
	IPv4 IPVersion = "IPv4"
	// IPv6 ...
	IPv6 IPVersion = "IPv6"
)

// PossibleIPVersionValues returns an array of possible values for the IPVersion const type.
func PossibleIPVersionValues() []IPVersion {
	return []IPVersion{IPv4, IPv6}
}

// MaintenanceOperationResultCodeTypes enumerates the values for maintenance operation result code types.
type MaintenanceOperationResultCodeTypes string

const (
	// MaintenanceOperationResultCodeTypesMaintenanceAborted ...
	MaintenanceOperationResultCodeTypesMaintenanceAborted MaintenanceOperationResultCodeTypes = "MaintenanceAborted"
	// MaintenanceOperationResultCodeTypesMaintenanceCompleted ...
	MaintenanceOperationResultCodeTypesMaintenanceCompleted MaintenanceOperationResultCodeTypes = "MaintenanceCompleted"
	// MaintenanceOperationResultCodeTypesNone ...
	MaintenanceOperationResultCodeTypesNone MaintenanceOperationResultCodeTypes = "None"
	// MaintenanceOperationResultCodeTypesRetryLater ...
	MaintenanceOperationResultCodeTypesRetryLater MaintenanceOperationResultCodeTypes = "RetryLater"
)

// PossibleMaintenanceOperationResultCodeTypesValues returns an array of possible values for the MaintenanceOperationResultCodeTypes const type.
func PossibleMaintenanceOperationResultCodeTypesValues() []MaintenanceOperationResultCodeTypes {
	return []MaintenanceOperationResultCodeTypes{MaintenanceOperationResultCodeTypesMaintenanceAborted, MaintenanceOperationResultCodeTypesMaintenanceCompleted, MaintenanceOperationResultCodeTypesNone, MaintenanceOperationResultCodeTypesRetryLater}
}

// OperatingSystemStateTypes enumerates the values for operating system state types.
type OperatingSystemStateTypes string

const (
	// Generalized ...
	Generalized OperatingSystemStateTypes = "Generalized"
	// Specialized ...
	Specialized OperatingSystemStateTypes = "Specialized"
)

// PossibleOperatingSystemStateTypesValues returns an array of possible values for the OperatingSystemStateTypes const type.
func PossibleOperatingSystemStateTypesValues() []OperatingSystemStateTypes {
	return []OperatingSystemStateTypes{Generalized, Specialized}
}

// OperatingSystemTypes enumerates the values for operating system types.
type OperatingSystemTypes string

const (
	// Linux ...
	Linux OperatingSystemTypes = "Linux"
	// Windows ...
	Windows OperatingSystemTypes = "Windows"
)

// PossibleOperatingSystemTypesValues returns an array of possible values for the OperatingSystemTypes const type.
func PossibleOperatingSystemTypesValues() []OperatingSystemTypes {
	return []OperatingSystemTypes{Linux, Windows}
}

// PassNames enumerates the values for pass names.
type PassNames string

const (
	// OobeSystem ...
	OobeSystem PassNames = "OobeSystem"
)

// PossiblePassNamesValues returns an array of possible values for the PassNames const type.
func PossiblePassNamesValues() []PassNames {
	return []PassNames{OobeSystem}
}

// ProtocolTypes enumerates the values for protocol types.
type ProtocolTypes string

const (
	// HTTP ...
	HTTP ProtocolTypes = "Http"
	// HTTPS ...
	HTTPS ProtocolTypes = "Https"
)

// PossibleProtocolTypesValues returns an array of possible values for the ProtocolTypes const type.
func PossibleProtocolTypesValues() []ProtocolTypes {
	return []ProtocolTypes{HTTP, HTTPS}
}

// ProvisioningState enumerates the values for provisioning state.
type ProvisioningState string

const (
	// ProvisioningStateCreating ...
	ProvisioningStateCreating ProvisioningState = "Creating"
	// ProvisioningStateDeleting ...
	ProvisioningStateDeleting ProvisioningState = "Deleting"
	// ProvisioningStateFailed ...
	ProvisioningStateFailed ProvisioningState = "Failed"
	// ProvisioningStateMigrating ...
	ProvisioningStateMigrating ProvisioningState = "Migrating"
	// ProvisioningStateSucceeded ...
	ProvisioningStateSucceeded ProvisioningState = "Succeeded"
	// ProvisioningStateUpdating ...
	ProvisioningStateUpdating ProvisioningState = "Updating"
)

// PossibleProvisioningStateValues returns an array of possible values for the ProvisioningState const type.
func PossibleProvisioningStateValues() []ProvisioningState {
	return []ProvisioningState{ProvisioningStateCreating, ProvisioningStateDeleting, ProvisioningStateFailed, ProvisioningStateMigrating, ProvisioningStateSucceeded, ProvisioningStateUpdating}
}

// ProvisioningState1 enumerates the values for provisioning state 1.
type ProvisioningState1 string

const (
	// ProvisioningState1Creating ...
	ProvisioningState1Creating ProvisioningState1 = "Creating"
	// ProvisioningState1Deleting ...
	ProvisioningState1Deleting ProvisioningState1 = "Deleting"
	// ProvisioningState1Failed ...
	ProvisioningState1Failed ProvisioningState1 = "Failed"
	// ProvisioningState1Migrating ...
	ProvisioningState1Migrating ProvisioningState1 = "Migrating"
	// ProvisioningState1Succeeded ...
	ProvisioningState1Succeeded ProvisioningState1 = "Succeeded"
	// ProvisioningState1Updating ...
	ProvisioningState1Updating ProvisioningState1 = "Updating"
)

// PossibleProvisioningState1Values returns an array of possible values for the ProvisioningState1 const type.
func PossibleProvisioningState1Values() []ProvisioningState1 {
	return []ProvisioningState1{ProvisioningState1Creating, ProvisioningState1Deleting, ProvisioningState1Failed, ProvisioningState1Migrating, ProvisioningState1Succeeded, ProvisioningState1Updating}
}

// ProvisioningState2 enumerates the values for provisioning state 2.
type ProvisioningState2 string

const (
	// ProvisioningState2Creating ...
	ProvisioningState2Creating ProvisioningState2 = "Creating"
	// ProvisioningState2Deleting ...
	ProvisioningState2Deleting ProvisioningState2 = "Deleting"
	// ProvisioningState2Failed ...
	ProvisioningState2Failed ProvisioningState2 = "Failed"
	// ProvisioningState2Migrating ...
	ProvisioningState2Migrating ProvisioningState2 = "Migrating"
	// ProvisioningState2Succeeded ...
	ProvisioningState2Succeeded ProvisioningState2 = "Succeeded"
	// ProvisioningState2Updating ...
	ProvisioningState2Updating ProvisioningState2 = "Updating"
)

// PossibleProvisioningState2Values returns an array of possible values for the ProvisioningState2 const type.
func PossibleProvisioningState2Values() []ProvisioningState2 {
	return []ProvisioningState2{ProvisioningState2Creating, ProvisioningState2Deleting, ProvisioningState2Failed, ProvisioningState2Migrating, ProvisioningState2Succeeded, ProvisioningState2Updating}
}

// ProximityPlacementGroupType enumerates the values for proximity placement group type.
type ProximityPlacementGroupType string

const (
	// Standard ...
	Standard ProximityPlacementGroupType = "Standard"
	// Ultra ...
	Ultra ProximityPlacementGroupType = "Ultra"
)

// PossibleProximityPlacementGroupTypeValues returns an array of possible values for the ProximityPlacementGroupType const type.
func PossibleProximityPlacementGroupTypeValues() []ProximityPlacementGroupType {
	return []ProximityPlacementGroupType{Standard, Ultra}
}

// ReplicationState enumerates the values for replication state.
type ReplicationState string

const (
	// ReplicationStateCompleted ...
	ReplicationStateCompleted ReplicationState = "Completed"
	// ReplicationStateFailed ...
	ReplicationStateFailed ReplicationState = "Failed"
	// ReplicationStateReplicating ...
	ReplicationStateReplicating ReplicationState = "Replicating"
	// ReplicationStateUnknown ...
	ReplicationStateUnknown ReplicationState = "Unknown"
)

// PossibleReplicationStateValues returns an array of possible values for the ReplicationState const type.
func PossibleReplicationStateValues() []ReplicationState {
	return []ReplicationState{ReplicationStateCompleted, ReplicationStateFailed, ReplicationStateReplicating, ReplicationStateUnknown}
}

// ReplicationStatusTypes enumerates the values for replication status types.
type ReplicationStatusTypes string

const (
	// ReplicationStatusTypesReplicationStatus ...
	ReplicationStatusTypesReplicationStatus ReplicationStatusTypes = "ReplicationStatus"
)

// PossibleReplicationStatusTypesValues returns an array of possible values for the ReplicationStatusTypes const type.
func PossibleReplicationStatusTypesValues() []ReplicationStatusTypes {
	return []ReplicationStatusTypes{ReplicationStatusTypesReplicationStatus}
}

// ResourceIdentityType enumerates the values for resource identity type.
type ResourceIdentityType string

const (
	// ResourceIdentityTypeNone ...
	ResourceIdentityTypeNone ResourceIdentityType = "None"
	// ResourceIdentityTypeSystemAssigned ...
	ResourceIdentityTypeSystemAssigned ResourceIdentityType = "SystemAssigned"
	// ResourceIdentityTypeSystemAssignedUserAssigned ...
	ResourceIdentityTypeSystemAssignedUserAssigned ResourceIdentityType = "SystemAssigned, UserAssigned"
	// ResourceIdentityTypeUserAssigned ...
	ResourceIdentityTypeUserAssigned ResourceIdentityType = "UserAssigned"
)

// PossibleResourceIdentityTypeValues returns an array of possible values for the ResourceIdentityType const type.
func PossibleResourceIdentityTypeValues() []ResourceIdentityType {
	return []ResourceIdentityType{ResourceIdentityTypeNone, ResourceIdentityTypeSystemAssigned, ResourceIdentityTypeSystemAssignedUserAssigned, ResourceIdentityTypeUserAssigned}
}

// ResourceSkuCapacityScaleType enumerates the values for resource sku capacity scale type.
type ResourceSkuCapacityScaleType string

const (
	// ResourceSkuCapacityScaleTypeAutomatic ...
	ResourceSkuCapacityScaleTypeAutomatic ResourceSkuCapacityScaleType = "Automatic"
	// ResourceSkuCapacityScaleTypeManual ...
	ResourceSkuCapacityScaleTypeManual ResourceSkuCapacityScaleType = "Manual"
	// ResourceSkuCapacityScaleTypeNone ...
	ResourceSkuCapacityScaleTypeNone ResourceSkuCapacityScaleType = "None"
)

// PossibleResourceSkuCapacityScaleTypeValues returns an array of possible values for the ResourceSkuCapacityScaleType const type.
func PossibleResourceSkuCapacityScaleTypeValues() []ResourceSkuCapacityScaleType {
	return []ResourceSkuCapacityScaleType{ResourceSkuCapacityScaleTypeAutomatic, ResourceSkuCapacityScaleTypeManual, ResourceSkuCapacityScaleTypeNone}
}

// ResourceSkuRestrictionsReasonCode enumerates the values for resource sku restrictions reason code.
type ResourceSkuRestrictionsReasonCode string

const (
	// NotAvailableForSubscription ...
	NotAvailableForSubscription ResourceSkuRestrictionsReasonCode = "NotAvailableForSubscription"
	// QuotaID ...
	QuotaID ResourceSkuRestrictionsReasonCode = "QuotaId"
)

// PossibleResourceSkuRestrictionsReasonCodeValues returns an array of possible values for the ResourceSkuRestrictionsReasonCode const type.
func PossibleResourceSkuRestrictionsReasonCodeValues() []ResourceSkuRestrictionsReasonCode {
	return []ResourceSkuRestrictionsReasonCode{NotAvailableForSubscription, QuotaID}
}

// ResourceSkuRestrictionsType enumerates the values for resource sku restrictions type.
type ResourceSkuRestrictionsType string

const (
	// Location ...
	Location ResourceSkuRestrictionsType = "Location"
	// Zone ...
	Zone ResourceSkuRestrictionsType = "Zone"
)

// PossibleResourceSkuRestrictionsTypeValues returns an array of possible values for the ResourceSkuRestrictionsType const type.
func PossibleResourceSkuRestrictionsTypeValues() []ResourceSkuRestrictionsType {
	return []ResourceSkuRestrictionsType{Location, Zone}
}

// RollingUpgradeActionType enumerates the values for rolling upgrade action type.
type RollingUpgradeActionType string

const (
	// Cancel ...
	Cancel RollingUpgradeActionType = "Cancel"
	// Start ...
	Start RollingUpgradeActionType = "Start"
)

// PossibleRollingUpgradeActionTypeValues returns an array of possible values for the RollingUpgradeActionType const type.
func PossibleRollingUpgradeActionTypeValues() []RollingUpgradeActionType {
	return []RollingUpgradeActionType{Cancel, Start}
}

// RollingUpgradeStatusCode enumerates the values for rolling upgrade status code.
type RollingUpgradeStatusCode string

const (
	// RollingUpgradeStatusCodeCancelled ...
	RollingUpgradeStatusCodeCancelled RollingUpgradeStatusCode = "Cancelled"
	// RollingUpgradeStatusCodeCompleted ...
	RollingUpgradeStatusCodeCompleted RollingUpgradeStatusCode = "Completed"
	// RollingUpgradeStatusCodeFaulted ...
	RollingUpgradeStatusCodeFaulted RollingUpgradeStatusCode = "Faulted"
	// RollingUpgradeStatusCodeRollingForward ...
	RollingUpgradeStatusCodeRollingForward RollingUpgradeStatusCode = "RollingForward"
)

// PossibleRollingUpgradeStatusCodeValues returns an array of possible values for the RollingUpgradeStatusCode const type.
func PossibleRollingUpgradeStatusCodeValues() []RollingUpgradeStatusCode {
	return []RollingUpgradeStatusCode{RollingUpgradeStatusCodeCancelled, RollingUpgradeStatusCodeCompleted, RollingUpgradeStatusCodeFaulted, RollingUpgradeStatusCodeRollingForward}
}

// SettingNames enumerates the values for setting names.
type SettingNames string

const (
	// AutoLogon ...
	AutoLogon SettingNames = "AutoLogon"
	// FirstLogonCommands ...
	FirstLogonCommands SettingNames = "FirstLogonCommands"
)

// PossibleSettingNamesValues returns an array of possible values for the SettingNames const type.
func PossibleSettingNamesValues() []SettingNames {
	return []SettingNames{AutoLogon, FirstLogonCommands}
}

// SnapshotStorageAccountTypes enumerates the values for snapshot storage account types.
type SnapshotStorageAccountTypes string

const (
	// SnapshotStorageAccountTypesPremiumLRS ...
	SnapshotStorageAccountTypesPremiumLRS SnapshotStorageAccountTypes = "Premium_LRS"
	// SnapshotStorageAccountTypesStandardLRS ...
	SnapshotStorageAccountTypesStandardLRS SnapshotStorageAccountTypes = "Standard_LRS"
	// SnapshotStorageAccountTypesStandardZRS ...
	SnapshotStorageAccountTypesStandardZRS SnapshotStorageAccountTypes = "Standard_ZRS"
)

// PossibleSnapshotStorageAccountTypesValues returns an array of possible values for the SnapshotStorageAccountTypes const type.
func PossibleSnapshotStorageAccountTypesValues() []SnapshotStorageAccountTypes {
	return []SnapshotStorageAccountTypes{SnapshotStorageAccountTypesPremiumLRS, SnapshotStorageAccountTypesStandardLRS, SnapshotStorageAccountTypesStandardZRS}
}

// StatusLevelTypes enumerates the values for status level types.
type StatusLevelTypes string

const (
	// Error ...
	Error StatusLevelTypes = "Error"
	// Info ...
	Info StatusLevelTypes = "Info"
	// Warning ...
	Warning StatusLevelTypes = "Warning"
)

// PossibleStatusLevelTypesValues returns an array of possible values for the StatusLevelTypes const type.
func PossibleStatusLevelTypesValues() []StatusLevelTypes {
	return []StatusLevelTypes{Error, Info, Warning}
}

// StorageAccountTypes enumerates the values for storage account types.
type StorageAccountTypes string

const (
	// StorageAccountTypesPremiumLRS ...
	StorageAccountTypesPremiumLRS StorageAccountTypes = "Premium_LRS"
	// StorageAccountTypesStandardLRS ...
	StorageAccountTypesStandardLRS StorageAccountTypes = "Standard_LRS"
	// StorageAccountTypesStandardSSDLRS ...
	StorageAccountTypesStandardSSDLRS StorageAccountTypes = "StandardSSD_LRS"
	// StorageAccountTypesUltraSSDLRS ...
	StorageAccountTypesUltraSSDLRS StorageAccountTypes = "UltraSSD_LRS"
)

// PossibleStorageAccountTypesValues returns an array of possible values for the StorageAccountTypes const type.
func PossibleStorageAccountTypesValues() []StorageAccountTypes {
	return []StorageAccountTypes{StorageAccountTypesPremiumLRS, StorageAccountTypesStandardLRS, StorageAccountTypesStandardSSDLRS, StorageAccountTypesUltraSSDLRS}
}

// UpgradeMode enumerates the values for upgrade mode.
type UpgradeMode string

const (
	// Automatic ...
	Automatic UpgradeMode = "Automatic"
	// Manual ...
	Manual UpgradeMode = "Manual"
	// Rolling ...
	Rolling UpgradeMode = "Rolling"
)

// PossibleUpgradeModeValues returns an array of possible values for the UpgradeMode const type.
func PossibleUpgradeModeValues() []UpgradeMode {
	return []UpgradeMode{Automatic, Manual, Rolling}
}

// UpgradeOperationInvoker enumerates the values for upgrade operation invoker.
type UpgradeOperationInvoker string

const (
	// UpgradeOperationInvokerPlatform ...
	UpgradeOperationInvokerPlatform UpgradeOperationInvoker = "Platform"
	// UpgradeOperationInvokerUnknown ...
	UpgradeOperationInvokerUnknown UpgradeOperationInvoker = "Unknown"
	// UpgradeOperationInvokerUser ...
	UpgradeOperationInvokerUser UpgradeOperationInvoker = "User"
)

// PossibleUpgradeOperationInvokerValues returns an array of possible values for the UpgradeOperationInvoker const type.
func PossibleUpgradeOperationInvokerValues() []UpgradeOperationInvoker {
	return []UpgradeOperationInvoker{UpgradeOperationInvokerPlatform, UpgradeOperationInvokerUnknown, UpgradeOperationInvokerUser}
}

// UpgradeState enumerates the values for upgrade state.
type UpgradeState string

const (
	// UpgradeStateCancelled ...
	UpgradeStateCancelled UpgradeState = "Cancelled"
	// UpgradeStateCompleted ...
	UpgradeStateCompleted UpgradeState = "Completed"
	// UpgradeStateFaulted ...
	UpgradeStateFaulted UpgradeState = "Faulted"
	// UpgradeStateRollingForward ...
	UpgradeStateRollingForward UpgradeState = "RollingForward"
)

// PossibleUpgradeStateValues returns an array of possible values for the UpgradeState const type.
func PossibleUpgradeStateValues() []UpgradeState {
	return []UpgradeState{UpgradeStateCancelled, UpgradeStateCompleted, UpgradeStateFaulted, UpgradeStateRollingForward}
}

// VirtualMachineEvictionPolicyTypes enumerates the values for virtual machine eviction policy types.
type VirtualMachineEvictionPolicyTypes string

const (
	// Deallocate ...
	Deallocate VirtualMachineEvictionPolicyTypes = "Deallocate"
	// Delete ...
	Delete VirtualMachineEvictionPolicyTypes = "Delete"
)

// PossibleVirtualMachineEvictionPolicyTypesValues returns an array of possible values for the VirtualMachineEvictionPolicyTypes const type.
func PossibleVirtualMachineEvictionPolicyTypesValues() []VirtualMachineEvictionPolicyTypes {
	return []VirtualMachineEvictionPolicyTypes{Deallocate, Delete}
}

// VirtualMachinePriorityTypes enumerates the values for virtual machine priority types.
type VirtualMachinePriorityTypes string

const (
	// Low ...
	Low VirtualMachinePriorityTypes = "Low"
	// Regular ...
	Regular VirtualMachinePriorityTypes = "Regular"
)

// PossibleVirtualMachinePriorityTypesValues returns an array of possible values for the VirtualMachinePriorityTypes const type.
func PossibleVirtualMachinePriorityTypesValues() []VirtualMachinePriorityTypes {
	return []VirtualMachinePriorityTypes{Low, Regular}
}

// VirtualMachineScaleSetSkuScaleType enumerates the values for virtual machine scale set sku scale type.
type VirtualMachineScaleSetSkuScaleType string

const (
	// VirtualMachineScaleSetSkuScaleTypeAutomatic ...
	VirtualMachineScaleSetSkuScaleTypeAutomatic VirtualMachineScaleSetSkuScaleType = "Automatic"
	// VirtualMachineScaleSetSkuScaleTypeNone ...
	VirtualMachineScaleSetSkuScaleTypeNone VirtualMachineScaleSetSkuScaleType = "None"
)

// PossibleVirtualMachineScaleSetSkuScaleTypeValues returns an array of possible values for the VirtualMachineScaleSetSkuScaleType const type.
func PossibleVirtualMachineScaleSetSkuScaleTypeValues() []VirtualMachineScaleSetSkuScaleType {
	return []VirtualMachineScaleSetSkuScaleType{VirtualMachineScaleSetSkuScaleTypeAutomatic, VirtualMachineScaleSetSkuScaleTypeNone}
}

// VirtualMachineSizeTypes enumerates the values for virtual machine size types.
type VirtualMachineSizeTypes string

const (
	// VirtualMachineSizeTypesBasicA0 ...
	VirtualMachineSizeTypesBasicA0 VirtualMachineSizeTypes = "Basic_A0"
	// VirtualMachineSizeTypesBasicA1 ...
	VirtualMachineSizeTypesBasicA1 VirtualMachineSizeTypes = "Basic_A1"
	// VirtualMachineSizeTypesBasicA2 ...
	VirtualMachineSizeTypesBasicA2 VirtualMachineSizeTypes = "Basic_A2"
	// VirtualMachineSizeTypesBasicA3 ...
	VirtualMachineSizeTypesBasicA3 VirtualMachineSizeTypes = "Basic_A3"
	// VirtualMachineSizeTypesBasicA4 ...
	VirtualMachineSizeTypesBasicA4 VirtualMachineSizeTypes = "Basic_A4"
	// VirtualMachineSizeTypesStandardA0 ...
	VirtualMachineSizeTypesStandardA0 VirtualMachineSizeTypes = "Standard_A0"
	// VirtualMachineSizeTypesStandardA1 ...
	VirtualMachineSizeTypesStandardA1 VirtualMachineSizeTypes = "Standard_A1"
	// VirtualMachineSizeTypesStandardA10 ...
	VirtualMachineSizeTypesStandardA10 VirtualMachineSizeTypes = "Standard_A10"
	// VirtualMachineSizeTypesStandardA11 ...
	VirtualMachineSizeTypesStandardA11 VirtualMachineSizeTypes = "Standard_A11"
	// VirtualMachineSizeTypesStandardA1V2 ...
	VirtualMachineSizeTypesStandardA1V2 VirtualMachineSizeTypes = "Standard_A1_v2"
	// VirtualMachineSizeTypesStandardA2 ...
	VirtualMachineSizeTypesStandardA2 VirtualMachineSizeTypes = "Standard_A2"
	// VirtualMachineSizeTypesStandardA2mV2 ...
	VirtualMachineSizeTypesStandardA2mV2 VirtualMachineSizeTypes = "Standard_A2m_v2"
	// VirtualMachineSizeTypesStandardA2V2 ...
	VirtualMachineSizeTypesStandardA2V2 VirtualMachineSizeTypes = "Standard_A2_v2"
	// VirtualMachineSizeTypesStandardA3 ...
	VirtualMachineSizeTypesStandardA3 VirtualMachineSizeTypes = "Standard_A3"
	// VirtualMachineSizeTypesStandardA4 ...
	VirtualMachineSizeTypesStandardA4 VirtualMachineSizeTypes = "Standard_A4"
	// VirtualMachineSizeTypesStandardA4mV2 ...
	VirtualMachineSizeTypesStandardA4mV2 VirtualMachineSizeTypes = "Standard_A4m_v2"
	// VirtualMachineSizeTypesStandardA4V2 ...
	VirtualMachineSizeTypesStandardA4V2 VirtualMachineSizeTypes = "Standard_A4_v2"
	// VirtualMachineSizeTypesStandardA5 ...
	VirtualMachineSizeTypesStandardA5 VirtualMachineSizeTypes = "Standard_A5"
	// VirtualMachineSizeTypesStandardA6 ...
	VirtualMachineSizeTypesStandardA6 VirtualMachineSizeTypes = "Standard_A6"
	// VirtualMachineSizeTypesStandardA7 ...
	VirtualMachineSizeTypesStandardA7 VirtualMachineSizeTypes = "Standard_A7"
	// VirtualMachineSizeTypesStandardA8 ...
	VirtualMachineSizeTypesStandardA8 VirtualMachineSizeTypes = "Standard_A8"
	// VirtualMachineSizeTypesStandardA8mV2 ...
	VirtualMachineSizeTypesStandardA8mV2 VirtualMachineSizeTypes = "Standard_A8m_v2"
	// VirtualMachineSizeTypesStandardA8V2 ...
	VirtualMachineSizeTypesStandardA8V2 VirtualMachineSizeTypes = "Standard_A8_v2"
	// VirtualMachineSizeTypesStandardA9 ...
	VirtualMachineSizeTypesStandardA9 VirtualMachineSizeTypes = "Standard_A9"
	// VirtualMachineSizeTypesStandardB1ms ...
	VirtualMachineSizeTypesStandardB1ms VirtualMachineSizeTypes = "Standard_B1ms"
	// VirtualMachineSizeTypesStandardB1s ...
	VirtualMachineSizeTypesStandardB1s VirtualMachineSizeTypes = "Standard_B1s"
	// VirtualMachineSizeTypesStandardB2ms ...
	VirtualMachineSizeTypesStandardB2ms VirtualMachineSizeTypes = "Standard_B2ms"
	// VirtualMachineSizeTypesStandardB2s ...
	VirtualMachineSizeTypesStandardB2s VirtualMachineSizeTypes = "Standard_B2s"
	// VirtualMachineSizeTypesStandardB4ms ...
	VirtualMachineSizeTypesStandardB4ms VirtualMachineSizeTypes = "Standard_B4ms"
	// VirtualMachineSizeTypesStandardB8ms ...
	VirtualMachineSizeTypesStandardB8ms VirtualMachineSizeTypes = "Standard_B8ms"
	// VirtualMachineSizeTypesStandardD1 ...
	VirtualMachineSizeTypesStandardD1 VirtualMachineSizeTypes = "Standard_D1"
	// VirtualMachineSizeTypesStandardD11 ...
	VirtualMachineSizeTypesStandardD11 VirtualMachineSizeTypes = "Standard_D11"
	// VirtualMachineSizeTypesStandardD11V2 ...
	VirtualMachineSizeTypesStandardD11V2 VirtualMachineSizeTypes = "Standard_D11_v2"
	// VirtualMachineSizeTypesStandardD12 ...
	VirtualMachineSizeTypesStandardD12 VirtualMachineSizeTypes = "Standard_D12"
	// VirtualMachineSizeTypesStandardD12V2 ...
	VirtualMachineSizeTypesStandardD12V2 VirtualMachineSizeTypes = "Standard_D12_v2"
	// VirtualMachineSizeTypesStandardD13 ...
	VirtualMachineSizeTypesStandardD13 VirtualMachineSizeTypes = "Standard_D13"
	// VirtualMachineSizeTypesStandardD13V2 ...
	VirtualMachineSizeTypesStandardD13V2 VirtualMachineSizeTypes = "Standard_D13_v2"
	// VirtualMachineSizeTypesStandardD14 ...
	VirtualMachineSizeTypesStandardD14 VirtualMachineSizeTypes = "Standard_D14"
	// VirtualMachineSizeTypesStandardD14V2 ...
	VirtualMachineSizeTypesStandardD14V2 VirtualMachineSizeTypes = "Standard_D14_v2"
	// VirtualMachineSizeTypesStandardD15V2 ...
	VirtualMachineSizeTypesStandardD15V2 VirtualMachineSizeTypes = "Standard_D15_v2"
	// VirtualMachineSizeTypesStandardD16sV3 ...
	VirtualMachineSizeTypesStandardD16sV3 VirtualMachineSizeTypes = "Standard_D16s_v3"
	// VirtualMachineSizeTypesStandardD16V3 ...
	VirtualMachineSizeTypesStandardD16V3 VirtualMachineSizeTypes = "Standard_D16_v3"
	// VirtualMachineSizeTypesStandardD1V2 ...
	VirtualMachineSizeTypesStandardD1V2 VirtualMachineSizeTypes = "Standard_D1_v2"
	// VirtualMachineSizeTypesStandardD2 ...
	VirtualMachineSizeTypesStandardD2 VirtualMachineSizeTypes = "Standard_D2"
	// VirtualMachineSizeTypesStandardD2sV3 ...
	VirtualMachineSizeTypesStandardD2sV3 VirtualMachineSizeTypes = "Standard_D2s_v3"
	// VirtualMachineSizeTypesStandardD2V2 ...
	VirtualMachineSizeTypesStandardD2V2 VirtualMachineSizeTypes = "Standard_D2_v2"
	// VirtualMachineSizeTypesStandardD2V3 ...
	VirtualMachineSizeTypesStandardD2V3 VirtualMachineSizeTypes = "Standard_D2_v3"
	// VirtualMachineSizeTypesStandardD3 ...
	VirtualMachineSizeTypesStandardD3 VirtualMachineSizeTypes = "Standard_D3"
	// VirtualMachineSizeTypesStandardD32sV3 ...
	VirtualMachineSizeTypesStandardD32sV3 VirtualMachineSizeTypes = "Standard_D32s_v3"
	// VirtualMachineSizeTypesStandardD32V3 ...
	VirtualMachineSizeTypesStandardD32V3 VirtualMachineSizeTypes = "Standard_D32_v3"
	// VirtualMachineSizeTypesStandardD3V2 ...
	VirtualMachineSizeTypesStandardD3V2 VirtualMachineSizeTypes = "Standard_D3_v2"
	// VirtualMachineSizeTypesStandardD4 ...
	VirtualMachineSizeTypesStandardD4 VirtualMachineSizeTypes = "Standard_D4"
	// VirtualMachineSizeTypesStandardD4sV3 ...
	VirtualMachineSizeTypesStandardD4sV3 VirtualMachineSizeTypes = "Standard_D4s_v3"
	// VirtualMachineSizeTypesStandardD4V2 ...
	VirtualMachineSizeTypesStandardD4V2 VirtualMachineSizeTypes = "Standard_D4_v2"
	// VirtualMachineSizeTypesStandardD4V3 ...
	VirtualMachineSizeTypesStandardD4V3 VirtualMachineSizeTypes = "Standard_D4_v3"
	// VirtualMachineSizeTypesStandardD5V2 ...
	VirtualMachineSizeTypesStandardD5V2 VirtualMachineSizeTypes = "Standard_D5_v2"
	// VirtualMachineSizeTypesStandardD64sV3 ...
	VirtualMachineSizeTypesStandardD64sV3 VirtualMachineSizeTypes = "Standard_D64s_v3"
	// VirtualMachineSizeTypesStandardD64V3 ...
	VirtualMachineSizeTypesStandardD64V3 VirtualMachineSizeTypes = "Standard_D64_v3"
	// VirtualMachineSizeTypesStandardD8sV3 ...
	VirtualMachineSizeTypesStandardD8sV3 VirtualMachineSizeTypes = "Standard_D8s_v3"
	// VirtualMachineSizeTypesStandardD8V3 ...
	VirtualMachineSizeTypesStandardD8V3 VirtualMachineSizeTypes = "Standard_D8_v3"
	// VirtualMachineSizeTypesStandardDS1 ...
	VirtualMachineSizeTypesStandardDS1 VirtualMachineSizeTypes = "Standard_DS1"
	// VirtualMachineSizeTypesStandardDS11 ...
	VirtualMachineSizeTypesStandardDS11 VirtualMachineSizeTypes = "Standard_DS11"
	// VirtualMachineSizeTypesStandardDS11V2 ...
	VirtualMachineSizeTypesStandardDS11V2 VirtualMachineSizeTypes = "Standard_DS11_v2"
	// VirtualMachineSizeTypesStandardDS12 ...
	VirtualMachineSizeTypesStandardDS12 VirtualMachineSizeTypes = "Standard_DS12"
	// VirtualMachineSizeTypesStandardDS12V2 ...
	VirtualMachineSizeTypesStandardDS12V2 VirtualMachineSizeTypes = "Standard_DS12_v2"
	// VirtualMachineSizeTypesStandardDS13 ...
	VirtualMachineSizeTypesStandardDS13 VirtualMachineSizeTypes = "Standard_DS13"
	// VirtualMachineSizeTypesStandardDS132V2 ...
	VirtualMachineSizeTypesStandardDS132V2 VirtualMachineSizeTypes = "Standard_DS13-2_v2"
	// VirtualMachineSizeTypesStandardDS134V2 ...
	VirtualMachineSizeTypesStandardDS134V2 VirtualMachineSizeTypes = "Standard_DS13-4_v2"
	// VirtualMachineSizeTypesStandardDS13V2 ...
	VirtualMachineSizeTypesStandardDS13V2 VirtualMachineSizeTypes = "Standard_DS13_v2"
	// VirtualMachineSizeTypesStandardDS14 ...
	VirtualMachineSizeTypesStandardDS14 VirtualMachineSizeTypes = "Standard_DS14"
	// VirtualMachineSizeTypesStandardDS144V2 ...
	VirtualMachineSizeTypesStandardDS144V2 VirtualMachineSizeTypes = "Standard_DS14-4_v2"
	// VirtualMachineSizeTypesStandardDS148V2 ...
	VirtualMachineSizeTypesStandardDS148V2 VirtualMachineSizeTypes = "Standard_DS14-8_v2"
	// VirtualMachineSizeTypesStandardDS14V2 ...
	VirtualMachineSizeTypesStandardDS14V2 VirtualMachineSizeTypes = "Standard_DS14_v2"
	// VirtualMachineSizeTypesStandardDS15V2 ...
	VirtualMachineSizeTypesStandardDS15V2 VirtualMachineSizeTypes = "Standard_DS15_v2"
	// VirtualMachineSizeTypesStandardDS1V2 ...
	VirtualMachineSizeTypesStandardDS1V2 VirtualMachineSizeTypes = "Standard_DS1_v2"
	// VirtualMachineSizeTypesStandardDS2 ...
	VirtualMachineSizeTypesStandardDS2 VirtualMachineSizeTypes = "Standard_DS2"
	// VirtualMachineSizeTypesStandardDS2V2 ...
	VirtualMachineSizeTypesStandardDS2V2 VirtualMachineSizeTypes = "Standard_DS2_v2"
	// VirtualMachineSizeTypesStandardDS3 ...
	VirtualMachineSizeTypesStandardDS3 VirtualMachineSizeTypes = "Standard_DS3"
	// VirtualMachineSizeTypesStandardDS3V2 ...
	VirtualMachineSizeTypesStandardDS3V2 VirtualMachineSizeTypes = "Standard_DS3_v2"
	// VirtualMachineSizeTypesStandardDS4 ...
	VirtualMachineSizeTypesStandardDS4 VirtualMachineSizeTypes = "Standard_DS4"
	// VirtualMachineSizeTypesStandardDS4V2 ...
	VirtualMachineSizeTypesStandardDS4V2 VirtualMachineSizeTypes = "Standard_DS4_v2"
	// VirtualMachineSizeTypesStandardDS5V2 ...
	VirtualMachineSizeTypesStandardDS5V2 VirtualMachineSizeTypes = "Standard_DS5_v2"
	// VirtualMachineSizeTypesStandardE16sV3 ...
	VirtualMachineSizeTypesStandardE16sV3 VirtualMachineSizeTypes = "Standard_E16s_v3"
	// VirtualMachineSizeTypesStandardE16V3 ...
	VirtualMachineSizeTypesStandardE16V3 VirtualMachineSizeTypes = "Standard_E16_v3"
	// VirtualMachineSizeTypesStandardE2sV3 ...
	VirtualMachineSizeTypesStandardE2sV3 VirtualMachineSizeTypes = "Standard_E2s_v3"
	// VirtualMachineSizeTypesStandardE2V3 ...
	VirtualMachineSizeTypesStandardE2V3 VirtualMachineSizeTypes = "Standard_E2_v3"
	// VirtualMachineSizeTypesStandardE3216V3 ...
	VirtualMachineSizeTypesStandardE3216V3 VirtualMachineSizeTypes = "Standard_E32-16_v3"
	// VirtualMachineSizeTypesStandardE328sV3 ...
	VirtualMachineSizeTypesStandardE328sV3 VirtualMachineSizeTypes = "Standard_E32-8s_v3"
	// VirtualMachineSizeTypesStandardE32sV3 ...
	VirtualMachineSizeTypesStandardE32sV3 VirtualMachineSizeTypes = "Standard_E32s_v3"
	// VirtualMachineSizeTypesStandardE32V3 ...
	VirtualMachineSizeTypesStandardE32V3 VirtualMachineSizeTypes = "Standard_E32_v3"
	// VirtualMachineSizeTypesStandardE4sV3 ...
	VirtualMachineSizeTypesStandardE4sV3 VirtualMachineSizeTypes = "Standard_E4s_v3"
	// VirtualMachineSizeTypesStandardE4V3 ...
	VirtualMachineSizeTypesStandardE4V3 VirtualMachineSizeTypes = "Standard_E4_v3"
	// VirtualMachineSizeTypesStandardE6416sV3 ...
	VirtualMachineSizeTypesStandardE6416sV3 VirtualMachineSizeTypes = "Standard_E64-16s_v3"
	// VirtualMachineSizeTypesStandardE6432sV3 ...
	VirtualMachineSizeTypesStandardE6432sV3 VirtualMachineSizeTypes = "Standard_E64-32s_v3"
	// VirtualMachineSizeTypesStandardE64sV3 ...
	VirtualMachineSizeTypesStandardE64sV3 VirtualMachineSizeTypes = "Standard_E64s_v3"
	// VirtualMachineSizeTypesStandardE64V3 ...
	VirtualMachineSizeTypesStandardE64V3 VirtualMachineSizeTypes = "Standard_E64_v3"
	// VirtualMachineSizeTypesStandardE8sV3 ...
	VirtualMachineSizeTypesStandardE8sV3 VirtualMachineSizeTypes = "Standard_E8s_v3"
	// VirtualMachineSizeTypesStandardE8V3 ...
	VirtualMachineSizeTypesStandardE8V3 VirtualMachineSizeTypes = "Standard_E8_v3"
	// VirtualMachineSizeTypesStandardF1 ...
	VirtualMachineSizeTypesStandardF1 VirtualMachineSizeTypes = "Standard_F1"
	// VirtualMachineSizeTypesStandardF16 ...
	VirtualMachineSizeTypesStandardF16 VirtualMachineSizeTypes = "Standard_F16"
	// VirtualMachineSizeTypesStandardF16s ...
	VirtualMachineSizeTypesStandardF16s VirtualMachineSizeTypes = "Standard_F16s"
	// VirtualMachineSizeTypesStandardF16sV2 ...
	VirtualMachineSizeTypesStandardF16sV2 VirtualMachineSizeTypes = "Standard_F16s_v2"
	// VirtualMachineSizeTypesStandardF1s ...
	VirtualMachineSizeTypesStandardF1s VirtualMachineSizeTypes = "Standard_F1s"
	// VirtualMachineSizeTypesStandardF2 ...
	VirtualMachineSizeTypesStandardF2 VirtualMachineSizeTypes = "Standard_F2"
	// VirtualMachineSizeTypesStandardF2s ...
	VirtualMachineSizeTypesStandardF2s VirtualMachineSizeTypes = "Standard_F2s"
	// VirtualMachineSizeTypesStandardF2sV2 ...
	VirtualMachineSizeTypesStandardF2sV2 VirtualMachineSizeTypes = "Standard_F2s_v2"
	// VirtualMachineSizeTypesStandardF32sV2 ...
	VirtualMachineSizeTypesStandardF32sV2 VirtualMachineSizeTypes = "Standard_F32s_v2"
	// VirtualMachineSizeTypesStandardF4 ...
	VirtualMachineSizeTypesStandardF4 VirtualMachineSizeTypes = "Standard_F4"
	// VirtualMachineSizeTypesStandardF4s ...
	VirtualMachineSizeTypesStandardF4s VirtualMachineSizeTypes = "Standard_F4s"
	// VirtualMachineSizeTypesStandardF4sV2 ...
	VirtualMachineSizeTypesStandardF4sV2 VirtualMachineSizeTypes = "Standard_F4s_v2"
	// VirtualMachineSizeTypesStandardF64sV2 ...
	VirtualMachineSizeTypesStandardF64sV2 VirtualMachineSizeTypes = "Standard_F64s_v2"
	// VirtualMachineSizeTypesStandardF72sV2 ...
	VirtualMachineSizeTypesStandardF72sV2 VirtualMachineSizeTypes = "Standard_F72s_v2"
	// VirtualMachineSizeTypesStandardF8 ...
	VirtualMachineSizeTypesStandardF8 VirtualMachineSizeTypes = "Standard_F8"
	// VirtualMachineSizeTypesStandardF8s ...
	VirtualMachineSizeTypesStandardF8s VirtualMachineSizeTypes = "Standard_F8s"
	// VirtualMachineSizeTypesStandardF8sV2 ...
	VirtualMachineSizeTypesStandardF8sV2 VirtualMachineSizeTypes = "Standard_F8s_v2"
	// VirtualMachineSizeTypesStandardG1 ...
	VirtualMachineSizeTypesStandardG1 VirtualMachineSizeTypes = "Standard_G1"
	// VirtualMachineSizeTypesStandardG2 ...
	VirtualMachineSizeTypesStandardG2 VirtualMachineSizeTypes = "Standard_G2"
	// VirtualMachineSizeTypesStandardG3 ...
	VirtualMachineSizeTypesStandardG3 VirtualMachineSizeTypes = "Standard_G3"
	// VirtualMachineSizeTypesStandardG4 ...
	VirtualMachineSizeTypesStandardG4 VirtualMachineSizeTypes = "Standard_G4"
	// VirtualMachineSizeTypesStandardG5 ...
	VirtualMachineSizeTypesStandardG5 VirtualMachineSizeTypes = "Standard_G5"
	// VirtualMachineSizeTypesStandardGS1 ...
	VirtualMachineSizeTypesStandardGS1 VirtualMachineSizeTypes = "Standard_GS1"
	// VirtualMachineSizeTypesStandardGS2 ...
	VirtualMachineSizeTypesStandardGS2 VirtualMachineSizeTypes = "Standard_GS2"
	// VirtualMachineSizeTypesStandardGS3 ...
	VirtualMachineSizeTypesStandardGS3 VirtualMachineSizeTypes = "Standard_GS3"
	// VirtualMachineSizeTypesStandardGS4 ...
	VirtualMachineSizeTypesStandardGS4 VirtualMachineSizeTypes = "Standard_GS4"
	// VirtualMachineSizeTypesStandardGS44 ...
	VirtualMachineSizeTypesStandardGS44 VirtualMachineSizeTypes = "Standard_GS4-4"
	// VirtualMachineSizeTypesStandardGS48 ...
	VirtualMachineSizeTypesStandardGS48 VirtualMachineSizeTypes = "Standard_GS4-8"
	// VirtualMachineSizeTypesStandardGS5 ...
	VirtualMachineSizeTypesStandardGS5 VirtualMachineSizeTypes = "Standard_GS5"
	// VirtualMachineSizeTypesStandardGS516 ...
	VirtualMachineSizeTypesStandardGS516 VirtualMachineSizeTypes = "Standard_GS5-16"
	// VirtualMachineSizeTypesStandardGS58 ...
	VirtualMachineSizeTypesStandardGS58 VirtualMachineSizeTypes = "Standard_GS5-8"
	// VirtualMachineSizeTypesStandardH16 ...
	VirtualMachineSizeTypesStandardH16 VirtualMachineSizeTypes = "Standard_H16"
	// VirtualMachineSizeTypesStandardH16m ...
	VirtualMachineSizeTypesStandardH16m VirtualMachineSizeTypes = "Standard_H16m"
	// VirtualMachineSizeTypesStandardH16mr ...
	VirtualMachineSizeTypesStandardH16mr VirtualMachineSizeTypes = "Standard_H16mr"
	// VirtualMachineSizeTypesStandardH16r ...
	VirtualMachineSizeTypesStandardH16r VirtualMachineSizeTypes = "Standard_H16r"
	// VirtualMachineSizeTypesStandardH8 ...
	VirtualMachineSizeTypesStandardH8 VirtualMachineSizeTypes = "Standard_H8"
	// VirtualMachineSizeTypesStandardH8m ...
	VirtualMachineSizeTypesStandardH8m VirtualMachineSizeTypes = "Standard_H8m"
	// VirtualMachineSizeTypesStandardL16s ...
	VirtualMachineSizeTypesStandardL16s VirtualMachineSizeTypes = "Standard_L16s"
	// VirtualMachineSizeTypesStandardL32s ...
	VirtualMachineSizeTypesStandardL32s VirtualMachineSizeTypes = "Standard_L32s"
	// VirtualMachineSizeTypesStandardL4s ...
	VirtualMachineSizeTypesStandardL4s VirtualMachineSizeTypes = "Standard_L4s"
	// VirtualMachineSizeTypesStandardL8s ...
	VirtualMachineSizeTypesStandardL8s VirtualMachineSizeTypes = "Standard_L8s"
	// VirtualMachineSizeTypesStandardM12832ms ...
	VirtualMachineSizeTypesStandardM12832ms VirtualMachineSizeTypes = "Standard_M128-32ms"
	// VirtualMachineSizeTypesStandardM12864ms ...
	VirtualMachineSizeTypesStandardM12864ms VirtualMachineSizeTypes = "Standard_M128-64ms"
	// VirtualMachineSizeTypesStandardM128ms ...
	VirtualMachineSizeTypesStandardM128ms VirtualMachineSizeTypes = "Standard_M128ms"
	// VirtualMachineSizeTypesStandardM128s ...
	VirtualMachineSizeTypesStandardM128s VirtualMachineSizeTypes = "Standard_M128s"
	// VirtualMachineSizeTypesStandardM6416ms ...
	VirtualMachineSizeTypesStandardM6416ms VirtualMachineSizeTypes = "Standard_M64-16ms"
	// VirtualMachineSizeTypesStandardM6432ms ...
	VirtualMachineSizeTypesStandardM6432ms VirtualMachineSizeTypes = "Standard_M64-32ms"
	// VirtualMachineSizeTypesStandardM64ms ...
	VirtualMachineSizeTypesStandardM64ms VirtualMachineSizeTypes = "Standard_M64ms"
	// VirtualMachineSizeTypesStandardM64s ...
	VirtualMachineSizeTypesStandardM64s VirtualMachineSizeTypes = "Standard_M64s"
	// VirtualMachineSizeTypesStandardNC12 ...
	VirtualMachineSizeTypesStandardNC12 VirtualMachineSizeTypes = "Standard_NC12"
	// VirtualMachineSizeTypesStandardNC12sV2 ...
	VirtualMachineSizeTypesStandardNC12sV2 VirtualMachineSizeTypes = "Standard_NC12s_v2"
	// VirtualMachineSizeTypesStandardNC12sV3 ...
	VirtualMachineSizeTypesStandardNC12sV3 VirtualMachineSizeTypes = "Standard_NC12s_v3"
	// VirtualMachineSizeTypesStandardNC24 ...
	VirtualMachineSizeTypesStandardNC24 VirtualMachineSizeTypes = "Standard_NC24"
	// VirtualMachineSizeTypesStandardNC24r ...
	VirtualMachineSizeTypesStandardNC24r VirtualMachineSizeTypes = "Standard_NC24r"
	// VirtualMachineSizeTypesStandardNC24rsV2 ...
	VirtualMachineSizeTypesStandardNC24rsV2 VirtualMachineSizeTypes = "Standard_NC24rs_v2"
	// VirtualMachineSizeTypesStandardNC24rsV3 ...
	VirtualMachineSizeTypesStandardNC24rsV3 VirtualMachineSizeTypes = "Standard_NC24rs_v3"
	// VirtualMachineSizeTypesStandardNC24sV2 ...
	VirtualMachineSizeTypesStandardNC24sV2 VirtualMachineSizeTypes = "Standard_NC24s_v2"
	// VirtualMachineSizeTypesStandardNC24sV3 ...
	VirtualMachineSizeTypesStandardNC24sV3 VirtualMachineSizeTypes = "Standard_NC24s_v3"
	// VirtualMachineSizeTypesStandardNC6 ...
	VirtualMachineSizeTypesStandardNC6 VirtualMachineSizeTypes = "Standard_NC6"
	// VirtualMachineSizeTypesStandardNC6sV2 ...
	VirtualMachineSizeTypesStandardNC6sV2 VirtualMachineSizeTypes = "Standard_NC6s_v2"
	// VirtualMachineSizeTypesStandardNC6sV3 ...
	VirtualMachineSizeTypesStandardNC6sV3 VirtualMachineSizeTypes = "Standard_NC6s_v3"
	// VirtualMachineSizeTypesStandardND12s ...
	VirtualMachineSizeTypesStandardND12s VirtualMachineSizeTypes = "Standard_ND12s"
	// VirtualMachineSizeTypesStandardND24rs ...
	VirtualMachineSizeTypesStandardND24rs VirtualMachineSizeTypes = "Standard_ND24rs"
	// VirtualMachineSizeTypesStandardND24s ...
	VirtualMachineSizeTypesStandardND24s VirtualMachineSizeTypes = "Standard_ND24s"
	// VirtualMachineSizeTypesStandardND6s ...
	VirtualMachineSizeTypesStandardND6s VirtualMachineSizeTypes = "Standard_ND6s"
	// VirtualMachineSizeTypesStandardNV12 ...
	VirtualMachineSizeTypesStandardNV12 VirtualMachineSizeTypes = "Standard_NV12"
	// VirtualMachineSizeTypesStandardNV24 ...
	VirtualMachineSizeTypesStandardNV24 VirtualMachineSizeTypes = "Standard_NV24"
	// VirtualMachineSizeTypesStandardNV6 ...
	VirtualMachineSizeTypesStandardNV6 VirtualMachineSizeTypes = "Standard_NV6"
)

// PossibleVirtualMachineSizeTypesValues returns an array of possible values for the VirtualMachineSizeTypes const type.
func PossibleVirtualMachineSizeTypesValues() []VirtualMachineSizeTypes {
	return []VirtualMachineSizeTypes{VirtualMachineSizeTypesBasicA0, VirtualMachineSizeTypesBasicA1, VirtualMachineSizeTypesBasicA2, VirtualMachineSizeTypesBasicA3, VirtualMachineSizeTypesBasicA4, VirtualMachineSizeTypesStandardA0, VirtualMachineSizeTypesStandardA1, VirtualMachineSizeTypesStandardA10, VirtualMachineSizeTypesStandardA11, VirtualMachineSizeTypesStandardA1V2, VirtualMachineSizeTypesStandardA2, VirtualMachineSizeTypesStandardA2mV2, VirtualMachineSizeTypesStandardA2V2, VirtualMachineSizeTypesStandardA3, VirtualMachineSizeTypesStandardA4, VirtualMachineSizeTypesStandardA4mV2, VirtualMachineSizeTypesStandardA4V2, VirtualMachineSizeTypesStandardA5, VirtualMachineSizeTypesStandardA6, VirtualMachineSizeTypesStandardA7, VirtualMachineSizeTypesStandardA8, VirtualMachineSizeTypesStandardA8mV2, VirtualMachineSizeTypesStandardA8V2, VirtualMachineSizeTypesStandardA9, VirtualMachineSizeTypesStandardB1ms, VirtualMachineSizeTypesStandardB1s, VirtualMachineSizeTypesStandardB2ms, VirtualMachineSizeTypesStandardB2s, VirtualMachineSizeTypesStandardB4ms, VirtualMachineSizeTypesStandardB8ms, VirtualMachineSizeTypesStandardD1, VirtualMachineSizeTypesStandardD11, VirtualMachineSizeTypesStandardD11V2, VirtualMachineSizeTypesStandardD12, VirtualMachineSizeTypesStandardD12V2, VirtualMachineSizeTypesStandardD13, VirtualMachineSizeTypesStandardD13V2, VirtualMachineSizeTypesStandardD14, VirtualMachineSizeTypesStandardD14V2, VirtualMachineSizeTypesStandardD15V2, VirtualMachineSizeTypesStandardD16sV3, VirtualMachineSizeTypesStandardD16V3, VirtualMachineSizeTypesStandardD1V2, VirtualMachineSizeTypesStandardD2, VirtualMachineSizeTypesStandardD2sV3, VirtualMachineSizeTypesStandardD2V2, VirtualMachineSizeTypesStandardD2V3, VirtualMachineSizeTypesStandardD3, VirtualMachineSizeTypesStandardD32sV3, VirtualMachineSizeTypesStandardD32V3, VirtualMachineSizeTypesStandardD3V2, VirtualMachineSizeTypesStandardD4, VirtualMachineSizeTypesStandardD4sV3, VirtualMachineSizeTypesStandardD4V2, VirtualMachineSizeTypesStandardD4V3, VirtualMachineSizeTypesStandardD5V2, VirtualMachineSizeTypesStandardD64sV3, VirtualMachineSizeTypesStandardD64V3, VirtualMachineSizeTypesStandardD8sV3, VirtualMachineSizeTypesStandardD8V3, VirtualMachineSizeTypesStandardDS1, VirtualMachineSizeTypesStandardDS11, VirtualMachineSizeTypesStandardDS11V2, VirtualMachineSizeTypesStandardDS12, VirtualMachineSizeTypesStandardDS12V2, VirtualMachineSizeTypesStandardDS13, VirtualMachineSizeTypesStandardDS132V2, VirtualMachineSizeTypesStandardDS134V2, VirtualMachineSizeTypesStandardDS13V2, VirtualMachineSizeTypesStandardDS14, VirtualMachineSizeTypesStandardDS144V2, VirtualMachineSizeTypesStandardDS148V2, VirtualMachineSizeTypesStandardDS14V2, VirtualMachineSizeTypesStandardDS15V2, VirtualMachineSizeTypesStandardDS1V2, VirtualMachineSizeTypesStandardDS2, VirtualMachineSizeTypesStandardDS2V2, VirtualMachineSizeTypesStandardDS3, VirtualMachineSizeTypesStandardDS3V2, VirtualMachineSizeTypesStandardDS4, VirtualMachineSizeTypesStandardDS4V2, VirtualMachineSizeTypesStandardDS5V2, VirtualMachineSizeTypesStandardE16sV3, VirtualMachineSizeTypesStandardE16V3, VirtualMachineSizeTypesStandardE2sV3, VirtualMachineSizeTypesStandardE2V3, VirtualMachineSizeTypesStandardE3216V3, VirtualMachineSizeTypesStandardE328sV3, VirtualMachineSizeTypesStandardE32sV3, VirtualMachineSizeTypesStandardE32V3, VirtualMachineSizeTypesStandardE4sV3, VirtualMachineSizeTypesStandardE4V3, VirtualMachineSizeTypesStandardE6416sV3, VirtualMachineSizeTypesStandardE6432sV3, VirtualMachineSizeTypesStandardE64sV3, VirtualMachineSizeTypesStandardE64V3, VirtualMachineSizeTypesStandardE8sV3, VirtualMachineSizeTypesStandardE8V3, VirtualMachineSizeTypesStandardF1, VirtualMachineSizeTypesStandardF16, VirtualMachineSizeTypesStandardF16s, VirtualMachineSizeTypesStandardF16sV2, VirtualMachineSizeTypesStandardF1s, VirtualMachineSizeTypesStandardF2, VirtualMachineSizeTypesStandardF2s, VirtualMachineSizeTypesStandardF2sV2, VirtualMachineSizeTypesStandardF32sV2, VirtualMachineSizeTypesStandardF4, VirtualMachineSizeTypesStandardF4s, VirtualMachineSizeTypesStandardF4sV2, VirtualMachineSizeTypesStandardF64sV2, VirtualMachineSizeTypesStandardF72sV2, VirtualMachineSizeTypesStandardF8, VirtualMachineSizeTypesStandardF8s, VirtualMachineSizeTypesStandardF8sV2, VirtualMachineSizeTypesStandardG1, VirtualMachineSizeTypesStandardG2, VirtualMachineSizeTypesStandardG3, VirtualMachineSizeTypesStandardG4, VirtualMachineSizeTypesStandardG5, VirtualMachineSizeTypesStandardGS1, VirtualMachineSizeTypesStandardGS2, VirtualMachineSizeTypesStandardGS3, VirtualMachineSizeTypesStandardGS4, VirtualMachineSizeTypesStandardGS44, VirtualMachineSizeTypesStandardGS48, VirtualMachineSizeTypesStandardGS5, VirtualMachineSizeTypesStandardGS516, VirtualMachineSizeTypesStandardGS58, VirtualMachineSizeTypesStandardH16, VirtualMachineSizeTypesStandardH16m, VirtualMachineSizeTypesStandardH16mr, VirtualMachineSizeTypesStandardH16r, VirtualMachineSizeTypesStandardH8, VirtualMachineSizeTypesStandardH8m, VirtualMachineSizeTypesStandardL16s, VirtualMachineSizeTypesStandardL32s, VirtualMachineSizeTypesStandardL4s, VirtualMachineSizeTypesStandardL8s, VirtualMachineSizeTypesStandardM12832ms, VirtualMachineSizeTypesStandardM12864ms, VirtualMachineSizeTypesStandardM128ms, VirtualMachineSizeTypesStandardM128s, VirtualMachineSizeTypesStandardM6416ms, VirtualMachineSizeTypesStandardM6432ms, VirtualMachineSizeTypesStandardM64ms, VirtualMachineSizeTypesStandardM64s, VirtualMachineSizeTypesStandardNC12, VirtualMachineSizeTypesStandardNC12sV2, VirtualMachineSizeTypesStandardNC12sV3, VirtualMachineSizeTypesStandardNC24, VirtualMachineSizeTypesStandardNC24r, VirtualMachineSizeTypesStandardNC24rsV2, VirtualMachineSizeTypesStandardNC24rsV3, VirtualMachineSizeTypesStandardNC24sV2, VirtualMachineSizeTypesStandardNC24sV3, VirtualMachineSizeTypesStandardNC6, VirtualMachineSizeTypesStandardNC6sV2, VirtualMachineSizeTypesStandardNC6sV3, VirtualMachineSizeTypesStandardND12s, VirtualMachineSizeTypesStandardND24rs, VirtualMachineSizeTypesStandardND24s, VirtualMachineSizeTypesStandardND6s, VirtualMachineSizeTypesStandardNV12, VirtualMachineSizeTypesStandardNV24, VirtualMachineSizeTypesStandardNV6}
}

// AccessURI a disk access SAS uri.
type AccessURI struct {
	autorest.Response `json:"-"`
	// AccessSAS - READ-ONLY; A SAS uri for accessing a disk.
	AccessSAS *string `json:"accessSAS,omitempty"`
}

// AdditionalCapabilities enables or disables a capability on the virtual machine or virtual machine scale
// set.
type AdditionalCapabilities struct {
	// UltraSSDEnabled - The flag that enables or disables a capability to have one or more managed data disks with UltraSSD_LRS storage account type on the VM or VMSS. Managed disks with storage account type UltraSSD_LRS can be added to a virtual machine or virtual machine scale set only if this property is enabled.
	UltraSSDEnabled *bool `json:"ultraSSDEnabled,omitempty"`
}

// AdditionalUnattendContent specifies additional XML formatted information that can be included in the
// Unattend.xml file, which is used by Windows Setup. Contents are defined by setting name, component name,
// and the pass in which the content is applied.
type AdditionalUnattendContent struct {
	// PassName - The pass name. Currently, the only allowable value is OobeSystem. Possible values include: 'OobeSystem'
	PassName PassNames `json:"passName,omitempty"`
	// ComponentName - The component name. Currently, the only allowable value is Microsoft-Windows-Shell-Setup. Possible values include: 'MicrosoftWindowsShellSetup'
	ComponentName ComponentNames `json:"componentName,omitempty"`
	// SettingName - Specifies the name of the setting to which the content applies. Possible values are: FirstLogonCommands and AutoLogon. Possible values include: 'AutoLogon', 'FirstLogonCommands'
	SettingName SettingNames `json:"settingName,omitempty"`
	// Content - Specifies the XML formatted content that is added to the unattend.xml file for the specified path and component. The XML must be less than 4KB and must include the root element for the setting or feature that is being inserted.
	Content *string `json:"content,omitempty"`
}

// APIEntityReference the API entity reference.
type APIEntityReference struct {
	// ID - The ARM resource id in the form of /subscriptions/{SubscriptionId}/resourceGroups/{ResourceGroupName}/...
	ID *string `json:"id,omitempty"`
}

// APIError api error.
type APIError struct {
	// Details - The Api error details
	Details *[]APIErrorBase `json:"details,omitempty"`
	// Innererror - The Api inner error
	Innererror *InnerError `json:"innererror,omitempty"`
	// Code - The error code.
	Code *string `json:"code,omitempty"`
	// Target - The target of the particular error.
	Target *string `json:"target,omitempty"`
	// Message - The error message.
	Message *string `json:"message,omitempty"`
}

// APIErrorBase api error base.
type APIErrorBase struct {
	// Code - The error code.
	Code *string `json:"code,omitempty"`
	// Target - The target of the particular error.
	Target *string `json:"target,omitempty"`
	// Message - The error message.
	Message *string `json:"message,omitempty"`
}

// AutomaticOSUpgradePolicy the configuration parameters used for performing automatic OS upgrade.
type AutomaticOSUpgradePolicy struct {
	// EnableAutomaticOSUpgrade - Indicates whether OS upgrades should automatically be applied to scale set instances in a rolling fashion when a newer version of the OS image becomes available. Default value is false. If this is set to true for Windows based scale sets, recommendation is to set [enableAutomaticUpdates](https://docs.microsoft.com/dotnet/api/microsoft.azure.management.compute.models.windowsconfiguration.enableautomaticupdates?view=azure-dotnet) to false.
	EnableAutomaticOSUpgrade *bool `json:"enableAutomaticOSUpgrade,omitempty"`
	// DisableAutomaticRollback - Whether OS image rollback feature should be disabled. Default value is false.
	DisableAutomaticRollback *bool `json:"disableAutomaticRollback,omitempty"`
}

// AutomaticOSUpgradeProperties describes automatic OS upgrade properties on the image.
type AutomaticOSUpgradeProperties struct {
	// AutomaticOSUpgradeSupported - Specifies whether automatic OS upgrade is supported on the image.
	AutomaticOSUpgradeSupported *bool `json:"automaticOSUpgradeSupported,omitempty"`
}

// AutomaticRepairsPolicy specifies the configuration parameters for automatic repairs on the virtual
// machine scale set.
type AutomaticRepairsPolicy struct {
	// Enabled - Specifies whether automatic repairs should be enabled on the virtual machine scale set. The default value is false.
	Enabled *bool `json:"enabled,omitempty"`
	// GracePeriod - The amount of time for which automatic repairs are suspended due to a state change on VM. The grace time starts after the state change has completed. This helps avoid premature or accidental repairs. The time duration should be specified in ISO 8601 format. The default value is 5 minutes (PT5M).
	GracePeriod *string `json:"gracePeriod,omitempty"`
	// MaxInstanceRepairsPercent - The percentage (capacity of scaleset) of virtual machines that will be simultaneously repaired. The default value is 20%.
	MaxInstanceRepairsPercent *int32 `json:"maxInstanceRepairsPercent,omitempty"`
}

// AvailabilitySet specifies information about the availability set that the virtual machine should be
// assigned to. Virtual machines specified in the same availability set are allocated to different nodes to
// maximize availability. For more information about availability sets, see [Manage the availability of
// virtual
// machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-manage-availability?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
// <br><br> For more information on Azure planned maintenance, see [Planned maintenance for virtual
// machines in
// Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-planned-maintenance?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json)
// <br><br> Currently, a VM can only be added to availability set at creation time. An existing VM cannot
// be added to an availability set.
type AvailabilitySet struct {
	autorest.Response          `json:"-"`
	*AvailabilitySetProperties `json:"properties,omitempty"`
	// Sku - Sku of the availability set, only name is required to be set. See AvailabilitySetSkuTypes for possible set of values. Use 'Aligned' for virtual machines with managed disks and 'Classic' for virtual machines with unmanaged disks. Default value is 'Classic'.
	Sku *Sku `json:"sku,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for AvailabilitySet.
func (as AvailabilitySet) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if as.AvailabilitySetProperties != nil {
		objectMap["properties"] = as.AvailabilitySetProperties
	}
	if as.Sku != nil {
		objectMap["sku"] = as.Sku
	}
	if as.Location != nil {
		objectMap["location"] = as.Location
	}
	if as.Tags != nil {
		objectMap["tags"] = as.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for AvailabilitySet struct.
func (as *AvailabilitySet) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var availabilitySetProperties AvailabilitySetProperties
				err = json.Unmarshal(*v, &availabilitySetProperties)
				if err != nil {
					return err
				}
				as.AvailabilitySetProperties = &availabilitySetProperties
			}
		case "sku":
			if v != nil {
				var sku Sku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				as.Sku = &sku
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				as.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				as.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				as.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				as.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				as.Tags = tags
			}
		}
	}

	return nil
}

// AvailabilitySetListResult the List Availability Set operation response.
type AvailabilitySetListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of availability sets
	Value *[]AvailabilitySet `json:"value,omitempty"`
	// NextLink - The URI to fetch the next page of AvailabilitySets. Call ListNext() with this URI to fetch the next page of AvailabilitySets.
	NextLink *string `json:"nextLink,omitempty"`
}

// AvailabilitySetListResultIterator provides access to a complete listing of AvailabilitySet values.
type AvailabilitySetListResultIterator struct {
	i    int
	page AvailabilitySetListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *AvailabilitySetListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/AvailabilitySetListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *AvailabilitySetListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter AvailabilitySetListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter AvailabilitySetListResultIterator) Response() AvailabilitySetListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter AvailabilitySetListResultIterator) Value() AvailabilitySet {
	if !iter.page.NotDone() {
		return AvailabilitySet{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the AvailabilitySetListResultIterator type.
func NewAvailabilitySetListResultIterator(page AvailabilitySetListResultPage) AvailabilitySetListResultIterator {
	return AvailabilitySetListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (aslr AvailabilitySetListResult) IsEmpty() bool {
	return aslr.Value == nil || len(*aslr.Value) == 0
}

// availabilitySetListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (aslr AvailabilitySetListResult) availabilitySetListResultPreparer(ctx context.Context) (*http.Request, error) {
	if aslr.NextLink == nil || len(to.String(aslr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(aslr.NextLink)))
}

// AvailabilitySetListResultPage contains a page of AvailabilitySet values.
type AvailabilitySetListResultPage struct {
	fn   func(context.Context, AvailabilitySetListResult) (AvailabilitySetListResult, error)
	aslr AvailabilitySetListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *AvailabilitySetListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/AvailabilitySetListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.aslr)
	if err != nil {
		return err
	}
	page.aslr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *AvailabilitySetListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page AvailabilitySetListResultPage) NotDone() bool {
	return !page.aslr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page AvailabilitySetListResultPage) Response() AvailabilitySetListResult {
	return page.aslr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page AvailabilitySetListResultPage) Values() []AvailabilitySet {
	if page.aslr.IsEmpty() {
		return nil
	}
	return *page.aslr.Value
}

// Creates a new instance of the AvailabilitySetListResultPage type.
func NewAvailabilitySetListResultPage(getNextPage func(context.Context, AvailabilitySetListResult) (AvailabilitySetListResult, error)) AvailabilitySetListResultPage {
	return AvailabilitySetListResultPage{fn: getNextPage}
}

// AvailabilitySetProperties the instance view of a resource.
type AvailabilitySetProperties struct {
	// PlatformUpdateDomainCount - Update Domain count.
	PlatformUpdateDomainCount *int32 `json:"platformUpdateDomainCount,omitempty"`
	// PlatformFaultDomainCount - Fault Domain count.
	PlatformFaultDomainCount *int32 `json:"platformFaultDomainCount,omitempty"`
	// VirtualMachines - A list of references to all virtual machines in the availability set.
	VirtualMachines *[]SubResource `json:"virtualMachines,omitempty"`
	// ProximityPlacementGroup - Specifies information about the proximity placement group that the availability set should be assigned to. <br><br>Minimum api-version: 2018-04-01.
	ProximityPlacementGroup *SubResource `json:"proximityPlacementGroup,omitempty"`
	// Statuses - READ-ONLY; The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
}

// AvailabilitySetUpdate specifies information about the availability set that the virtual machine should
// be assigned to. Only tags may be updated.
type AvailabilitySetUpdate struct {
	*AvailabilitySetProperties `json:"properties,omitempty"`
	// Sku - Sku of the availability set
	Sku *Sku `json:"sku,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for AvailabilitySetUpdate.
func (asu AvailabilitySetUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if asu.AvailabilitySetProperties != nil {
		objectMap["properties"] = asu.AvailabilitySetProperties
	}
	if asu.Sku != nil {
		objectMap["sku"] = asu.Sku
	}
	if asu.Tags != nil {
		objectMap["tags"] = asu.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for AvailabilitySetUpdate struct.
func (asu *AvailabilitySetUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var availabilitySetProperties AvailabilitySetProperties
				err = json.Unmarshal(*v, &availabilitySetProperties)
				if err != nil {
					return err
				}
				asu.AvailabilitySetProperties = &availabilitySetProperties
			}
		case "sku":
			if v != nil {
				var sku Sku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				asu.Sku = &sku
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				asu.Tags = tags
			}
		}
	}

	return nil
}

// BootDiagnostics boot Diagnostics is a debugging feature which allows you to view Console Output and
// Screenshot to diagnose VM status. <br><br> You can easily view the output of your console log. <br><br>
// Azure also enables you to see a screenshot of the VM from the hypervisor.
type BootDiagnostics struct {
	// Enabled - Whether boot diagnostics should be enabled on the Virtual Machine.
	Enabled *bool `json:"enabled,omitempty"`
	// StorageURI - Uri of the storage account to use for placing the console output and screenshot.
	StorageURI *string `json:"storageUri,omitempty"`
}

// BootDiagnosticsInstanceView the instance view of a virtual machine boot diagnostics.
type BootDiagnosticsInstanceView struct {
	// ConsoleScreenshotBlobURI - READ-ONLY; The console screenshot blob URI.
	ConsoleScreenshotBlobURI *string `json:"consoleScreenshotBlobUri,omitempty"`
	// SerialConsoleLogBlobURI - READ-ONLY; The Linux serial console log blob Uri.
	SerialConsoleLogBlobURI *string `json:"serialConsoleLogBlobUri,omitempty"`
	// Status - READ-ONLY; The boot diagnostics status information for the VM. <br><br> NOTE: It will be set only if there are errors encountered in enabling boot diagnostics.
	Status *InstanceViewStatus `json:"status,omitempty"`
}

// CloudError an error response from the Gallery service.
type CloudError struct {
	Error *APIError `json:"error,omitempty"`
}

// ContainerService container service.
type ContainerService struct {
	autorest.Response           `json:"-"`
	*ContainerServiceProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for ContainerService.
func (cs ContainerService) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if cs.ContainerServiceProperties != nil {
		objectMap["properties"] = cs.ContainerServiceProperties
	}
	if cs.Location != nil {
		objectMap["location"] = cs.Location
	}
	if cs.Tags != nil {
		objectMap["tags"] = cs.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for ContainerService struct.
func (cs *ContainerService) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var containerServiceProperties ContainerServiceProperties
				err = json.Unmarshal(*v, &containerServiceProperties)
				if err != nil {
					return err
				}
				cs.ContainerServiceProperties = &containerServiceProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				cs.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				cs.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				cs.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				cs.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				cs.Tags = tags
			}
		}
	}

	return nil
}

// ContainerServiceAgentPoolProfile profile for the container service agent pool.
type ContainerServiceAgentPoolProfile struct {
	// Name - Unique name of the agent pool profile in the context of the subscription and resource group.
	Name *string `json:"name,omitempty"`
	// Count - Number of agents (VMs) to host docker containers. Allowed values must be in the range of 1 to 100 (inclusive). The default value is 1.
	Count *int32 `json:"count,omitempty"`
	// VMSize - Size of agent VMs. Possible values include: 'StandardA0', 'StandardA1', 'StandardA2', 'StandardA3', 'StandardA4', 'StandardA5', 'StandardA6', 'StandardA7', 'StandardA8', 'StandardA9', 'StandardA10', 'StandardA11', 'StandardD1', 'StandardD2', 'StandardD3', 'StandardD4', 'StandardD11', 'StandardD12', 'StandardD13', 'StandardD14', 'StandardD1V2', 'StandardD2V2', 'StandardD3V2', 'StandardD4V2', 'StandardD5V2', 'StandardD11V2', 'StandardD12V2', 'StandardD13V2', 'StandardD14V2', 'StandardG1', 'StandardG2', 'StandardG3', 'StandardG4', 'StandardG5', 'StandardDS1', 'StandardDS2', 'StandardDS3', 'StandardDS4', 'StandardDS11', 'StandardDS12', 'StandardDS13', 'StandardDS14', 'StandardGS1', 'StandardGS2', 'StandardGS3', 'StandardGS4', 'StandardGS5'
	VMSize ContainerServiceVMSizeTypes `json:"vmSize,omitempty"`
	// DNSPrefix - DNS prefix to be used to create the FQDN for the agent pool.
	DNSPrefix *string `json:"dnsPrefix,omitempty"`
	// Fqdn - READ-ONLY; FQDN for the agent pool.
	Fqdn *string `json:"fqdn,omitempty"`
}

// ContainerServiceCustomProfile properties to configure a custom container service cluster.
type ContainerServiceCustomProfile struct {
	// Orchestrator - The name of the custom orchestrator to use.
	Orchestrator *string `json:"orchestrator,omitempty"`
}

// ContainerServiceDiagnosticsProfile ...
type ContainerServiceDiagnosticsProfile struct {
	// VMDiagnostics - Profile for the container service VM diagnostic agent.
	VMDiagnostics *ContainerServiceVMDiagnostics `json:"vmDiagnostics,omitempty"`
}

// ContainerServiceLinuxProfile profile for Linux VMs in the container service cluster.
type ContainerServiceLinuxProfile struct {
	// AdminUsername - The administrator username to use for Linux VMs.
	AdminUsername *string `json:"adminUsername,omitempty"`
	// SSH - The ssh key configuration for Linux VMs.
	SSH *ContainerServiceSSHConfiguration `json:"ssh,omitempty"`
}

// ContainerServiceListResult the response from the List Container Services operation.
type ContainerServiceListResult struct {
	autorest.Response `json:"-"`
	// Value - the list of container services.
	Value *[]ContainerService `json:"value,omitempty"`
	// NextLink - The URL to get the next set of container service results.
	NextLink *string `json:"nextLink,omitempty"`
}

// ContainerServiceListResultIterator provides access to a complete listing of ContainerService values.
type ContainerServiceListResultIterator struct {
	i    int
	page ContainerServiceListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *ContainerServiceListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ContainerServiceListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *ContainerServiceListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter ContainerServiceListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter ContainerServiceListResultIterator) Response() ContainerServiceListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter ContainerServiceListResultIterator) Value() ContainerService {
	if !iter.page.NotDone() {
		return ContainerService{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the ContainerServiceListResultIterator type.
func NewContainerServiceListResultIterator(page ContainerServiceListResultPage) ContainerServiceListResultIterator {
	return ContainerServiceListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (cslr ContainerServiceListResult) IsEmpty() bool {
	return cslr.Value == nil || len(*cslr.Value) == 0
}

// containerServiceListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (cslr ContainerServiceListResult) containerServiceListResultPreparer(ctx context.Context) (*http.Request, error) {
	if cslr.NextLink == nil || len(to.String(cslr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(cslr.NextLink)))
}

// ContainerServiceListResultPage contains a page of ContainerService values.
type ContainerServiceListResultPage struct {
	fn   func(context.Context, ContainerServiceListResult) (ContainerServiceListResult, error)
	cslr ContainerServiceListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *ContainerServiceListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ContainerServiceListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.cslr)
	if err != nil {
		return err
	}
	page.cslr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *ContainerServiceListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page ContainerServiceListResultPage) NotDone() bool {
	return !page.cslr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page ContainerServiceListResultPage) Response() ContainerServiceListResult {
	return page.cslr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page ContainerServiceListResultPage) Values() []ContainerService {
	if page.cslr.IsEmpty() {
		return nil
	}
	return *page.cslr.Value
}

// Creates a new instance of the ContainerServiceListResultPage type.
func NewContainerServiceListResultPage(getNextPage func(context.Context, ContainerServiceListResult) (ContainerServiceListResult, error)) ContainerServiceListResultPage {
	return ContainerServiceListResultPage{fn: getNextPage}
}

// ContainerServiceMasterProfile profile for the container service master.
type ContainerServiceMasterProfile struct {
	// Count - Number of masters (VMs) in the container service cluster. Allowed values are 1, 3, and 5. The default value is 1.
	Count *int32 `json:"count,omitempty"`
	// DNSPrefix - DNS prefix to be used to create the FQDN for master.
	DNSPrefix *string `json:"dnsPrefix,omitempty"`
	// Fqdn - READ-ONLY; FQDN for the master.
	Fqdn *string `json:"fqdn,omitempty"`
}

// ContainerServiceOrchestratorProfile profile for the container service orchestrator.
type ContainerServiceOrchestratorProfile struct {
	// OrchestratorType - The orchestrator to use to manage container service cluster resources. Valid values are Swarm, DCOS, and Custom. Possible values include: 'Swarm', 'DCOS', 'Custom', 'Kubernetes'
	OrchestratorType ContainerServiceOrchestratorTypes `json:"orchestratorType,omitempty"`
}

// ContainerServiceProperties properties of the container service.
type ContainerServiceProperties struct {
	// ProvisioningState - READ-ONLY; the current deployment or provisioning state, which only appears in the response.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// OrchestratorProfile - Properties of the orchestrator.
	OrchestratorProfile *ContainerServiceOrchestratorProfile `json:"orchestratorProfile,omitempty"`
	// CustomProfile - Properties for custom clusters.
	CustomProfile *ContainerServiceCustomProfile `json:"customProfile,omitempty"`
	// ServicePrincipalProfile - Properties for cluster service principals.
	ServicePrincipalProfile *ContainerServiceServicePrincipalProfile `json:"servicePrincipalProfile,omitempty"`
	// MasterProfile - Properties of master agents.
	MasterProfile *ContainerServiceMasterProfile `json:"masterProfile,omitempty"`
	// AgentPoolProfiles - Properties of the agent pool.
	AgentPoolProfiles *[]ContainerServiceAgentPoolProfile `json:"agentPoolProfiles,omitempty"`
	// WindowsProfile - Properties of Windows VMs.
	WindowsProfile *ContainerServiceWindowsProfile `json:"windowsProfile,omitempty"`
	// LinuxProfile - Properties of Linux VMs.
	LinuxProfile *ContainerServiceLinuxProfile `json:"linuxProfile,omitempty"`
	// DiagnosticsProfile - Properties of the diagnostic agent.
	DiagnosticsProfile *ContainerServiceDiagnosticsProfile `json:"diagnosticsProfile,omitempty"`
}

// ContainerServicesCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type ContainerServicesCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *ContainerServicesCreateOrUpdateFuture) Result(client ContainerServicesClient) (cs ContainerService, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.ContainerServicesCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.ContainerServicesCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if cs.Response.Response, err = future.GetResult(sender); err == nil && cs.Response.Response.StatusCode != http.StatusNoContent {
		cs, err = client.CreateOrUpdateResponder(cs.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.ContainerServicesCreateOrUpdateFuture", "Result", cs.Response.Response, "Failure responding to request")
		}
	}
	return
}

// ContainerServicesDeleteFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type ContainerServicesDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *ContainerServicesDeleteFuture) Result(client ContainerServicesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.ContainerServicesDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.ContainerServicesDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// ContainerServiceServicePrincipalProfile information about a service principal identity for the cluster
// to use for manipulating Azure APIs.
type ContainerServiceServicePrincipalProfile struct {
	// ClientID - The ID for the service principal.
	ClientID *string `json:"clientId,omitempty"`
	// Secret - The secret password associated with the service principal.
	Secret *string `json:"secret,omitempty"`
}

// ContainerServiceSSHConfiguration SSH configuration for Linux-based VMs running on Azure.
type ContainerServiceSSHConfiguration struct {
	// PublicKeys - the list of SSH public keys used to authenticate with Linux-based VMs.
	PublicKeys *[]ContainerServiceSSHPublicKey `json:"publicKeys,omitempty"`
}

// ContainerServiceSSHPublicKey contains information about SSH certificate public key data.
type ContainerServiceSSHPublicKey struct {
	// KeyData - Certificate public key used to authenticate with VMs through SSH. The certificate must be in PEM format with or without headers.
	KeyData *string `json:"keyData,omitempty"`
}

// ContainerServiceVMDiagnostics profile for diagnostics on the container service VMs.
type ContainerServiceVMDiagnostics struct {
	// Enabled - Whether the VM diagnostic agent is provisioned on the VM.
	Enabled *bool `json:"enabled,omitempty"`
	// StorageURI - READ-ONLY; The URI of the storage account where diagnostics are stored.
	StorageURI *string `json:"storageUri,omitempty"`
}

// ContainerServiceWindowsProfile profile for Windows VMs in the container service cluster.
type ContainerServiceWindowsProfile struct {
	// AdminUsername - The administrator username to use for Windows VMs.
	AdminUsername *string `json:"adminUsername,omitempty"`
	// AdminPassword - The administrator password to use for Windows VMs.
	AdminPassword *string `json:"adminPassword,omitempty"`
}

// CreationData data used when creating a disk.
type CreationData struct {
	// CreateOption - This enumerates the possible sources of a disk's creation. Possible values include: 'Empty', 'Attach', 'FromImage', 'Import', 'Copy', 'Restore'
	CreateOption DiskCreateOption `json:"createOption,omitempty"`
	// StorageAccountID - If createOption is Import, the Azure Resource Manager identifier of the storage account containing the blob to import as a disk. Required only if the blob is in a different subscription
	StorageAccountID *string `json:"storageAccountId,omitempty"`
	// ImageReference - Disk source information.
	ImageReference *ImageDiskReference `json:"imageReference,omitempty"`
	// SourceURI - If createOption is Import, this is the URI of a blob to be imported into a managed disk.
	SourceURI *string `json:"sourceUri,omitempty"`
	// SourceResourceID - If createOption is Copy, this is the ARM id of the source snapshot or disk.
	SourceResourceID *string `json:"sourceResourceId,omitempty"`
}

// DataDisk describes a data disk.
type DataDisk struct {
	// Lun - Specifies the logical unit number of the data disk. This value is used to identify data disks within the VM and therefore must be unique for each data disk attached to a VM.
	Lun *int32 `json:"lun,omitempty"`
	// Name - The disk name.
	Name *string `json:"name,omitempty"`
	// Vhd - The virtual hard disk.
	Vhd *VirtualHardDisk `json:"vhd,omitempty"`
	// Image - The source user image virtual hard disk. The virtual hard disk will be copied before being attached to the virtual machine. If SourceImage is provided, the destination virtual hard drive must not exist.
	Image *VirtualHardDisk `json:"image,omitempty"`
	// Caching - Specifies the caching requirements. <br><br> Possible values are: <br><br> **None** <br><br> **ReadOnly** <br><br> **ReadWrite** <br><br> Default: **None for Standard storage. ReadOnly for Premium storage**. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// WriteAcceleratorEnabled - Specifies whether writeAccelerator should be enabled or disabled on the disk.
	WriteAcceleratorEnabled *bool `json:"writeAcceleratorEnabled,omitempty"`
	// CreateOption - Specifies how the virtual machine should be created.<br><br> Possible values are:<br><br> **Attach** \u2013 This value is used when you are using a specialized disk to create the virtual machine.<br><br> **FromImage** \u2013 This value is used when you are using an image to create the virtual machine. If you are using a platform image, you also use the imageReference element described above. If you are using a marketplace image, you  also use the plan element previously described. Possible values include: 'DiskCreateOptionTypesFromImage', 'DiskCreateOptionTypesEmpty', 'DiskCreateOptionTypesAttach'
	CreateOption DiskCreateOptionTypes `json:"createOption,omitempty"`
	// DiskSizeGB - Specifies the size of an empty data disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// ManagedDisk - The managed disk parameters.
	ManagedDisk *ManagedDiskParameters `json:"managedDisk,omitempty"`
}

// DataDiskImage contains the data disk images information.
type DataDiskImage struct {
	// Lun - READ-ONLY; Specifies the logical unit number of the data disk. This value is used to identify data disks within the VM and therefore must be unique for each data disk attached to a VM.
	Lun *int32 `json:"lun,omitempty"`
}

// DiagnosticsProfile specifies the boot diagnostic settings state. <br><br>Minimum api-version:
// 2015-06-15.
type DiagnosticsProfile struct {
	// BootDiagnostics - Boot Diagnostics is a debugging feature which allows you to view Console Output and Screenshot to diagnose VM status. <br><br> You can easily view the output of your console log. <br><br> Azure also enables you to see a screenshot of the VM from the hypervisor.
	BootDiagnostics *BootDiagnostics `json:"bootDiagnostics,omitempty"`
}

// DiffDiskSettings describes the parameters of ephemeral disk settings that can be specified for operating
// system disk. <br><br> NOTE: The ephemeral disk settings can only be specified for managed disk.
type DiffDiskSettings struct {
	// Option - Specifies the ephemeral disk settings for operating system disk. Possible values include: 'Local'
	Option DiffDiskOptions `json:"option,omitempty"`
}

// Disallowed describes the disallowed disk types.
type Disallowed struct {
	// DiskTypes - A list of disk types.
	DiskTypes *[]string `json:"diskTypes,omitempty"`
}

// Disk disk resource.
type Disk struct {
	autorest.Response `json:"-"`
	// ManagedBy - READ-ONLY; A relative URI containing the ID of the VM that has the disk attached.
	ManagedBy *string  `json:"managedBy,omitempty"`
	Sku       *DiskSku `json:"sku,omitempty"`
	// Zones - The Logical zone list for Disk.
	Zones           *[]string `json:"zones,omitempty"`
	*DiskProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for Disk.
func (d Disk) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if d.Sku != nil {
		objectMap["sku"] = d.Sku
	}
	if d.Zones != nil {
		objectMap["zones"] = d.Zones
	}
	if d.DiskProperties != nil {
		objectMap["properties"] = d.DiskProperties
	}
	if d.Location != nil {
		objectMap["location"] = d.Location
	}
	if d.Tags != nil {
		objectMap["tags"] = d.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for Disk struct.
func (d *Disk) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "managedBy":
			if v != nil {
				var managedBy string
				err = json.Unmarshal(*v, &managedBy)
				if err != nil {
					return err
				}
				d.ManagedBy = &managedBy
			}
		case "sku":
			if v != nil {
				var sku DiskSku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				d.Sku = &sku
			}
		case "zones":
			if v != nil {
				var zones []string
				err = json.Unmarshal(*v, &zones)
				if err != nil {
					return err
				}
				d.Zones = &zones
			}
		case "properties":
			if v != nil {
				var diskProperties DiskProperties
				err = json.Unmarshal(*v, &diskProperties)
				if err != nil {
					return err
				}
				d.DiskProperties = &diskProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				d.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				d.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				d.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				d.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				d.Tags = tags
			}
		}
	}

	return nil
}

// DiskEncryptionSettings describes a Encryption Settings for a Disk
type DiskEncryptionSettings struct {
	// DiskEncryptionKey - Specifies the location of the disk encryption key, which is a Key Vault Secret.
	DiskEncryptionKey *KeyVaultSecretReference `json:"diskEncryptionKey,omitempty"`
	// KeyEncryptionKey - Specifies the location of the key encryption key in Key Vault.
	KeyEncryptionKey *KeyVaultKeyReference `json:"keyEncryptionKey,omitempty"`
	// Enabled - Specifies whether disk encryption should be enabled on the virtual machine.
	Enabled *bool `json:"enabled,omitempty"`
}

// DiskInstanceView the instance view of the disk.
type DiskInstanceView struct {
	// Name - The disk name.
	Name *string `json:"name,omitempty"`
	// EncryptionSettings - Specifies the encryption settings for the OS Disk. <br><br> Minimum api-version: 2015-06-15
	EncryptionSettings *[]DiskEncryptionSettings `json:"encryptionSettings,omitempty"`
	// Statuses - The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
}

// DiskList the List Disks operation response.
type DiskList struct {
	autorest.Response `json:"-"`
	// Value - A list of disks.
	Value *[]Disk `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of disks. Call ListNext() with this to fetch the next page of disks.
	NextLink *string `json:"nextLink,omitempty"`
}

// DiskListIterator provides access to a complete listing of Disk values.
type DiskListIterator struct {
	i    int
	page DiskListPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *DiskListIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/DiskListIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *DiskListIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter DiskListIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter DiskListIterator) Response() DiskList {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter DiskListIterator) Value() Disk {
	if !iter.page.NotDone() {
		return Disk{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the DiskListIterator type.
func NewDiskListIterator(page DiskListPage) DiskListIterator {
	return DiskListIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (dl DiskList) IsEmpty() bool {
	return dl.Value == nil || len(*dl.Value) == 0
}

// diskListPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (dl DiskList) diskListPreparer(ctx context.Context) (*http.Request, error) {
	if dl.NextLink == nil || len(to.String(dl.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(dl.NextLink)))
}

// DiskListPage contains a page of Disk values.
type DiskListPage struct {
	fn func(context.Context, DiskList) (DiskList, error)
	dl DiskList
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *DiskListPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/DiskListPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.dl)
	if err != nil {
		return err
	}
	page.dl = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *DiskListPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page DiskListPage) NotDone() bool {
	return !page.dl.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page DiskListPage) Response() DiskList {
	return page.dl
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page DiskListPage) Values() []Disk {
	if page.dl.IsEmpty() {
		return nil
	}
	return *page.dl.Value
}

// Creates a new instance of the DiskListPage type.
func NewDiskListPage(getNextPage func(context.Context, DiskList) (DiskList, error)) DiskListPage {
	return DiskListPage{fn: getNextPage}
}

// DiskProperties disk resource properties.
type DiskProperties struct {
	// TimeCreated - READ-ONLY; The time when the disk was created.
	TimeCreated *date.Time `json:"timeCreated,omitempty"`
	// OsType - The Operating System type. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// CreationData - Disk source information. CreationData information cannot be changed after the disk has been created.
	CreationData *CreationData `json:"creationData,omitempty"`
	// DiskSizeGB - If creationData.createOption is Empty, this field is mandatory and it indicates the size of the VHD to create. If this field is present for updates or creation with other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a running VM, and can only increase the disk's size.
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// EncryptionSettings - Encryption settings for disk or snapshot
	EncryptionSettings *EncryptionSettings `json:"encryptionSettings,omitempty"`
	// ProvisioningState - READ-ONLY; The disk provisioning state.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// DiskIOPSReadWrite - The number of IOPS allowed for this disk; only settable for UltraSSD disks. One operation can transfer between 4k and 256k bytes. For a description of the range of values you can set, see [Ultra SSD Managed Disk Offerings](https://docs.microsoft.com/azure/virtual-machines/windows/disks-ultra-ssd#ultra-ssd-managed-disk-offerings).
	DiskIOPSReadWrite *int64 `json:"diskIOPSReadWrite,omitempty"`
	// DiskMBpsReadWrite - The bandwidth allowed for this disk; only settable for UltraSSD disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of 10. For a description of the range of values you can set, see [Ultra SSD Managed Disk Offerings](https://docs.microsoft.com/azure/virtual-machines/windows/disks-ultra-ssd#ultra-ssd-managed-disk-offerings).
	DiskMBpsReadWrite *int32 `json:"diskMBpsReadWrite,omitempty"`
}

// DisksCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type DisksCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *DisksCreateOrUpdateFuture) Result(client DisksClient) (d Disk, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.DisksCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.DisksCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if d.Response.Response, err = future.GetResult(sender); err == nil && d.Response.Response.StatusCode != http.StatusNoContent {
		d, err = client.CreateOrUpdateResponder(d.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.DisksCreateOrUpdateFuture", "Result", d.Response.Response, "Failure responding to request")
		}
	}
	return
}

// DisksDeleteFuture an abstraction for monitoring and retrieving the results of a long-running operation.
type DisksDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *DisksDeleteFuture) Result(client DisksClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.DisksDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.DisksDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// DisksGrantAccessFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type DisksGrantAccessFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *DisksGrantAccessFuture) Result(client DisksClient) (au AccessURI, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.DisksGrantAccessFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.DisksGrantAccessFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if au.Response.Response, err = future.GetResult(sender); err == nil && au.Response.Response.StatusCode != http.StatusNoContent {
		au, err = client.GrantAccessResponder(au.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.DisksGrantAccessFuture", "Result", au.Response.Response, "Failure responding to request")
		}
	}
	return
}

// DiskSku the disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS, or UltraSSD_LRS.
type DiskSku struct {
	// Name - The sku name. Possible values include: 'StandardLRS', 'PremiumLRS', 'StandardSSDLRS', 'UltraSSDLRS'
	Name DiskStorageAccountTypes `json:"name,omitempty"`
	// Tier - READ-ONLY; The sku tier.
	Tier *string `json:"tier,omitempty"`
}

// DisksRevokeAccessFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type DisksRevokeAccessFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *DisksRevokeAccessFuture) Result(client DisksClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.DisksRevokeAccessFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.DisksRevokeAccessFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// DisksUpdateFuture an abstraction for monitoring and retrieving the results of a long-running operation.
type DisksUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *DisksUpdateFuture) Result(client DisksClient) (d Disk, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.DisksUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.DisksUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if d.Response.Response, err = future.GetResult(sender); err == nil && d.Response.Response.StatusCode != http.StatusNoContent {
		d, err = client.UpdateResponder(d.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.DisksUpdateFuture", "Result", d.Response.Response, "Failure responding to request")
		}
	}
	return
}

// DiskUpdate disk update resource.
type DiskUpdate struct {
	*DiskUpdateProperties `json:"properties,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
	Sku  *DiskSku           `json:"sku,omitempty"`
}

// MarshalJSON is the custom marshaler for DiskUpdate.
func (du DiskUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if du.DiskUpdateProperties != nil {
		objectMap["properties"] = du.DiskUpdateProperties
	}
	if du.Tags != nil {
		objectMap["tags"] = du.Tags
	}
	if du.Sku != nil {
		objectMap["sku"] = du.Sku
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for DiskUpdate struct.
func (du *DiskUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var diskUpdateProperties DiskUpdateProperties
				err = json.Unmarshal(*v, &diskUpdateProperties)
				if err != nil {
					return err
				}
				du.DiskUpdateProperties = &diskUpdateProperties
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				du.Tags = tags
			}
		case "sku":
			if v != nil {
				var sku DiskSku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				du.Sku = &sku
			}
		}
	}

	return nil
}

// DiskUpdateProperties disk resource update properties.
type DiskUpdateProperties struct {
	// OsType - the Operating System type. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// DiskSizeGB - If creationData.createOption is Empty, this field is mandatory and it indicates the size of the VHD to create. If this field is present for updates or creation with other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a running VM, and can only increase the disk's size.
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// EncryptionSettings - Encryption settings for disk or snapshot
	EncryptionSettings *EncryptionSettings `json:"encryptionSettings,omitempty"`
	// DiskIOPSReadWrite - The number of IOPS allowed for this disk; only settable for UltraSSD disks. One operation can transfer between 4k and 256k bytes.
	DiskIOPSReadWrite *int64 `json:"diskIOPSReadWrite,omitempty"`
	// DiskMBpsReadWrite - The bandwidth allowed for this disk; only settable for UltraSSD disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of 10.
	DiskMBpsReadWrite *int32 `json:"diskMBpsReadWrite,omitempty"`
}

// EncryptionSettings encryption settings for disk or snapshot
type EncryptionSettings struct {
	// Enabled - Set this flag to true and provide DiskEncryptionKey and optional KeyEncryptionKey to enable encryption. Set this flag to false and remove DiskEncryptionKey and KeyEncryptionKey to disable encryption. If EncryptionSettings is null in the request object, the existing settings remain unchanged.
	Enabled *bool `json:"enabled,omitempty"`
	// DiskEncryptionKey - Key Vault Secret Url and vault id of the disk encryption key
	DiskEncryptionKey *KeyVaultAndSecretReference `json:"diskEncryptionKey,omitempty"`
	// KeyEncryptionKey - Key Vault Key Url and vault id of the key encryption key
	KeyEncryptionKey *KeyVaultAndKeyReference `json:"keyEncryptionKey,omitempty"`
}

// GalleriesCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type GalleriesCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *GalleriesCreateOrUpdateFuture) Result(client GalleriesClient) (g Gallery, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.GalleriesCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.GalleriesCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if g.Response.Response, err = future.GetResult(sender); err == nil && g.Response.Response.StatusCode != http.StatusNoContent {
		g, err = client.CreateOrUpdateResponder(g.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.GalleriesCreateOrUpdateFuture", "Result", g.Response.Response, "Failure responding to request")
		}
	}
	return
}

// GalleriesDeleteFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type GalleriesDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *GalleriesDeleteFuture) Result(client GalleriesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.GalleriesDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.GalleriesDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// Gallery specifies information about the Shared Image Gallery that you want to create or update.
type Gallery struct {
	autorest.Response  `json:"-"`
	*GalleryProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for Gallery.
func (g Gallery) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if g.GalleryProperties != nil {
		objectMap["properties"] = g.GalleryProperties
	}
	if g.Location != nil {
		objectMap["location"] = g.Location
	}
	if g.Tags != nil {
		objectMap["tags"] = g.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for Gallery struct.
func (g *Gallery) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var galleryProperties GalleryProperties
				err = json.Unmarshal(*v, &galleryProperties)
				if err != nil {
					return err
				}
				g.GalleryProperties = &galleryProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				g.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				g.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				g.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				g.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				g.Tags = tags
			}
		}
	}

	return nil
}

// GalleryArtifactPublishingProfileBase describes the basic gallery artifact publishing profile.
type GalleryArtifactPublishingProfileBase struct {
	// TargetRegions - The target regions where the Image Version is going to be replicated to. This property is updatable.
	TargetRegions *[]TargetRegion        `json:"targetRegions,omitempty"`
	Source        *GalleryArtifactSource `json:"source,omitempty"`
}

// GalleryArtifactSource the source image from which the Image Version is going to be created.
type GalleryArtifactSource struct {
	ManagedImage *ManagedArtifact `json:"managedImage,omitempty"`
}

// GalleryDataDiskImage this is the data disk image.
type GalleryDataDiskImage struct {
	// Lun - READ-ONLY; This property specifies the logical unit number of the data disk. This value is used to identify data disks within the Virtual Machine and therefore must be unique for each data disk attached to the Virtual Machine.
	Lun *int32 `json:"lun,omitempty"`
	// SizeInGB - READ-ONLY; This property indicates the size of the VHD to be created.
	SizeInGB *int32 `json:"sizeInGB,omitempty"`
	// HostCaching - READ-ONLY; The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'. Possible values include: 'HostCachingNone', 'HostCachingReadOnly', 'HostCachingReadWrite'
	HostCaching HostCaching `json:"hostCaching,omitempty"`
}

// GalleryDiskImage this is the disk image base class.
type GalleryDiskImage struct {
	// SizeInGB - READ-ONLY; This property indicates the size of the VHD to be created.
	SizeInGB *int32 `json:"sizeInGB,omitempty"`
	// HostCaching - READ-ONLY; The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'. Possible values include: 'HostCachingNone', 'HostCachingReadOnly', 'HostCachingReadWrite'
	HostCaching HostCaching `json:"hostCaching,omitempty"`
}

// GalleryIdentifier describes the gallery unique name.
type GalleryIdentifier struct {
	// UniqueName - READ-ONLY; The unique name of the Shared Image Gallery. This name is generated automatically by Azure.
	UniqueName *string `json:"uniqueName,omitempty"`
}

// GalleryImage specifies information about the gallery Image Definition that you want to create or update.
type GalleryImage struct {
	autorest.Response       `json:"-"`
	*GalleryImageProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for GalleryImage.
func (gi GalleryImage) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if gi.GalleryImageProperties != nil {
		objectMap["properties"] = gi.GalleryImageProperties
	}
	if gi.Location != nil {
		objectMap["location"] = gi.Location
	}
	if gi.Tags != nil {
		objectMap["tags"] = gi.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for GalleryImage struct.
func (gi *GalleryImage) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var galleryImageProperties GalleryImageProperties
				err = json.Unmarshal(*v, &galleryImageProperties)
				if err != nil {
					return err
				}
				gi.GalleryImageProperties = &galleryImageProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				gi.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				gi.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				gi.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				gi.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				gi.Tags = tags
			}
		}
	}

	return nil
}

// GalleryImageIdentifier this is the gallery Image Definition identifier.
type GalleryImageIdentifier struct {
	// Publisher - The name of the gallery Image Definition publisher.
	Publisher *string `json:"publisher,omitempty"`
	// Offer - The name of the gallery Image Definition offer.
	Offer *string `json:"offer,omitempty"`
	// Sku - The name of the gallery Image Definition SKU.
	Sku *string `json:"sku,omitempty"`
}

// GalleryImageList the List Gallery Images operation response.
type GalleryImageList struct {
	autorest.Response `json:"-"`
	// Value - A list of Shared Image Gallery images.
	Value *[]GalleryImage `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of Image Definitions in the Shared Image Gallery. Call ListNext() with this to fetch the next page of gallery Image Definitions.
	NextLink *string `json:"nextLink,omitempty"`
}

// GalleryImageListIterator provides access to a complete listing of GalleryImage values.
type GalleryImageListIterator struct {
	i    int
	page GalleryImageListPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *GalleryImageListIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/GalleryImageListIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *GalleryImageListIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter GalleryImageListIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter GalleryImageListIterator) Response() GalleryImageList {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter GalleryImageListIterator) Value() GalleryImage {
	if !iter.page.NotDone() {
		return GalleryImage{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the GalleryImageListIterator type.
func NewGalleryImageListIterator(page GalleryImageListPage) GalleryImageListIterator {
	return GalleryImageListIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (gil GalleryImageList) IsEmpty() bool {
	return gil.Value == nil || len(*gil.Value) == 0
}

// galleryImageListPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (gil GalleryImageList) galleryImageListPreparer(ctx context.Context) (*http.Request, error) {
	if gil.NextLink == nil || len(to.String(gil.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(gil.NextLink)))
}

// GalleryImageListPage contains a page of GalleryImage values.
type GalleryImageListPage struct {
	fn  func(context.Context, GalleryImageList) (GalleryImageList, error)
	gil GalleryImageList
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *GalleryImageListPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/GalleryImageListPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.gil)
	if err != nil {
		return err
	}
	page.gil = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *GalleryImageListPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page GalleryImageListPage) NotDone() bool {
	return !page.gil.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page GalleryImageListPage) Response() GalleryImageList {
	return page.gil
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page GalleryImageListPage) Values() []GalleryImage {
	if page.gil.IsEmpty() {
		return nil
	}
	return *page.gil.Value
}

// Creates a new instance of the GalleryImageListPage type.
func NewGalleryImageListPage(getNextPage func(context.Context, GalleryImageList) (GalleryImageList, error)) GalleryImageListPage {
	return GalleryImageListPage{fn: getNextPage}
}

// GalleryImageProperties describes the properties of a gallery Image Definition.
type GalleryImageProperties struct {
	// Description - The description of this gallery Image Definition resource. This property is updatable.
	Description *string `json:"description,omitempty"`
	// Eula - The Eula agreement for the gallery Image Definition.
	Eula *string `json:"eula,omitempty"`
	// PrivacyStatementURI - The privacy statement uri.
	PrivacyStatementURI *string `json:"privacyStatementUri,omitempty"`
	// ReleaseNoteURI - The release note uri.
	ReleaseNoteURI *string `json:"releaseNoteUri,omitempty"`
	// OsType - This property allows you to specify the type of the OS that is included in the disk when creating a VM from a managed image. <br><br> Possible values are: <br><br> **Windows** <br><br> **Linux**. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// OsState - The allowed values for OS State are 'Generalized'. Possible values include: 'Generalized', 'Specialized'
	OsState OperatingSystemStateTypes `json:"osState,omitempty"`
	// EndOfLifeDate - The end of life date of the gallery Image Definition. This property can be used for decommissioning purposes. This property is updatable.
	EndOfLifeDate *date.Time                       `json:"endOfLifeDate,omitempty"`
	Identifier    *GalleryImageIdentifier          `json:"identifier,omitempty"`
	Recommended   *RecommendedMachineConfiguration `json:"recommended,omitempty"`
	Disallowed    *Disallowed                      `json:"disallowed,omitempty"`
	PurchasePlan  *ImagePurchasePlan               `json:"purchasePlan,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response. Possible values include: 'ProvisioningState1Creating', 'ProvisioningState1Updating', 'ProvisioningState1Failed', 'ProvisioningState1Succeeded', 'ProvisioningState1Deleting', 'ProvisioningState1Migrating'
	ProvisioningState ProvisioningState1 `json:"provisioningState,omitempty"`
}

// GalleryImagesCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type GalleryImagesCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *GalleryImagesCreateOrUpdateFuture) Result(client GalleryImagesClient) (gi GalleryImage, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.GalleryImagesCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.GalleryImagesCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if gi.Response.Response, err = future.GetResult(sender); err == nil && gi.Response.Response.StatusCode != http.StatusNoContent {
		gi, err = client.CreateOrUpdateResponder(gi.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.GalleryImagesCreateOrUpdateFuture", "Result", gi.Response.Response, "Failure responding to request")
		}
	}
	return
}

// GalleryImagesDeleteFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type GalleryImagesDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *GalleryImagesDeleteFuture) Result(client GalleryImagesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.GalleryImagesDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.GalleryImagesDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// GalleryImageVersion specifies information about the gallery Image Version that you want to create or
// update.
type GalleryImageVersion struct {
	autorest.Response              `json:"-"`
	*GalleryImageVersionProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for GalleryImageVersion.
func (giv GalleryImageVersion) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if giv.GalleryImageVersionProperties != nil {
		objectMap["properties"] = giv.GalleryImageVersionProperties
	}
	if giv.Location != nil {
		objectMap["location"] = giv.Location
	}
	if giv.Tags != nil {
		objectMap["tags"] = giv.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for GalleryImageVersion struct.
func (giv *GalleryImageVersion) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var galleryImageVersionProperties GalleryImageVersionProperties
				err = json.Unmarshal(*v, &galleryImageVersionProperties)
				if err != nil {
					return err
				}
				giv.GalleryImageVersionProperties = &galleryImageVersionProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				giv.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				giv.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				giv.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				giv.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				giv.Tags = tags
			}
		}
	}

	return nil
}

// GalleryImageVersionList the List Gallery Image version operation response.
type GalleryImageVersionList struct {
	autorest.Response `json:"-"`
	// Value - A list of gallery Image Versions.
	Value *[]GalleryImageVersion `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of gallery Image Versions. Call ListNext() with this to fetch the next page of gallery Image Versions.
	NextLink *string `json:"nextLink,omitempty"`
}

// GalleryImageVersionListIterator provides access to a complete listing of GalleryImageVersion values.
type GalleryImageVersionListIterator struct {
	i    int
	page GalleryImageVersionListPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *GalleryImageVersionListIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/GalleryImageVersionListIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *GalleryImageVersionListIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter GalleryImageVersionListIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter GalleryImageVersionListIterator) Response() GalleryImageVersionList {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter GalleryImageVersionListIterator) Value() GalleryImageVersion {
	if !iter.page.NotDone() {
		return GalleryImageVersion{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the GalleryImageVersionListIterator type.
func NewGalleryImageVersionListIterator(page GalleryImageVersionListPage) GalleryImageVersionListIterator {
	return GalleryImageVersionListIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (givl GalleryImageVersionList) IsEmpty() bool {
	return givl.Value == nil || len(*givl.Value) == 0
}

// galleryImageVersionListPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (givl GalleryImageVersionList) galleryImageVersionListPreparer(ctx context.Context) (*http.Request, error) {
	if givl.NextLink == nil || len(to.String(givl.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(givl.NextLink)))
}

// GalleryImageVersionListPage contains a page of GalleryImageVersion values.
type GalleryImageVersionListPage struct {
	fn   func(context.Context, GalleryImageVersionList) (GalleryImageVersionList, error)
	givl GalleryImageVersionList
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *GalleryImageVersionListPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/GalleryImageVersionListPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.givl)
	if err != nil {
		return err
	}
	page.givl = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *GalleryImageVersionListPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page GalleryImageVersionListPage) NotDone() bool {
	return !page.givl.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page GalleryImageVersionListPage) Response() GalleryImageVersionList {
	return page.givl
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page GalleryImageVersionListPage) Values() []GalleryImageVersion {
	if page.givl.IsEmpty() {
		return nil
	}
	return *page.givl.Value
}

// Creates a new instance of the GalleryImageVersionListPage type.
func NewGalleryImageVersionListPage(getNextPage func(context.Context, GalleryImageVersionList) (GalleryImageVersionList, error)) GalleryImageVersionListPage {
	return GalleryImageVersionListPage{fn: getNextPage}
}

// GalleryImageVersionProperties describes the properties of a gallery Image Version.
type GalleryImageVersionProperties struct {
	PublishingProfile *GalleryImageVersionPublishingProfile `json:"publishingProfile,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response. Possible values include: 'ProvisioningState2Creating', 'ProvisioningState2Updating', 'ProvisioningState2Failed', 'ProvisioningState2Succeeded', 'ProvisioningState2Deleting', 'ProvisioningState2Migrating'
	ProvisioningState ProvisioningState2 `json:"provisioningState,omitempty"`
	// StorageProfile - READ-ONLY
	StorageProfile *GalleryImageVersionStorageProfile `json:"storageProfile,omitempty"`
	// ReplicationStatus - READ-ONLY
	ReplicationStatus *ReplicationStatus `json:"replicationStatus,omitempty"`
}

// GalleryImageVersionPublishingProfile the publishing profile of a gallery Image Version.
type GalleryImageVersionPublishingProfile struct {
	// ReplicaCount - The number of replicas of the Image Version to be created per region. This property would take effect for a region when regionalReplicaCount is not specified. This property is updatable.
	ReplicaCount *int32 `json:"replicaCount,omitempty"`
	// ExcludeFromLatest - If set to true, Virtual Machines deployed from the latest version of the Image Definition won't use this Image Version.
	ExcludeFromLatest *bool `json:"excludeFromLatest,omitempty"`
	// PublishedDate - READ-ONLY; The timestamp for when the gallery Image Version is published.
	PublishedDate *date.Time `json:"publishedDate,omitempty"`
	// EndOfLifeDate - The end of life date of the gallery Image Version. This property can be used for decommissioning purposes. This property is updatable.
	EndOfLifeDate *date.Time `json:"endOfLifeDate,omitempty"`
	// TargetRegions - The target regions where the Image Version is going to be replicated to. This property is updatable.
	TargetRegions *[]TargetRegion        `json:"targetRegions,omitempty"`
	Source        *GalleryArtifactSource `json:"source,omitempty"`
}

// GalleryImageVersionsCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type GalleryImageVersionsCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *GalleryImageVersionsCreateOrUpdateFuture) Result(client GalleryImageVersionsClient) (giv GalleryImageVersion, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.GalleryImageVersionsCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.GalleryImageVersionsCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if giv.Response.Response, err = future.GetResult(sender); err == nil && giv.Response.Response.StatusCode != http.StatusNoContent {
		giv, err = client.CreateOrUpdateResponder(giv.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.GalleryImageVersionsCreateOrUpdateFuture", "Result", giv.Response.Response, "Failure responding to request")
		}
	}
	return
}

// GalleryImageVersionsDeleteFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type GalleryImageVersionsDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *GalleryImageVersionsDeleteFuture) Result(client GalleryImageVersionsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.GalleryImageVersionsDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.GalleryImageVersionsDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// GalleryImageVersionStorageProfile this is the storage profile of a gallery Image Version.
type GalleryImageVersionStorageProfile struct {
	// OsDiskImage - READ-ONLY
	OsDiskImage *GalleryOSDiskImage `json:"osDiskImage,omitempty"`
	// DataDiskImages - READ-ONLY; A list of data disk images.
	DataDiskImages *[]GalleryDataDiskImage `json:"dataDiskImages,omitempty"`
}

// GalleryList the List Galleries operation response.
type GalleryList struct {
	autorest.Response `json:"-"`
	// Value - A list of galleries.
	Value *[]Gallery `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of galleries. Call ListNext() with this to fetch the next page of galleries.
	NextLink *string `json:"nextLink,omitempty"`
}

// GalleryListIterator provides access to a complete listing of Gallery values.
type GalleryListIterator struct {
	i    int
	page GalleryListPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *GalleryListIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/GalleryListIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *GalleryListIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter GalleryListIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter GalleryListIterator) Response() GalleryList {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter GalleryListIterator) Value() Gallery {
	if !iter.page.NotDone() {
		return Gallery{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the GalleryListIterator type.
func NewGalleryListIterator(page GalleryListPage) GalleryListIterator {
	return GalleryListIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (gl GalleryList) IsEmpty() bool {
	return gl.Value == nil || len(*gl.Value) == 0
}

// galleryListPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (gl GalleryList) galleryListPreparer(ctx context.Context) (*http.Request, error) {
	if gl.NextLink == nil || len(to.String(gl.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(gl.NextLink)))
}

// GalleryListPage contains a page of Gallery values.
type GalleryListPage struct {
	fn func(context.Context, GalleryList) (GalleryList, error)
	gl GalleryList
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *GalleryListPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/GalleryListPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.gl)
	if err != nil {
		return err
	}
	page.gl = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *GalleryListPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page GalleryListPage) NotDone() bool {
	return !page.gl.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page GalleryListPage) Response() GalleryList {
	return page.gl
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page GalleryListPage) Values() []Gallery {
	if page.gl.IsEmpty() {
		return nil
	}
	return *page.gl.Value
}

// Creates a new instance of the GalleryListPage type.
func NewGalleryListPage(getNextPage func(context.Context, GalleryList) (GalleryList, error)) GalleryListPage {
	return GalleryListPage{fn: getNextPage}
}

// GalleryOSDiskImage this is the OS disk image.
type GalleryOSDiskImage struct {
	// SizeInGB - READ-ONLY; This property indicates the size of the VHD to be created.
	SizeInGB *int32 `json:"sizeInGB,omitempty"`
	// HostCaching - READ-ONLY; The host caching of the disk. Valid values are 'None', 'ReadOnly', and 'ReadWrite'. Possible values include: 'HostCachingNone', 'HostCachingReadOnly', 'HostCachingReadWrite'
	HostCaching HostCaching `json:"hostCaching,omitempty"`
}

// GalleryProperties describes the properties of a Shared Image Gallery.
type GalleryProperties struct {
	// Description - The description of this Shared Image Gallery resource. This property is updatable.
	Description *string            `json:"description,omitempty"`
	Identifier  *GalleryIdentifier `json:"identifier,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response. Possible values include: 'ProvisioningStateCreating', 'ProvisioningStateUpdating', 'ProvisioningStateFailed', 'ProvisioningStateSucceeded', 'ProvisioningStateDeleting', 'ProvisioningStateMigrating'
	ProvisioningState ProvisioningState `json:"provisioningState,omitempty"`
}

// GrantAccessData data used for requesting a SAS.
type GrantAccessData struct {
	// Access - Possible values include: 'None', 'Read'
	Access AccessLevel `json:"access,omitempty"`
	// DurationInSeconds - Time duration in seconds until the SAS access expires.
	DurationInSeconds *int32 `json:"durationInSeconds,omitempty"`
}

// HardwareProfile specifies the hardware settings for the virtual machine.
type HardwareProfile struct {
	// VMSize - Specifies the size of the virtual machine. For more information about virtual machine sizes, see [Sizes for virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-sizes?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json). <br><br> The available VM sizes depend on region and availability set. For a list of available sizes use these APIs:  <br><br> [List all available virtual machine sizes in an availability set](https://docs.microsoft.com/rest/api/compute/availabilitysets/listavailablesizes) <br><br> [List all available virtual machine sizes in a region](https://docs.microsoft.com/rest/api/compute/virtualmachinesizes/list) <br><br> [List all available virtual machine sizes for resizing](https://docs.microsoft.com/rest/api/compute/virtualmachines/listavailablesizes). Possible values include: 'VirtualMachineSizeTypesBasicA0', 'VirtualMachineSizeTypesBasicA1', 'VirtualMachineSizeTypesBasicA2', 'VirtualMachineSizeTypesBasicA3', 'VirtualMachineSizeTypesBasicA4', 'VirtualMachineSizeTypesStandardA0', 'VirtualMachineSizeTypesStandardA1', 'VirtualMachineSizeTypesStandardA2', 'VirtualMachineSizeTypesStandardA3', 'VirtualMachineSizeTypesStandardA4', 'VirtualMachineSizeTypesStandardA5', 'VirtualMachineSizeTypesStandardA6', 'VirtualMachineSizeTypesStandardA7', 'VirtualMachineSizeTypesStandardA8', 'VirtualMachineSizeTypesStandardA9', 'VirtualMachineSizeTypesStandardA10', 'VirtualMachineSizeTypesStandardA11', 'VirtualMachineSizeTypesStandardA1V2', 'VirtualMachineSizeTypesStandardA2V2', 'VirtualMachineSizeTypesStandardA4V2', 'VirtualMachineSizeTypesStandardA8V2', 'VirtualMachineSizeTypesStandardA2mV2', 'VirtualMachineSizeTypesStandardA4mV2', 'VirtualMachineSizeTypesStandardA8mV2', 'VirtualMachineSizeTypesStandardB1s', 'VirtualMachineSizeTypesStandardB1ms', 'VirtualMachineSizeTypesStandardB2s', 'VirtualMachineSizeTypesStandardB2ms', 'VirtualMachineSizeTypesStandardB4ms', 'VirtualMachineSizeTypesStandardB8ms', 'VirtualMachineSizeTypesStandardD1', 'VirtualMachineSizeTypesStandardD2', 'VirtualMachineSizeTypesStandardD3', 'VirtualMachineSizeTypesStandardD4', 'VirtualMachineSizeTypesStandardD11', 'VirtualMachineSizeTypesStandardD12', 'VirtualMachineSizeTypesStandardD13', 'VirtualMachineSizeTypesStandardD14', 'VirtualMachineSizeTypesStandardD1V2', 'VirtualMachineSizeTypesStandardD2V2', 'VirtualMachineSizeTypesStandardD3V2', 'VirtualMachineSizeTypesStandardD4V2', 'VirtualMachineSizeTypesStandardD5V2', 'VirtualMachineSizeTypesStandardD2V3', 'VirtualMachineSizeTypesStandardD4V3', 'VirtualMachineSizeTypesStandardD8V3', 'VirtualMachineSizeTypesStandardD16V3', 'VirtualMachineSizeTypesStandardD32V3', 'VirtualMachineSizeTypesStandardD64V3', 'VirtualMachineSizeTypesStandardD2sV3', 'VirtualMachineSizeTypesStandardD4sV3', 'VirtualMachineSizeTypesStandardD8sV3', 'VirtualMachineSizeTypesStandardD16sV3', 'VirtualMachineSizeTypesStandardD32sV3', 'VirtualMachineSizeTypesStandardD64sV3', 'VirtualMachineSizeTypesStandardD11V2', 'VirtualMachineSizeTypesStandardD12V2', 'VirtualMachineSizeTypesStandardD13V2', 'VirtualMachineSizeTypesStandardD14V2', 'VirtualMachineSizeTypesStandardD15V2', 'VirtualMachineSizeTypesStandardDS1', 'VirtualMachineSizeTypesStandardDS2', 'VirtualMachineSizeTypesStandardDS3', 'VirtualMachineSizeTypesStandardDS4', 'VirtualMachineSizeTypesStandardDS11', 'VirtualMachineSizeTypesStandardDS12', 'VirtualMachineSizeTypesStandardDS13', 'VirtualMachineSizeTypesStandardDS14', 'VirtualMachineSizeTypesStandardDS1V2', 'VirtualMachineSizeTypesStandardDS2V2', 'VirtualMachineSizeTypesStandardDS3V2', 'VirtualMachineSizeTypesStandardDS4V2', 'VirtualMachineSizeTypesStandardDS5V2', 'VirtualMachineSizeTypesStandardDS11V2', 'VirtualMachineSizeTypesStandardDS12V2', 'VirtualMachineSizeTypesStandardDS13V2', 'VirtualMachineSizeTypesStandardDS14V2', 'VirtualMachineSizeTypesStandardDS15V2', 'VirtualMachineSizeTypesStandardDS134V2', 'VirtualMachineSizeTypesStandardDS132V2', 'VirtualMachineSizeTypesStandardDS148V2', 'VirtualMachineSizeTypesStandardDS144V2', 'VirtualMachineSizeTypesStandardE2V3', 'VirtualMachineSizeTypesStandardE4V3', 'VirtualMachineSizeTypesStandardE8V3', 'VirtualMachineSizeTypesStandardE16V3', 'VirtualMachineSizeTypesStandardE32V3', 'VirtualMachineSizeTypesStandardE64V3', 'VirtualMachineSizeTypesStandardE2sV3', 'VirtualMachineSizeTypesStandardE4sV3', 'VirtualMachineSizeTypesStandardE8sV3', 'VirtualMachineSizeTypesStandardE16sV3', 'VirtualMachineSizeTypesStandardE32sV3', 'VirtualMachineSizeTypesStandardE64sV3', 'VirtualMachineSizeTypesStandardE3216V3', 'VirtualMachineSizeTypesStandardE328sV3', 'VirtualMachineSizeTypesStandardE6432sV3', 'VirtualMachineSizeTypesStandardE6416sV3', 'VirtualMachineSizeTypesStandardF1', 'VirtualMachineSizeTypesStandardF2', 'VirtualMachineSizeTypesStandardF4', 'VirtualMachineSizeTypesStandardF8', 'VirtualMachineSizeTypesStandardF16', 'VirtualMachineSizeTypesStandardF1s', 'VirtualMachineSizeTypesStandardF2s', 'VirtualMachineSizeTypesStandardF4s', 'VirtualMachineSizeTypesStandardF8s', 'VirtualMachineSizeTypesStandardF16s', 'VirtualMachineSizeTypesStandardF2sV2', 'VirtualMachineSizeTypesStandardF4sV2', 'VirtualMachineSizeTypesStandardF8sV2', 'VirtualMachineSizeTypesStandardF16sV2', 'VirtualMachineSizeTypesStandardF32sV2', 'VirtualMachineSizeTypesStandardF64sV2', 'VirtualMachineSizeTypesStandardF72sV2', 'VirtualMachineSizeTypesStandardG1', 'VirtualMachineSizeTypesStandardG2', 'VirtualMachineSizeTypesStandardG3', 'VirtualMachineSizeTypesStandardG4', 'VirtualMachineSizeTypesStandardG5', 'VirtualMachineSizeTypesStandardGS1', 'VirtualMachineSizeTypesStandardGS2', 'VirtualMachineSizeTypesStandardGS3', 'VirtualMachineSizeTypesStandardGS4', 'VirtualMachineSizeTypesStandardGS5', 'VirtualMachineSizeTypesStandardGS48', 'VirtualMachineSizeTypesStandardGS44', 'VirtualMachineSizeTypesStandardGS516', 'VirtualMachineSizeTypesStandardGS58', 'VirtualMachineSizeTypesStandardH8', 'VirtualMachineSizeTypesStandardH16', 'VirtualMachineSizeTypesStandardH8m', 'VirtualMachineSizeTypesStandardH16m', 'VirtualMachineSizeTypesStandardH16r', 'VirtualMachineSizeTypesStandardH16mr', 'VirtualMachineSizeTypesStandardL4s', 'VirtualMachineSizeTypesStandardL8s', 'VirtualMachineSizeTypesStandardL16s', 'VirtualMachineSizeTypesStandardL32s', 'VirtualMachineSizeTypesStandardM64s', 'VirtualMachineSizeTypesStandardM64ms', 'VirtualMachineSizeTypesStandardM128s', 'VirtualMachineSizeTypesStandardM128ms', 'VirtualMachineSizeTypesStandardM6432ms', 'VirtualMachineSizeTypesStandardM6416ms', 'VirtualMachineSizeTypesStandardM12864ms', 'VirtualMachineSizeTypesStandardM12832ms', 'VirtualMachineSizeTypesStandardNC6', 'VirtualMachineSizeTypesStandardNC12', 'VirtualMachineSizeTypesStandardNC24', 'VirtualMachineSizeTypesStandardNC24r', 'VirtualMachineSizeTypesStandardNC6sV2', 'VirtualMachineSizeTypesStandardNC12sV2', 'VirtualMachineSizeTypesStandardNC24sV2', 'VirtualMachineSizeTypesStandardNC24rsV2', 'VirtualMachineSizeTypesStandardNC6sV3', 'VirtualMachineSizeTypesStandardNC12sV3', 'VirtualMachineSizeTypesStandardNC24sV3', 'VirtualMachineSizeTypesStandardNC24rsV3', 'VirtualMachineSizeTypesStandardND6s', 'VirtualMachineSizeTypesStandardND12s', 'VirtualMachineSizeTypesStandardND24s', 'VirtualMachineSizeTypesStandardND24rs', 'VirtualMachineSizeTypesStandardNV6', 'VirtualMachineSizeTypesStandardNV12', 'VirtualMachineSizeTypesStandardNV24'
	VMSize VirtualMachineSizeTypes `json:"vmSize,omitempty"`
}

// Image the source user image virtual hard disk. The virtual hard disk will be copied before being
// attached to the virtual machine. If SourceImage is provided, the destination virtual hard drive must not
// exist.
type Image struct {
	autorest.Response `json:"-"`
	*ImageProperties  `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for Image.
func (i Image) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if i.ImageProperties != nil {
		objectMap["properties"] = i.ImageProperties
	}
	if i.Location != nil {
		objectMap["location"] = i.Location
	}
	if i.Tags != nil {
		objectMap["tags"] = i.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for Image struct.
func (i *Image) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var imageProperties ImageProperties
				err = json.Unmarshal(*v, &imageProperties)
				if err != nil {
					return err
				}
				i.ImageProperties = &imageProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				i.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				i.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				i.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				i.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				i.Tags = tags
			}
		}
	}

	return nil
}

// ImageDataDisk describes a data disk.
type ImageDataDisk struct {
	// Lun - Specifies the logical unit number of the data disk. This value is used to identify data disks within the VM and therefore must be unique for each data disk attached to a VM.
	Lun *int32 `json:"lun,omitempty"`
	// Snapshot - The snapshot.
	Snapshot *SubResource `json:"snapshot,omitempty"`
	// ManagedDisk - The managedDisk.
	ManagedDisk *SubResource `json:"managedDisk,omitempty"`
	// BlobURI - The Virtual Hard Disk.
	BlobURI *string `json:"blobUri,omitempty"`
	// Caching - Specifies the caching requirements. <br><br> Possible values are: <br><br> **None** <br><br> **ReadOnly** <br><br> **ReadWrite** <br><br> Default: **None for Standard storage. ReadOnly for Premium storage**. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// DiskSizeGB - Specifies the size of empty data disks in gigabytes. This element can be used to overwrite the name of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// StorageAccountType - Specifies the storage account type for the managed disk. NOTE: UltraSSD_LRS can only be used with data disks, it cannot be used with OS Disk. Possible values include: 'StorageAccountTypesStandardLRS', 'StorageAccountTypesPremiumLRS', 'StorageAccountTypesStandardSSDLRS', 'StorageAccountTypesUltraSSDLRS'
	StorageAccountType StorageAccountTypes `json:"storageAccountType,omitempty"`
}

// ImageDiskReference the source image used for creating the disk.
type ImageDiskReference struct {
	// ID - A relative uri containing either a Platform Image Repository or user image reference.
	ID *string `json:"id,omitempty"`
	// Lun - If the disk is created from an image's data disk, this is an index that indicates which of the data disks in the image to use. For OS disks, this field is null.
	Lun *int32 `json:"lun,omitempty"`
}

// ImageListResult the List Image operation response.
type ImageListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of Images.
	Value *[]Image `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of Images. Call ListNext() with this to fetch the next page of Images.
	NextLink *string `json:"nextLink,omitempty"`
}

// ImageListResultIterator provides access to a complete listing of Image values.
type ImageListResultIterator struct {
	i    int
	page ImageListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *ImageListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ImageListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *ImageListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter ImageListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter ImageListResultIterator) Response() ImageListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter ImageListResultIterator) Value() Image {
	if !iter.page.NotDone() {
		return Image{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the ImageListResultIterator type.
func NewImageListResultIterator(page ImageListResultPage) ImageListResultIterator {
	return ImageListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (ilr ImageListResult) IsEmpty() bool {
	return ilr.Value == nil || len(*ilr.Value) == 0
}

// imageListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (ilr ImageListResult) imageListResultPreparer(ctx context.Context) (*http.Request, error) {
	if ilr.NextLink == nil || len(to.String(ilr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(ilr.NextLink)))
}

// ImageListResultPage contains a page of Image values.
type ImageListResultPage struct {
	fn  func(context.Context, ImageListResult) (ImageListResult, error)
	ilr ImageListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *ImageListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ImageListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.ilr)
	if err != nil {
		return err
	}
	page.ilr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *ImageListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page ImageListResultPage) NotDone() bool {
	return !page.ilr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page ImageListResultPage) Response() ImageListResult {
	return page.ilr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page ImageListResultPage) Values() []Image {
	if page.ilr.IsEmpty() {
		return nil
	}
	return *page.ilr.Value
}

// Creates a new instance of the ImageListResultPage type.
func NewImageListResultPage(getNextPage func(context.Context, ImageListResult) (ImageListResult, error)) ImageListResultPage {
	return ImageListResultPage{fn: getNextPage}
}

// ImageOSDisk describes an Operating System disk.
type ImageOSDisk struct {
	// OsType - This property allows you to specify the type of the OS that is included in the disk if creating a VM from a custom image. <br><br> Possible values are: <br><br> **Windows** <br><br> **Linux**. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// OsState - The OS State. Possible values include: 'Generalized', 'Specialized'
	OsState OperatingSystemStateTypes `json:"osState,omitempty"`
	// Snapshot - The snapshot.
	Snapshot *SubResource `json:"snapshot,omitempty"`
	// ManagedDisk - The managedDisk.
	ManagedDisk *SubResource `json:"managedDisk,omitempty"`
	// BlobURI - The Virtual Hard Disk.
	BlobURI *string `json:"blobUri,omitempty"`
	// Caching - Specifies the caching requirements. <br><br> Possible values are: <br><br> **None** <br><br> **ReadOnly** <br><br> **ReadWrite** <br><br> Default: **None for Standard storage. ReadOnly for Premium storage**. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// DiskSizeGB - Specifies the size of empty data disks in gigabytes. This element can be used to overwrite the name of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// StorageAccountType - Specifies the storage account type for the managed disk. UltraSSD_LRS cannot be used with OS Disk. Possible values include: 'StorageAccountTypesStandardLRS', 'StorageAccountTypesPremiumLRS', 'StorageAccountTypesStandardSSDLRS', 'StorageAccountTypesUltraSSDLRS'
	StorageAccountType StorageAccountTypes `json:"storageAccountType,omitempty"`
}

// ImageProperties describes the properties of an Image.
type ImageProperties struct {
	// SourceVirtualMachine - The source virtual machine from which Image is created.
	SourceVirtualMachine *SubResource `json:"sourceVirtualMachine,omitempty"`
	// StorageProfile - Specifies the storage settings for the virtual machine disks.
	StorageProfile *ImageStorageProfile `json:"storageProfile,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state.
	ProvisioningState *string `json:"provisioningState,omitempty"`
}

// ImagePurchasePlan describes the gallery Image Definition purchase plan. This is used by marketplace
// images.
type ImagePurchasePlan struct {
	// Name - The plan ID.
	Name *string `json:"name,omitempty"`
	// Publisher - The publisher ID.
	Publisher *string `json:"publisher,omitempty"`
	// Product - The product ID.
	Product *string `json:"product,omitempty"`
}

// ImageReference specifies information about the image to use. You can specify information about platform
// images, marketplace images, or virtual machine images. This element is required when you want to use a
// platform image, marketplace image, or virtual machine image, but is not used in other creation
// operations.
type ImageReference struct {
	// Publisher - The image publisher.
	Publisher *string `json:"publisher,omitempty"`
	// Offer - Specifies the offer of the platform image or marketplace image used to create the virtual machine.
	Offer *string `json:"offer,omitempty"`
	// Sku - The image SKU.
	Sku *string `json:"sku,omitempty"`
	// Version - Specifies the version of the platform image or marketplace image used to create the virtual machine. The allowed formats are Major.Minor.Build or 'latest'. Major, Minor, and Build are decimal numbers. Specify 'latest' to use the latest version of an image available at deploy time. Even if you use 'latest', the VM image will not automatically update after deploy time even if a new version becomes available.
	Version *string `json:"version,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// ImagesCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type ImagesCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *ImagesCreateOrUpdateFuture) Result(client ImagesClient) (i Image, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.ImagesCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.ImagesCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if i.Response.Response, err = future.GetResult(sender); err == nil && i.Response.Response.StatusCode != http.StatusNoContent {
		i, err = client.CreateOrUpdateResponder(i.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.ImagesCreateOrUpdateFuture", "Result", i.Response.Response, "Failure responding to request")
		}
	}
	return
}

// ImagesDeleteFuture an abstraction for monitoring and retrieving the results of a long-running operation.
type ImagesDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *ImagesDeleteFuture) Result(client ImagesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.ImagesDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.ImagesDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// ImageStorageProfile describes a storage profile.
type ImageStorageProfile struct {
	// OsDisk - Specifies information about the operating system disk used by the virtual machine. <br><br> For more information about disks, see [About disks and VHDs for Azure virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
	OsDisk *ImageOSDisk `json:"osDisk,omitempty"`
	// DataDisks - Specifies the parameters that are used to add a data disk to a virtual machine. <br><br> For more information about disks, see [About disks and VHDs for Azure virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
	DataDisks *[]ImageDataDisk `json:"dataDisks,omitempty"`
	// ZoneResilient - Specifies whether an image is zone resilient or not. Default is false. Zone resilient images can be created only in regions that provide Zone Redundant Storage (ZRS).
	ZoneResilient *bool `json:"zoneResilient,omitempty"`
}

// ImagesUpdateFuture an abstraction for monitoring and retrieving the results of a long-running operation.
type ImagesUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *ImagesUpdateFuture) Result(client ImagesClient) (i Image, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.ImagesUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.ImagesUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if i.Response.Response, err = future.GetResult(sender); err == nil && i.Response.Response.StatusCode != http.StatusNoContent {
		i, err = client.UpdateResponder(i.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.ImagesUpdateFuture", "Result", i.Response.Response, "Failure responding to request")
		}
	}
	return
}

// ImageUpdate the source user image virtual hard disk. Only tags may be updated.
type ImageUpdate struct {
	*ImageProperties `json:"properties,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for ImageUpdate.
func (iu ImageUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if iu.ImageProperties != nil {
		objectMap["properties"] = iu.ImageProperties
	}
	if iu.Tags != nil {
		objectMap["tags"] = iu.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for ImageUpdate struct.
func (iu *ImageUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var imageProperties ImageProperties
				err = json.Unmarshal(*v, &imageProperties)
				if err != nil {
					return err
				}
				iu.ImageProperties = &imageProperties
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				iu.Tags = tags
			}
		}
	}

	return nil
}

// InnerError inner error details.
type InnerError struct {
	// Exceptiontype - The exception type.
	Exceptiontype *string `json:"exceptiontype,omitempty"`
	// Errordetail - The internal error message or exception dump.
	Errordetail *string `json:"errordetail,omitempty"`
}

// InstanceViewStatus instance view status.
type InstanceViewStatus struct {
	// Code - The status code.
	Code *string `json:"code,omitempty"`
	// Level - The level code. Possible values include: 'Info', 'Warning', 'Error'
	Level StatusLevelTypes `json:"level,omitempty"`
	// DisplayStatus - The short localizable label for the status.
	DisplayStatus *string `json:"displayStatus,omitempty"`
	// Message - The detailed status message, including for alerts and error messages.
	Message *string `json:"message,omitempty"`
	// Time - The time of the status.
	Time *date.Time `json:"time,omitempty"`
}

// KeyVaultAndKeyReference key Vault Key Url and vault id of KeK, KeK is optional and when provided is used
// to unwrap the encryptionKey
type KeyVaultAndKeyReference struct {
	// SourceVault - Resource id of the KeyVault containing the key or secret
	SourceVault *SourceVault `json:"sourceVault,omitempty"`
	// KeyURL - Url pointing to a key or secret in KeyVault
	KeyURL *string `json:"keyUrl,omitempty"`
}

// KeyVaultAndSecretReference key Vault Secret Url and vault id of the encryption key
type KeyVaultAndSecretReference struct {
	// SourceVault - Resource id of the KeyVault containing the key or secret
	SourceVault *SourceVault `json:"sourceVault,omitempty"`
	// SecretURL - Url pointing to a key or secret in KeyVault
	SecretURL *string `json:"secretUrl,omitempty"`
}

// KeyVaultKeyReference describes a reference to Key Vault Key
type KeyVaultKeyReference struct {
	// KeyURL - The URL referencing a key encryption key in Key Vault.
	KeyURL *string `json:"keyUrl,omitempty"`
	// SourceVault - The relative URL of the Key Vault containing the key.
	SourceVault *SubResource `json:"sourceVault,omitempty"`
}

// KeyVaultSecretReference describes a reference to Key Vault Secret
type KeyVaultSecretReference struct {
	// SecretURL - The URL referencing a secret in a Key Vault.
	SecretURL *string `json:"secretUrl,omitempty"`
	// SourceVault - The relative URL of the Key Vault containing the secret.
	SourceVault *SubResource `json:"sourceVault,omitempty"`
}

// LinuxConfiguration specifies the Linux operating system settings on the virtual machine. <br><br>For a
// list of supported Linux distributions, see [Linux on Azure-Endorsed
// Distributions](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-endorsed-distros?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)
// <br><br> For running non-endorsed distributions, see [Information for Non-Endorsed
// Distributions](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-create-upload-generic?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json).
type LinuxConfiguration struct {
	// DisablePasswordAuthentication - Specifies whether password authentication should be disabled.
	DisablePasswordAuthentication *bool `json:"disablePasswordAuthentication,omitempty"`
	// SSH - Specifies the ssh key configuration for a Linux OS.
	SSH *SSHConfiguration `json:"ssh,omitempty"`
	// ProvisionVMAgent - Indicates whether virtual machine agent should be provisioned on the virtual machine. <br><br> When this property is not specified in the request body, default behavior is to set it to true.  This will ensure that VM Agent is installed on the VM so that extensions can be added to the VM later.
	ProvisionVMAgent *bool `json:"provisionVMAgent,omitempty"`
}

// ListUsagesResult the List Usages operation response.
type ListUsagesResult struct {
	autorest.Response `json:"-"`
	// Value - The list of compute resource usages.
	Value *[]Usage `json:"value,omitempty"`
	// NextLink - The URI to fetch the next page of compute resource usage information. Call ListNext() with this to fetch the next page of compute resource usage information.
	NextLink *string `json:"nextLink,omitempty"`
}

// ListUsagesResultIterator provides access to a complete listing of Usage values.
type ListUsagesResultIterator struct {
	i    int
	page ListUsagesResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *ListUsagesResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ListUsagesResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *ListUsagesResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter ListUsagesResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter ListUsagesResultIterator) Response() ListUsagesResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter ListUsagesResultIterator) Value() Usage {
	if !iter.page.NotDone() {
		return Usage{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the ListUsagesResultIterator type.
func NewListUsagesResultIterator(page ListUsagesResultPage) ListUsagesResultIterator {
	return ListUsagesResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (lur ListUsagesResult) IsEmpty() bool {
	return lur.Value == nil || len(*lur.Value) == 0
}

// listUsagesResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (lur ListUsagesResult) listUsagesResultPreparer(ctx context.Context) (*http.Request, error) {
	if lur.NextLink == nil || len(to.String(lur.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(lur.NextLink)))
}

// ListUsagesResultPage contains a page of Usage values.
type ListUsagesResultPage struct {
	fn  func(context.Context, ListUsagesResult) (ListUsagesResult, error)
	lur ListUsagesResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *ListUsagesResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ListUsagesResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.lur)
	if err != nil {
		return err
	}
	page.lur = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *ListUsagesResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page ListUsagesResultPage) NotDone() bool {
	return !page.lur.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page ListUsagesResultPage) Response() ListUsagesResult {
	return page.lur
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page ListUsagesResultPage) Values() []Usage {
	if page.lur.IsEmpty() {
		return nil
	}
	return *page.lur.Value
}

// Creates a new instance of the ListUsagesResultPage type.
func NewListUsagesResultPage(getNextPage func(context.Context, ListUsagesResult) (ListUsagesResult, error)) ListUsagesResultPage {
	return ListUsagesResultPage{fn: getNextPage}
}

// ListVirtualMachineExtensionImage ...
type ListVirtualMachineExtensionImage struct {
	autorest.Response `json:"-"`
	Value             *[]VirtualMachineExtensionImage `json:"value,omitempty"`
}

// ListVirtualMachineImageResource ...
type ListVirtualMachineImageResource struct {
	autorest.Response `json:"-"`
	Value             *[]VirtualMachineImageResource `json:"value,omitempty"`
}

// LogAnalyticsExportRequestRateByIntervalFuture an abstraction for monitoring and retrieving the results
// of a long-running operation.
type LogAnalyticsExportRequestRateByIntervalFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *LogAnalyticsExportRequestRateByIntervalFuture) Result(client LogAnalyticsClient) (laor LogAnalyticsOperationResult, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.LogAnalyticsExportRequestRateByIntervalFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.LogAnalyticsExportRequestRateByIntervalFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if laor.Response.Response, err = future.GetResult(sender); err == nil && laor.Response.Response.StatusCode != http.StatusNoContent {
		laor, err = client.ExportRequestRateByIntervalResponder(laor.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.LogAnalyticsExportRequestRateByIntervalFuture", "Result", laor.Response.Response, "Failure responding to request")
		}
	}
	return
}

// LogAnalyticsExportThrottledRequestsFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type LogAnalyticsExportThrottledRequestsFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *LogAnalyticsExportThrottledRequestsFuture) Result(client LogAnalyticsClient) (laor LogAnalyticsOperationResult, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.LogAnalyticsExportThrottledRequestsFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.LogAnalyticsExportThrottledRequestsFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if laor.Response.Response, err = future.GetResult(sender); err == nil && laor.Response.Response.StatusCode != http.StatusNoContent {
		laor, err = client.ExportThrottledRequestsResponder(laor.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.LogAnalyticsExportThrottledRequestsFuture", "Result", laor.Response.Response, "Failure responding to request")
		}
	}
	return
}

// LogAnalyticsInputBase api input base class for LogAnalytics Api.
type LogAnalyticsInputBase struct {
	// BlobContainerSasURI - SAS Uri of the logging blob container to which LogAnalytics Api writes output logs to.
	BlobContainerSasURI *string `json:"blobContainerSasUri,omitempty"`
	// FromTime - From time of the query
	FromTime *date.Time `json:"fromTime,omitempty"`
	// ToTime - To time of the query
	ToTime *date.Time `json:"toTime,omitempty"`
	// GroupByThrottlePolicy - Group query result by Throttle Policy applied.
	GroupByThrottlePolicy *bool `json:"groupByThrottlePolicy,omitempty"`
	// GroupByOperationName - Group query result by Operation Name.
	GroupByOperationName *bool `json:"groupByOperationName,omitempty"`
	// GroupByResourceName - Group query result by Resource Name.
	GroupByResourceName *bool `json:"groupByResourceName,omitempty"`
}

// LogAnalyticsOperationResult logAnalytics operation status response
type LogAnalyticsOperationResult struct {
	autorest.Response `json:"-"`
	// Properties - READ-ONLY; LogAnalyticsOutput
	Properties *LogAnalyticsOutput `json:"properties,omitempty"`
}

// LogAnalyticsOutput logAnalytics output properties
type LogAnalyticsOutput struct {
	// Output - READ-ONLY; Output file Uri path to blob container.
	Output *string `json:"output,omitempty"`
}

// MaintenanceRedeployStatus maintenance Operation Status.
type MaintenanceRedeployStatus struct {
	// IsCustomerInitiatedMaintenanceAllowed - True, if customer is allowed to perform Maintenance.
	IsCustomerInitiatedMaintenanceAllowed *bool `json:"isCustomerInitiatedMaintenanceAllowed,omitempty"`
	// PreMaintenanceWindowStartTime - Start Time for the Pre Maintenance Window.
	PreMaintenanceWindowStartTime *date.Time `json:"preMaintenanceWindowStartTime,omitempty"`
	// PreMaintenanceWindowEndTime - End Time for the Pre Maintenance Window.
	PreMaintenanceWindowEndTime *date.Time `json:"preMaintenanceWindowEndTime,omitempty"`
	// MaintenanceWindowStartTime - Start Time for the Maintenance Window.
	MaintenanceWindowStartTime *date.Time `json:"maintenanceWindowStartTime,omitempty"`
	// MaintenanceWindowEndTime - End Time for the Maintenance Window.
	MaintenanceWindowEndTime *date.Time `json:"maintenanceWindowEndTime,omitempty"`
	// LastOperationResultCode - The Last Maintenance Operation Result Code. Possible values include: 'MaintenanceOperationResultCodeTypesNone', 'MaintenanceOperationResultCodeTypesRetryLater', 'MaintenanceOperationResultCodeTypesMaintenanceAborted', 'MaintenanceOperationResultCodeTypesMaintenanceCompleted'
	LastOperationResultCode MaintenanceOperationResultCodeTypes `json:"lastOperationResultCode,omitempty"`
	// LastOperationMessage - Message returned for the last Maintenance Operation.
	LastOperationMessage *string `json:"lastOperationMessage,omitempty"`
}

// ManagedArtifact the managed artifact.
type ManagedArtifact struct {
	// ID - The managed artifact id.
	ID *string `json:"id,omitempty"`
}

// ManagedDiskParameters the parameters of a managed disk.
type ManagedDiskParameters struct {
	// StorageAccountType - Specifies the storage account type for the managed disk. NOTE: UltraSSD_LRS can only be used with data disks, it cannot be used with OS Disk. Possible values include: 'StorageAccountTypesStandardLRS', 'StorageAccountTypesPremiumLRS', 'StorageAccountTypesStandardSSDLRS', 'StorageAccountTypesUltraSSDLRS'
	StorageAccountType StorageAccountTypes `json:"storageAccountType,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// NetworkInterfaceReference describes a network interface reference.
type NetworkInterfaceReference struct {
	*NetworkInterfaceReferenceProperties `json:"properties,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for NetworkInterfaceReference.
func (nir NetworkInterfaceReference) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if nir.NetworkInterfaceReferenceProperties != nil {
		objectMap["properties"] = nir.NetworkInterfaceReferenceProperties
	}
	if nir.ID != nil {
		objectMap["id"] = nir.ID
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for NetworkInterfaceReference struct.
func (nir *NetworkInterfaceReference) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var networkInterfaceReferenceProperties NetworkInterfaceReferenceProperties
				err = json.Unmarshal(*v, &networkInterfaceReferenceProperties)
				if err != nil {
					return err
				}
				nir.NetworkInterfaceReferenceProperties = &networkInterfaceReferenceProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				nir.ID = &ID
			}
		}
	}

	return nil
}

// NetworkInterfaceReferenceProperties describes a network interface reference properties.
type NetworkInterfaceReferenceProperties struct {
	// Primary - Specifies the primary network interface in case the virtual machine has more than 1 network interface.
	Primary *bool `json:"primary,omitempty"`
}

// NetworkProfile specifies the network interfaces of the virtual machine.
type NetworkProfile struct {
	// NetworkInterfaces - Specifies the list of resource Ids for the network interfaces associated with the virtual machine.
	NetworkInterfaces *[]NetworkInterfaceReference `json:"networkInterfaces,omitempty"`
}

// OperationListResult the List Compute Operation operation response.
type OperationListResult struct {
	autorest.Response `json:"-"`
	// Value - READ-ONLY; The list of compute operations
	Value *[]OperationValue `json:"value,omitempty"`
}

// OperationValue describes the properties of a Compute Operation value.
type OperationValue struct {
	// Origin - READ-ONLY; The origin of the compute operation.
	Origin *string `json:"origin,omitempty"`
	// Name - READ-ONLY; The name of the compute operation.
	Name                   *string `json:"name,omitempty"`
	*OperationValueDisplay `json:"display,omitempty"`
}

// MarshalJSON is the custom marshaler for OperationValue.
func (ov OperationValue) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if ov.OperationValueDisplay != nil {
		objectMap["display"] = ov.OperationValueDisplay
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for OperationValue struct.
func (ov *OperationValue) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "origin":
			if v != nil {
				var origin string
				err = json.Unmarshal(*v, &origin)
				if err != nil {
					return err
				}
				ov.Origin = &origin
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				ov.Name = &name
			}
		case "display":
			if v != nil {
				var operationValueDisplay OperationValueDisplay
				err = json.Unmarshal(*v, &operationValueDisplay)
				if err != nil {
					return err
				}
				ov.OperationValueDisplay = &operationValueDisplay
			}
		}
	}

	return nil
}

// OperationValueDisplay describes the properties of a Compute Operation Value Display.
type OperationValueDisplay struct {
	// Operation - READ-ONLY; The display name of the compute operation.
	Operation *string `json:"operation,omitempty"`
	// Resource - READ-ONLY; The display name of the resource the operation applies to.
	Resource *string `json:"resource,omitempty"`
	// Description - READ-ONLY; The description of the operation.
	Description *string `json:"description,omitempty"`
	// Provider - READ-ONLY; The resource provider for the operation.
	Provider *string `json:"provider,omitempty"`
}

// OSDisk specifies information about the operating system disk used by the virtual machine. <br><br> For
// more information about disks, see [About disks and VHDs for Azure virtual
// machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
type OSDisk struct {
	// OsType - This property allows you to specify the type of the OS that is included in the disk if creating a VM from user-image or a specialized VHD. <br><br> Possible values are: <br><br> **Windows** <br><br> **Linux**. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// EncryptionSettings - Specifies the encryption settings for the OS Disk. <br><br> Minimum api-version: 2015-06-15
	EncryptionSettings *DiskEncryptionSettings `json:"encryptionSettings,omitempty"`
	// Name - The disk name.
	Name *string `json:"name,omitempty"`
	// Vhd - The virtual hard disk.
	Vhd *VirtualHardDisk `json:"vhd,omitempty"`
	// Image - The source user image virtual hard disk. The virtual hard disk will be copied before being attached to the virtual machine. If SourceImage is provided, the destination virtual hard drive must not exist.
	Image *VirtualHardDisk `json:"image,omitempty"`
	// Caching - Specifies the caching requirements. <br><br> Possible values are: <br><br> **None** <br><br> **ReadOnly** <br><br> **ReadWrite** <br><br> Default: **None for Standard storage. ReadOnly for Premium storage**. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// WriteAcceleratorEnabled - Specifies whether writeAccelerator should be enabled or disabled on the disk.
	WriteAcceleratorEnabled *bool `json:"writeAcceleratorEnabled,omitempty"`
	// DiffDiskSettings - Specifies the ephemeral Disk Settings for the operating system disk used by the virtual machine.
	DiffDiskSettings *DiffDiskSettings `json:"diffDiskSettings,omitempty"`
	// CreateOption - Specifies how the virtual machine should be created.<br><br> Possible values are:<br><br> **Attach** \u2013 This value is used when you are using a specialized disk to create the virtual machine.<br><br> **FromImage** \u2013 This value is used when you are using an image to create the virtual machine. If you are using a platform image, you also use the imageReference element described above. If you are using a marketplace image, you  also use the plan element previously described. Possible values include: 'DiskCreateOptionTypesFromImage', 'DiskCreateOptionTypesEmpty', 'DiskCreateOptionTypesAttach'
	CreateOption DiskCreateOptionTypes `json:"createOption,omitempty"`
	// DiskSizeGB - Specifies the size of an empty data disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// ManagedDisk - The managed disk parameters.
	ManagedDisk *ManagedDiskParameters `json:"managedDisk,omitempty"`
}

// OSDiskImage contains the os disk image information.
type OSDiskImage struct {
	// OperatingSystem - The operating system of the osDiskImage. Possible values include: 'Windows', 'Linux'
	OperatingSystem OperatingSystemTypes `json:"operatingSystem,omitempty"`
}

// OSProfile specifies the operating system settings for the virtual machine.
type OSProfile struct {
	// ComputerName - Specifies the host OS name of the virtual machine. <br><br> This name cannot be updated after the VM is created. <br><br> **Max-length (Windows):** 15 characters <br><br> **Max-length (Linux):** 64 characters. <br><br> For naming conventions and restrictions see [Azure infrastructure services implementation guidelines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-infrastructure-subscription-accounts-guidelines?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json#1-naming-conventions).
	ComputerName *string `json:"computerName,omitempty"`
	// AdminUsername - Specifies the name of the administrator account. <br><br> **Windows-only restriction:** Cannot end in "." <br><br> **Disallowed values:** "administrator", "admin", "user", "user1", "test", "user2", "test1", "user3", "admin1", "1", "123", "a", "actuser", "adm", "admin2", "aspnet", "backup", "console", "david", "guest", "john", "owner", "root", "server", "sql", "support", "support_388945a0", "sys", "test2", "test3", "user4", "user5". <br><br> **Minimum-length (Linux):** 1  character <br><br> **Max-length (Linux):** 64 characters <br><br> **Max-length (Windows):** 20 characters  <br><br><li> For root access to the Linux VM, see [Using root privileges on Linux virtual machines in Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-use-root-privileges?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)<br><li> For a list of built-in system users on Linux that should not be used in this field, see [Selecting User Names for Linux on Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-usernames?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)
	AdminUsername *string `json:"adminUsername,omitempty"`
	// AdminPassword - Specifies the password of the administrator account. <br><br> **Minimum-length (Windows):** 8 characters <br><br> **Minimum-length (Linux):** 6 characters <br><br> **Max-length (Windows):** 123 characters <br><br> **Max-length (Linux):** 72 characters <br><br> **Complexity requirements:** 3 out of 4 conditions below need to be fulfilled <br> Has lower characters <br>Has upper characters <br> Has a digit <br> Has a special character (Regex match [\W_]) <br><br> **Disallowed values:** "abc@123", "P@$$w0rd", "P@ssw0rd", "P@ssword123", "Pa$$word", "pass@word1", "Password!", "Password1", "Password22", "iloveyou!" <br><br> For resetting the password, see [How to reset the Remote Desktop service or its login password in a Windows VM](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-reset-rdp?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> For resetting root password, see [Manage users, SSH, and check or repair disks on Azure Linux VMs using the VMAccess Extension](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-using-vmaccess-extension?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json#reset-root-password)
	AdminPassword *string `json:"adminPassword,omitempty"`
	// CustomData - Specifies a base-64 encoded string of custom data. The base-64 encoded string is decoded to a binary array that is saved as a file on the Virtual Machine. The maximum length of the binary array is 65535 bytes. <br><br> For using cloud-init for your VM, see [Using cloud-init to customize a Linux VM during creation](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-using-cloud-init?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)
	CustomData *string `json:"customData,omitempty"`
	// WindowsConfiguration - Specifies Windows operating system settings on the virtual machine.
	WindowsConfiguration *WindowsConfiguration `json:"windowsConfiguration,omitempty"`
	// LinuxConfiguration - Specifies the Linux operating system settings on the virtual machine. <br><br>For a list of supported Linux distributions, see [Linux on Azure-Endorsed Distributions](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-endorsed-distros?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json) <br><br> For running non-endorsed distributions, see [Information for Non-Endorsed Distributions](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-create-upload-generic?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json).
	LinuxConfiguration *LinuxConfiguration `json:"linuxConfiguration,omitempty"`
	// Secrets - Specifies set of certificates that should be installed onto the virtual machine.
	Secrets *[]VaultSecretGroup `json:"secrets,omitempty"`
	// AllowExtensionOperations - Specifies whether extension operations should be allowed on the virtual machine. <br><br>This may only be set to False when no extensions are present on the virtual machine.
	AllowExtensionOperations *bool `json:"allowExtensionOperations,omitempty"`
}

// Plan specifies information about the marketplace image used to create the virtual machine. This element
// is only used for marketplace images. Before you can use a marketplace image from an API, you must enable
// the image for programmatic use.  In the Azure portal, find the marketplace image that you want to use
// and then click **Want to deploy programmatically, Get Started ->**. Enter any required information and
// then click **Save**.
type Plan struct {
	// Name - The plan ID.
	Name *string `json:"name,omitempty"`
	// Publisher - The publisher ID.
	Publisher *string `json:"publisher,omitempty"`
	// Product - Specifies the product of the image from the marketplace. This is the same value as Offer under the imageReference element.
	Product *string `json:"product,omitempty"`
	// PromotionCode - The promotion code.
	PromotionCode *string `json:"promotionCode,omitempty"`
}

// ProximityPlacementGroup specifies information about the proximity placement group.
type ProximityPlacementGroup struct {
	autorest.Response `json:"-"`
	// ProximityPlacementGroupProperties - Describes the properties of a Proximity Placement Group.
	*ProximityPlacementGroupProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for ProximityPlacementGroup.
func (ppg ProximityPlacementGroup) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if ppg.ProximityPlacementGroupProperties != nil {
		objectMap["properties"] = ppg.ProximityPlacementGroupProperties
	}
	if ppg.Location != nil {
		objectMap["location"] = ppg.Location
	}
	if ppg.Tags != nil {
		objectMap["tags"] = ppg.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for ProximityPlacementGroup struct.
func (ppg *ProximityPlacementGroup) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var proximityPlacementGroupProperties ProximityPlacementGroupProperties
				err = json.Unmarshal(*v, &proximityPlacementGroupProperties)
				if err != nil {
					return err
				}
				ppg.ProximityPlacementGroupProperties = &proximityPlacementGroupProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				ppg.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				ppg.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				ppg.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				ppg.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				ppg.Tags = tags
			}
		}
	}

	return nil
}

// ProximityPlacementGroupListResult the List Proximity Placement Group operation response.
type ProximityPlacementGroupListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of proximity placement groups
	Value *[]ProximityPlacementGroup `json:"value,omitempty"`
	// NextLink - The URI to fetch the next page of proximity placement groups.
	NextLink *string `json:"nextLink,omitempty"`
}

// ProximityPlacementGroupListResultIterator provides access to a complete listing of
// ProximityPlacementGroup values.
type ProximityPlacementGroupListResultIterator struct {
	i    int
	page ProximityPlacementGroupListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *ProximityPlacementGroupListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ProximityPlacementGroupListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *ProximityPlacementGroupListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter ProximityPlacementGroupListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter ProximityPlacementGroupListResultIterator) Response() ProximityPlacementGroupListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter ProximityPlacementGroupListResultIterator) Value() ProximityPlacementGroup {
	if !iter.page.NotDone() {
		return ProximityPlacementGroup{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the ProximityPlacementGroupListResultIterator type.
func NewProximityPlacementGroupListResultIterator(page ProximityPlacementGroupListResultPage) ProximityPlacementGroupListResultIterator {
	return ProximityPlacementGroupListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (ppglr ProximityPlacementGroupListResult) IsEmpty() bool {
	return ppglr.Value == nil || len(*ppglr.Value) == 0
}

// proximityPlacementGroupListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (ppglr ProximityPlacementGroupListResult) proximityPlacementGroupListResultPreparer(ctx context.Context) (*http.Request, error) {
	if ppglr.NextLink == nil || len(to.String(ppglr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(ppglr.NextLink)))
}

// ProximityPlacementGroupListResultPage contains a page of ProximityPlacementGroup values.
type ProximityPlacementGroupListResultPage struct {
	fn    func(context.Context, ProximityPlacementGroupListResult) (ProximityPlacementGroupListResult, error)
	ppglr ProximityPlacementGroupListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *ProximityPlacementGroupListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ProximityPlacementGroupListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.ppglr)
	if err != nil {
		return err
	}
	page.ppglr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *ProximityPlacementGroupListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page ProximityPlacementGroupListResultPage) NotDone() bool {
	return !page.ppglr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page ProximityPlacementGroupListResultPage) Response() ProximityPlacementGroupListResult {
	return page.ppglr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page ProximityPlacementGroupListResultPage) Values() []ProximityPlacementGroup {
	if page.ppglr.IsEmpty() {
		return nil
	}
	return *page.ppglr.Value
}

// Creates a new instance of the ProximityPlacementGroupListResultPage type.
func NewProximityPlacementGroupListResultPage(getNextPage func(context.Context, ProximityPlacementGroupListResult) (ProximityPlacementGroupListResult, error)) ProximityPlacementGroupListResultPage {
	return ProximityPlacementGroupListResultPage{fn: getNextPage}
}

// ProximityPlacementGroupProperties describes the properties of a Proximity Placement Group.
type ProximityPlacementGroupProperties struct {
	// ProximityPlacementGroupType - Specifies the type of the proximity placement group. <br><br> Possible values are: <br><br> **Standard** : Co-locate resources within an Azure region or Availability Zone. <br><br> **Ultra** : For future use. Possible values include: 'Standard', 'Ultra'
	ProximityPlacementGroupType ProximityPlacementGroupType `json:"proximityPlacementGroupType,omitempty"`
	// VirtualMachines - READ-ONLY; A list of references to all virtual machines in the proximity placement group.
	VirtualMachines *[]SubResource `json:"virtualMachines,omitempty"`
	// VirtualMachineScaleSets - READ-ONLY; A list of references to all virtual machine scale sets in the proximity placement group.
	VirtualMachineScaleSets *[]SubResource `json:"virtualMachineScaleSets,omitempty"`
	// AvailabilitySets - READ-ONLY; A list of references to all availability sets in the proximity placement group.
	AvailabilitySets *[]SubResource `json:"availabilitySets,omitempty"`
}

// ProximityPlacementGroupUpdate specifies information about the proximity placement group.
type ProximityPlacementGroupUpdate struct {
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for ProximityPlacementGroupUpdate.
func (ppgu ProximityPlacementGroupUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if ppgu.Tags != nil {
		objectMap["tags"] = ppgu.Tags
	}
	return json.Marshal(objectMap)
}

// PurchasePlan used for establishing the purchase context of any 3rd Party artifact through MarketPlace.
type PurchasePlan struct {
	// Publisher - The publisher ID.
	Publisher *string `json:"publisher,omitempty"`
	// Name - The plan ID.
	Name *string `json:"name,omitempty"`
	// Product - Specifies the product of the image from the marketplace. This is the same value as Offer under the imageReference element.
	Product *string `json:"product,omitempty"`
}

// RecommendedMachineConfiguration the properties describe the recommended machine configuration for this
// Image Definition. These properties are updatable.
type RecommendedMachineConfiguration struct {
	VCPUs  *ResourceRange `json:"vCPUs,omitempty"`
	Memory *ResourceRange `json:"memory,omitempty"`
}

// RecoveryWalkResponse response after calling a manual recovery walk
type RecoveryWalkResponse struct {
	autorest.Response `json:"-"`
	// WalkPerformed - READ-ONLY; Whether the recovery walk was performed
	WalkPerformed *bool `json:"walkPerformed,omitempty"`
	// NextPlatformUpdateDomain - READ-ONLY; The next update domain that needs to be walked. Null means walk spanning all update domains has been completed
	NextPlatformUpdateDomain *int32 `json:"nextPlatformUpdateDomain,omitempty"`
}

// RegionalReplicationStatus this is the regional replication status.
type RegionalReplicationStatus struct {
	// Region - READ-ONLY; The region to which the gallery Image Version is being replicated to.
	Region *string `json:"region,omitempty"`
	// State - READ-ONLY; This is the regional replication state. Possible values include: 'ReplicationStateUnknown', 'ReplicationStateReplicating', 'ReplicationStateCompleted', 'ReplicationStateFailed'
	State ReplicationState `json:"state,omitempty"`
	// Details - READ-ONLY; The details of the replication status.
	Details *string `json:"details,omitempty"`
	// Progress - READ-ONLY; It indicates progress of the replication job.
	Progress *int32 `json:"progress,omitempty"`
}

// ReplicationStatus this is the replication status of the gallery Image Version.
type ReplicationStatus struct {
	// AggregatedState - READ-ONLY; This is the aggregated replication status based on all the regional replication status flags. Possible values include: 'Unknown', 'InProgress', 'Completed', 'Failed'
	AggregatedState AggregatedReplicationState `json:"aggregatedState,omitempty"`
	// Summary - READ-ONLY; This is a summary of replication status for each region.
	Summary *[]RegionalReplicationStatus `json:"summary,omitempty"`
}

// RequestRateByIntervalInput api request input for LogAnalytics getRequestRateByInterval Api.
type RequestRateByIntervalInput struct {
	// IntervalLength - Interval value in minutes used to create LogAnalytics call rate logs. Possible values include: 'ThreeMins', 'FiveMins', 'ThirtyMins', 'SixtyMins'
	IntervalLength IntervalInMins `json:"intervalLength,omitempty"`
	// BlobContainerSasURI - SAS Uri of the logging blob container to which LogAnalytics Api writes output logs to.
	BlobContainerSasURI *string `json:"blobContainerSasUri,omitempty"`
	// FromTime - From time of the query
	FromTime *date.Time `json:"fromTime,omitempty"`
	// ToTime - To time of the query
	ToTime *date.Time `json:"toTime,omitempty"`
	// GroupByThrottlePolicy - Group query result by Throttle Policy applied.
	GroupByThrottlePolicy *bool `json:"groupByThrottlePolicy,omitempty"`
	// GroupByOperationName - Group query result by Operation Name.
	GroupByOperationName *bool `json:"groupByOperationName,omitempty"`
	// GroupByResourceName - Group query result by Resource Name.
	GroupByResourceName *bool `json:"groupByResourceName,omitempty"`
}

// Resource the Resource model definition.
type Resource struct {
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for Resource.
func (r Resource) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if r.Location != nil {
		objectMap["location"] = r.Location
	}
	if r.Tags != nil {
		objectMap["tags"] = r.Tags
	}
	return json.Marshal(objectMap)
}

// ResourceRange describes the resource range.
type ResourceRange struct {
	// Min - The minimum number of the resource.
	Min *int32 `json:"min,omitempty"`
	// Max - The maximum number of the resource.
	Max *int32 `json:"max,omitempty"`
}

// ResourceSku describes an available Compute SKU.
type ResourceSku struct {
	// ResourceType - READ-ONLY; The type of resource the SKU applies to.
	ResourceType *string `json:"resourceType,omitempty"`
	// Name - READ-ONLY; The name of SKU.
	Name *string `json:"name,omitempty"`
	// Tier - READ-ONLY; Specifies the tier of virtual machines in a scale set.<br /><br /> Possible Values:<br /><br /> **Standard**<br /><br /> **Basic**
	Tier *string `json:"tier,omitempty"`
	// Size - READ-ONLY; The Size of the SKU.
	Size *string `json:"size,omitempty"`
	// Family - READ-ONLY; The Family of this particular SKU.
	Family *string `json:"family,omitempty"`
	// Kind - READ-ONLY; The Kind of resources that are supported in this SKU.
	Kind *string `json:"kind,omitempty"`
	// Capacity - READ-ONLY; Specifies the number of virtual machines in the scale set.
	Capacity *ResourceSkuCapacity `json:"capacity,omitempty"`
	// Locations - READ-ONLY; The set of locations that the SKU is available.
	Locations *[]string `json:"locations,omitempty"`
	// LocationInfo - READ-ONLY; A list of locations and availability zones in those locations where the SKU is available.
	LocationInfo *[]ResourceSkuLocationInfo `json:"locationInfo,omitempty"`
	// APIVersions - READ-ONLY; The api versions that support this SKU.
	APIVersions *[]string `json:"apiVersions,omitempty"`
	// Costs - READ-ONLY; Metadata for retrieving price info.
	Costs *[]ResourceSkuCosts `json:"costs,omitempty"`
	// Capabilities - READ-ONLY; A name value pair to describe the capability.
	Capabilities *[]ResourceSkuCapabilities `json:"capabilities,omitempty"`
	// Restrictions - READ-ONLY; The restrictions because of which SKU cannot be used. This is empty if there are no restrictions.
	Restrictions *[]ResourceSkuRestrictions `json:"restrictions,omitempty"`
}

// ResourceSkuCapabilities describes The SKU capabilities object.
type ResourceSkuCapabilities struct {
	// Name - READ-ONLY; An invariant to describe the feature.
	Name *string `json:"name,omitempty"`
	// Value - READ-ONLY; An invariant if the feature is measured by quantity.
	Value *string `json:"value,omitempty"`
}

// ResourceSkuCapacity describes scaling information of a SKU.
type ResourceSkuCapacity struct {
	// Minimum - READ-ONLY; The minimum capacity.
	Minimum *int64 `json:"minimum,omitempty"`
	// Maximum - READ-ONLY; The maximum capacity that can be set.
	Maximum *int64 `json:"maximum,omitempty"`
	// Default - READ-ONLY; The default capacity.
	Default *int64 `json:"default,omitempty"`
	// ScaleType - READ-ONLY; The scale type applicable to the sku. Possible values include: 'ResourceSkuCapacityScaleTypeAutomatic', 'ResourceSkuCapacityScaleTypeManual', 'ResourceSkuCapacityScaleTypeNone'
	ScaleType ResourceSkuCapacityScaleType `json:"scaleType,omitempty"`
}

// ResourceSkuCosts describes metadata for retrieving price info.
type ResourceSkuCosts struct {
	// MeterID - READ-ONLY; Used for querying price from commerce.
	MeterID *string `json:"meterID,omitempty"`
	// Quantity - READ-ONLY; The multiplier is needed to extend the base metered cost.
	Quantity *int64 `json:"quantity,omitempty"`
	// ExtendedUnit - READ-ONLY; An invariant to show the extended unit.
	ExtendedUnit *string `json:"extendedUnit,omitempty"`
}

// ResourceSkuLocationInfo ...
type ResourceSkuLocationInfo struct {
	// Location - READ-ONLY; Location of the SKU
	Location *string `json:"location,omitempty"`
	// Zones - READ-ONLY; List of availability zones where the SKU is supported.
	Zones *[]string `json:"zones,omitempty"`
}

// ResourceSkuRestrictionInfo ...
type ResourceSkuRestrictionInfo struct {
	// Locations - READ-ONLY; Locations where the SKU is restricted
	Locations *[]string `json:"locations,omitempty"`
	// Zones - READ-ONLY; List of availability zones where the SKU is restricted.
	Zones *[]string `json:"zones,omitempty"`
}

// ResourceSkuRestrictions describes scaling information of a SKU.
type ResourceSkuRestrictions struct {
	// Type - READ-ONLY; The type of restrictions. Possible values include: 'Location', 'Zone'
	Type ResourceSkuRestrictionsType `json:"type,omitempty"`
	// Values - READ-ONLY; The value of restrictions. If the restriction type is set to location. This would be different locations where the SKU is restricted.
	Values *[]string `json:"values,omitempty"`
	// RestrictionInfo - READ-ONLY; The information about the restriction where the SKU cannot be used.
	RestrictionInfo *ResourceSkuRestrictionInfo `json:"restrictionInfo,omitempty"`
	// ReasonCode - READ-ONLY; The reason for restriction. Possible values include: 'QuotaID', 'NotAvailableForSubscription'
	ReasonCode ResourceSkuRestrictionsReasonCode `json:"reasonCode,omitempty"`
}

// ResourceSkusResult the List Resource Skus operation response.
type ResourceSkusResult struct {
	autorest.Response `json:"-"`
	// Value - The list of skus available for the subscription.
	Value *[]ResourceSku `json:"value,omitempty"`
	// NextLink - The URI to fetch the next page of Resource Skus. Call ListNext() with this URI to fetch the next page of Resource Skus
	NextLink *string `json:"nextLink,omitempty"`
}

// ResourceSkusResultIterator provides access to a complete listing of ResourceSku values.
type ResourceSkusResultIterator struct {
	i    int
	page ResourceSkusResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *ResourceSkusResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ResourceSkusResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *ResourceSkusResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter ResourceSkusResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter ResourceSkusResultIterator) Response() ResourceSkusResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter ResourceSkusResultIterator) Value() ResourceSku {
	if !iter.page.NotDone() {
		return ResourceSku{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the ResourceSkusResultIterator type.
func NewResourceSkusResultIterator(page ResourceSkusResultPage) ResourceSkusResultIterator {
	return ResourceSkusResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (rsr ResourceSkusResult) IsEmpty() bool {
	return rsr.Value == nil || len(*rsr.Value) == 0
}

// resourceSkusResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (rsr ResourceSkusResult) resourceSkusResultPreparer(ctx context.Context) (*http.Request, error) {
	if rsr.NextLink == nil || len(to.String(rsr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(rsr.NextLink)))
}

// ResourceSkusResultPage contains a page of ResourceSku values.
type ResourceSkusResultPage struct {
	fn  func(context.Context, ResourceSkusResult) (ResourceSkusResult, error)
	rsr ResourceSkusResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *ResourceSkusResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/ResourceSkusResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.rsr)
	if err != nil {
		return err
	}
	page.rsr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *ResourceSkusResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page ResourceSkusResultPage) NotDone() bool {
	return !page.rsr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page ResourceSkusResultPage) Response() ResourceSkusResult {
	return page.rsr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page ResourceSkusResultPage) Values() []ResourceSku {
	if page.rsr.IsEmpty() {
		return nil
	}
	return *page.rsr.Value
}

// Creates a new instance of the ResourceSkusResultPage type.
func NewResourceSkusResultPage(getNextPage func(context.Context, ResourceSkusResult) (ResourceSkusResult, error)) ResourceSkusResultPage {
	return ResourceSkusResultPage{fn: getNextPage}
}

// RollbackStatusInfo information about rollback on failed VM instances after a OS Upgrade operation.
type RollbackStatusInfo struct {
	// SuccessfullyRolledbackInstanceCount - READ-ONLY; The number of instances which have been successfully rolled back.
	SuccessfullyRolledbackInstanceCount *int32 `json:"successfullyRolledbackInstanceCount,omitempty"`
	// FailedRolledbackInstanceCount - READ-ONLY; The number of instances which failed to rollback.
	FailedRolledbackInstanceCount *int32 `json:"failedRolledbackInstanceCount,omitempty"`
	// RollbackError - READ-ONLY; Error details if OS rollback failed.
	RollbackError *APIError `json:"rollbackError,omitempty"`
}

// RollingUpgradePolicy the configuration parameters used while performing a rolling upgrade.
type RollingUpgradePolicy struct {
	// MaxBatchInstancePercent - The maximum percent of total virtual machine instances that will be upgraded simultaneously by the rolling upgrade in one batch. As this is a maximum, unhealthy instances in previous or future batches can cause the percentage of instances in a batch to decrease to ensure higher reliability. The default value for this parameter is 20%.
	MaxBatchInstancePercent *int32 `json:"maxBatchInstancePercent,omitempty"`
	// MaxUnhealthyInstancePercent - The maximum percentage of the total virtual machine instances in the scale set that can be simultaneously unhealthy, either as a result of being upgraded, or by being found in an unhealthy state by the virtual machine health checks before the rolling upgrade aborts. This constraint will be checked prior to starting any batch. The default value for this parameter is 20%.
	MaxUnhealthyInstancePercent *int32 `json:"maxUnhealthyInstancePercent,omitempty"`
	// MaxUnhealthyUpgradedInstancePercent - The maximum percentage of upgraded virtual machine instances that can be found to be in an unhealthy state. This check will happen after each batch is upgraded. If this percentage is ever exceeded, the rolling update aborts. The default value for this parameter is 20%.
	MaxUnhealthyUpgradedInstancePercent *int32 `json:"maxUnhealthyUpgradedInstancePercent,omitempty"`
	// PauseTimeBetweenBatches - The wait time between completing the update for all virtual machines in one batch and starting the next batch. The time duration should be specified in ISO 8601 format. The default value is 0 seconds (PT0S).
	PauseTimeBetweenBatches *string `json:"pauseTimeBetweenBatches,omitempty"`
}

// RollingUpgradeProgressInfo information about the number of virtual machine instances in each upgrade
// state.
type RollingUpgradeProgressInfo struct {
	// SuccessfulInstanceCount - READ-ONLY; The number of instances that have been successfully upgraded.
	SuccessfulInstanceCount *int32 `json:"successfulInstanceCount,omitempty"`
	// FailedInstanceCount - READ-ONLY; The number of instances that have failed to be upgraded successfully.
	FailedInstanceCount *int32 `json:"failedInstanceCount,omitempty"`
	// InProgressInstanceCount - READ-ONLY; The number of instances that are currently being upgraded.
	InProgressInstanceCount *int32 `json:"inProgressInstanceCount,omitempty"`
	// PendingInstanceCount - READ-ONLY; The number of instances that have not yet begun to be upgraded.
	PendingInstanceCount *int32 `json:"pendingInstanceCount,omitempty"`
}

// RollingUpgradeRunningStatus information about the current running state of the overall upgrade.
type RollingUpgradeRunningStatus struct {
	// Code - READ-ONLY; Code indicating the current status of the upgrade. Possible values include: 'RollingUpgradeStatusCodeRollingForward', 'RollingUpgradeStatusCodeCancelled', 'RollingUpgradeStatusCodeCompleted', 'RollingUpgradeStatusCodeFaulted'
	Code RollingUpgradeStatusCode `json:"code,omitempty"`
	// StartTime - READ-ONLY; Start time of the upgrade.
	StartTime *date.Time `json:"startTime,omitempty"`
	// LastAction - READ-ONLY; The last action performed on the rolling upgrade. Possible values include: 'Start', 'Cancel'
	LastAction RollingUpgradeActionType `json:"lastAction,omitempty"`
	// LastActionTime - READ-ONLY; Last action time of the upgrade.
	LastActionTime *date.Time `json:"lastActionTime,omitempty"`
}

// RollingUpgradeStatusInfo the status of the latest virtual machine scale set rolling upgrade.
type RollingUpgradeStatusInfo struct {
	autorest.Response                   `json:"-"`
	*RollingUpgradeStatusInfoProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for RollingUpgradeStatusInfo.
func (rusi RollingUpgradeStatusInfo) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if rusi.RollingUpgradeStatusInfoProperties != nil {
		objectMap["properties"] = rusi.RollingUpgradeStatusInfoProperties
	}
	if rusi.Location != nil {
		objectMap["location"] = rusi.Location
	}
	if rusi.Tags != nil {
		objectMap["tags"] = rusi.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for RollingUpgradeStatusInfo struct.
func (rusi *RollingUpgradeStatusInfo) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var rollingUpgradeStatusInfoProperties RollingUpgradeStatusInfoProperties
				err = json.Unmarshal(*v, &rollingUpgradeStatusInfoProperties)
				if err != nil {
					return err
				}
				rusi.RollingUpgradeStatusInfoProperties = &rollingUpgradeStatusInfoProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				rusi.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				rusi.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				rusi.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				rusi.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				rusi.Tags = tags
			}
		}
	}

	return nil
}

// RollingUpgradeStatusInfoProperties the status of the latest virtual machine scale set rolling upgrade.
type RollingUpgradeStatusInfoProperties struct {
	// Policy - READ-ONLY; The rolling upgrade policies applied for this upgrade.
	Policy *RollingUpgradePolicy `json:"policy,omitempty"`
	// RunningStatus - READ-ONLY; Information about the current running state of the overall upgrade.
	RunningStatus *RollingUpgradeRunningStatus `json:"runningStatus,omitempty"`
	// Progress - READ-ONLY; Information about the number of virtual machine instances in each upgrade state.
	Progress *RollingUpgradeProgressInfo `json:"progress,omitempty"`
	// Error - READ-ONLY; Error details for this upgrade, if there are any.
	Error *APIError `json:"error,omitempty"`
}

// RunCommandDocument describes the properties of a Run Command.
type RunCommandDocument struct {
	autorest.Response `json:"-"`
	// Script - The script to be executed.
	Script *[]string `json:"script,omitempty"`
	// Parameters - The parameters used by the script.
	Parameters *[]RunCommandParameterDefinition `json:"parameters,omitempty"`
	// Schema - The VM run command schema.
	Schema *string `json:"$schema,omitempty"`
	// ID - The VM run command id.
	ID *string `json:"id,omitempty"`
	// OsType - The Operating System type. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// Label - The VM run command label.
	Label *string `json:"label,omitempty"`
	// Description - The VM run command description.
	Description *string `json:"description,omitempty"`
}

// RunCommandDocumentBase describes the properties of a Run Command metadata.
type RunCommandDocumentBase struct {
	// Schema - The VM run command schema.
	Schema *string `json:"$schema,omitempty"`
	// ID - The VM run command id.
	ID *string `json:"id,omitempty"`
	// OsType - The Operating System type. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// Label - The VM run command label.
	Label *string `json:"label,omitempty"`
	// Description - The VM run command description.
	Description *string `json:"description,omitempty"`
}

// RunCommandInput capture Virtual Machine parameters.
type RunCommandInput struct {
	// CommandID - The run command id.
	CommandID *string `json:"commandId,omitempty"`
	// Script - Optional. The script to be executed.  When this value is given, the given script will override the default script of the command.
	Script *[]string `json:"script,omitempty"`
	// Parameters - The run command parameters.
	Parameters *[]RunCommandInputParameter `json:"parameters,omitempty"`
}

// RunCommandInputParameter describes the properties of a run command parameter.
type RunCommandInputParameter struct {
	// Name - The run command parameter name.
	Name *string `json:"name,omitempty"`
	// Value - The run command parameter value.
	Value *string `json:"value,omitempty"`
}

// RunCommandListResult the List Virtual Machine operation response.
type RunCommandListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of virtual machine run commands.
	Value *[]RunCommandDocumentBase `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of run commands. Call ListNext() with this to fetch the next page of run commands.
	NextLink *string `json:"nextLink,omitempty"`
}

// RunCommandListResultIterator provides access to a complete listing of RunCommandDocumentBase values.
type RunCommandListResultIterator struct {
	i    int
	page RunCommandListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *RunCommandListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/RunCommandListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *RunCommandListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter RunCommandListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter RunCommandListResultIterator) Response() RunCommandListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter RunCommandListResultIterator) Value() RunCommandDocumentBase {
	if !iter.page.NotDone() {
		return RunCommandDocumentBase{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the RunCommandListResultIterator type.
func NewRunCommandListResultIterator(page RunCommandListResultPage) RunCommandListResultIterator {
	return RunCommandListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (rclr RunCommandListResult) IsEmpty() bool {
	return rclr.Value == nil || len(*rclr.Value) == 0
}

// runCommandListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (rclr RunCommandListResult) runCommandListResultPreparer(ctx context.Context) (*http.Request, error) {
	if rclr.NextLink == nil || len(to.String(rclr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(rclr.NextLink)))
}

// RunCommandListResultPage contains a page of RunCommandDocumentBase values.
type RunCommandListResultPage struct {
	fn   func(context.Context, RunCommandListResult) (RunCommandListResult, error)
	rclr RunCommandListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *RunCommandListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/RunCommandListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.rclr)
	if err != nil {
		return err
	}
	page.rclr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *RunCommandListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page RunCommandListResultPage) NotDone() bool {
	return !page.rclr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page RunCommandListResultPage) Response() RunCommandListResult {
	return page.rclr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page RunCommandListResultPage) Values() []RunCommandDocumentBase {
	if page.rclr.IsEmpty() {
		return nil
	}
	return *page.rclr.Value
}

// Creates a new instance of the RunCommandListResultPage type.
func NewRunCommandListResultPage(getNextPage func(context.Context, RunCommandListResult) (RunCommandListResult, error)) RunCommandListResultPage {
	return RunCommandListResultPage{fn: getNextPage}
}

// RunCommandParameterDefinition describes the properties of a run command parameter.
type RunCommandParameterDefinition struct {
	// Name - The run command parameter name.
	Name *string `json:"name,omitempty"`
	// Type - The run command parameter type.
	Type *string `json:"type,omitempty"`
	// DefaultValue - The run command parameter default value.
	DefaultValue *string `json:"defaultValue,omitempty"`
	// Required - The run command parameter required.
	Required *bool `json:"required,omitempty"`
}

// RunCommandResult ...
type RunCommandResult struct {
	autorest.Response `json:"-"`
	// Value - Run command operation response.
	Value *[]InstanceViewStatus `json:"value,omitempty"`
}

// Sku describes a virtual machine scale set sku.
type Sku struct {
	// Name - The sku name.
	Name *string `json:"name,omitempty"`
	// Tier - Specifies the tier of virtual machines in a scale set.<br /><br /> Possible Values:<br /><br /> **Standard**<br /><br /> **Basic**
	Tier *string `json:"tier,omitempty"`
	// Capacity - Specifies the number of virtual machines in the scale set.
	Capacity *int64 `json:"capacity,omitempty"`
}

// Snapshot snapshot resource.
type Snapshot struct {
	autorest.Response `json:"-"`
	// ManagedBy - READ-ONLY; Unused. Always Null.
	ManagedBy           *string      `json:"managedBy,omitempty"`
	Sku                 *SnapshotSku `json:"sku,omitempty"`
	*SnapshotProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for Snapshot.
func (s Snapshot) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if s.Sku != nil {
		objectMap["sku"] = s.Sku
	}
	if s.SnapshotProperties != nil {
		objectMap["properties"] = s.SnapshotProperties
	}
	if s.Location != nil {
		objectMap["location"] = s.Location
	}
	if s.Tags != nil {
		objectMap["tags"] = s.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for Snapshot struct.
func (s *Snapshot) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "managedBy":
			if v != nil {
				var managedBy string
				err = json.Unmarshal(*v, &managedBy)
				if err != nil {
					return err
				}
				s.ManagedBy = &managedBy
			}
		case "sku":
			if v != nil {
				var sku SnapshotSku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				s.Sku = &sku
			}
		case "properties":
			if v != nil {
				var snapshotProperties SnapshotProperties
				err = json.Unmarshal(*v, &snapshotProperties)
				if err != nil {
					return err
				}
				s.SnapshotProperties = &snapshotProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				s.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				s.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				s.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				s.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				s.Tags = tags
			}
		}
	}

	return nil
}

// SnapshotList the List Snapshots operation response.
type SnapshotList struct {
	autorest.Response `json:"-"`
	// Value - A list of snapshots.
	Value *[]Snapshot `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of snapshots. Call ListNext() with this to fetch the next page of snapshots.
	NextLink *string `json:"nextLink,omitempty"`
}

// SnapshotListIterator provides access to a complete listing of Snapshot values.
type SnapshotListIterator struct {
	i    int
	page SnapshotListPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *SnapshotListIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/SnapshotListIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *SnapshotListIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter SnapshotListIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter SnapshotListIterator) Response() SnapshotList {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter SnapshotListIterator) Value() Snapshot {
	if !iter.page.NotDone() {
		return Snapshot{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the SnapshotListIterator type.
func NewSnapshotListIterator(page SnapshotListPage) SnapshotListIterator {
	return SnapshotListIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (sl SnapshotList) IsEmpty() bool {
	return sl.Value == nil || len(*sl.Value) == 0
}

// snapshotListPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (sl SnapshotList) snapshotListPreparer(ctx context.Context) (*http.Request, error) {
	if sl.NextLink == nil || len(to.String(sl.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(sl.NextLink)))
}

// SnapshotListPage contains a page of Snapshot values.
type SnapshotListPage struct {
	fn func(context.Context, SnapshotList) (SnapshotList, error)
	sl SnapshotList
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *SnapshotListPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/SnapshotListPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.sl)
	if err != nil {
		return err
	}
	page.sl = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *SnapshotListPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page SnapshotListPage) NotDone() bool {
	return !page.sl.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page SnapshotListPage) Response() SnapshotList {
	return page.sl
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page SnapshotListPage) Values() []Snapshot {
	if page.sl.IsEmpty() {
		return nil
	}
	return *page.sl.Value
}

// Creates a new instance of the SnapshotListPage type.
func NewSnapshotListPage(getNextPage func(context.Context, SnapshotList) (SnapshotList, error)) SnapshotListPage {
	return SnapshotListPage{fn: getNextPage}
}

// SnapshotProperties snapshot resource properties.
type SnapshotProperties struct {
	// TimeCreated - READ-ONLY; The time when the disk was created.
	TimeCreated *date.Time `json:"timeCreated,omitempty"`
	// OsType - The Operating System type. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// CreationData - Disk source information. CreationData information cannot be changed after the disk has been created.
	CreationData *CreationData `json:"creationData,omitempty"`
	// DiskSizeGB - If creationData.createOption is Empty, this field is mandatory and it indicates the size of the VHD to create. If this field is present for updates or creation with other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a running VM, and can only increase the disk's size.
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// EncryptionSettings - Encryption settings for disk or snapshot
	EncryptionSettings *EncryptionSettings `json:"encryptionSettings,omitempty"`
	// ProvisioningState - READ-ONLY; The disk provisioning state.
	ProvisioningState *string `json:"provisioningState,omitempty"`
}

// SnapshotsCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type SnapshotsCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *SnapshotsCreateOrUpdateFuture) Result(client SnapshotsClient) (s Snapshot, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.SnapshotsCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.SnapshotsCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if s.Response.Response, err = future.GetResult(sender); err == nil && s.Response.Response.StatusCode != http.StatusNoContent {
		s, err = client.CreateOrUpdateResponder(s.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.SnapshotsCreateOrUpdateFuture", "Result", s.Response.Response, "Failure responding to request")
		}
	}
	return
}

// SnapshotsDeleteFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type SnapshotsDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *SnapshotsDeleteFuture) Result(client SnapshotsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.SnapshotsDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.SnapshotsDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// SnapshotsGrantAccessFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type SnapshotsGrantAccessFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *SnapshotsGrantAccessFuture) Result(client SnapshotsClient) (au AccessURI, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.SnapshotsGrantAccessFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.SnapshotsGrantAccessFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if au.Response.Response, err = future.GetResult(sender); err == nil && au.Response.Response.StatusCode != http.StatusNoContent {
		au, err = client.GrantAccessResponder(au.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.SnapshotsGrantAccessFuture", "Result", au.Response.Response, "Failure responding to request")
		}
	}
	return
}

// SnapshotSku the snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS.
type SnapshotSku struct {
	// Name - The sku name. Possible values include: 'SnapshotStorageAccountTypesStandardLRS', 'SnapshotStorageAccountTypesPremiumLRS', 'SnapshotStorageAccountTypesStandardZRS'
	Name SnapshotStorageAccountTypes `json:"name,omitempty"`
	// Tier - READ-ONLY; The sku tier.
	Tier *string `json:"tier,omitempty"`
}

// SnapshotsRevokeAccessFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type SnapshotsRevokeAccessFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *SnapshotsRevokeAccessFuture) Result(client SnapshotsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.SnapshotsRevokeAccessFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.SnapshotsRevokeAccessFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// SnapshotsUpdateFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type SnapshotsUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *SnapshotsUpdateFuture) Result(client SnapshotsClient) (s Snapshot, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.SnapshotsUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.SnapshotsUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if s.Response.Response, err = future.GetResult(sender); err == nil && s.Response.Response.StatusCode != http.StatusNoContent {
		s, err = client.UpdateResponder(s.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.SnapshotsUpdateFuture", "Result", s.Response.Response, "Failure responding to request")
		}
	}
	return
}

// SnapshotUpdate snapshot update resource.
type SnapshotUpdate struct {
	*SnapshotUpdateProperties `json:"properties,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
	Sku  *SnapshotSku       `json:"sku,omitempty"`
}

// MarshalJSON is the custom marshaler for SnapshotUpdate.
func (su SnapshotUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if su.SnapshotUpdateProperties != nil {
		objectMap["properties"] = su.SnapshotUpdateProperties
	}
	if su.Tags != nil {
		objectMap["tags"] = su.Tags
	}
	if su.Sku != nil {
		objectMap["sku"] = su.Sku
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for SnapshotUpdate struct.
func (su *SnapshotUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var snapshotUpdateProperties SnapshotUpdateProperties
				err = json.Unmarshal(*v, &snapshotUpdateProperties)
				if err != nil {
					return err
				}
				su.SnapshotUpdateProperties = &snapshotUpdateProperties
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				su.Tags = tags
			}
		case "sku":
			if v != nil {
				var sku SnapshotSku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				su.Sku = &sku
			}
		}
	}

	return nil
}

// SnapshotUpdateProperties snapshot resource update properties.
type SnapshotUpdateProperties struct {
	// OsType - the Operating System type. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// DiskSizeGB - If creationData.createOption is Empty, this field is mandatory and it indicates the size of the VHD to create. If this field is present for updates or creation with other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a running VM, and can only increase the disk's size.
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// EncryptionSettings - Encryption settings for disk or snapshot
	EncryptionSettings *EncryptionSettings `json:"encryptionSettings,omitempty"`
}

// SourceVault the vault id is an Azure Resource Manager Resource id in the form
// /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.KeyVault/vaults/{vaultName}
type SourceVault struct {
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// SSHConfiguration SSH configuration for Linux based VMs running on Azure
type SSHConfiguration struct {
	// PublicKeys - The list of SSH public keys used to authenticate with linux based VMs.
	PublicKeys *[]SSHPublicKey `json:"publicKeys,omitempty"`
}

// SSHPublicKey contains information about SSH certificate public key and the path on the Linux VM where
// the public key is placed.
type SSHPublicKey struct {
	// Path - Specifies the full path on the created VM where ssh public key is stored. If the file already exists, the specified key is appended to the file. Example: /home/<USER>/.ssh/authorized_keys
	Path *string `json:"path,omitempty"`
	// KeyData - SSH public key certificate used to authenticate with the VM through ssh. The key needs to be at least 2048-bit and in ssh-rsa format. <br><br> For creating ssh keys, see [Create SSH keys on Linux and Mac for Linux VMs in Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-mac-create-ssh-keys?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json).
	KeyData *string `json:"keyData,omitempty"`
}

// StorageProfile specifies the storage settings for the virtual machine disks.
type StorageProfile struct {
	// ImageReference - Specifies information about the image to use. You can specify information about platform images, marketplace images, or virtual machine images. This element is required when you want to use a platform image, marketplace image, or virtual machine image, but is not used in other creation operations.
	ImageReference *ImageReference `json:"imageReference,omitempty"`
	// OsDisk - Specifies information about the operating system disk used by the virtual machine. <br><br> For more information about disks, see [About disks and VHDs for Azure virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
	OsDisk *OSDisk `json:"osDisk,omitempty"`
	// DataDisks - Specifies the parameters that are used to add a data disk to a virtual machine. <br><br> For more information about disks, see [About disks and VHDs for Azure virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
	DataDisks *[]DataDisk `json:"dataDisks,omitempty"`
}

// SubResource ...
type SubResource struct {
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// SubResourceReadOnly ...
type SubResourceReadOnly struct {
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
}

// TargetRegion describes the target region information.
type TargetRegion struct {
	// Name - The name of the region.
	Name *string `json:"name,omitempty"`
	// RegionalReplicaCount - The number of replicas of the Image Version to be created per region. This property is updatable.
	RegionalReplicaCount *int32 `json:"regionalReplicaCount,omitempty"`
}

// ThrottledRequestsInput api request input for LogAnalytics getThrottledRequests Api.
type ThrottledRequestsInput struct {
	// BlobContainerSasURI - SAS Uri of the logging blob container to which LogAnalytics Api writes output logs to.
	BlobContainerSasURI *string `json:"blobContainerSasUri,omitempty"`
	// FromTime - From time of the query
	FromTime *date.Time `json:"fromTime,omitempty"`
	// ToTime - To time of the query
	ToTime *date.Time `json:"toTime,omitempty"`
	// GroupByThrottlePolicy - Group query result by Throttle Policy applied.
	GroupByThrottlePolicy *bool `json:"groupByThrottlePolicy,omitempty"`
	// GroupByOperationName - Group query result by Operation Name.
	GroupByOperationName *bool `json:"groupByOperationName,omitempty"`
	// GroupByResourceName - Group query result by Resource Name.
	GroupByResourceName *bool `json:"groupByResourceName,omitempty"`
}

// UpdateResource the Update Resource model definition.
type UpdateResource struct {
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for UpdateResource.
func (ur UpdateResource) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if ur.Tags != nil {
		objectMap["tags"] = ur.Tags
	}
	return json.Marshal(objectMap)
}

// UpgradeOperationHistoricalStatusInfo virtual Machine Scale Set OS Upgrade History operation response.
type UpgradeOperationHistoricalStatusInfo struct {
	// Properties - READ-ONLY; Information about the properties of the upgrade operation.
	Properties *UpgradeOperationHistoricalStatusInfoProperties `json:"properties,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - READ-ONLY; Resource location
	Location *string `json:"location,omitempty"`
}

// UpgradeOperationHistoricalStatusInfoProperties describes each OS upgrade on the Virtual Machine Scale
// Set.
type UpgradeOperationHistoricalStatusInfoProperties struct {
	// RunningStatus - READ-ONLY; Information about the overall status of the upgrade operation.
	RunningStatus *UpgradeOperationHistoryStatus `json:"runningStatus,omitempty"`
	// Progress - READ-ONLY; Counts of the VMs in each state.
	Progress *RollingUpgradeProgressInfo `json:"progress,omitempty"`
	// Error - READ-ONLY; Error Details for this upgrade if there are any.
	Error *APIError `json:"error,omitempty"`
	// StartedBy - READ-ONLY; Invoker of the Upgrade Operation. Possible values include: 'UpgradeOperationInvokerUnknown', 'UpgradeOperationInvokerUser', 'UpgradeOperationInvokerPlatform'
	StartedBy UpgradeOperationInvoker `json:"startedBy,omitempty"`
	// TargetImageReference - READ-ONLY; Image Reference details
	TargetImageReference *ImageReference `json:"targetImageReference,omitempty"`
	// RollbackInfo - READ-ONLY; Information about OS rollback if performed
	RollbackInfo *RollbackStatusInfo `json:"rollbackInfo,omitempty"`
}

// UpgradeOperationHistoryStatus information about the current running state of the overall upgrade.
type UpgradeOperationHistoryStatus struct {
	// Code - READ-ONLY; Code indicating the current status of the upgrade. Possible values include: 'UpgradeStateRollingForward', 'UpgradeStateCancelled', 'UpgradeStateCompleted', 'UpgradeStateFaulted'
	Code UpgradeState `json:"code,omitempty"`
	// StartTime - READ-ONLY; Start time of the upgrade.
	StartTime *date.Time `json:"startTime,omitempty"`
	// EndTime - READ-ONLY; End time of the upgrade.
	EndTime *date.Time `json:"endTime,omitempty"`
}

// UpgradePolicy describes an upgrade policy - automatic, manual, or rolling.
type UpgradePolicy struct {
	// Mode - Specifies the mode of an upgrade to virtual machines in the scale set.<br /><br /> Possible values are:<br /><br /> **Manual** - You  control the application of updates to virtual machines in the scale set. You do this by using the manualUpgrade action.<br /><br /> **Automatic** - All virtual machines in the scale set are  automatically updated at the same time. Possible values include: 'Automatic', 'Manual', 'Rolling'
	Mode UpgradeMode `json:"mode,omitempty"`
	// RollingUpgradePolicy - The configuration parameters used while performing a rolling upgrade.
	RollingUpgradePolicy *RollingUpgradePolicy `json:"rollingUpgradePolicy,omitempty"`
	// AutomaticOSUpgradePolicy - Configuration parameters used for performing automatic OS Upgrade.
	AutomaticOSUpgradePolicy *AutomaticOSUpgradePolicy `json:"automaticOSUpgradePolicy,omitempty"`
}

// Usage describes Compute Resource Usage.
type Usage struct {
	// Unit - An enum describing the unit of usage measurement.
	Unit *string `json:"unit,omitempty"`
	// CurrentValue - The current usage of the resource.
	CurrentValue *int32 `json:"currentValue,omitempty"`
	// Limit - The maximum permitted usage of the resource.
	Limit *int64 `json:"limit,omitempty"`
	// Name - The name of the type of usage.
	Name *UsageName `json:"name,omitempty"`
}

// UsageName the Usage Names.
type UsageName struct {
	// Value - The name of the resource.
	Value *string `json:"value,omitempty"`
	// LocalizedValue - The localized name of the resource.
	LocalizedValue *string `json:"localizedValue,omitempty"`
}

// VaultCertificate describes a single certificate reference in a Key Vault, and where the certificate
// should reside on the VM.
type VaultCertificate struct {
	// CertificateURL - This is the URL of a certificate that has been uploaded to Key Vault as a secret. For adding a secret to the Key Vault, see [Add a key or secret to the key vault](https://docs.microsoft.com/azure/key-vault/key-vault-get-started/#add). In this case, your certificate needs to be It is the Base64 encoding of the following JSON Object which is encoded in UTF-8: <br><br> {<br>  "data":"<Base64-encoded-certificate>",<br>  "dataType":"pfx",<br>  "password":"<pfx-file-password>"<br>}
	CertificateURL *string `json:"certificateUrl,omitempty"`
	// CertificateStore - For Windows VMs, specifies the certificate store on the Virtual Machine to which the certificate should be added. The specified certificate store is implicitly in the LocalMachine account. <br><br>For Linux VMs, the certificate file is placed under the /var/lib/waagent directory, with the file name &lt;UppercaseThumbprint&gt;.crt for the X509 certificate file and &lt;UppercaseThumbprint&gt;.prv for private key. Both of these files are .pem formatted.
	CertificateStore *string `json:"certificateStore,omitempty"`
}

// VaultSecretGroup describes a set of certificates which are all in the same Key Vault.
type VaultSecretGroup struct {
	// SourceVault - The relative URL of the Key Vault containing all of the certificates in VaultCertificates.
	SourceVault *SubResource `json:"sourceVault,omitempty"`
	// VaultCertificates - The list of key vault references in SourceVault which contain certificates.
	VaultCertificates *[]VaultCertificate `json:"vaultCertificates,omitempty"`
}

// VirtualHardDisk describes the uri of a disk.
type VirtualHardDisk struct {
	// URI - Specifies the virtual hard disk's uri.
	URI *string `json:"uri,omitempty"`
}

// VirtualMachine describes a Virtual Machine.
type VirtualMachine struct {
	autorest.Response `json:"-"`
	// Plan - Specifies information about the marketplace image used to create the virtual machine. This element is only used for marketplace images. Before you can use a marketplace image from an API, you must enable the image for programmatic use.  In the Azure portal, find the marketplace image that you want to use and then click **Want to deploy programmatically, Get Started ->**. Enter any required information and then click **Save**.
	Plan                      *Plan `json:"plan,omitempty"`
	*VirtualMachineProperties `json:"properties,omitempty"`
	// Resources - READ-ONLY; The virtual machine child extension resources.
	Resources *[]VirtualMachineExtension `json:"resources,omitempty"`
	// Identity - The identity of the virtual machine, if configured.
	Identity *VirtualMachineIdentity `json:"identity,omitempty"`
	// Zones - The virtual machine zones.
	Zones *[]string `json:"zones,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachine.
func (VM VirtualMachine) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if VM.Plan != nil {
		objectMap["plan"] = VM.Plan
	}
	if VM.VirtualMachineProperties != nil {
		objectMap["properties"] = VM.VirtualMachineProperties
	}
	if VM.Identity != nil {
		objectMap["identity"] = VM.Identity
	}
	if VM.Zones != nil {
		objectMap["zones"] = VM.Zones
	}
	if VM.Location != nil {
		objectMap["location"] = VM.Location
	}
	if VM.Tags != nil {
		objectMap["tags"] = VM.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachine struct.
func (VM *VirtualMachine) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "plan":
			if v != nil {
				var plan Plan
				err = json.Unmarshal(*v, &plan)
				if err != nil {
					return err
				}
				VM.Plan = &plan
			}
		case "properties":
			if v != nil {
				var virtualMachineProperties VirtualMachineProperties
				err = json.Unmarshal(*v, &virtualMachineProperties)
				if err != nil {
					return err
				}
				VM.VirtualMachineProperties = &virtualMachineProperties
			}
		case "resources":
			if v != nil {
				var resources []VirtualMachineExtension
				err = json.Unmarshal(*v, &resources)
				if err != nil {
					return err
				}
				VM.Resources = &resources
			}
		case "identity":
			if v != nil {
				var identity VirtualMachineIdentity
				err = json.Unmarshal(*v, &identity)
				if err != nil {
					return err
				}
				VM.Identity = &identity
			}
		case "zones":
			if v != nil {
				var zones []string
				err = json.Unmarshal(*v, &zones)
				if err != nil {
					return err
				}
				VM.Zones = &zones
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				VM.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				VM.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				VM.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				VM.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				VM.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineAgentInstanceView the instance view of the VM Agent running on the virtual machine.
type VirtualMachineAgentInstanceView struct {
	// VMAgentVersion - The VM Agent full version.
	VMAgentVersion *string `json:"vmAgentVersion,omitempty"`
	// ExtensionHandlers - The virtual machine extension handler instance view.
	ExtensionHandlers *[]VirtualMachineExtensionHandlerInstanceView `json:"extensionHandlers,omitempty"`
	// Statuses - The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
}

// VirtualMachineCaptureParameters capture Virtual Machine parameters.
type VirtualMachineCaptureParameters struct {
	// VhdPrefix - The captured virtual hard disk's name prefix.
	VhdPrefix *string `json:"vhdPrefix,omitempty"`
	// DestinationContainerName - The destination container name.
	DestinationContainerName *string `json:"destinationContainerName,omitempty"`
	// OverwriteVhds - Specifies whether to overwrite the destination virtual hard disk, in case of conflict.
	OverwriteVhds *bool `json:"overwriteVhds,omitempty"`
}

// VirtualMachineCaptureResult output of virtual machine capture operation.
type VirtualMachineCaptureResult struct {
	autorest.Response `json:"-"`
	// Schema - READ-ONLY; the schema of the captured virtual machine
	Schema *string `json:"$schema,omitempty"`
	// ContentVersion - READ-ONLY; the version of the content
	ContentVersion *string `json:"contentVersion,omitempty"`
	// Parameters - READ-ONLY; parameters of the captured virtual machine
	Parameters interface{} `json:"parameters,omitempty"`
	// Resources - READ-ONLY; a list of resource items of the captured virtual machine
	Resources *[]interface{} `json:"resources,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// VirtualMachineExtension describes a Virtual Machine Extension.
type VirtualMachineExtension struct {
	autorest.Response                  `json:"-"`
	*VirtualMachineExtensionProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineExtension.
func (vme VirtualMachineExtension) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vme.VirtualMachineExtensionProperties != nil {
		objectMap["properties"] = vme.VirtualMachineExtensionProperties
	}
	if vme.Location != nil {
		objectMap["location"] = vme.Location
	}
	if vme.Tags != nil {
		objectMap["tags"] = vme.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineExtension struct.
func (vme *VirtualMachineExtension) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var virtualMachineExtensionProperties VirtualMachineExtensionProperties
				err = json.Unmarshal(*v, &virtualMachineExtensionProperties)
				if err != nil {
					return err
				}
				vme.VirtualMachineExtensionProperties = &virtualMachineExtensionProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vme.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vme.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				vme.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				vme.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vme.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineExtensionHandlerInstanceView the instance view of a virtual machine extension handler.
type VirtualMachineExtensionHandlerInstanceView struct {
	// Type - Specifies the type of the extension; an example is "CustomScriptExtension".
	Type *string `json:"type,omitempty"`
	// TypeHandlerVersion - Specifies the version of the script handler.
	TypeHandlerVersion *string `json:"typeHandlerVersion,omitempty"`
	// Status - The extension handler status.
	Status *InstanceViewStatus `json:"status,omitempty"`
}

// VirtualMachineExtensionImage describes a Virtual Machine Extension Image.
type VirtualMachineExtensionImage struct {
	autorest.Response                       `json:"-"`
	*VirtualMachineExtensionImageProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineExtensionImage.
func (vmei VirtualMachineExtensionImage) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmei.VirtualMachineExtensionImageProperties != nil {
		objectMap["properties"] = vmei.VirtualMachineExtensionImageProperties
	}
	if vmei.Location != nil {
		objectMap["location"] = vmei.Location
	}
	if vmei.Tags != nil {
		objectMap["tags"] = vmei.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineExtensionImage struct.
func (vmei *VirtualMachineExtensionImage) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var virtualMachineExtensionImageProperties VirtualMachineExtensionImageProperties
				err = json.Unmarshal(*v, &virtualMachineExtensionImageProperties)
				if err != nil {
					return err
				}
				vmei.VirtualMachineExtensionImageProperties = &virtualMachineExtensionImageProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmei.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmei.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				vmei.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				vmei.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmei.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineExtensionImageProperties describes the properties of a Virtual Machine Extension Image.
type VirtualMachineExtensionImageProperties struct {
	// OperatingSystem - The operating system this extension supports.
	OperatingSystem *string `json:"operatingSystem,omitempty"`
	// ComputeRole - The type of role (IaaS or PaaS) this extension supports.
	ComputeRole *string `json:"computeRole,omitempty"`
	// HandlerSchema - The schema defined by publisher, where extension consumers should provide settings in a matching schema.
	HandlerSchema *string `json:"handlerSchema,omitempty"`
	// VMScaleSetEnabled - Whether the extension can be used on xRP VMScaleSets. By default existing extensions are usable on scalesets, but there might be cases where a publisher wants to explicitly indicate the extension is only enabled for CRP VMs but not VMSS.
	VMScaleSetEnabled *bool `json:"vmScaleSetEnabled,omitempty"`
	// SupportsMultipleExtensions - Whether the handler can support multiple extensions.
	SupportsMultipleExtensions *bool `json:"supportsMultipleExtensions,omitempty"`
}

// VirtualMachineExtensionInstanceView the instance view of a virtual machine extension.
type VirtualMachineExtensionInstanceView struct {
	// Name - The virtual machine extension name.
	Name *string `json:"name,omitempty"`
	// Type - Specifies the type of the extension; an example is "CustomScriptExtension".
	Type *string `json:"type,omitempty"`
	// TypeHandlerVersion - Specifies the version of the script handler.
	TypeHandlerVersion *string `json:"typeHandlerVersion,omitempty"`
	// Substatuses - The resource status information.
	Substatuses *[]InstanceViewStatus `json:"substatuses,omitempty"`
	// Statuses - The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
}

// VirtualMachineExtensionProperties describes the properties of a Virtual Machine Extension.
type VirtualMachineExtensionProperties struct {
	// ForceUpdateTag - How the extension handler should be forced to update even if the extension configuration has not changed.
	ForceUpdateTag *string `json:"forceUpdateTag,omitempty"`
	// Publisher - The name of the extension handler publisher.
	Publisher *string `json:"publisher,omitempty"`
	// Type - Specifies the type of the extension; an example is "CustomScriptExtension".
	Type *string `json:"type,omitempty"`
	// TypeHandlerVersion - Specifies the version of the script handler.
	TypeHandlerVersion *string `json:"typeHandlerVersion,omitempty"`
	// AutoUpgradeMinorVersion - Indicates whether the extension should use a newer minor version if one is available at deployment time. Once deployed, however, the extension will not upgrade minor versions unless redeployed, even with this property set to true.
	AutoUpgradeMinorVersion *bool `json:"autoUpgradeMinorVersion,omitempty"`
	// Settings - Json formatted public settings for the extension.
	Settings interface{} `json:"settings,omitempty"`
	// ProtectedSettings - The extension can contain either protectedSettings or protectedSettingsFromKeyVault or no protected settings at all.
	ProtectedSettings interface{} `json:"protectedSettings,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// InstanceView - The virtual machine extension instance view.
	InstanceView *VirtualMachineExtensionInstanceView `json:"instanceView,omitempty"`
}

// VirtualMachineExtensionsCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of
// a long-running operation.
type VirtualMachineExtensionsCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineExtensionsCreateOrUpdateFuture) Result(client VirtualMachineExtensionsClient) (vme VirtualMachineExtension, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineExtensionsCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineExtensionsCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vme.Response.Response, err = future.GetResult(sender); err == nil && vme.Response.Response.StatusCode != http.StatusNoContent {
		vme, err = client.CreateOrUpdateResponder(vme.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineExtensionsCreateOrUpdateFuture", "Result", vme.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineExtensionsDeleteFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineExtensionsDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineExtensionsDeleteFuture) Result(client VirtualMachineExtensionsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineExtensionsDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineExtensionsDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineExtensionsListResult the List Extension operation response
type VirtualMachineExtensionsListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of extensions
	Value *[]VirtualMachineExtension `json:"value,omitempty"`
}

// VirtualMachineExtensionsUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineExtensionsUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineExtensionsUpdateFuture) Result(client VirtualMachineExtensionsClient) (vme VirtualMachineExtension, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineExtensionsUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineExtensionsUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vme.Response.Response, err = future.GetResult(sender); err == nil && vme.Response.Response.StatusCode != http.StatusNoContent {
		vme, err = client.UpdateResponder(vme.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineExtensionsUpdateFuture", "Result", vme.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineExtensionUpdate describes a Virtual Machine Extension.
type VirtualMachineExtensionUpdate struct {
	*VirtualMachineExtensionUpdateProperties `json:"properties,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineExtensionUpdate.
func (vmeu VirtualMachineExtensionUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmeu.VirtualMachineExtensionUpdateProperties != nil {
		objectMap["properties"] = vmeu.VirtualMachineExtensionUpdateProperties
	}
	if vmeu.Tags != nil {
		objectMap["tags"] = vmeu.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineExtensionUpdate struct.
func (vmeu *VirtualMachineExtensionUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var virtualMachineExtensionUpdateProperties VirtualMachineExtensionUpdateProperties
				err = json.Unmarshal(*v, &virtualMachineExtensionUpdateProperties)
				if err != nil {
					return err
				}
				vmeu.VirtualMachineExtensionUpdateProperties = &virtualMachineExtensionUpdateProperties
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmeu.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineExtensionUpdateProperties describes the properties of a Virtual Machine Extension.
type VirtualMachineExtensionUpdateProperties struct {
	// ForceUpdateTag - How the extension handler should be forced to update even if the extension configuration has not changed.
	ForceUpdateTag *string `json:"forceUpdateTag,omitempty"`
	// Publisher - The name of the extension handler publisher.
	Publisher *string `json:"publisher,omitempty"`
	// Type - Specifies the type of the extension; an example is "CustomScriptExtension".
	Type *string `json:"type,omitempty"`
	// TypeHandlerVersion - Specifies the version of the script handler.
	TypeHandlerVersion *string `json:"typeHandlerVersion,omitempty"`
	// AutoUpgradeMinorVersion - Indicates whether the extension should use a newer minor version if one is available at deployment time. Once deployed, however, the extension will not upgrade minor versions unless redeployed, even with this property set to true.
	AutoUpgradeMinorVersion *bool `json:"autoUpgradeMinorVersion,omitempty"`
	// Settings - Json formatted public settings for the extension.
	Settings interface{} `json:"settings,omitempty"`
	// ProtectedSettings - The extension can contain either protectedSettings or protectedSettingsFromKeyVault or no protected settings at all.
	ProtectedSettings interface{} `json:"protectedSettings,omitempty"`
}

// VirtualMachineHealthStatus the health status of the VM.
type VirtualMachineHealthStatus struct {
	// Status - READ-ONLY; The health status information for the VM.
	Status *InstanceViewStatus `json:"status,omitempty"`
}

// VirtualMachineIdentity identity for the virtual machine.
type VirtualMachineIdentity struct {
	// PrincipalID - READ-ONLY; The principal id of virtual machine identity. This property will only be provided for a system assigned identity.
	PrincipalID *string `json:"principalId,omitempty"`
	// TenantID - READ-ONLY; The tenant id associated with the virtual machine. This property will only be provided for a system assigned identity.
	TenantID *string `json:"tenantId,omitempty"`
	// Type - The type of identity used for the virtual machine. The type 'SystemAssigned, UserAssigned' includes both an implicitly created identity and a set of user assigned identities. The type 'None' will remove any identities from the virtual machine. Possible values include: 'ResourceIdentityTypeSystemAssigned', 'ResourceIdentityTypeUserAssigned', 'ResourceIdentityTypeSystemAssignedUserAssigned', 'ResourceIdentityTypeNone'
	Type ResourceIdentityType `json:"type,omitempty"`
	// UserAssignedIdentities - The list of user identities associated with the Virtual Machine. The user identity dictionary key references will be ARM resource ids in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.
	UserAssignedIdentities map[string]*VirtualMachineIdentityUserAssignedIdentitiesValue `json:"userAssignedIdentities"`
}

// MarshalJSON is the custom marshaler for VirtualMachineIdentity.
func (vmi VirtualMachineIdentity) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmi.Type != "" {
		objectMap["type"] = vmi.Type
	}
	if vmi.UserAssignedIdentities != nil {
		objectMap["userAssignedIdentities"] = vmi.UserAssignedIdentities
	}
	return json.Marshal(objectMap)
}

// VirtualMachineIdentityUserAssignedIdentitiesValue ...
type VirtualMachineIdentityUserAssignedIdentitiesValue struct {
	// PrincipalID - READ-ONLY; The principal id of user assigned identity.
	PrincipalID *string `json:"principalId,omitempty"`
	// ClientID - READ-ONLY; The client id of user assigned identity.
	ClientID *string `json:"clientId,omitempty"`
}

// VirtualMachineImage describes a Virtual Machine Image.
type VirtualMachineImage struct {
	autorest.Response              `json:"-"`
	*VirtualMachineImageProperties `json:"properties,omitempty"`
	// Name - The name of the resource.
	Name *string `json:"name,omitempty"`
	// Location - The supported Azure location of the resource.
	Location *string `json:"location,omitempty"`
	// Tags - Specifies the tags that are assigned to the virtual machine. For more information about using tags, see [Using tags to organize your Azure resources](https://docs.microsoft.com/azure/azure-resource-manager/resource-group-using-tags.md).
	Tags map[string]*string `json:"tags"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineImage.
func (vmi VirtualMachineImage) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmi.VirtualMachineImageProperties != nil {
		objectMap["properties"] = vmi.VirtualMachineImageProperties
	}
	if vmi.Name != nil {
		objectMap["name"] = vmi.Name
	}
	if vmi.Location != nil {
		objectMap["location"] = vmi.Location
	}
	if vmi.Tags != nil {
		objectMap["tags"] = vmi.Tags
	}
	if vmi.ID != nil {
		objectMap["id"] = vmi.ID
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineImage struct.
func (vmi *VirtualMachineImage) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "properties":
			if v != nil {
				var virtualMachineImageProperties VirtualMachineImageProperties
				err = json.Unmarshal(*v, &virtualMachineImageProperties)
				if err != nil {
					return err
				}
				vmi.VirtualMachineImageProperties = &virtualMachineImageProperties
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmi.Name = &name
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				vmi.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmi.Tags = tags
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmi.ID = &ID
			}
		}
	}

	return nil
}

// VirtualMachineImageProperties describes the properties of a Virtual Machine Image.
type VirtualMachineImageProperties struct {
	Plan                         *PurchasePlan                 `json:"plan,omitempty"`
	OsDiskImage                  *OSDiskImage                  `json:"osDiskImage,omitempty"`
	DataDiskImages               *[]DataDiskImage              `json:"dataDiskImages,omitempty"`
	AutomaticOSUpgradeProperties *AutomaticOSUpgradeProperties `json:"automaticOSUpgradeProperties,omitempty"`
}

// VirtualMachineImageResource virtual machine image resource information.
type VirtualMachineImageResource struct {
	// Name - The name of the resource.
	Name *string `json:"name,omitempty"`
	// Location - The supported Azure location of the resource.
	Location *string `json:"location,omitempty"`
	// Tags - Specifies the tags that are assigned to the virtual machine. For more information about using tags, see [Using tags to organize your Azure resources](https://docs.microsoft.com/azure/azure-resource-manager/resource-group-using-tags.md).
	Tags map[string]*string `json:"tags"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineImageResource.
func (vmir VirtualMachineImageResource) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmir.Name != nil {
		objectMap["name"] = vmir.Name
	}
	if vmir.Location != nil {
		objectMap["location"] = vmir.Location
	}
	if vmir.Tags != nil {
		objectMap["tags"] = vmir.Tags
	}
	if vmir.ID != nil {
		objectMap["id"] = vmir.ID
	}
	return json.Marshal(objectMap)
}

// VirtualMachineInstanceView the instance view of a virtual machine.
type VirtualMachineInstanceView struct {
	autorest.Response `json:"-"`
	// PlatformUpdateDomain - Specifies the update domain of the virtual machine.
	PlatformUpdateDomain *int32 `json:"platformUpdateDomain,omitempty"`
	// PlatformFaultDomain - Specifies the fault domain of the virtual machine.
	PlatformFaultDomain *int32 `json:"platformFaultDomain,omitempty"`
	// ComputerName - The computer name assigned to the virtual machine.
	ComputerName *string `json:"computerName,omitempty"`
	// OsName - The Operating System running on the virtual machine.
	OsName *string `json:"osName,omitempty"`
	// OsVersion - The version of Operating System running on the virtual machine.
	OsVersion *string `json:"osVersion,omitempty"`
	// RdpThumbPrint - The Remote desktop certificate thumbprint.
	RdpThumbPrint *string `json:"rdpThumbPrint,omitempty"`
	// VMAgent - The VM Agent running on the virtual machine.
	VMAgent *VirtualMachineAgentInstanceView `json:"vmAgent,omitempty"`
	// MaintenanceRedeployStatus - The Maintenance Operation status on the virtual machine.
	MaintenanceRedeployStatus *MaintenanceRedeployStatus `json:"maintenanceRedeployStatus,omitempty"`
	// Disks - The virtual machine disk information.
	Disks *[]DiskInstanceView `json:"disks,omitempty"`
	// Extensions - The extensions information.
	Extensions *[]VirtualMachineExtensionInstanceView `json:"extensions,omitempty"`
	// BootDiagnostics - Boot Diagnostics is a debugging feature which allows you to view Console Output and Screenshot to diagnose VM status. <br><br> You can easily view the output of your console log. <br><br> Azure also enables you to see a screenshot of the VM from the hypervisor.
	BootDiagnostics *BootDiagnosticsInstanceView `json:"bootDiagnostics,omitempty"`
	// Statuses - The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
}

// VirtualMachineListResult the List Virtual Machine operation response.
type VirtualMachineListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of virtual machines.
	Value *[]VirtualMachine `json:"value,omitempty"`
	// NextLink - The URI to fetch the next page of VMs. Call ListNext() with this URI to fetch the next page of Virtual Machines.
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineListResultIterator provides access to a complete listing of VirtualMachine values.
type VirtualMachineListResultIterator struct {
	i    int
	page VirtualMachineListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineListResultIterator) Response() VirtualMachineListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineListResultIterator) Value() VirtualMachine {
	if !iter.page.NotDone() {
		return VirtualMachine{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineListResultIterator type.
func NewVirtualMachineListResultIterator(page VirtualMachineListResultPage) VirtualMachineListResultIterator {
	return VirtualMachineListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmlr VirtualMachineListResult) IsEmpty() bool {
	return vmlr.Value == nil || len(*vmlr.Value) == 0
}

// virtualMachineListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmlr VirtualMachineListResult) virtualMachineListResultPreparer(ctx context.Context) (*http.Request, error) {
	if vmlr.NextLink == nil || len(to.String(vmlr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmlr.NextLink)))
}

// VirtualMachineListResultPage contains a page of VirtualMachine values.
type VirtualMachineListResultPage struct {
	fn   func(context.Context, VirtualMachineListResult) (VirtualMachineListResult, error)
	vmlr VirtualMachineListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmlr)
	if err != nil {
		return err
	}
	page.vmlr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineListResultPage) NotDone() bool {
	return !page.vmlr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineListResultPage) Response() VirtualMachineListResult {
	return page.vmlr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineListResultPage) Values() []VirtualMachine {
	if page.vmlr.IsEmpty() {
		return nil
	}
	return *page.vmlr.Value
}

// Creates a new instance of the VirtualMachineListResultPage type.
func NewVirtualMachineListResultPage(getNextPage func(context.Context, VirtualMachineListResult) (VirtualMachineListResult, error)) VirtualMachineListResultPage {
	return VirtualMachineListResultPage{fn: getNextPage}
}

// VirtualMachineProperties describes the properties of a Virtual Machine.
type VirtualMachineProperties struct {
	// HardwareProfile - Specifies the hardware settings for the virtual machine.
	HardwareProfile *HardwareProfile `json:"hardwareProfile,omitempty"`
	// StorageProfile - Specifies the storage settings for the virtual machine disks.
	StorageProfile *StorageProfile `json:"storageProfile,omitempty"`
	// AdditionalCapabilities - Specifies additional capabilities enabled or disabled on the virtual machine.
	AdditionalCapabilities *AdditionalCapabilities `json:"additionalCapabilities,omitempty"`
	// OsProfile - Specifies the operating system settings for the virtual machine.
	OsProfile *OSProfile `json:"osProfile,omitempty"`
	// NetworkProfile - Specifies the network interfaces of the virtual machine.
	NetworkProfile *NetworkProfile `json:"networkProfile,omitempty"`
	// DiagnosticsProfile - Specifies the boot diagnostic settings state. <br><br>Minimum api-version: 2015-06-15.
	DiagnosticsProfile *DiagnosticsProfile `json:"diagnosticsProfile,omitempty"`
	// AvailabilitySet - Specifies information about the availability set that the virtual machine should be assigned to. Virtual machines specified in the same availability set are allocated to different nodes to maximize availability. For more information about availability sets, see [Manage the availability of virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-manage-availability?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json). <br><br> For more information on Azure planned maintenance, see [Planned maintenance for virtual machines in Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-planned-maintenance?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> Currently, a VM can only be added to availability set at creation time. An existing VM cannot be added to an availability set.
	AvailabilitySet *SubResource `json:"availabilitySet,omitempty"`
	// ProximityPlacementGroup - Specifies information about the proximity placement group that the virtual machine should be assigned to. <br><br>Minimum api-version: 2018-04-01.
	ProximityPlacementGroup *SubResource `json:"proximityPlacementGroup,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// InstanceView - READ-ONLY; The virtual machine instance view.
	InstanceView *VirtualMachineInstanceView `json:"instanceView,omitempty"`
	// LicenseType - Specifies that the image or disk that is being used was licensed on-premises. This element is only used for images that contain the Windows Server operating system. <br><br> Possible values are: <br><br> Windows_Client <br><br> Windows_Server <br><br> If this element is included in a request for an update, the value must match the initial value. This value cannot be updated. <br><br> For more information, see [Azure Hybrid Use Benefit for Windows Server](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-hybrid-use-benefit-licensing?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> Minimum api-version: 2015-06-15
	LicenseType *string `json:"licenseType,omitempty"`
	// VMID - READ-ONLY; Specifies the VM unique ID which is a 128-bits identifier that is encoded and stored in all Azure IaaS VMs SMBIOS and can be read using platform BIOS commands.
	VMID *string `json:"vmId,omitempty"`
}

// VirtualMachineReimageParameters parameters for Reimaging Virtual Machine. NOTE: Virtual Machine OS disk
// will always be reimaged
type VirtualMachineReimageParameters struct {
	// TempDisk - Specifies whether to reimage temp disk. Default value: false. Note: This temp disk reimage parameter is only supported for VM/VMSS with Ephemeral OS disk.
	TempDisk *bool `json:"tempDisk,omitempty"`
}

// VirtualMachineScaleSet describes a Virtual Machine Scale Set.
type VirtualMachineScaleSet struct {
	autorest.Response `json:"-"`
	// Sku - The virtual machine scale set sku.
	Sku *Sku `json:"sku,omitempty"`
	// Plan - Specifies information about the marketplace image used to create the virtual machine. This element is only used for marketplace images. Before you can use a marketplace image from an API, you must enable the image for programmatic use.  In the Azure portal, find the marketplace image that you want to use and then click **Want to deploy programmatically, Get Started ->**. Enter any required information and then click **Save**.
	Plan                              *Plan `json:"plan,omitempty"`
	*VirtualMachineScaleSetProperties `json:"properties,omitempty"`
	// Identity - The identity of the virtual machine scale set, if configured.
	Identity *VirtualMachineScaleSetIdentity `json:"identity,omitempty"`
	// Zones - The virtual machine scale set zones.
	Zones *[]string `json:"zones,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSet.
func (vmss VirtualMachineScaleSet) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmss.Sku != nil {
		objectMap["sku"] = vmss.Sku
	}
	if vmss.Plan != nil {
		objectMap["plan"] = vmss.Plan
	}
	if vmss.VirtualMachineScaleSetProperties != nil {
		objectMap["properties"] = vmss.VirtualMachineScaleSetProperties
	}
	if vmss.Identity != nil {
		objectMap["identity"] = vmss.Identity
	}
	if vmss.Zones != nil {
		objectMap["zones"] = vmss.Zones
	}
	if vmss.Location != nil {
		objectMap["location"] = vmss.Location
	}
	if vmss.Tags != nil {
		objectMap["tags"] = vmss.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSet struct.
func (vmss *VirtualMachineScaleSet) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "sku":
			if v != nil {
				var sku Sku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				vmss.Sku = &sku
			}
		case "plan":
			if v != nil {
				var plan Plan
				err = json.Unmarshal(*v, &plan)
				if err != nil {
					return err
				}
				vmss.Plan = &plan
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetProperties VirtualMachineScaleSetProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetProperties)
				if err != nil {
					return err
				}
				vmss.VirtualMachineScaleSetProperties = &virtualMachineScaleSetProperties
			}
		case "identity":
			if v != nil {
				var identity VirtualMachineScaleSetIdentity
				err = json.Unmarshal(*v, &identity)
				if err != nil {
					return err
				}
				vmss.Identity = &identity
			}
		case "zones":
			if v != nil {
				var zones []string
				err = json.Unmarshal(*v, &zones)
				if err != nil {
					return err
				}
				vmss.Zones = &zones
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmss.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmss.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				vmss.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				vmss.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmss.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetDataDisk describes a virtual machine scale set data disk.
type VirtualMachineScaleSetDataDisk struct {
	// Name - The disk name.
	Name *string `json:"name,omitempty"`
	// Lun - Specifies the logical unit number of the data disk. This value is used to identify data disks within the VM and therefore must be unique for each data disk attached to a VM.
	Lun *int32 `json:"lun,omitempty"`
	// Caching - Specifies the caching requirements. <br><br> Possible values are: <br><br> **None** <br><br> **ReadOnly** <br><br> **ReadWrite** <br><br> Default: **None for Standard storage. ReadOnly for Premium storage**. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// WriteAcceleratorEnabled - Specifies whether writeAccelerator should be enabled or disabled on the disk.
	WriteAcceleratorEnabled *bool `json:"writeAcceleratorEnabled,omitempty"`
	// CreateOption - The create option. Possible values include: 'DiskCreateOptionTypesFromImage', 'DiskCreateOptionTypesEmpty', 'DiskCreateOptionTypesAttach'
	CreateOption DiskCreateOptionTypes `json:"createOption,omitempty"`
	// DiskSizeGB - Specifies the size of an empty data disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// ManagedDisk - The managed disk parameters.
	ManagedDisk *VirtualMachineScaleSetManagedDiskParameters `json:"managedDisk,omitempty"`
}

// VirtualMachineScaleSetExtension describes a Virtual Machine Scale Set Extension.
type VirtualMachineScaleSetExtension struct {
	autorest.Response `json:"-"`
	// Name - The name of the extension.
	Name                                       *string `json:"name,omitempty"`
	*VirtualMachineScaleSetExtensionProperties `json:"properties,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetExtension.
func (vmsse VirtualMachineScaleSetExtension) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmsse.Name != nil {
		objectMap["name"] = vmsse.Name
	}
	if vmsse.VirtualMachineScaleSetExtensionProperties != nil {
		objectMap["properties"] = vmsse.VirtualMachineScaleSetExtensionProperties
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetExtension struct.
func (vmsse *VirtualMachineScaleSetExtension) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmsse.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetExtensionProperties VirtualMachineScaleSetExtensionProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetExtensionProperties)
				if err != nil {
					return err
				}
				vmsse.VirtualMachineScaleSetExtensionProperties = &virtualMachineScaleSetExtensionProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmsse.ID = &ID
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetExtensionListResult the List VM scale set extension operation response.
type VirtualMachineScaleSetExtensionListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of VM scale set extensions.
	Value *[]VirtualMachineScaleSetExtension `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of VM scale set extensions. Call ListNext() with this to fetch the next page of VM scale set extensions.
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineScaleSetExtensionListResultIterator provides access to a complete listing of
// VirtualMachineScaleSetExtension values.
type VirtualMachineScaleSetExtensionListResultIterator struct {
	i    int
	page VirtualMachineScaleSetExtensionListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineScaleSetExtensionListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetExtensionListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineScaleSetExtensionListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineScaleSetExtensionListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineScaleSetExtensionListResultIterator) Response() VirtualMachineScaleSetExtensionListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineScaleSetExtensionListResultIterator) Value() VirtualMachineScaleSetExtension {
	if !iter.page.NotDone() {
		return VirtualMachineScaleSetExtension{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineScaleSetExtensionListResultIterator type.
func NewVirtualMachineScaleSetExtensionListResultIterator(page VirtualMachineScaleSetExtensionListResultPage) VirtualMachineScaleSetExtensionListResultIterator {
	return VirtualMachineScaleSetExtensionListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmsselr VirtualMachineScaleSetExtensionListResult) IsEmpty() bool {
	return vmsselr.Value == nil || len(*vmsselr.Value) == 0
}

// virtualMachineScaleSetExtensionListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmsselr VirtualMachineScaleSetExtensionListResult) virtualMachineScaleSetExtensionListResultPreparer(ctx context.Context) (*http.Request, error) {
	if vmsselr.NextLink == nil || len(to.String(vmsselr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmsselr.NextLink)))
}

// VirtualMachineScaleSetExtensionListResultPage contains a page of VirtualMachineScaleSetExtension values.
type VirtualMachineScaleSetExtensionListResultPage struct {
	fn      func(context.Context, VirtualMachineScaleSetExtensionListResult) (VirtualMachineScaleSetExtensionListResult, error)
	vmsselr VirtualMachineScaleSetExtensionListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineScaleSetExtensionListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetExtensionListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmsselr)
	if err != nil {
		return err
	}
	page.vmsselr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineScaleSetExtensionListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineScaleSetExtensionListResultPage) NotDone() bool {
	return !page.vmsselr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineScaleSetExtensionListResultPage) Response() VirtualMachineScaleSetExtensionListResult {
	return page.vmsselr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineScaleSetExtensionListResultPage) Values() []VirtualMachineScaleSetExtension {
	if page.vmsselr.IsEmpty() {
		return nil
	}
	return *page.vmsselr.Value
}

// Creates a new instance of the VirtualMachineScaleSetExtensionListResultPage type.
func NewVirtualMachineScaleSetExtensionListResultPage(getNextPage func(context.Context, VirtualMachineScaleSetExtensionListResult) (VirtualMachineScaleSetExtensionListResult, error)) VirtualMachineScaleSetExtensionListResultPage {
	return VirtualMachineScaleSetExtensionListResultPage{fn: getNextPage}
}

// VirtualMachineScaleSetExtensionProfile describes a virtual machine scale set extension profile.
type VirtualMachineScaleSetExtensionProfile struct {
	// Extensions - The virtual machine scale set child extension resources.
	Extensions *[]VirtualMachineScaleSetExtension `json:"extensions,omitempty"`
}

// VirtualMachineScaleSetExtensionProperties describes the properties of a Virtual Machine Scale Set
// Extension.
type VirtualMachineScaleSetExtensionProperties struct {
	// ForceUpdateTag - If a value is provided and is different from the previous value, the extension handler will be forced to update even if the extension configuration has not changed.
	ForceUpdateTag *string `json:"forceUpdateTag,omitempty"`
	// Publisher - The name of the extension handler publisher.
	Publisher *string `json:"publisher,omitempty"`
	// Type - Specifies the type of the extension; an example is "CustomScriptExtension".
	Type *string `json:"type,omitempty"`
	// TypeHandlerVersion - Specifies the version of the script handler.
	TypeHandlerVersion *string `json:"typeHandlerVersion,omitempty"`
	// AutoUpgradeMinorVersion - Indicates whether the extension should use a newer minor version if one is available at deployment time. Once deployed, however, the extension will not upgrade minor versions unless redeployed, even with this property set to true.
	AutoUpgradeMinorVersion *bool `json:"autoUpgradeMinorVersion,omitempty"`
	// Settings - Json formatted public settings for the extension.
	Settings interface{} `json:"settings,omitempty"`
	// ProtectedSettings - The extension can contain either protectedSettings or protectedSettingsFromKeyVault or no protected settings at all.
	ProtectedSettings interface{} `json:"protectedSettings,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// ProvisionAfterExtensions - Collection of extension names after which this extension needs to be provisioned.
	ProvisionAfterExtensions *[]string `json:"provisionAfterExtensions,omitempty"`
}

// VirtualMachineScaleSetExtensionsCreateOrUpdateFuture an abstraction for monitoring and retrieving the
// results of a long-running operation.
type VirtualMachineScaleSetExtensionsCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetExtensionsCreateOrUpdateFuture) Result(client VirtualMachineScaleSetExtensionsClient) (vmsse VirtualMachineScaleSetExtension, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetExtensionsCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetExtensionsCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vmsse.Response.Response, err = future.GetResult(sender); err == nil && vmsse.Response.Response.StatusCode != http.StatusNoContent {
		vmsse, err = client.CreateOrUpdateResponder(vmsse.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetExtensionsCreateOrUpdateFuture", "Result", vmsse.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineScaleSetExtensionsDeleteFuture an abstraction for monitoring and retrieving the results of
// a long-running operation.
type VirtualMachineScaleSetExtensionsDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetExtensionsDeleteFuture) Result(client VirtualMachineScaleSetExtensionsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetExtensionsDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetExtensionsDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetIdentity identity for the virtual machine scale set.
type VirtualMachineScaleSetIdentity struct {
	// PrincipalID - READ-ONLY; The principal id of virtual machine scale set identity. This property will only be provided for a system assigned identity.
	PrincipalID *string `json:"principalId,omitempty"`
	// TenantID - READ-ONLY; The tenant id associated with the virtual machine scale set. This property will only be provided for a system assigned identity.
	TenantID *string `json:"tenantId,omitempty"`
	// Type - The type of identity used for the virtual machine scale set. The type 'SystemAssigned, UserAssigned' includes both an implicitly created identity and a set of user assigned identities. The type 'None' will remove any identities from the virtual machine scale set. Possible values include: 'ResourceIdentityTypeSystemAssigned', 'ResourceIdentityTypeUserAssigned', 'ResourceIdentityTypeSystemAssignedUserAssigned', 'ResourceIdentityTypeNone'
	Type ResourceIdentityType `json:"type,omitempty"`
	// UserAssignedIdentities - The list of user identities associated with the virtual machine scale set. The user identity dictionary key references will be ARM resource ids in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.
	UserAssignedIdentities map[string]*VirtualMachineScaleSetIdentityUserAssignedIdentitiesValue `json:"userAssignedIdentities"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetIdentity.
func (vmssi VirtualMachineScaleSetIdentity) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssi.Type != "" {
		objectMap["type"] = vmssi.Type
	}
	if vmssi.UserAssignedIdentities != nil {
		objectMap["userAssignedIdentities"] = vmssi.UserAssignedIdentities
	}
	return json.Marshal(objectMap)
}

// VirtualMachineScaleSetIdentityUserAssignedIdentitiesValue ...
type VirtualMachineScaleSetIdentityUserAssignedIdentitiesValue struct {
	// PrincipalID - READ-ONLY; The principal id of user assigned identity.
	PrincipalID *string `json:"principalId,omitempty"`
	// ClientID - READ-ONLY; The client id of user assigned identity.
	ClientID *string `json:"clientId,omitempty"`
}

// VirtualMachineScaleSetInstanceView the instance view of a virtual machine scale set.
type VirtualMachineScaleSetInstanceView struct {
	autorest.Response `json:"-"`
	// VirtualMachine - READ-ONLY; The instance view status summary for the virtual machine scale set.
	VirtualMachine *VirtualMachineScaleSetInstanceViewStatusesSummary `json:"virtualMachine,omitempty"`
	// Extensions - READ-ONLY; The extensions information.
	Extensions *[]VirtualMachineScaleSetVMExtensionsSummary `json:"extensions,omitempty"`
	// Statuses - The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
}

// VirtualMachineScaleSetInstanceViewStatusesSummary instance view statuses summary for virtual machines of
// a virtual machine scale set.
type VirtualMachineScaleSetInstanceViewStatusesSummary struct {
	// StatusesSummary - READ-ONLY; The extensions information.
	StatusesSummary *[]VirtualMachineStatusCodeCount `json:"statusesSummary,omitempty"`
}

// VirtualMachineScaleSetIPConfiguration describes a virtual machine scale set network profile's IP
// configuration.
type VirtualMachineScaleSetIPConfiguration struct {
	// Name - The IP configuration name.
	Name                                             *string `json:"name,omitempty"`
	*VirtualMachineScaleSetIPConfigurationProperties `json:"properties,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetIPConfiguration.
func (vmssic VirtualMachineScaleSetIPConfiguration) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssic.Name != nil {
		objectMap["name"] = vmssic.Name
	}
	if vmssic.VirtualMachineScaleSetIPConfigurationProperties != nil {
		objectMap["properties"] = vmssic.VirtualMachineScaleSetIPConfigurationProperties
	}
	if vmssic.ID != nil {
		objectMap["id"] = vmssic.ID
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetIPConfiguration struct.
func (vmssic *VirtualMachineScaleSetIPConfiguration) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmssic.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetIPConfigurationProperties VirtualMachineScaleSetIPConfigurationProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetIPConfigurationProperties)
				if err != nil {
					return err
				}
				vmssic.VirtualMachineScaleSetIPConfigurationProperties = &virtualMachineScaleSetIPConfigurationProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmssic.ID = &ID
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetIPConfigurationProperties describes a virtual machine scale set network profile's
// IP configuration properties.
type VirtualMachineScaleSetIPConfigurationProperties struct {
	// Subnet - Specifies the identifier of the subnet.
	Subnet *APIEntityReference `json:"subnet,omitempty"`
	// Primary - Specifies the primary network interface in case the virtual machine has more than 1 network interface.
	Primary *bool `json:"primary,omitempty"`
	// PublicIPAddressConfiguration - The publicIPAddressConfiguration.
	PublicIPAddressConfiguration *VirtualMachineScaleSetPublicIPAddressConfiguration `json:"publicIPAddressConfiguration,omitempty"`
	// PrivateIPAddressVersion - Available from Api-Version 2017-03-30 onwards, it represents whether the specific ipconfiguration is IPv4 or IPv6. Default is taken as IPv4.  Possible values are: 'IPv4' and 'IPv6'. Possible values include: 'IPv4', 'IPv6'
	PrivateIPAddressVersion IPVersion `json:"privateIPAddressVersion,omitempty"`
	// ApplicationGatewayBackendAddressPools - Specifies an array of references to backend address pools of application gateways. A scale set can reference backend address pools of multiple application gateways. Multiple scale sets cannot use the same application gateway.
	ApplicationGatewayBackendAddressPools *[]SubResource `json:"applicationGatewayBackendAddressPools,omitempty"`
	// ApplicationSecurityGroups - Specifies an array of references to application security group.
	ApplicationSecurityGroups *[]SubResource `json:"applicationSecurityGroups,omitempty"`
	// LoadBalancerBackendAddressPools - Specifies an array of references to backend address pools of load balancers. A scale set can reference backend address pools of one public and one internal load balancer. Multiple scale sets cannot use the same load balancer.
	LoadBalancerBackendAddressPools *[]SubResource `json:"loadBalancerBackendAddressPools,omitempty"`
	// LoadBalancerInboundNatPools - Specifies an array of references to inbound Nat pools of the load balancers. A scale set can reference inbound nat pools of one public and one internal load balancer. Multiple scale sets cannot use the same load balancer
	LoadBalancerInboundNatPools *[]SubResource `json:"loadBalancerInboundNatPools,omitempty"`
}

// VirtualMachineScaleSetIPTag contains the IP tag associated with the public IP address.
type VirtualMachineScaleSetIPTag struct {
	// IPTagType - IP tag type. Example: FirstPartyUsage.
	IPTagType *string `json:"ipTagType,omitempty"`
	// Tag - IP tag associated with the public IP. Example: SQL, Storage etc.
	Tag *string `json:"tag,omitempty"`
}

// VirtualMachineScaleSetListOSUpgradeHistory list of Virtual Machine Scale Set OS Upgrade History
// operation response.
type VirtualMachineScaleSetListOSUpgradeHistory struct {
	autorest.Response `json:"-"`
	// Value - The list of OS upgrades performed on the virtual machine scale set.
	Value *[]UpgradeOperationHistoricalStatusInfo `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of OS Upgrade History. Call ListNext() with this to fetch the next page of history of upgrades.
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineScaleSetListOSUpgradeHistoryIterator provides access to a complete listing of
// UpgradeOperationHistoricalStatusInfo values.
type VirtualMachineScaleSetListOSUpgradeHistoryIterator struct {
	i    int
	page VirtualMachineScaleSetListOSUpgradeHistoryPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineScaleSetListOSUpgradeHistoryIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListOSUpgradeHistoryIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineScaleSetListOSUpgradeHistoryIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineScaleSetListOSUpgradeHistoryIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineScaleSetListOSUpgradeHistoryIterator) Response() VirtualMachineScaleSetListOSUpgradeHistory {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineScaleSetListOSUpgradeHistoryIterator) Value() UpgradeOperationHistoricalStatusInfo {
	if !iter.page.NotDone() {
		return UpgradeOperationHistoricalStatusInfo{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineScaleSetListOSUpgradeHistoryIterator type.
func NewVirtualMachineScaleSetListOSUpgradeHistoryIterator(page VirtualMachineScaleSetListOSUpgradeHistoryPage) VirtualMachineScaleSetListOSUpgradeHistoryIterator {
	return VirtualMachineScaleSetListOSUpgradeHistoryIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmsslouh VirtualMachineScaleSetListOSUpgradeHistory) IsEmpty() bool {
	return vmsslouh.Value == nil || len(*vmsslouh.Value) == 0
}

// virtualMachineScaleSetListOSUpgradeHistoryPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmsslouh VirtualMachineScaleSetListOSUpgradeHistory) virtualMachineScaleSetListOSUpgradeHistoryPreparer(ctx context.Context) (*http.Request, error) {
	if vmsslouh.NextLink == nil || len(to.String(vmsslouh.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmsslouh.NextLink)))
}

// VirtualMachineScaleSetListOSUpgradeHistoryPage contains a page of UpgradeOperationHistoricalStatusInfo
// values.
type VirtualMachineScaleSetListOSUpgradeHistoryPage struct {
	fn       func(context.Context, VirtualMachineScaleSetListOSUpgradeHistory) (VirtualMachineScaleSetListOSUpgradeHistory, error)
	vmsslouh VirtualMachineScaleSetListOSUpgradeHistory
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineScaleSetListOSUpgradeHistoryPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListOSUpgradeHistoryPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmsslouh)
	if err != nil {
		return err
	}
	page.vmsslouh = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineScaleSetListOSUpgradeHistoryPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineScaleSetListOSUpgradeHistoryPage) NotDone() bool {
	return !page.vmsslouh.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineScaleSetListOSUpgradeHistoryPage) Response() VirtualMachineScaleSetListOSUpgradeHistory {
	return page.vmsslouh
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineScaleSetListOSUpgradeHistoryPage) Values() []UpgradeOperationHistoricalStatusInfo {
	if page.vmsslouh.IsEmpty() {
		return nil
	}
	return *page.vmsslouh.Value
}

// Creates a new instance of the VirtualMachineScaleSetListOSUpgradeHistoryPage type.
func NewVirtualMachineScaleSetListOSUpgradeHistoryPage(getNextPage func(context.Context, VirtualMachineScaleSetListOSUpgradeHistory) (VirtualMachineScaleSetListOSUpgradeHistory, error)) VirtualMachineScaleSetListOSUpgradeHistoryPage {
	return VirtualMachineScaleSetListOSUpgradeHistoryPage{fn: getNextPage}
}

// VirtualMachineScaleSetListResult the List Virtual Machine operation response.
type VirtualMachineScaleSetListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of virtual machine scale sets.
	Value *[]VirtualMachineScaleSet `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of Virtual Machine Scale Sets. Call ListNext() with this to fetch the next page of VMSS.
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineScaleSetListResultIterator provides access to a complete listing of VirtualMachineScaleSet
// values.
type VirtualMachineScaleSetListResultIterator struct {
	i    int
	page VirtualMachineScaleSetListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineScaleSetListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineScaleSetListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineScaleSetListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineScaleSetListResultIterator) Response() VirtualMachineScaleSetListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineScaleSetListResultIterator) Value() VirtualMachineScaleSet {
	if !iter.page.NotDone() {
		return VirtualMachineScaleSet{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineScaleSetListResultIterator type.
func NewVirtualMachineScaleSetListResultIterator(page VirtualMachineScaleSetListResultPage) VirtualMachineScaleSetListResultIterator {
	return VirtualMachineScaleSetListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmsslr VirtualMachineScaleSetListResult) IsEmpty() bool {
	return vmsslr.Value == nil || len(*vmsslr.Value) == 0
}

// virtualMachineScaleSetListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmsslr VirtualMachineScaleSetListResult) virtualMachineScaleSetListResultPreparer(ctx context.Context) (*http.Request, error) {
	if vmsslr.NextLink == nil || len(to.String(vmsslr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmsslr.NextLink)))
}

// VirtualMachineScaleSetListResultPage contains a page of VirtualMachineScaleSet values.
type VirtualMachineScaleSetListResultPage struct {
	fn     func(context.Context, VirtualMachineScaleSetListResult) (VirtualMachineScaleSetListResult, error)
	vmsslr VirtualMachineScaleSetListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineScaleSetListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmsslr)
	if err != nil {
		return err
	}
	page.vmsslr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineScaleSetListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineScaleSetListResultPage) NotDone() bool {
	return !page.vmsslr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineScaleSetListResultPage) Response() VirtualMachineScaleSetListResult {
	return page.vmsslr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineScaleSetListResultPage) Values() []VirtualMachineScaleSet {
	if page.vmsslr.IsEmpty() {
		return nil
	}
	return *page.vmsslr.Value
}

// Creates a new instance of the VirtualMachineScaleSetListResultPage type.
func NewVirtualMachineScaleSetListResultPage(getNextPage func(context.Context, VirtualMachineScaleSetListResult) (VirtualMachineScaleSetListResult, error)) VirtualMachineScaleSetListResultPage {
	return VirtualMachineScaleSetListResultPage{fn: getNextPage}
}

// VirtualMachineScaleSetListSkusResult the Virtual Machine Scale Set List Skus operation response.
type VirtualMachineScaleSetListSkusResult struct {
	autorest.Response `json:"-"`
	// Value - The list of skus available for the virtual machine scale set.
	Value *[]VirtualMachineScaleSetSku `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of Virtual Machine Scale Set Skus. Call ListNext() with this to fetch the next page of VMSS Skus.
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineScaleSetListSkusResultIterator provides access to a complete listing of
// VirtualMachineScaleSetSku values.
type VirtualMachineScaleSetListSkusResultIterator struct {
	i    int
	page VirtualMachineScaleSetListSkusResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineScaleSetListSkusResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListSkusResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineScaleSetListSkusResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineScaleSetListSkusResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineScaleSetListSkusResultIterator) Response() VirtualMachineScaleSetListSkusResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineScaleSetListSkusResultIterator) Value() VirtualMachineScaleSetSku {
	if !iter.page.NotDone() {
		return VirtualMachineScaleSetSku{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineScaleSetListSkusResultIterator type.
func NewVirtualMachineScaleSetListSkusResultIterator(page VirtualMachineScaleSetListSkusResultPage) VirtualMachineScaleSetListSkusResultIterator {
	return VirtualMachineScaleSetListSkusResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmsslsr VirtualMachineScaleSetListSkusResult) IsEmpty() bool {
	return vmsslsr.Value == nil || len(*vmsslsr.Value) == 0
}

// virtualMachineScaleSetListSkusResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmsslsr VirtualMachineScaleSetListSkusResult) virtualMachineScaleSetListSkusResultPreparer(ctx context.Context) (*http.Request, error) {
	if vmsslsr.NextLink == nil || len(to.String(vmsslsr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmsslsr.NextLink)))
}

// VirtualMachineScaleSetListSkusResultPage contains a page of VirtualMachineScaleSetSku values.
type VirtualMachineScaleSetListSkusResultPage struct {
	fn      func(context.Context, VirtualMachineScaleSetListSkusResult) (VirtualMachineScaleSetListSkusResult, error)
	vmsslsr VirtualMachineScaleSetListSkusResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineScaleSetListSkusResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListSkusResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmsslsr)
	if err != nil {
		return err
	}
	page.vmsslsr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineScaleSetListSkusResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineScaleSetListSkusResultPage) NotDone() bool {
	return !page.vmsslsr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineScaleSetListSkusResultPage) Response() VirtualMachineScaleSetListSkusResult {
	return page.vmsslsr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineScaleSetListSkusResultPage) Values() []VirtualMachineScaleSetSku {
	if page.vmsslsr.IsEmpty() {
		return nil
	}
	return *page.vmsslsr.Value
}

// Creates a new instance of the VirtualMachineScaleSetListSkusResultPage type.
func NewVirtualMachineScaleSetListSkusResultPage(getNextPage func(context.Context, VirtualMachineScaleSetListSkusResult) (VirtualMachineScaleSetListSkusResult, error)) VirtualMachineScaleSetListSkusResultPage {
	return VirtualMachineScaleSetListSkusResultPage{fn: getNextPage}
}

// VirtualMachineScaleSetListWithLinkResult the List Virtual Machine operation response.
type VirtualMachineScaleSetListWithLinkResult struct {
	autorest.Response `json:"-"`
	// Value - The list of virtual machine scale sets.
	Value *[]VirtualMachineScaleSet `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of Virtual Machine Scale Sets. Call ListNext() with this to fetch the next page of Virtual Machine Scale Sets.
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineScaleSetListWithLinkResultIterator provides access to a complete listing of
// VirtualMachineScaleSet values.
type VirtualMachineScaleSetListWithLinkResultIterator struct {
	i    int
	page VirtualMachineScaleSetListWithLinkResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineScaleSetListWithLinkResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListWithLinkResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineScaleSetListWithLinkResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineScaleSetListWithLinkResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineScaleSetListWithLinkResultIterator) Response() VirtualMachineScaleSetListWithLinkResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineScaleSetListWithLinkResultIterator) Value() VirtualMachineScaleSet {
	if !iter.page.NotDone() {
		return VirtualMachineScaleSet{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineScaleSetListWithLinkResultIterator type.
func NewVirtualMachineScaleSetListWithLinkResultIterator(page VirtualMachineScaleSetListWithLinkResultPage) VirtualMachineScaleSetListWithLinkResultIterator {
	return VirtualMachineScaleSetListWithLinkResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmsslwlr VirtualMachineScaleSetListWithLinkResult) IsEmpty() bool {
	return vmsslwlr.Value == nil || len(*vmsslwlr.Value) == 0
}

// virtualMachineScaleSetListWithLinkResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmsslwlr VirtualMachineScaleSetListWithLinkResult) virtualMachineScaleSetListWithLinkResultPreparer(ctx context.Context) (*http.Request, error) {
	if vmsslwlr.NextLink == nil || len(to.String(vmsslwlr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmsslwlr.NextLink)))
}

// VirtualMachineScaleSetListWithLinkResultPage contains a page of VirtualMachineScaleSet values.
type VirtualMachineScaleSetListWithLinkResultPage struct {
	fn       func(context.Context, VirtualMachineScaleSetListWithLinkResult) (VirtualMachineScaleSetListWithLinkResult, error)
	vmsslwlr VirtualMachineScaleSetListWithLinkResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineScaleSetListWithLinkResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetListWithLinkResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmsslwlr)
	if err != nil {
		return err
	}
	page.vmsslwlr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineScaleSetListWithLinkResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineScaleSetListWithLinkResultPage) NotDone() bool {
	return !page.vmsslwlr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineScaleSetListWithLinkResultPage) Response() VirtualMachineScaleSetListWithLinkResult {
	return page.vmsslwlr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineScaleSetListWithLinkResultPage) Values() []VirtualMachineScaleSet {
	if page.vmsslwlr.IsEmpty() {
		return nil
	}
	return *page.vmsslwlr.Value
}

// Creates a new instance of the VirtualMachineScaleSetListWithLinkResultPage type.
func NewVirtualMachineScaleSetListWithLinkResultPage(getNextPage func(context.Context, VirtualMachineScaleSetListWithLinkResult) (VirtualMachineScaleSetListWithLinkResult, error)) VirtualMachineScaleSetListWithLinkResultPage {
	return VirtualMachineScaleSetListWithLinkResultPage{fn: getNextPage}
}

// VirtualMachineScaleSetManagedDiskParameters describes the parameters of a ScaleSet managed disk.
type VirtualMachineScaleSetManagedDiskParameters struct {
	// StorageAccountType - Specifies the storage account type for the managed disk. NOTE: UltraSSD_LRS can only be used with data disks, it cannot be used with OS Disk. Possible values include: 'StorageAccountTypesStandardLRS', 'StorageAccountTypesPremiumLRS', 'StorageAccountTypesStandardSSDLRS', 'StorageAccountTypesUltraSSDLRS'
	StorageAccountType StorageAccountTypes `json:"storageAccountType,omitempty"`
}

// VirtualMachineScaleSetNetworkConfiguration describes a virtual machine scale set network profile's
// network configurations.
type VirtualMachineScaleSetNetworkConfiguration struct {
	// Name - The network configuration name.
	Name                                                  *string `json:"name,omitempty"`
	*VirtualMachineScaleSetNetworkConfigurationProperties `json:"properties,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetNetworkConfiguration.
func (vmssnc VirtualMachineScaleSetNetworkConfiguration) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssnc.Name != nil {
		objectMap["name"] = vmssnc.Name
	}
	if vmssnc.VirtualMachineScaleSetNetworkConfigurationProperties != nil {
		objectMap["properties"] = vmssnc.VirtualMachineScaleSetNetworkConfigurationProperties
	}
	if vmssnc.ID != nil {
		objectMap["id"] = vmssnc.ID
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetNetworkConfiguration struct.
func (vmssnc *VirtualMachineScaleSetNetworkConfiguration) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmssnc.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetNetworkConfigurationProperties VirtualMachineScaleSetNetworkConfigurationProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetNetworkConfigurationProperties)
				if err != nil {
					return err
				}
				vmssnc.VirtualMachineScaleSetNetworkConfigurationProperties = &virtualMachineScaleSetNetworkConfigurationProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmssnc.ID = &ID
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetNetworkConfigurationDNSSettings describes a virtual machines scale sets network
// configuration's DNS settings.
type VirtualMachineScaleSetNetworkConfigurationDNSSettings struct {
	// DNSServers - List of DNS servers IP addresses
	DNSServers *[]string `json:"dnsServers,omitempty"`
}

// VirtualMachineScaleSetNetworkConfigurationProperties describes a virtual machine scale set network
// profile's IP configuration.
type VirtualMachineScaleSetNetworkConfigurationProperties struct {
	// Primary - Specifies the primary network interface in case the virtual machine has more than 1 network interface.
	Primary *bool `json:"primary,omitempty"`
	// EnableAcceleratedNetworking - Specifies whether the network interface is accelerated networking-enabled.
	EnableAcceleratedNetworking *bool `json:"enableAcceleratedNetworking,omitempty"`
	// NetworkSecurityGroup - The network security group.
	NetworkSecurityGroup *SubResource `json:"networkSecurityGroup,omitempty"`
	// DNSSettings - The dns settings to be applied on the network interfaces.
	DNSSettings *VirtualMachineScaleSetNetworkConfigurationDNSSettings `json:"dnsSettings,omitempty"`
	// IPConfigurations - Specifies the IP configurations of the network interface.
	IPConfigurations *[]VirtualMachineScaleSetIPConfiguration `json:"ipConfigurations,omitempty"`
	// EnableIPForwarding - Whether IP forwarding enabled on this NIC.
	EnableIPForwarding *bool `json:"enableIPForwarding,omitempty"`
}

// VirtualMachineScaleSetNetworkProfile describes a virtual machine scale set network profile.
type VirtualMachineScaleSetNetworkProfile struct {
	// HealthProbe - A reference to a load balancer probe used to determine the health of an instance in the virtual machine scale set. The reference will be in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/loadBalancers/{loadBalancerName}/probes/{probeName}'.
	HealthProbe *APIEntityReference `json:"healthProbe,omitempty"`
	// NetworkInterfaceConfigurations - The list of network configurations.
	NetworkInterfaceConfigurations *[]VirtualMachineScaleSetNetworkConfiguration `json:"networkInterfaceConfigurations,omitempty"`
}

// VirtualMachineScaleSetOSDisk describes a virtual machine scale set operating system disk.
type VirtualMachineScaleSetOSDisk struct {
	// Name - The disk name.
	Name *string `json:"name,omitempty"`
	// Caching - Specifies the caching requirements. <br><br> Possible values are: <br><br> **None** <br><br> **ReadOnly** <br><br> **ReadWrite** <br><br> Default: **None for Standard storage. ReadOnly for Premium storage**. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// WriteAcceleratorEnabled - Specifies whether writeAccelerator should be enabled or disabled on the disk.
	WriteAcceleratorEnabled *bool `json:"writeAcceleratorEnabled,omitempty"`
	// CreateOption - Specifies how the virtual machines in the scale set should be created.<br><br> The only allowed value is: **FromImage** \u2013 This value is used when you are using an image to create the virtual machine. If you are using a platform image, you also use the imageReference element described above. If you are using a marketplace image, you  also use the plan element previously described. Possible values include: 'DiskCreateOptionTypesFromImage', 'DiskCreateOptionTypesEmpty', 'DiskCreateOptionTypesAttach'
	CreateOption DiskCreateOptionTypes `json:"createOption,omitempty"`
	// DiffDiskSettings - Specifies the ephemeral disk Settings for the operating system disk used by the virtual machine scale set.
	DiffDiskSettings *DiffDiskSettings `json:"diffDiskSettings,omitempty"`
	// DiskSizeGB - Specifies the size of the operating system disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// OsType - This property allows you to specify the type of the OS that is included in the disk if creating a VM from user-image or a specialized VHD. <br><br> Possible values are: <br><br> **Windows** <br><br> **Linux**. Possible values include: 'Windows', 'Linux'
	OsType OperatingSystemTypes `json:"osType,omitempty"`
	// Image - Specifies information about the unmanaged user image to base the scale set on.
	Image *VirtualHardDisk `json:"image,omitempty"`
	// VhdContainers - Specifies the container urls that are used to store operating system disks for the scale set.
	VhdContainers *[]string `json:"vhdContainers,omitempty"`
	// ManagedDisk - The managed disk parameters.
	ManagedDisk *VirtualMachineScaleSetManagedDiskParameters `json:"managedDisk,omitempty"`
}

// VirtualMachineScaleSetOSProfile describes a virtual machine scale set OS profile.
type VirtualMachineScaleSetOSProfile struct {
	// ComputerNamePrefix - Specifies the computer name prefix for all of the virtual machines in the scale set. Computer name prefixes must be 1 to 15 characters long.
	ComputerNamePrefix *string `json:"computerNamePrefix,omitempty"`
	// AdminUsername - Specifies the name of the administrator account. <br><br> **Windows-only restriction:** Cannot end in "." <br><br> **Disallowed values:** "administrator", "admin", "user", "user1", "test", "user2", "test1", "user3", "admin1", "1", "123", "a", "actuser", "adm", "admin2", "aspnet", "backup", "console", "david", "guest", "john", "owner", "root", "server", "sql", "support", "support_388945a0", "sys", "test2", "test3", "user4", "user5". <br><br> **Minimum-length (Linux):** 1  character <br><br> **Max-length (Linux):** 64 characters <br><br> **Max-length (Windows):** 20 characters  <br><br><li> For root access to the Linux VM, see [Using root privileges on Linux virtual machines in Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-use-root-privileges?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)<br><li> For a list of built-in system users on Linux that should not be used in this field, see [Selecting User Names for Linux on Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-usernames?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)
	AdminUsername *string `json:"adminUsername,omitempty"`
	// AdminPassword - Specifies the password of the administrator account. <br><br> **Minimum-length (Windows):** 8 characters <br><br> **Minimum-length (Linux):** 6 characters <br><br> **Max-length (Windows):** 123 characters <br><br> **Max-length (Linux):** 72 characters <br><br> **Complexity requirements:** 3 out of 4 conditions below need to be fulfilled <br> Has lower characters <br>Has upper characters <br> Has a digit <br> Has a special character (Regex match [\W_]) <br><br> **Disallowed values:** "abc@123", "P@$$w0rd", "P@ssw0rd", "P@ssword123", "Pa$$word", "pass@word1", "Password!", "Password1", "Password22", "iloveyou!" <br><br> For resetting the password, see [How to reset the Remote Desktop service or its login password in a Windows VM](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-reset-rdp?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> For resetting root password, see [Manage users, SSH, and check or repair disks on Azure Linux VMs using the VMAccess Extension](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-using-vmaccess-extension?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json#reset-root-password)
	AdminPassword *string `json:"adminPassword,omitempty"`
	// CustomData - Specifies a base-64 encoded string of custom data. The base-64 encoded string is decoded to a binary array that is saved as a file on the Virtual Machine. The maximum length of the binary array is 65535 bytes. <br><br> For using cloud-init for your VM, see [Using cloud-init to customize a Linux VM during creation](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-using-cloud-init?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json)
	CustomData *string `json:"customData,omitempty"`
	// WindowsConfiguration - Specifies Windows operating system settings on the virtual machine.
	WindowsConfiguration *WindowsConfiguration `json:"windowsConfiguration,omitempty"`
	// LinuxConfiguration - Specifies the Linux operating system settings on the virtual machine. <br><br>For a list of supported Linux distributions, see [Linux on Azure-Endorsed Distributions](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-endorsed-distros?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json) <br><br> For running non-endorsed distributions, see [Information for Non-Endorsed Distributions](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-linux-create-upload-generic?toc=%2fazure%2fvirtual-machines%2flinux%2ftoc.json).
	LinuxConfiguration *LinuxConfiguration `json:"linuxConfiguration,omitempty"`
	// Secrets - Specifies set of certificates that should be installed onto the virtual machines in the scale set.
	Secrets *[]VaultSecretGroup `json:"secrets,omitempty"`
}

// VirtualMachineScaleSetProperties describes the properties of a Virtual Machine Scale Set.
type VirtualMachineScaleSetProperties struct {
	// UpgradePolicy - The upgrade policy.
	UpgradePolicy *UpgradePolicy `json:"upgradePolicy,omitempty"`
	// AutomaticRepairsPolicy - Policy for automatic repairs.
	AutomaticRepairsPolicy *AutomaticRepairsPolicy `json:"automaticRepairsPolicy,omitempty"`
	// VirtualMachineProfile - The virtual machine profile.
	VirtualMachineProfile *VirtualMachineScaleSetVMProfile `json:"virtualMachineProfile,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// Overprovision - Specifies whether the Virtual Machine Scale Set should be overprovisioned.
	Overprovision *bool `json:"overprovision,omitempty"`
	// DoNotRunExtensionsOnOverprovisionedVMs - When Overprovision is enabled, extensions are launched only on the requested number of VMs which are finally kept. This property will hence ensure that the extensions do not run on the extra overprovisioned VMs.
	DoNotRunExtensionsOnOverprovisionedVMs *bool `json:"doNotRunExtensionsOnOverprovisionedVMs,omitempty"`
	// UniqueID - READ-ONLY; Specifies the ID which uniquely identifies a Virtual Machine Scale Set.
	UniqueID *string `json:"uniqueId,omitempty"`
	// SinglePlacementGroup - When true this limits the scale set to a single placement group, of max size 100 virtual machines.
	SinglePlacementGroup *bool `json:"singlePlacementGroup,omitempty"`
	// ZoneBalance - Whether to force strictly even Virtual Machine distribution cross x-zones in case there is zone outage.
	ZoneBalance *bool `json:"zoneBalance,omitempty"`
	// PlatformFaultDomainCount - Fault Domain count for each placement group.
	PlatformFaultDomainCount *int32 `json:"platformFaultDomainCount,omitempty"`
	// ProximityPlacementGroup - Specifies information about the proximity placement group that the virtual machine scale set should be assigned to. <br><br>Minimum api-version: 2018-04-01.
	ProximityPlacementGroup *SubResource `json:"proximityPlacementGroup,omitempty"`
}

// VirtualMachineScaleSetPublicIPAddressConfiguration describes a virtual machines scale set IP
// Configuration's PublicIPAddress configuration
type VirtualMachineScaleSetPublicIPAddressConfiguration struct {
	// Name - The publicIP address configuration name.
	Name                                                          *string `json:"name,omitempty"`
	*VirtualMachineScaleSetPublicIPAddressConfigurationProperties `json:"properties,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetPublicIPAddressConfiguration.
func (vmsspiac VirtualMachineScaleSetPublicIPAddressConfiguration) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmsspiac.Name != nil {
		objectMap["name"] = vmsspiac.Name
	}
	if vmsspiac.VirtualMachineScaleSetPublicIPAddressConfigurationProperties != nil {
		objectMap["properties"] = vmsspiac.VirtualMachineScaleSetPublicIPAddressConfigurationProperties
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetPublicIPAddressConfiguration struct.
func (vmsspiac *VirtualMachineScaleSetPublicIPAddressConfiguration) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmsspiac.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetPublicIPAddressConfigurationProperties VirtualMachineScaleSetPublicIPAddressConfigurationProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetPublicIPAddressConfigurationProperties)
				if err != nil {
					return err
				}
				vmsspiac.VirtualMachineScaleSetPublicIPAddressConfigurationProperties = &virtualMachineScaleSetPublicIPAddressConfigurationProperties
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetPublicIPAddressConfigurationDNSSettings describes a virtual machines scale sets
// network configuration's DNS settings.
type VirtualMachineScaleSetPublicIPAddressConfigurationDNSSettings struct {
	// DomainNameLabel - The Domain name label.The concatenation of the domain name label and vm index will be the domain name labels of the PublicIPAddress resources that will be created
	DomainNameLabel *string `json:"domainNameLabel,omitempty"`
}

// VirtualMachineScaleSetPublicIPAddressConfigurationProperties describes a virtual machines scale set IP
// Configuration's PublicIPAddress configuration
type VirtualMachineScaleSetPublicIPAddressConfigurationProperties struct {
	// IdleTimeoutInMinutes - The idle timeout of the public IP address.
	IdleTimeoutInMinutes *int32 `json:"idleTimeoutInMinutes,omitempty"`
	// DNSSettings - The dns settings to be applied on the publicIP addresses .
	DNSSettings *VirtualMachineScaleSetPublicIPAddressConfigurationDNSSettings `json:"dnsSettings,omitempty"`
	// IPTags - The list of IP tags associated with the public IP address.
	IPTags *[]VirtualMachineScaleSetIPTag `json:"ipTags,omitempty"`
	// PublicIPPrefix - The PublicIPPrefix from which to allocate publicIP addresses.
	PublicIPPrefix *SubResource `json:"publicIPPrefix,omitempty"`
}

// VirtualMachineScaleSetReimageParameters describes a Virtual Machine Scale Set VM Reimage Parameters.
type VirtualMachineScaleSetReimageParameters struct {
	// InstanceIds - The virtual machine scale set instance ids. Omitting the virtual machine scale set instance ids will result in the operation being performed on all virtual machines in the virtual machine scale set.
	InstanceIds *[]string `json:"instanceIds,omitempty"`
	// TempDisk - Specifies whether to reimage temp disk. Default value: false. Note: This temp disk reimage parameter is only supported for VM/VMSS with Ephemeral OS disk.
	TempDisk *bool `json:"tempDisk,omitempty"`
}

// VirtualMachineScaleSetRollingUpgradesCancelFuture an abstraction for monitoring and retrieving the
// results of a long-running operation.
type VirtualMachineScaleSetRollingUpgradesCancelFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetRollingUpgradesCancelFuture) Result(client VirtualMachineScaleSetRollingUpgradesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetRollingUpgradesCancelFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetRollingUpgradesCancelFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetRollingUpgradesStartExtensionUpgradeFuture an abstraction for monitoring and
// retrieving the results of a long-running operation.
type VirtualMachineScaleSetRollingUpgradesStartExtensionUpgradeFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetRollingUpgradesStartExtensionUpgradeFuture) Result(client VirtualMachineScaleSetRollingUpgradesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetRollingUpgradesStartExtensionUpgradeFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetRollingUpgradesStartExtensionUpgradeFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetRollingUpgradesStartOSUpgradeFuture an abstraction for monitoring and retrieving
// the results of a long-running operation.
type VirtualMachineScaleSetRollingUpgradesStartOSUpgradeFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetRollingUpgradesStartOSUpgradeFuture) Result(client VirtualMachineScaleSetRollingUpgradesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetRollingUpgradesStartOSUpgradeFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetRollingUpgradesStartOSUpgradeFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of
// a long-running operation.
type VirtualMachineScaleSetsCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsCreateOrUpdateFuture) Result(client VirtualMachineScaleSetsClient) (vmss VirtualMachineScaleSet, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vmss.Response.Response, err = future.GetResult(sender); err == nil && vmss.Response.Response.StatusCode != http.StatusNoContent {
		vmss, err = client.CreateOrUpdateResponder(vmss.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsCreateOrUpdateFuture", "Result", vmss.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineScaleSetsDeallocateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsDeallocateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsDeallocateFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsDeallocateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsDeallocateFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsDeleteFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsDeleteFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsDeleteInstancesFuture an abstraction for monitoring and retrieving the results of
// a long-running operation.
type VirtualMachineScaleSetsDeleteInstancesFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsDeleteInstancesFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsDeleteInstancesFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsDeleteInstancesFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetSku describes an available virtual machine scale set sku.
type VirtualMachineScaleSetSku struct {
	// ResourceType - READ-ONLY; The type of resource the sku applies to.
	ResourceType *string `json:"resourceType,omitempty"`
	// Sku - READ-ONLY; The Sku.
	Sku *Sku `json:"sku,omitempty"`
	// Capacity - READ-ONLY; Specifies the number of virtual machines in the scale set.
	Capacity *VirtualMachineScaleSetSkuCapacity `json:"capacity,omitempty"`
}

// VirtualMachineScaleSetSkuCapacity describes scaling information of a sku.
type VirtualMachineScaleSetSkuCapacity struct {
	// Minimum - READ-ONLY; The minimum capacity.
	Minimum *int64 `json:"minimum,omitempty"`
	// Maximum - READ-ONLY; The maximum capacity that can be set.
	Maximum *int64 `json:"maximum,omitempty"`
	// DefaultCapacity - READ-ONLY; The default capacity.
	DefaultCapacity *int64 `json:"defaultCapacity,omitempty"`
	// ScaleType - READ-ONLY; The scale type applicable to the sku. Possible values include: 'VirtualMachineScaleSetSkuScaleTypeAutomatic', 'VirtualMachineScaleSetSkuScaleTypeNone'
	ScaleType VirtualMachineScaleSetSkuScaleType `json:"scaleType,omitempty"`
}

// VirtualMachineScaleSetsPerformMaintenanceFuture an abstraction for monitoring and retrieving the results
// of a long-running operation.
type VirtualMachineScaleSetsPerformMaintenanceFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsPerformMaintenanceFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsPerformMaintenanceFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsPerformMaintenanceFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsPowerOffFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsPowerOffFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsPowerOffFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsPowerOffFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsPowerOffFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsRedeployFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsRedeployFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsRedeployFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsRedeployFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsRedeployFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsReimageAllFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsReimageAllFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsReimageAllFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsReimageAllFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsReimageAllFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsReimageFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsReimageFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsReimageFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsReimageFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsReimageFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsRestartFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsRestartFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsRestartFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsRestartFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsRestartFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetsStartFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsStartFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsStartFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsStartFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsStartFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetStorageProfile describes a virtual machine scale set storage profile.
type VirtualMachineScaleSetStorageProfile struct {
	// ImageReference - Specifies information about the image to use. You can specify information about platform images, marketplace images, or virtual machine images. This element is required when you want to use a platform image, marketplace image, or virtual machine image, but is not used in other creation operations.
	ImageReference *ImageReference `json:"imageReference,omitempty"`
	// OsDisk - Specifies information about the operating system disk used by the virtual machines in the scale set. <br><br> For more information about disks, see [About disks and VHDs for Azure virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
	OsDisk *VirtualMachineScaleSetOSDisk `json:"osDisk,omitempty"`
	// DataDisks - Specifies the parameters that are used to add data disks to the virtual machines in the scale set. <br><br> For more information about disks, see [About disks and VHDs for Azure virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-about-disks-vhds?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json).
	DataDisks *[]VirtualMachineScaleSetDataDisk `json:"dataDisks,omitempty"`
}

// VirtualMachineScaleSetsUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetsUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsUpdateFuture) Result(client VirtualMachineScaleSetsClient) (vmss VirtualMachineScaleSet, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vmss.Response.Response, err = future.GetResult(sender); err == nil && vmss.Response.Response.StatusCode != http.StatusNoContent {
		vmss, err = client.UpdateResponder(vmss.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsUpdateFuture", "Result", vmss.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineScaleSetsUpdateInstancesFuture an abstraction for monitoring and retrieving the results of
// a long-running operation.
type VirtualMachineScaleSetsUpdateInstancesFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetsUpdateInstancesFuture) Result(client VirtualMachineScaleSetsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetsUpdateInstancesFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetsUpdateInstancesFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetUpdate describes a Virtual Machine Scale Set.
type VirtualMachineScaleSetUpdate struct {
	// Sku - The virtual machine scale set sku.
	Sku *Sku `json:"sku,omitempty"`
	// Plan - The purchase plan when deploying a virtual machine scale set from VM Marketplace images.
	Plan                                    *Plan `json:"plan,omitempty"`
	*VirtualMachineScaleSetUpdateProperties `json:"properties,omitempty"`
	// Identity - The identity of the virtual machine scale set, if configured.
	Identity *VirtualMachineScaleSetIdentity `json:"identity,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetUpdate.
func (vmssu VirtualMachineScaleSetUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssu.Sku != nil {
		objectMap["sku"] = vmssu.Sku
	}
	if vmssu.Plan != nil {
		objectMap["plan"] = vmssu.Plan
	}
	if vmssu.VirtualMachineScaleSetUpdateProperties != nil {
		objectMap["properties"] = vmssu.VirtualMachineScaleSetUpdateProperties
	}
	if vmssu.Identity != nil {
		objectMap["identity"] = vmssu.Identity
	}
	if vmssu.Tags != nil {
		objectMap["tags"] = vmssu.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetUpdate struct.
func (vmssu *VirtualMachineScaleSetUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "sku":
			if v != nil {
				var sku Sku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				vmssu.Sku = &sku
			}
		case "plan":
			if v != nil {
				var plan Plan
				err = json.Unmarshal(*v, &plan)
				if err != nil {
					return err
				}
				vmssu.Plan = &plan
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetUpdateProperties VirtualMachineScaleSetUpdateProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetUpdateProperties)
				if err != nil {
					return err
				}
				vmssu.VirtualMachineScaleSetUpdateProperties = &virtualMachineScaleSetUpdateProperties
			}
		case "identity":
			if v != nil {
				var identity VirtualMachineScaleSetIdentity
				err = json.Unmarshal(*v, &identity)
				if err != nil {
					return err
				}
				vmssu.Identity = &identity
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmssu.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetUpdateIPConfiguration describes a virtual machine scale set network profile's IP
// configuration.
type VirtualMachineScaleSetUpdateIPConfiguration struct {
	// Name - The IP configuration name.
	Name                                                   *string `json:"name,omitempty"`
	*VirtualMachineScaleSetUpdateIPConfigurationProperties `json:"properties,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetUpdateIPConfiguration.
func (vmssuic VirtualMachineScaleSetUpdateIPConfiguration) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssuic.Name != nil {
		objectMap["name"] = vmssuic.Name
	}
	if vmssuic.VirtualMachineScaleSetUpdateIPConfigurationProperties != nil {
		objectMap["properties"] = vmssuic.VirtualMachineScaleSetUpdateIPConfigurationProperties
	}
	if vmssuic.ID != nil {
		objectMap["id"] = vmssuic.ID
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetUpdateIPConfiguration struct.
func (vmssuic *VirtualMachineScaleSetUpdateIPConfiguration) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmssuic.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetUpdateIPConfigurationProperties VirtualMachineScaleSetUpdateIPConfigurationProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetUpdateIPConfigurationProperties)
				if err != nil {
					return err
				}
				vmssuic.VirtualMachineScaleSetUpdateIPConfigurationProperties = &virtualMachineScaleSetUpdateIPConfigurationProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmssuic.ID = &ID
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetUpdateIPConfigurationProperties describes a virtual machine scale set network
// profile's IP configuration properties.
type VirtualMachineScaleSetUpdateIPConfigurationProperties struct {
	// Subnet - The subnet.
	Subnet *APIEntityReference `json:"subnet,omitempty"`
	// Primary - Specifies the primary IP Configuration in case the network interface has more than one IP Configuration.
	Primary *bool `json:"primary,omitempty"`
	// PublicIPAddressConfiguration - The publicIPAddressConfiguration.
	PublicIPAddressConfiguration *VirtualMachineScaleSetUpdatePublicIPAddressConfiguration `json:"publicIPAddressConfiguration,omitempty"`
	// PrivateIPAddressVersion - Available from Api-Version 2017-03-30 onwards, it represents whether the specific ipconfiguration is IPv4 or IPv6. Default is taken as IPv4.  Possible values are: 'IPv4' and 'IPv6'. Possible values include: 'IPv4', 'IPv6'
	PrivateIPAddressVersion IPVersion `json:"privateIPAddressVersion,omitempty"`
	// ApplicationGatewayBackendAddressPools - The application gateway backend address pools.
	ApplicationGatewayBackendAddressPools *[]SubResource `json:"applicationGatewayBackendAddressPools,omitempty"`
	// ApplicationSecurityGroups - Specifies an array of references to application security group.
	ApplicationSecurityGroups *[]SubResource `json:"applicationSecurityGroups,omitempty"`
	// LoadBalancerBackendAddressPools - The load balancer backend address pools.
	LoadBalancerBackendAddressPools *[]SubResource `json:"loadBalancerBackendAddressPools,omitempty"`
	// LoadBalancerInboundNatPools - The load balancer inbound nat pools.
	LoadBalancerInboundNatPools *[]SubResource `json:"loadBalancerInboundNatPools,omitempty"`
}

// VirtualMachineScaleSetUpdateNetworkConfiguration describes a virtual machine scale set network profile's
// network configurations.
type VirtualMachineScaleSetUpdateNetworkConfiguration struct {
	// Name - The network configuration name.
	Name                                                        *string `json:"name,omitempty"`
	*VirtualMachineScaleSetUpdateNetworkConfigurationProperties `json:"properties,omitempty"`
	// ID - Resource Id
	ID *string `json:"id,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetUpdateNetworkConfiguration.
func (vmssunc VirtualMachineScaleSetUpdateNetworkConfiguration) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssunc.Name != nil {
		objectMap["name"] = vmssunc.Name
	}
	if vmssunc.VirtualMachineScaleSetUpdateNetworkConfigurationProperties != nil {
		objectMap["properties"] = vmssunc.VirtualMachineScaleSetUpdateNetworkConfigurationProperties
	}
	if vmssunc.ID != nil {
		objectMap["id"] = vmssunc.ID
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetUpdateNetworkConfiguration struct.
func (vmssunc *VirtualMachineScaleSetUpdateNetworkConfiguration) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmssunc.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetUpdateNetworkConfigurationProperties VirtualMachineScaleSetUpdateNetworkConfigurationProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetUpdateNetworkConfigurationProperties)
				if err != nil {
					return err
				}
				vmssunc.VirtualMachineScaleSetUpdateNetworkConfigurationProperties = &virtualMachineScaleSetUpdateNetworkConfigurationProperties
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmssunc.ID = &ID
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetUpdateNetworkConfigurationProperties describes a virtual machine scale set
// updatable network profile's IP configuration.Use this object for updating network profile's IP
// Configuration.
type VirtualMachineScaleSetUpdateNetworkConfigurationProperties struct {
	// Primary - Whether this is a primary NIC on a virtual machine.
	Primary *bool `json:"primary,omitempty"`
	// EnableAcceleratedNetworking - Specifies whether the network interface is accelerated networking-enabled.
	EnableAcceleratedNetworking *bool `json:"enableAcceleratedNetworking,omitempty"`
	// NetworkSecurityGroup - The network security group.
	NetworkSecurityGroup *SubResource `json:"networkSecurityGroup,omitempty"`
	// DNSSettings - The dns settings to be applied on the network interfaces.
	DNSSettings *VirtualMachineScaleSetNetworkConfigurationDNSSettings `json:"dnsSettings,omitempty"`
	// IPConfigurations - The virtual machine scale set IP Configuration.
	IPConfigurations *[]VirtualMachineScaleSetUpdateIPConfiguration `json:"ipConfigurations,omitempty"`
	// EnableIPForwarding - Whether IP forwarding enabled on this NIC.
	EnableIPForwarding *bool `json:"enableIPForwarding,omitempty"`
}

// VirtualMachineScaleSetUpdateNetworkProfile describes a virtual machine scale set network profile.
type VirtualMachineScaleSetUpdateNetworkProfile struct {
	// NetworkInterfaceConfigurations - The list of network configurations.
	NetworkInterfaceConfigurations *[]VirtualMachineScaleSetUpdateNetworkConfiguration `json:"networkInterfaceConfigurations,omitempty"`
}

// VirtualMachineScaleSetUpdateOSDisk describes virtual machine scale set operating system disk Update
// Object. This should be used for Updating VMSS OS Disk.
type VirtualMachineScaleSetUpdateOSDisk struct {
	// Caching - The caching type. Possible values include: 'CachingTypesNone', 'CachingTypesReadOnly', 'CachingTypesReadWrite'
	Caching CachingTypes `json:"caching,omitempty"`
	// WriteAcceleratorEnabled - Specifies whether writeAccelerator should be enabled or disabled on the disk.
	WriteAcceleratorEnabled *bool `json:"writeAcceleratorEnabled,omitempty"`
	// DiskSizeGB - Specifies the size of the operating system disk in gigabytes. This element can be used to overwrite the size of the disk in a virtual machine image. <br><br> This value cannot be larger than 1023 GB
	DiskSizeGB *int32 `json:"diskSizeGB,omitempty"`
	// Image - The Source User Image VirtualHardDisk. This VirtualHardDisk will be copied before using it to attach to the Virtual Machine. If SourceImage is provided, the destination VirtualHardDisk should not exist.
	Image *VirtualHardDisk `json:"image,omitempty"`
	// VhdContainers - The list of virtual hard disk container uris.
	VhdContainers *[]string `json:"vhdContainers,omitempty"`
	// ManagedDisk - The managed disk parameters.
	ManagedDisk *VirtualMachineScaleSetManagedDiskParameters `json:"managedDisk,omitempty"`
}

// VirtualMachineScaleSetUpdateOSProfile describes a virtual machine scale set OS profile.
type VirtualMachineScaleSetUpdateOSProfile struct {
	// CustomData - A base-64 encoded string of custom data.
	CustomData *string `json:"customData,omitempty"`
	// WindowsConfiguration - The Windows Configuration of the OS profile.
	WindowsConfiguration *WindowsConfiguration `json:"windowsConfiguration,omitempty"`
	// LinuxConfiguration - The Linux Configuration of the OS profile.
	LinuxConfiguration *LinuxConfiguration `json:"linuxConfiguration,omitempty"`
	// Secrets - The List of certificates for addition to the VM.
	Secrets *[]VaultSecretGroup `json:"secrets,omitempty"`
}

// VirtualMachineScaleSetUpdateProperties describes the properties of a Virtual Machine Scale Set.
type VirtualMachineScaleSetUpdateProperties struct {
	// UpgradePolicy - The upgrade policy.
	UpgradePolicy *UpgradePolicy `json:"upgradePolicy,omitempty"`
	// AutomaticRepairsPolicy - Policy for automatic repairs.
	AutomaticRepairsPolicy *AutomaticRepairsPolicy `json:"automaticRepairsPolicy,omitempty"`
	// VirtualMachineProfile - The virtual machine profile.
	VirtualMachineProfile *VirtualMachineScaleSetUpdateVMProfile `json:"virtualMachineProfile,omitempty"`
	// Overprovision - Specifies whether the Virtual Machine Scale Set should be overprovisioned.
	Overprovision *bool `json:"overprovision,omitempty"`
	// DoNotRunExtensionsOnOverprovisionedVMs - When Overprovision is enabled, extensions are launched only on the requested number of VMs which are finally kept. This property will hence ensure that the extensions do not run on the extra overprovisioned VMs.
	DoNotRunExtensionsOnOverprovisionedVMs *bool `json:"doNotRunExtensionsOnOverprovisionedVMs,omitempty"`
	// SinglePlacementGroup - When true this limits the scale set to a single placement group, of max size 100 virtual machines.
	SinglePlacementGroup *bool `json:"singlePlacementGroup,omitempty"`
}

// VirtualMachineScaleSetUpdatePublicIPAddressConfiguration describes a virtual machines scale set IP
// Configuration's PublicIPAddress configuration
type VirtualMachineScaleSetUpdatePublicIPAddressConfiguration struct {
	// Name - The publicIP address configuration name.
	Name                                                                *string `json:"name,omitempty"`
	*VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties `json:"properties,omitempty"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetUpdatePublicIPAddressConfiguration.
func (vmssupiac VirtualMachineScaleSetUpdatePublicIPAddressConfiguration) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssupiac.Name != nil {
		objectMap["name"] = vmssupiac.Name
	}
	if vmssupiac.VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties != nil {
		objectMap["properties"] = vmssupiac.VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetUpdatePublicIPAddressConfiguration struct.
func (vmssupiac *VirtualMachineScaleSetUpdatePublicIPAddressConfiguration) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmssupiac.Name = &name
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties)
				if err != nil {
					return err
				}
				vmssupiac.VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties = &virtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties describes a virtual machines scale
// set IP Configuration's PublicIPAddress configuration
type VirtualMachineScaleSetUpdatePublicIPAddressConfigurationProperties struct {
	// IdleTimeoutInMinutes - The idle timeout of the public IP address.
	IdleTimeoutInMinutes *int32 `json:"idleTimeoutInMinutes,omitempty"`
	// DNSSettings - The dns settings to be applied on the publicIP addresses .
	DNSSettings *VirtualMachineScaleSetPublicIPAddressConfigurationDNSSettings `json:"dnsSettings,omitempty"`
}

// VirtualMachineScaleSetUpdateStorageProfile describes a virtual machine scale set storage profile.
type VirtualMachineScaleSetUpdateStorageProfile struct {
	// ImageReference - The image reference.
	ImageReference *ImageReference `json:"imageReference,omitempty"`
	// OsDisk - The OS disk.
	OsDisk *VirtualMachineScaleSetUpdateOSDisk `json:"osDisk,omitempty"`
	// DataDisks - The data disks.
	DataDisks *[]VirtualMachineScaleSetDataDisk `json:"dataDisks,omitempty"`
}

// VirtualMachineScaleSetUpdateVMProfile describes a virtual machine scale set virtual machine profile.
type VirtualMachineScaleSetUpdateVMProfile struct {
	// OsProfile - The virtual machine scale set OS profile.
	OsProfile *VirtualMachineScaleSetUpdateOSProfile `json:"osProfile,omitempty"`
	// StorageProfile - The virtual machine scale set storage profile.
	StorageProfile *VirtualMachineScaleSetUpdateStorageProfile `json:"storageProfile,omitempty"`
	// NetworkProfile - The virtual machine scale set network profile.
	NetworkProfile *VirtualMachineScaleSetUpdateNetworkProfile `json:"networkProfile,omitempty"`
	// DiagnosticsProfile - The virtual machine scale set diagnostics profile.
	DiagnosticsProfile *DiagnosticsProfile `json:"diagnosticsProfile,omitempty"`
	// ExtensionProfile - The virtual machine scale set extension profile.
	ExtensionProfile *VirtualMachineScaleSetExtensionProfile `json:"extensionProfile,omitempty"`
	// LicenseType - The license type, which is for bring your own license scenario.
	LicenseType *string `json:"licenseType,omitempty"`
}

// VirtualMachineScaleSetVM describes a virtual machine scale set virtual machine.
type VirtualMachineScaleSetVM struct {
	autorest.Response `json:"-"`
	// InstanceID - READ-ONLY; The virtual machine instance ID.
	InstanceID *string `json:"instanceId,omitempty"`
	// Sku - READ-ONLY; The virtual machine SKU.
	Sku                                 *Sku `json:"sku,omitempty"`
	*VirtualMachineScaleSetVMProperties `json:"properties,omitempty"`
	// Plan - Specifies information about the marketplace image used to create the virtual machine. This element is only used for marketplace images. Before you can use a marketplace image from an API, you must enable the image for programmatic use.  In the Azure portal, find the marketplace image that you want to use and then click **Want to deploy programmatically, Get Started ->**. Enter any required information and then click **Save**.
	Plan *Plan `json:"plan,omitempty"`
	// Resources - READ-ONLY; The virtual machine child extension resources.
	Resources *[]VirtualMachineExtension `json:"resources,omitempty"`
	// Zones - READ-ONLY; The virtual machine zones.
	Zones *[]string `json:"zones,omitempty"`
	// ID - READ-ONLY; Resource Id
	ID *string `json:"id,omitempty"`
	// Name - READ-ONLY; Resource name
	Name *string `json:"name,omitempty"`
	// Type - READ-ONLY; Resource type
	Type *string `json:"type,omitempty"`
	// Location - Resource location
	Location *string `json:"location,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineScaleSetVM.
func (vmssv VirtualMachineScaleSetVM) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmssv.VirtualMachineScaleSetVMProperties != nil {
		objectMap["properties"] = vmssv.VirtualMachineScaleSetVMProperties
	}
	if vmssv.Plan != nil {
		objectMap["plan"] = vmssv.Plan
	}
	if vmssv.Location != nil {
		objectMap["location"] = vmssv.Location
	}
	if vmssv.Tags != nil {
		objectMap["tags"] = vmssv.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineScaleSetVM struct.
func (vmssv *VirtualMachineScaleSetVM) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "instanceId":
			if v != nil {
				var instanceID string
				err = json.Unmarshal(*v, &instanceID)
				if err != nil {
					return err
				}
				vmssv.InstanceID = &instanceID
			}
		case "sku":
			if v != nil {
				var sku Sku
				err = json.Unmarshal(*v, &sku)
				if err != nil {
					return err
				}
				vmssv.Sku = &sku
			}
		case "properties":
			if v != nil {
				var virtualMachineScaleSetVMProperties VirtualMachineScaleSetVMProperties
				err = json.Unmarshal(*v, &virtualMachineScaleSetVMProperties)
				if err != nil {
					return err
				}
				vmssv.VirtualMachineScaleSetVMProperties = &virtualMachineScaleSetVMProperties
			}
		case "plan":
			if v != nil {
				var plan Plan
				err = json.Unmarshal(*v, &plan)
				if err != nil {
					return err
				}
				vmssv.Plan = &plan
			}
		case "resources":
			if v != nil {
				var resources []VirtualMachineExtension
				err = json.Unmarshal(*v, &resources)
				if err != nil {
					return err
				}
				vmssv.Resources = &resources
			}
		case "zones":
			if v != nil {
				var zones []string
				err = json.Unmarshal(*v, &zones)
				if err != nil {
					return err
				}
				vmssv.Zones = &zones
			}
		case "id":
			if v != nil {
				var ID string
				err = json.Unmarshal(*v, &ID)
				if err != nil {
					return err
				}
				vmssv.ID = &ID
			}
		case "name":
			if v != nil {
				var name string
				err = json.Unmarshal(*v, &name)
				if err != nil {
					return err
				}
				vmssv.Name = &name
			}
		case "type":
			if v != nil {
				var typeVar string
				err = json.Unmarshal(*v, &typeVar)
				if err != nil {
					return err
				}
				vmssv.Type = &typeVar
			}
		case "location":
			if v != nil {
				var location string
				err = json.Unmarshal(*v, &location)
				if err != nil {
					return err
				}
				vmssv.Location = &location
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmssv.Tags = tags
			}
		}
	}

	return nil
}

// VirtualMachineScaleSetVMExtensionsSummary extensions summary for virtual machines of a virtual machine
// scale set.
type VirtualMachineScaleSetVMExtensionsSummary struct {
	// Name - READ-ONLY; The extension name.
	Name *string `json:"name,omitempty"`
	// StatusesSummary - READ-ONLY; The extensions information.
	StatusesSummary *[]VirtualMachineStatusCodeCount `json:"statusesSummary,omitempty"`
}

// VirtualMachineScaleSetVMInstanceIDs specifies a list of virtual machine instance IDs from the VM scale
// set.
type VirtualMachineScaleSetVMInstanceIDs struct {
	// InstanceIds - The virtual machine scale set instance ids. Omitting the virtual machine scale set instance ids will result in the operation being performed on all virtual machines in the virtual machine scale set.
	InstanceIds *[]string `json:"instanceIds,omitempty"`
}

// VirtualMachineScaleSetVMInstanceRequiredIDs specifies a list of virtual machine instance IDs from the VM
// scale set.
type VirtualMachineScaleSetVMInstanceRequiredIDs struct {
	// InstanceIds - The virtual machine scale set instance ids.
	InstanceIds *[]string `json:"instanceIds,omitempty"`
}

// VirtualMachineScaleSetVMInstanceView the instance view of a virtual machine scale set VM.
type VirtualMachineScaleSetVMInstanceView struct {
	autorest.Response `json:"-"`
	// PlatformUpdateDomain - The Update Domain count.
	PlatformUpdateDomain *int32 `json:"platformUpdateDomain,omitempty"`
	// PlatformFaultDomain - The Fault Domain count.
	PlatformFaultDomain *int32 `json:"platformFaultDomain,omitempty"`
	// RdpThumbPrint - The Remote desktop certificate thumbprint.
	RdpThumbPrint *string `json:"rdpThumbPrint,omitempty"`
	// VMAgent - The VM Agent running on the virtual machine.
	VMAgent *VirtualMachineAgentInstanceView `json:"vmAgent,omitempty"`
	// MaintenanceRedeployStatus - The Maintenance Operation status on the virtual machine.
	MaintenanceRedeployStatus *MaintenanceRedeployStatus `json:"maintenanceRedeployStatus,omitempty"`
	// Disks - The disks information.
	Disks *[]DiskInstanceView `json:"disks,omitempty"`
	// Extensions - The extensions information.
	Extensions *[]VirtualMachineExtensionInstanceView `json:"extensions,omitempty"`
	// VMHealth - READ-ONLY; The health status for the VM.
	VMHealth *VirtualMachineHealthStatus `json:"vmHealth,omitempty"`
	// BootDiagnostics - Boot Diagnostics is a debugging feature which allows you to view Console Output and Screenshot to diagnose VM status. <br><br> You can easily view the output of your console log. <br><br> Azure also enables you to see a screenshot of the VM from the hypervisor.
	BootDiagnostics *BootDiagnosticsInstanceView `json:"bootDiagnostics,omitempty"`
	// Statuses - The resource status information.
	Statuses *[]InstanceViewStatus `json:"statuses,omitempty"`
	// PlacementGroupID - The placement group in which the VM is running. If the VM is deallocated it will not have a placementGroupId.
	PlacementGroupID *string `json:"placementGroupId,omitempty"`
}

// VirtualMachineScaleSetVMListResult the List Virtual Machine Scale Set VMs operation response.
type VirtualMachineScaleSetVMListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of virtual machine scale sets VMs.
	Value *[]VirtualMachineScaleSetVM `json:"value,omitempty"`
	// NextLink - The uri to fetch the next page of Virtual Machine Scale Set VMs. Call ListNext() with this to fetch the next page of VMSS VMs
	NextLink *string `json:"nextLink,omitempty"`
}

// VirtualMachineScaleSetVMListResultIterator provides access to a complete listing of
// VirtualMachineScaleSetVM values.
type VirtualMachineScaleSetVMListResultIterator struct {
	i    int
	page VirtualMachineScaleSetVMListResultPage
}

// NextWithContext advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
func (iter *VirtualMachineScaleSetVMListResultIterator) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetVMListResultIterator.NextWithContext")
		defer func() {
			sc := -1
			if iter.Response().Response.Response != nil {
				sc = iter.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	iter.i++
	if iter.i < len(iter.page.Values()) {
		return nil
	}
	err = iter.page.NextWithContext(ctx)
	if err != nil {
		iter.i--
		return err
	}
	iter.i = 0
	return nil
}

// Next advances to the next value.  If there was an error making
// the request the iterator does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (iter *VirtualMachineScaleSetVMListResultIterator) Next() error {
	return iter.NextWithContext(context.Background())
}

// NotDone returns true if the enumeration should be started or is not yet complete.
func (iter VirtualMachineScaleSetVMListResultIterator) NotDone() bool {
	return iter.page.NotDone() && iter.i < len(iter.page.Values())
}

// Response returns the raw server response from the last page request.
func (iter VirtualMachineScaleSetVMListResultIterator) Response() VirtualMachineScaleSetVMListResult {
	return iter.page.Response()
}

// Value returns the current value or a zero-initialized value if the
// iterator has advanced beyond the end of the collection.
func (iter VirtualMachineScaleSetVMListResultIterator) Value() VirtualMachineScaleSetVM {
	if !iter.page.NotDone() {
		return VirtualMachineScaleSetVM{}
	}
	return iter.page.Values()[iter.i]
}

// Creates a new instance of the VirtualMachineScaleSetVMListResultIterator type.
func NewVirtualMachineScaleSetVMListResultIterator(page VirtualMachineScaleSetVMListResultPage) VirtualMachineScaleSetVMListResultIterator {
	return VirtualMachineScaleSetVMListResultIterator{page: page}
}

// IsEmpty returns true if the ListResult contains no values.
func (vmssvlr VirtualMachineScaleSetVMListResult) IsEmpty() bool {
	return vmssvlr.Value == nil || len(*vmssvlr.Value) == 0
}

// virtualMachineScaleSetVMListResultPreparer prepares a request to retrieve the next set of results.
// It returns nil if no more results exist.
func (vmssvlr VirtualMachineScaleSetVMListResult) virtualMachineScaleSetVMListResultPreparer(ctx context.Context) (*http.Request, error) {
	if vmssvlr.NextLink == nil || len(to.String(vmssvlr.NextLink)) < 1 {
		return nil, nil
	}
	return autorest.Prepare((&http.Request{}).WithContext(ctx),
		autorest.AsJSON(),
		autorest.AsGet(),
		autorest.WithBaseURL(to.String(vmssvlr.NextLink)))
}

// VirtualMachineScaleSetVMListResultPage contains a page of VirtualMachineScaleSetVM values.
type VirtualMachineScaleSetVMListResultPage struct {
	fn      func(context.Context, VirtualMachineScaleSetVMListResult) (VirtualMachineScaleSetVMListResult, error)
	vmssvlr VirtualMachineScaleSetVMListResult
}

// NextWithContext advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
func (page *VirtualMachineScaleSetVMListResultPage) NextWithContext(ctx context.Context) (err error) {
	if tracing.IsEnabled() {
		ctx = tracing.StartSpan(ctx, fqdn+"/VirtualMachineScaleSetVMListResultPage.NextWithContext")
		defer func() {
			sc := -1
			if page.Response().Response.Response != nil {
				sc = page.Response().Response.Response.StatusCode
			}
			tracing.EndSpan(ctx, sc, err)
		}()
	}
	next, err := page.fn(ctx, page.vmssvlr)
	if err != nil {
		return err
	}
	page.vmssvlr = next
	return nil
}

// Next advances to the next page of values.  If there was an error making
// the request the page does not advance and the error is returned.
// Deprecated: Use NextWithContext() instead.
func (page *VirtualMachineScaleSetVMListResultPage) Next() error {
	return page.NextWithContext(context.Background())
}

// NotDone returns true if the page enumeration should be started or is not yet complete.
func (page VirtualMachineScaleSetVMListResultPage) NotDone() bool {
	return !page.vmssvlr.IsEmpty()
}

// Response returns the raw server response from the last page request.
func (page VirtualMachineScaleSetVMListResultPage) Response() VirtualMachineScaleSetVMListResult {
	return page.vmssvlr
}

// Values returns the slice of values for the current page or nil if there are no values.
func (page VirtualMachineScaleSetVMListResultPage) Values() []VirtualMachineScaleSetVM {
	if page.vmssvlr.IsEmpty() {
		return nil
	}
	return *page.vmssvlr.Value
}

// Creates a new instance of the VirtualMachineScaleSetVMListResultPage type.
func NewVirtualMachineScaleSetVMListResultPage(getNextPage func(context.Context, VirtualMachineScaleSetVMListResult) (VirtualMachineScaleSetVMListResult, error)) VirtualMachineScaleSetVMListResultPage {
	return VirtualMachineScaleSetVMListResultPage{fn: getNextPage}
}

// VirtualMachineScaleSetVMProfile describes a virtual machine scale set virtual machine profile.
type VirtualMachineScaleSetVMProfile struct {
	// OsProfile - Specifies the operating system settings for the virtual machines in the scale set.
	OsProfile *VirtualMachineScaleSetOSProfile `json:"osProfile,omitempty"`
	// StorageProfile - Specifies the storage settings for the virtual machine disks.
	StorageProfile *VirtualMachineScaleSetStorageProfile `json:"storageProfile,omitempty"`
	// AdditionalCapabilities - Specifies additional capabilities enabled or disabled on the virtual machine in the scale set. For instance: whether the virtual machine has the capability to support attaching managed data disks with UltraSSD_LRS storage account type.
	AdditionalCapabilities *AdditionalCapabilities `json:"additionalCapabilities,omitempty"`
	// NetworkProfile - Specifies properties of the network interfaces of the virtual machines in the scale set.
	NetworkProfile *VirtualMachineScaleSetNetworkProfile `json:"networkProfile,omitempty"`
	// DiagnosticsProfile - Specifies the boot diagnostic settings state. <br><br>Minimum api-version: 2015-06-15.
	DiagnosticsProfile *DiagnosticsProfile `json:"diagnosticsProfile,omitempty"`
	// ExtensionProfile - Specifies a collection of settings for extensions installed on virtual machines in the scale set.
	ExtensionProfile *VirtualMachineScaleSetExtensionProfile `json:"extensionProfile,omitempty"`
	// LicenseType - Specifies that the image or disk that is being used was licensed on-premises. This element is only used for images that contain the Windows Server operating system. <br><br> Possible values are: <br><br> Windows_Client <br><br> Windows_Server <br><br> If this element is included in a request for an update, the value must match the initial value. This value cannot be updated. <br><br> For more information, see [Azure Hybrid Use Benefit for Windows Server](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-hybrid-use-benefit-licensing?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> Minimum api-version: 2015-06-15
	LicenseType *string `json:"licenseType,omitempty"`
	// Priority - Specifies the priority for the virtual machines in the scale set. <br><br>Minimum api-version: 2017-10-30-preview. Possible values include: 'Regular', 'Low'
	Priority VirtualMachinePriorityTypes `json:"priority,omitempty"`
	// EvictionPolicy - Specifies the eviction policy for virtual machines in a low priority scale set. <br><br>Minimum api-version: 2017-10-30-preview. Possible values include: 'Deallocate', 'Delete'
	EvictionPolicy VirtualMachineEvictionPolicyTypes `json:"evictionPolicy,omitempty"`
}

// VirtualMachineScaleSetVMProperties describes the properties of a virtual machine scale set virtual
// machine.
type VirtualMachineScaleSetVMProperties struct {
	// LatestModelApplied - READ-ONLY; Specifies whether the latest model has been applied to the virtual machine.
	LatestModelApplied *bool `json:"latestModelApplied,omitempty"`
	// VMID - READ-ONLY; Azure VM unique ID.
	VMID *string `json:"vmId,omitempty"`
	// InstanceView - READ-ONLY; The virtual machine instance view.
	InstanceView *VirtualMachineScaleSetVMInstanceView `json:"instanceView,omitempty"`
	// HardwareProfile - Specifies the hardware settings for the virtual machine.
	HardwareProfile *HardwareProfile `json:"hardwareProfile,omitempty"`
	// StorageProfile - Specifies the storage settings for the virtual machine disks.
	StorageProfile *StorageProfile `json:"storageProfile,omitempty"`
	// AdditionalCapabilities - Specifies additional capabilities enabled or disabled on the virtual machine in the scale set. For instance: whether the virtual machine has the capability to support attaching managed data disks with UltraSSD_LRS storage account type.
	AdditionalCapabilities *AdditionalCapabilities `json:"additionalCapabilities,omitempty"`
	// OsProfile - Specifies the operating system settings for the virtual machine.
	OsProfile *OSProfile `json:"osProfile,omitempty"`
	// NetworkProfile - Specifies the network interfaces of the virtual machine.
	NetworkProfile *NetworkProfile `json:"networkProfile,omitempty"`
	// DiagnosticsProfile - Specifies the boot diagnostic settings state. <br><br>Minimum api-version: 2015-06-15.
	DiagnosticsProfile *DiagnosticsProfile `json:"diagnosticsProfile,omitempty"`
	// AvailabilitySet - Specifies information about the availability set that the virtual machine should be assigned to. Virtual machines specified in the same availability set are allocated to different nodes to maximize availability. For more information about availability sets, see [Manage the availability of virtual machines](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-manage-availability?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json). <br><br> For more information on Azure planned maintenance, see [Planned maintenance for virtual machines in Azure](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-planned-maintenance?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> Currently, a VM can only be added to availability set at creation time. An existing VM cannot be added to an availability set.
	AvailabilitySet *SubResource `json:"availabilitySet,omitempty"`
	// ProvisioningState - READ-ONLY; The provisioning state, which only appears in the response.
	ProvisioningState *string `json:"provisioningState,omitempty"`
	// LicenseType - Specifies that the image or disk that is being used was licensed on-premises. This element is only used for images that contain the Windows Server operating system. <br><br> Possible values are: <br><br> Windows_Client <br><br> Windows_Server <br><br> If this element is included in a request for an update, the value must match the initial value. This value cannot be updated. <br><br> For more information, see [Azure Hybrid Use Benefit for Windows Server](https://docs.microsoft.com/azure/virtual-machines/virtual-machines-windows-hybrid-use-benefit-licensing?toc=%2fazure%2fvirtual-machines%2fwindows%2ftoc.json) <br><br> Minimum api-version: 2015-06-15
	LicenseType *string `json:"licenseType,omitempty"`
}

// VirtualMachineScaleSetVMReimageParameters describes a Virtual Machine Scale Set VM Reimage Parameters.
type VirtualMachineScaleSetVMReimageParameters struct {
	// TempDisk - Specifies whether to reimage temp disk. Default value: false. Note: This temp disk reimage parameter is only supported for VM/VMSS with Ephemeral OS disk.
	TempDisk *bool `json:"tempDisk,omitempty"`
}

// VirtualMachineScaleSetVMsDeallocateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsDeallocateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsDeallocateFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsDeallocateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsDeallocateFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsDeleteFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsDeleteFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsPerformMaintenanceFuture an abstraction for monitoring and retrieving the
// results of a long-running operation.
type VirtualMachineScaleSetVMsPerformMaintenanceFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsPerformMaintenanceFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsPerformMaintenanceFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsPerformMaintenanceFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsPowerOffFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsPowerOffFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsPowerOffFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsPowerOffFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsPowerOffFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsRedeployFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsRedeployFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsRedeployFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsRedeployFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsRedeployFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsReimageAllFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsReimageAllFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsReimageAllFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsReimageAllFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsReimageAllFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsReimageFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsReimageFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsReimageFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsReimageFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsReimageFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsRestartFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsRestartFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsRestartFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsRestartFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsRestartFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsRunCommandFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsRunCommandFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsRunCommandFuture) Result(client VirtualMachineScaleSetVMsClient) (rcr RunCommandResult, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsRunCommandFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsRunCommandFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if rcr.Response.Response, err = future.GetResult(sender); err == nil && rcr.Response.Response.StatusCode != http.StatusNoContent {
		rcr, err = client.RunCommandResponder(rcr.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsRunCommandFuture", "Result", rcr.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineScaleSetVMsStartFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsStartFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsStartFuture) Result(client VirtualMachineScaleSetVMsClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsStartFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsStartFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineScaleSetVMsUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachineScaleSetVMsUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachineScaleSetVMsUpdateFuture) Result(client VirtualMachineScaleSetVMsClient) (vmssv VirtualMachineScaleSetVM, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachineScaleSetVMsUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vmssv.Response.Response, err = future.GetResult(sender); err == nil && vmssv.Response.Response.StatusCode != http.StatusNoContent {
		vmssv, err = client.UpdateResponder(vmssv.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachineScaleSetVMsUpdateFuture", "Result", vmssv.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachinesCaptureFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesCaptureFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesCaptureFuture) Result(client VirtualMachinesClient) (vmcr VirtualMachineCaptureResult, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesCaptureFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesCaptureFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if vmcr.Response.Response, err = future.GetResult(sender); err == nil && vmcr.Response.Response.StatusCode != http.StatusNoContent {
		vmcr, err = client.CaptureResponder(vmcr.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachinesCaptureFuture", "Result", vmcr.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachinesConvertToManagedDisksFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachinesConvertToManagedDisksFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesConvertToManagedDisksFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesConvertToManagedDisksFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesConvertToManagedDisksFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesCreateOrUpdateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachinesCreateOrUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesCreateOrUpdateFuture) Result(client VirtualMachinesClient) (VM VirtualMachine, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesCreateOrUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesCreateOrUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if VM.Response.Response, err = future.GetResult(sender); err == nil && VM.Response.Response.StatusCode != http.StatusNoContent {
		VM, err = client.CreateOrUpdateResponder(VM.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachinesCreateOrUpdateFuture", "Result", VM.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachinesDeallocateFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachinesDeallocateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesDeallocateFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesDeallocateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesDeallocateFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesDeleteFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesDeleteFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesDeleteFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesDeleteFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesDeleteFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineSize describes the properties of a VM size.
type VirtualMachineSize struct {
	// Name - The name of the virtual machine size.
	Name *string `json:"name,omitempty"`
	// NumberOfCores - The number of cores supported by the virtual machine size.
	NumberOfCores *int32 `json:"numberOfCores,omitempty"`
	// OsDiskSizeInMB - The OS disk size, in MB, allowed by the virtual machine size.
	OsDiskSizeInMB *int32 `json:"osDiskSizeInMB,omitempty"`
	// ResourceDiskSizeInMB - The resource disk size, in MB, allowed by the virtual machine size.
	ResourceDiskSizeInMB *int32 `json:"resourceDiskSizeInMB,omitempty"`
	// MemoryInMB - The amount of memory, in MB, supported by the virtual machine size.
	MemoryInMB *int32 `json:"memoryInMB,omitempty"`
	// MaxDataDiskCount - The maximum number of data disks that can be attached to the virtual machine size.
	MaxDataDiskCount *int32 `json:"maxDataDiskCount,omitempty"`
}

// VirtualMachineSizeListResult the List Virtual Machine operation response.
type VirtualMachineSizeListResult struct {
	autorest.Response `json:"-"`
	// Value - The list of virtual machine sizes.
	Value *[]VirtualMachineSize `json:"value,omitempty"`
}

// VirtualMachinesPerformMaintenanceFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachinesPerformMaintenanceFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesPerformMaintenanceFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesPerformMaintenanceFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesPerformMaintenanceFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesPowerOffFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesPowerOffFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesPowerOffFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesPowerOffFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesPowerOffFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesRedeployFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesRedeployFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesRedeployFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesRedeployFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesRedeployFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesReimageFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesReimageFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesReimageFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesReimageFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesReimageFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesRestartFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesRestartFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesRestartFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesRestartFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesRestartFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachinesRunCommandFuture an abstraction for monitoring and retrieving the results of a
// long-running operation.
type VirtualMachinesRunCommandFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesRunCommandFuture) Result(client VirtualMachinesClient) (rcr RunCommandResult, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesRunCommandFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesRunCommandFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if rcr.Response.Response, err = future.GetResult(sender); err == nil && rcr.Response.Response.StatusCode != http.StatusNoContent {
		rcr, err = client.RunCommandResponder(rcr.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachinesRunCommandFuture", "Result", rcr.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachinesStartFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesStartFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesStartFuture) Result(client VirtualMachinesClient) (ar autorest.Response, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesStartFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesStartFuture")
		return
	}
	ar.Response = future.Response()
	return
}

// VirtualMachineStatusCodeCount the status code and count of the virtual machine scale set instance view
// status summary.
type VirtualMachineStatusCodeCount struct {
	// Code - READ-ONLY; The instance view status code.
	Code *string `json:"code,omitempty"`
	// Count - READ-ONLY; The number of instances having a particular status code.
	Count *int32 `json:"count,omitempty"`
}

// VirtualMachinesUpdateFuture an abstraction for monitoring and retrieving the results of a long-running
// operation.
type VirtualMachinesUpdateFuture struct {
	azure.Future
}

// Result returns the result of the asynchronous operation.
// If the operation has not completed it will return an error.
func (future *VirtualMachinesUpdateFuture) Result(client VirtualMachinesClient) (VM VirtualMachine, err error) {
	var done bool
	done, err = future.DoneWithContext(context.Background(), client)
	if err != nil {
		err = autorest.NewErrorWithError(err, "compute.VirtualMachinesUpdateFuture", "Result", future.Response(), "Polling failure")
		return
	}
	if !done {
		err = azure.NewAsyncOpIncompleteError("compute.VirtualMachinesUpdateFuture")
		return
	}
	sender := autorest.DecorateSender(client, autorest.DoRetryForStatusCodes(client.RetryAttempts, client.RetryDuration, autorest.StatusCodesForRetry...))
	if VM.Response.Response, err = future.GetResult(sender); err == nil && VM.Response.Response.StatusCode != http.StatusNoContent {
		VM, err = client.UpdateResponder(VM.Response.Response)
		if err != nil {
			err = autorest.NewErrorWithError(err, "compute.VirtualMachinesUpdateFuture", "Result", VM.Response.Response, "Failure responding to request")
		}
	}
	return
}

// VirtualMachineUpdate describes a Virtual Machine Update.
type VirtualMachineUpdate struct {
	// Plan - Specifies information about the marketplace image used to create the virtual machine. This element is only used for marketplace images. Before you can use a marketplace image from an API, you must enable the image for programmatic use.  In the Azure portal, find the marketplace image that you want to use and then click **Want to deploy programmatically, Get Started ->**. Enter any required information and then click **Save**.
	Plan                      *Plan `json:"plan,omitempty"`
	*VirtualMachineProperties `json:"properties,omitempty"`
	// Identity - The identity of the virtual machine, if configured.
	Identity *VirtualMachineIdentity `json:"identity,omitempty"`
	// Zones - The virtual machine zones.
	Zones *[]string `json:"zones,omitempty"`
	// Tags - Resource tags
	Tags map[string]*string `json:"tags"`
}

// MarshalJSON is the custom marshaler for VirtualMachineUpdate.
func (vmu VirtualMachineUpdate) MarshalJSON() ([]byte, error) {
	objectMap := make(map[string]interface{})
	if vmu.Plan != nil {
		objectMap["plan"] = vmu.Plan
	}
	if vmu.VirtualMachineProperties != nil {
		objectMap["properties"] = vmu.VirtualMachineProperties
	}
	if vmu.Identity != nil {
		objectMap["identity"] = vmu.Identity
	}
	if vmu.Zones != nil {
		objectMap["zones"] = vmu.Zones
	}
	if vmu.Tags != nil {
		objectMap["tags"] = vmu.Tags
	}
	return json.Marshal(objectMap)
}

// UnmarshalJSON is the custom unmarshaler for VirtualMachineUpdate struct.
func (vmu *VirtualMachineUpdate) UnmarshalJSON(body []byte) error {
	var m map[string]*json.RawMessage
	err := json.Unmarshal(body, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		switch k {
		case "plan":
			if v != nil {
				var plan Plan
				err = json.Unmarshal(*v, &plan)
				if err != nil {
					return err
				}
				vmu.Plan = &plan
			}
		case "properties":
			if v != nil {
				var virtualMachineProperties VirtualMachineProperties
				err = json.Unmarshal(*v, &virtualMachineProperties)
				if err != nil {
					return err
				}
				vmu.VirtualMachineProperties = &virtualMachineProperties
			}
		case "identity":
			if v != nil {
				var identity VirtualMachineIdentity
				err = json.Unmarshal(*v, &identity)
				if err != nil {
					return err
				}
				vmu.Identity = &identity
			}
		case "zones":
			if v != nil {
				var zones []string
				err = json.Unmarshal(*v, &zones)
				if err != nil {
					return err
				}
				vmu.Zones = &zones
			}
		case "tags":
			if v != nil {
				var tags map[string]*string
				err = json.Unmarshal(*v, &tags)
				if err != nil {
					return err
				}
				vmu.Tags = tags
			}
		}
	}

	return nil
}

// WindowsConfiguration specifies Windows operating system settings on the virtual machine.
type WindowsConfiguration struct {
	// ProvisionVMAgent - Indicates whether virtual machine agent should be provisioned on the virtual machine. <br><br> When this property is not specified in the request body, default behavior is to set it to true.  This will ensure that VM Agent is installed on the VM so that extensions can be added to the VM later.
	ProvisionVMAgent *bool `json:"provisionVMAgent,omitempty"`
	// EnableAutomaticUpdates - Indicates whether virtual machine is enabled for automatic Windows updates. Default value is true. <br><br> For virtual machine scale sets, this property can be updated and updates will take effect on OS reprovisioning.
	EnableAutomaticUpdates *bool `json:"enableAutomaticUpdates,omitempty"`
	// TimeZone - Specifies the time zone of the virtual machine. e.g. "Pacific Standard Time"
	TimeZone *string `json:"timeZone,omitempty"`
	// AdditionalUnattendContent - Specifies additional base-64 encoded XML formatted information that can be included in the Unattend.xml file, which is used by Windows Setup.
	AdditionalUnattendContent *[]AdditionalUnattendContent `json:"additionalUnattendContent,omitempty"`
	// WinRM - Specifies the Windows Remote Management listeners. This enables remote Windows PowerShell.
	WinRM *WinRMConfiguration `json:"winRM,omitempty"`
}

// WinRMConfiguration describes Windows Remote Management configuration of the VM
type WinRMConfiguration struct {
	// Listeners - The list of Windows Remote Management listeners
	Listeners *[]WinRMListener `json:"listeners,omitempty"`
}

// WinRMListener describes Protocol and thumbprint of Windows Remote Management listener
type WinRMListener struct {
	// Protocol - Specifies the protocol of listener. <br><br> Possible values are: <br>**http** <br><br> **https**. Possible values include: 'HTTP', 'HTTPS'
	Protocol ProtocolTypes `json:"protocol,omitempty"`
	// CertificateURL - This is the URL of a certificate that has been uploaded to Key Vault as a secret. For adding a secret to the Key Vault, see [Add a key or secret to the key vault](https://docs.microsoft.com/azure/key-vault/key-vault-get-started/#add). In this case, your certificate needs to be It is the Base64 encoding of the following JSON Object which is encoded in UTF-8: <br><br> {<br>  "data":"<Base64-encoded-certificate>",<br>  "dataType":"pfx",<br>  "password":"<pfx-file-password>"<br>}
	CertificateURL *string `json:"certificateUrl,omitempty"`
}
