language: go
sudo: false
go:
  - 1.10.x
install:
  - go get -v github.com/golang/lint/golint
  - go get golang.org/x/tools/cmd/cover
  - go get github.com/mattn/goveralls
  - go get -d -t -v ./...
  - go build -v ./...
script:
  - go vet ./...
  - $HOME/gopath/bin/golint .
  - go test -v -race ./...
  - go test -v -covermode=count -coverprofile=cov.out
  - $HOME/gopath/bin/goveralls -coverprofile=cov.out -service=travis-ci -repotoken "$COVERALLS_TOKEN" || true
