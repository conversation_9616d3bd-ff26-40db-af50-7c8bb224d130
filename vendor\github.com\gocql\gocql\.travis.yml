language: go

sudo: required
dist: trusty

cache:
  directories:
    - $HOME/.ccm/repository
    - $HOME/.local/lib/python2.7

matrix:
  fast_finish: true

branches:
  only:
    - master

env:
  global:
    - GOMAXPROCS=2
  matrix:
    - CASS=2.1.21
      AUTH=true
    - CASS=2.2.14
      AUTH=true
    - CASS=2.2.14
      AUTH=false
    - CASS=3.0.18
      AUTH=false
    - CASS=3.11.4
      AUTH=false

go:
  - 1.12.x
  - 1.13.x

install:
  - ./install_test_deps.sh $TRAVIS_REPO_SLUG
  - cd ../..
  - cd gocql/gocql
  - go get .

script:
  - set -e
  - PATH=$PATH:$HOME/.local/bin bash integration.sh $CASS $AUTH
  - go vet .

notifications:
  - email: false
