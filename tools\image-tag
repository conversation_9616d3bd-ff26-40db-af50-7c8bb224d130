#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail

WIP=$(git diff --quiet || echo '-WIP')
BRANCH=$(git rev-parse --abbrev-ref HEAD | sed 's#/#-#g')
# When 7 chars are not enough to be unique, git automatically uses more.
# We are forcing to 7 here, as we are doing for grafana/grafana as well.
SHA=$(git rev-parse --short=7 HEAD | head -c7)

# If not a tag, use branch-hash else use tag
TAG=$((git describe --exact-match 2> /dev/null || echo "") | sed 's/v//g')

if [ -z "$TAG" ]
then
      echo "${BRANCH}"-"${SHA}""${WIP}"
else
      echo "${TAG}"
fi
