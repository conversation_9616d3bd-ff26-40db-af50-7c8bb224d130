# See https://github.com/golangci/golangci-lint#config-file
run:
  issues-exit-code: 1 #Default
  tests: true #Default

linters:
  # Disable everything by default so upgrades to not include new "default
  # enabled" linters.
  disable-all: true
  # Specifically enable linters we want to use.
  enable:
    - deadcode
    - errcheck
    - gofmt
    - goimports
    - gosimple
    - govet
    - ineffassign
    - misspell
    - revive
    - staticcheck
    - structcheck
    - typecheck
    - unused
    - varcheck


issues:
  exclude-rules:
    # helpers in tests often (rightfully) pass a *testing.T as their first argument
    - path: _test\.go
      text: "context.Context should be the first parameter of a function"
      linters:
        - revive
    # Yes, they are, but it's okay in a test
    - path: _test\.go
      text: "exported func.*returns unexported type.*which can be annoying to use"
      linters:
        - revive

linters-settings:
  misspell:
    locale: US
    ignore-words:
      - cancelled
  goimports:
    local-prefixes: go.opentelemetry.io
