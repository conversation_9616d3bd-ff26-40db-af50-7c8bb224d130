- job:
    name: gophercloud-unittest
    parent: golang-test
    description: |
      Run gophercloud unit test
    run: .zuul/playbooks/gophercloud-unittest/run.yaml
    nodeset: ubuntu-xenial-ut

- job:
    name: gophercloud-acceptance-test-base
    parent: golang-test
    run: .zuul/playbooks/gophercloud-acceptance-test/run.yaml
    description: |
      Base job for all gophercloud acceptance tests
    timeout: 18000 # 5 hours
    abstract: true
    nodeset: ubuntu-focal
    irrelevant-files:
      - ^.*\.md$
      - ^LICENSE$

- job:
    name: gophercloud-acceptance-test-compute
    parent: gophercloud-acceptance-test-base
    description: |
      Run gophercloud compute acceptance test on master branch. This runs when any file is patched
      except if it's doc.
    vars:
      # the list of all available tests can generated by:
      # find acceptance/openstack -name '*_test.go' -exec dirname {} \; | sort -n | uniq -c | awk '{print $2}'
      acceptance_tests:
        - acceptance/openstack
        - acceptance/openstack/compute/v2
        - acceptance/openstack/container/v1
        - acceptance/openstack/identity/v2
        - acceptance/openstack/identity/v3
        - acceptance/openstack/keymanager/v1
        - acceptance/openstack/orchestration/v1
        - acceptance/openstack/placement/v1
      devstack_services:
        - barbican
        - heat
        - zun

- job:
    name: gophercloud-acceptance-test-networking
    parent: gophercloud-acceptance-test-base
    description: |
      Run gophercloud networking acceptance test on master branch
    files:
      - ^.*dns.*$
      - ^.*loadbalancer.*$
      - ^.*networking.*$
    vars:
      prefetch_amphora: true
      devstack_env:
        OCTAVIA_AMP_IMAGE_FILE: /opt/octavia-amphora/amphora-x64-haproxy.qcow2
      # the list of all available tests can generated by:
      # find acceptance/openstack -name '*_test.go' -exec dirname {} \; | sort -n | uniq -c | awk '{print $2}'
      acceptance_tests:
        - acceptance/openstack/dns/v2
        - acceptance/openstack/loadbalancer/v2
        - acceptance/openstack/networking/v2
        - acceptance/openstack/networking/v2/extensions
        - acceptance/openstack/networking/v2/extensions/agents
        - acceptance/openstack/networking/v2/extensions/dns
        - acceptance/openstack/networking/v2/extensions/layer3
        - acceptance/openstack/networking/v2/extensions/mtu
        - acceptance/openstack/networking/v2/extensions/networkipavailabilities
        - acceptance/openstack/networking/v2/extensions/portsbinding
        - acceptance/openstack/networking/v2/extensions/qos/policies
        - acceptance/openstack/networking/v2/extensions/qos/rules
        - acceptance/openstack/networking/v2/extensions/qos/ruletypes
        - acceptance/openstack/networking/v2/extensions/quotas
        - acceptance/openstack/networking/v2/extensions/rbacpolicies
        - acceptance/openstack/networking/v2/extensions/subnetpools
        - acceptance/openstack/networking/v2/extensions/trunks
        - acceptance/openstack/networking/v2/extensions/vlantransparent
      devstack_projects: 'openstack/neutron-dynamic-routing'
      devstack_services:
        - designate
        - neutron-dynamic-routing
        - neutron-ext
        - octavia

- job:
    name: gophercloud-acceptance-test-storage
    parent: gophercloud-acceptance-test-base
    description: |
      Run gophercloud storage acceptance test on master branch
    files:
      - ^.*blockstorage.*$
      - ^.*imageservice.*$
      - ^.*objectstorage.*$
      - ^.*sharedfilesystems.*$
    vars:
      acceptance_tests:
        - acceptance/openstack/blockstorage
        - acceptance/openstack/blockstorage/extensions
        - acceptance/openstack/blockstorage/v3
        - acceptance/openstack/imageservice/v2
        - acceptance/openstack/objectstorage/v1
        - acceptance/openstack/sharedfilesystems/v2
        - acceptance/openstack/sharedfilesystems/v2/messages
      devstack_services:
        - manila

- job:
    name: gophercloud-acceptance-test-ironic
    parent: gophercloud-acceptance-test-base
    description: |
      Run gophercloud ironic acceptance test on master branch
    files:
      - ^.*baremetal.*$
    vars:
      devstack_services:
        - ironic
      devstack_override_enabled_services: 'g-api,g-reg,q-agt,q-dhcp,q-l3,q-svc,key,mysql,rabbit,ir-api,ir-cond,s-account,s-container,s-object,s-proxy'
      devstack_projects: 'openstack/ironic-python-agent-builder openstack/ironic'
      acceptance_tests:
        - acceptance/openstack/baremetal/v1

- job:
    name: gophercloud-acceptance-test-compute-ussuri
    parent: gophercloud-acceptance-test-compute
    nodeset: ubuntu-bionic
    description: |
      Run gophercloud compute acceptance test on ussuri branch
    vars:
      global_env:
        OS_BRANCH: stable/ussuri

- job:
    name: gophercloud-acceptance-test-networking-ussuri
    parent: gophercloud-acceptance-test-networking
    nodeset: ubuntu-bionic
    description: |
      Run gophercloud networking acceptance test on ussuri branch
    vars:
      global_env:
        OS_BRANCH: stable/ussuri

- job:
    name: gophercloud-acceptance-test-storage-ussuri
    parent: gophercloud-acceptance-test-storage
    nodeset: ubuntu-bionic
    description: |
      Run gophercloud storage test on ussuri branch
    vars:
      global_env:
        OS_BRANCH: stable/ussuri

- job:
    name: gophercloud-acceptance-test-compute-train
    parent: gophercloud-acceptance-test-compute
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud compute acceptance test on train branch
    vars:
      global_env:
        OS_BRANCH: stable/train

- job:
    name: gophercloud-acceptance-test-networking-train
    parent: gophercloud-acceptance-test-networking
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud networking acceptance test on train branch
    vars:
      global_env:
        OS_BRANCH: stable/train

- job:
    name: gophercloud-acceptance-test-storage-train
    parent: gophercloud-acceptance-test-storage
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud storage acceptance test on train branch
    vars:
      global_env:
        OS_BRANCH: stable/train

- job:
    name: gophercloud-acceptance-test-compute-stein
    parent: gophercloud-acceptance-test-compute
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud compute acceptance test on stein branch
    vars:
      global_env:
        OS_BRANCH: stable/stein

- job:
    name: gophercloud-acceptance-test-networking-stein
    parent: gophercloud-acceptance-test-networking
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud networking acceptance test on stein branch
    vars:
      global_env:
        OS_BRANCH: stable/stein

- job:
    name: gophercloud-acceptance-test-storage-stein
    parent: gophercloud-acceptance-test-storage
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud storage acceptance test on stein branch
    vars:
      global_env:
        OS_BRANCH: stable/stein

- job:
    name: gophercloud-acceptance-test-compute-rocky
    parent: gophercloud-acceptance-test-compute
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud compute acceptance test on rocky branch
    vars:
      global_env:
        OS_BRANCH: stable/rocky

- job:
    name: gophercloud-acceptance-test-networking-rocky
    parent: gophercloud-acceptance-test-networking
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud networking acceptance test on rocky branch
    vars:
      global_env:
        OS_BRANCH: stable/rocky

- job:
    name: gophercloud-acceptance-test-storage-rocky
    parent: gophercloud-acceptance-test-storage
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud storage acceptance test on rocky branch
    vars:
      global_env:
        OS_BRANCH: stable/rocky

- job:
    name: gophercloud-acceptance-test-compute-queens
    parent: gophercloud-acceptance-test-compute
    description: |
      Run gophercloud compute acceptance test on queens branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/queens

- job:
    name: gophercloud-acceptance-test-networking-queens
    parent: gophercloud-acceptance-test-networking
    description: |
      Run gophercloud networking acceptance test on queens branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/queens

- job:
    name: gophercloud-acceptance-test-storage-queens
    parent: gophercloud-acceptance-test-storage
    description: |
      Run gophercloud storage acceptance test on queens branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/queens

# NOTE: A Pike-based devstack environment is currently
# not building correctly. This might be a temporary issue.
- job:
    name: gophercloud-acceptance-test-compute-pike
    parent: gophercloud-acceptance-test-compute
    description: |
      Run gophercloud compute acceptance test on pike branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/pike

- job:
    name: gophercloud-acceptance-test-networking-pike
    parent: gophercloud-acceptance-test-networking
    description: |
      Run gophercloud networking acceptance test on pike branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/pike

- job:
    name: gophercloud-acceptance-test-storage-pike
    parent: gophercloud-acceptance-test-storage
    description: |
      Run gophercloud storage acceptance test on pike branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/pike

- job:
    name: gophercloud-acceptance-test-compute-ocata
    parent: gophercloud-acceptance-test-compute
    description: |
      Run gophercloud compute acceptance test on ocata branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/ocata

- job:
    name: gophercloud-acceptance-test-networking-ocata
    parent: gophercloud-acceptance-test-networking
    description: |
      Run gophercloud networking acceptance test on ocata branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/ocata

- job:
    name: gophercloud-acceptance-test-storage-ocata
    parent: gophercloud-acceptance-test-storage
    description: |
      Run gophercloud storage acceptance test on ocata branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/ocata

# NOTE: A Newton-based devstack environment is currently
# not building correctly. This might be a temporary issue.
- job:
    name: gophercloud-acceptance-test-compute-newton
    parent: gophercloud-acceptance-test-compute
    description: |
      Run gophercloud compute acceptance test on newton branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/newton

- job:
    name: gophercloud-acceptance-test-networking-newton
    parent: gophercloud-acceptance-test-networking
    description: |
      Run gophercloud networking acceptance test on newton branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/newton

- job:
    name: gophercloud-acceptance-test-storage-newton
    parent: gophercloud-acceptance-test-storage
    description: |
      Run gophercloud storage acceptance test on newton branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/newton

# The following jobs are maintained because they are parents
# for gophercloud v0.4.0 acceptance tests in theopenlab/openlab-zuul-jobs.
# TODO(emilien) update the childs to use the new variants.
- job:
    name: gophercloud-acceptance-test-legacy
    parent: gophercloud-acceptance-test-base
    description: |
      THIS JOB REMAINS FOR LEGACY. Will be removed soon to be replaced by its variants.
      Run gophercloud acceptance test on the master branch. This runs when any file is patched
      except if it's doc.
    vars:
      # the list of all available tests can generated by:
      # find acceptance/openstack -name '*_test.go' -exec dirname {} \; | sort -n | uniq -c | awk '{print $2}'
      acceptance_tests:
        - acceptance/openstack
        - acceptance/openstack/blockstorage
        - acceptance/openstack/blockstorage/extensions
        - acceptance/openstack/blockstorage/v3
        - acceptance/openstack/compute/v2
        - acceptance/openstack/container/v1
        - acceptance/openstack/dns/v2
        - acceptance/openstack/identity/v2
        - acceptance/openstack/identity/v3
        - acceptance/openstack/imageservice/v2
        - acceptance/openstack/keymanager/v1
        - acceptance/openstack/loadbalancer/v2
        - acceptance/openstack/networking/v2
        - acceptance/openstack/networking/v2/extensions
        - acceptance/openstack/networking/v2/extensions/agents
        - acceptance/openstack/networking/v2/extensions/dns
        - acceptance/openstack/networking/v2/extensions/layer3
        - acceptance/openstack/networking/v2/extensions/mtu
        - acceptance/openstack/networking/v2/extensions/networkipavailabilities
        - acceptance/openstack/networking/v2/extensions/portsbinding
        - acceptance/openstack/networking/v2/extensions/qos/policies
        - acceptance/openstack/networking/v2/extensions/qos/rules
        - acceptance/openstack/networking/v2/extensions/qos/ruletypes
        - acceptance/openstack/networking/v2/extensions/quotas
        - acceptance/openstack/networking/v2/extensions/rbacpolicies
        - acceptance/openstack/networking/v2/extensions/subnetpools
        - acceptance/openstack/networking/v2/extensions/trunks
        - acceptance/openstack/networking/v2/extensions/vlantransparent
        - acceptance/openstack/objectstorage/v1
        - acceptance/openstack/orchestration/v1
        - acceptance/openstack/placement/v1
        - acceptance/openstack/sharedfilesystems/v2
        - acceptance/openstack/sharedfilesystems/v2/messages
      devstack_services:
        - designate
        - manila
        - neutron-ext
        - octavia
        - zun

- job:
    name: gophercloud-acceptance-test-stein
    parent: gophercloud-acceptance-test-legacy
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud acceptance test on stein branch
    vars:
      global_env:
        OS_BRANCH: stable/stein

- job:
    name: gophercloud-acceptance-test-rocky
    parent: gophercloud-acceptance-test-legacy
    nodeset: ubuntu-xenial
    description: |
      Run gophercloud acceptance test on rocky branch
    vars:
      global_env:
        OS_BRANCH: stable/rocky

- job:
    name: gophercloud-acceptance-test-queens
    parent: gophercloud-acceptance-test-legacy
    description: |
      Run gophercloud acceptance test on queens branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/queens

# NOTE: A Pike-based devstack environment is currently
# not building correctly. This might be a temporary issue.
- job:
    name: gophercloud-acceptance-test-pike
    parent: gophercloud-acceptance-test-legacy
    description: |
      Run gophercloud acceptance test on pike branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/pike

- job:
    name: gophercloud-acceptance-test-ocata
    parent: gophercloud-acceptance-test-legacy
    description: |
      Run gophercloud acceptance test on ocata branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/ocata

# NOTE: A Newton-based devstack environment is currently
# not building correctly. This might be a temporary issue.
- job:
    name: gophercloud-acceptance-test-newton
    parent: gophercloud-acceptance-test-legacy
    description: |
      Run gophercloud acceptance test on newton branch
    nodeset: ubuntu-xenial
    vars:
      global_env:
        OS_BRANCH: stable/newton

- project:
    name: gophercloud/gophercloud
    check:
      jobs:
        - gophercloud-unittest
        - gophercloud-acceptance-test-compute
        - gophercloud-acceptance-test-networking
        - gophercloud-acceptance-test-storage
        - gophercloud-acceptance-test-ironic
    recheck-newton:
      jobs:
        - gophercloud-acceptance-test-compute-newton
        - gophercloud-acceptance-test-networking-newton
        - gophercloud-acceptance-test-storage-newton
    recheck-ocata:
      jobs:
        - gophercloud-acceptance-test-compute-ocata
        - gophercloud-acceptance-test-networking-ocata
        - gophercloud-acceptance-test-storage-ocata
    recheck-pike:
      jobs:
        - gophercloud-acceptance-test-compute-pike
        - gophercloud-acceptance-test-networking-pike
        - gophercloud-acceptance-test-storage-pike
    recheck-queens:
      jobs:
        - gophercloud-acceptance-test-compute-queens
        - gophercloud-acceptance-test-networking-queens
        - gophercloud-acceptance-test-storage-queens
    recheck-rocky:
      jobs:
        - gophercloud-acceptance-test-compute-rocky
        - gophercloud-acceptance-test-networking-rocky
        - gophercloud-acceptance-test-storage-rocky
    recheck-stein:
      jobs:
        - gophercloud-acceptance-test-compute-stein
        - gophercloud-acceptance-test-networking-stein
        - gophercloud-acceptance-test-storage-stein
    recheck-train:
      jobs:
        - gophercloud-acceptance-test-compute-train
        - gophercloud-acceptance-test-networking-train
        - gophercloud-acceptance-test-storage-train
    recheck-ussuri:
      jobs:
        - gophercloud-acceptance-test-compute-ussuri
        - gophercloud-acceptance-test-networking-ussuri
        - gophercloud-acceptance-test-storage-ussuri
