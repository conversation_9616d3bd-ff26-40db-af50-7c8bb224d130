syntax = "proto3";

package rules;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/any.proto";
import "google/protobuf/duration.proto";
import "pkg/logproto/logproto.proto";

option go_package = "github.com/grafana/loki/pkg/ruler/rulespb";
option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;

// RuleGroupDesc is a proto representation of a rule group.
message RuleGroupDesc {
  reserved 5, 7, 8;
  string name = 1;
  string namespace = 2;
  google.protobuf.Duration interval = 3 [
    (gogoproto.nullable) = false,
    (gogoproto.stdduration) = true
  ];
  repeated RuleDesc rules = 4;
  string user = 6;
  // The options field can be used to extend Ruler functionality without
  // having to repeatedly redefine the proto description. It can also be leveraged
  // to create custom `ManagerOpts` based on rule configs which can then be passed
  // to the Prometheus Manager.
  repeated google.protobuf.Any options = 9;
}

// RuleDesc is a proto representation of a Prometheus Rule
message RuleDesc {
  reserved 7 to 12;
  string expr = 1;
  string record = 2;
  string alert = 3;
  google.protobuf.Duration for = 4 [
    (gogoproto.nullable) = false,
    (gogoproto.stdduration) = true
  ];
  repeated logproto.LegacyLabelPair labels = 5 [
    (gogoproto.nullable) = false,
    (gogoproto.customtype) = "github.com/grafana/loki/pkg/logproto.LabelAdapter"
  ];
  repeated logproto.LegacyLabelPair annotations = 6 [
    (gogoproto.nullable) = false,
    (gogoproto.customtype) = "github.com/grafana/loki/pkg/logproto.LabelAdapter"
  ];
}
