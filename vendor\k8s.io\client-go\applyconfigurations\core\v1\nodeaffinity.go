/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// NodeAffinityApplyConfiguration represents an declarative configuration of the NodeAffinity type for use
// with apply.
type NodeAffinityApplyConfiguration struct {
	RequiredDuringSchedulingIgnoredDuringExecution  *NodeSelectorApplyConfiguration             `json:"requiredDuringSchedulingIgnoredDuringExecution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []PreferredSchedulingTermApplyConfiguration `json:"preferredDuringSchedulingIgnoredDuringExecution,omitempty"`
}

// NodeAffinityApplyConfiguration constructs an declarative configuration of the NodeAffinity type for use with
// apply.
func NodeAffinity() *NodeAffinityApplyConfiguration {
	return &NodeAffinityApplyConfiguration{}
}

// WithRequiredDuringSchedulingIgnoredDuringExecution sets the RequiredDuringSchedulingIgnoredDuringExecution field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RequiredDuringSchedulingIgnoredDuringExecution field is set to the value of the last call.
func (b *NodeAffinityApplyConfiguration) WithRequiredDuringSchedulingIgnoredDuringExecution(value *NodeSelectorApplyConfiguration) *NodeAffinityApplyConfiguration {
	b.RequiredDuringSchedulingIgnoredDuringExecution = value
	return b
}

// WithPreferredDuringSchedulingIgnoredDuringExecution adds the given value to the PreferredDuringSchedulingIgnoredDuringExecution field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the PreferredDuringSchedulingIgnoredDuringExecution field.
func (b *NodeAffinityApplyConfiguration) WithPreferredDuringSchedulingIgnoredDuringExecution(values ...*PreferredSchedulingTermApplyConfiguration) *NodeAffinityApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithPreferredDuringSchedulingIgnoredDuringExecution")
		}
		b.PreferredDuringSchedulingIgnoredDuringExecution = append(b.PreferredDuringSchedulingIgnoredDuringExecution, *values[i])
	}
	return b
}
