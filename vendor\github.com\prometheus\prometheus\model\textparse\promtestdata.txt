# HELP go_gc_duration_seconds A summary of the GC invocation durations.
# TYPE go_gc_duration_seconds summary
go_gc_duration_seconds{quantile="0"} 4.9351e-05
go_gc_duration_seconds{quantile="0.25"} 7.424100000000001e-05
go_gc_duration_seconds{quantile="0.5"} 8.3835e-05
go_gc_duration_seconds{quantile="0.75"} 0.000106744
go_gc_duration_seconds{quantile="1"} 0.002072195
go_gc_duration_seconds_sum 0.012139815
go_gc_duration_seconds_count 99
# HELP go_goroutines Number of goroutines that currently exist.
# TYPE go_goroutines gauge
go_goroutines 33
# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
# TYPE go_memstats_alloc_bytes gauge
go_memstats_alloc_bytes 1.7518624e+07
# HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even if freed.
# TYPE go_memstats_alloc_bytes_total counter
go_memstats_alloc_bytes_total 8.3062296e+08
# HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table.
# TYPE go_memstats_buck_hash_sys_bytes gauge
go_memstats_buck_hash_sys_bytes 1.494637e+06
# HELP go_memstats_frees_total Total number of frees.
# TYPE go_memstats_frees_total counter
go_memstats_frees_total 4.65658e+06
# HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
# TYPE go_memstats_gc_sys_bytes gauge
go_memstats_gc_sys_bytes 1.107968e+06
# HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use.
# TYPE go_memstats_heap_alloc_bytes gauge
go_memstats_heap_alloc_bytes 1.7518624e+07
# HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used.
# TYPE go_memstats_heap_idle_bytes gauge
go_memstats_heap_idle_bytes 6.668288e+06
# HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use.
# TYPE go_memstats_heap_inuse_bytes gauge
go_memstats_heap_inuse_bytes 1.8956288e+07
# HELP go_memstats_heap_objects Number of allocated objects.
# TYPE go_memstats_heap_objects gauge
go_memstats_heap_objects 72755
# HELP go_memstats_heap_released_bytes_total Total number of heap bytes released to OS.
# TYPE go_memstats_heap_released_bytes_total counter
go_memstats_heap_released_bytes_total 0
# HELP go_memstats_heap_sys_bytes Number of heap bytes obtained from system.
# TYPE go_memstats_heap_sys_bytes gauge
go_memstats_heap_sys_bytes 2.5624576e+07
# HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection.
# TYPE go_memstats_last_gc_time_seconds gauge
go_memstats_last_gc_time_seconds 1.4843955586166437e+09
# HELP go_memstats_lookups_total Total number of pointer lookups.
# TYPE go_memstats_lookups_total counter
go_memstats_lookups_total 2089
# HELP go_memstats_mallocs_total Total number of mallocs.
# TYPE go_memstats_mallocs_total counter
go_memstats_mallocs_total 4.729335e+06
# HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures.
# TYPE go_memstats_mcache_inuse_bytes gauge
go_memstats_mcache_inuse_bytes 9600
# HELP go_memstats_mcache_sys_bytes Number of bytes used for mcache structures obtained from system.
# TYPE go_memstats_mcache_sys_bytes gauge
go_memstats_mcache_sys_bytes 16384
# HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures.
# TYPE go_memstats_mspan_inuse_bytes gauge
go_memstats_mspan_inuse_bytes 211520
# HELP go_memstats_mspan_sys_bytes Number of bytes used for mspan structures obtained from system.
# TYPE go_memstats_mspan_sys_bytes gauge
go_memstats_mspan_sys_bytes 245760
# HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place.
# TYPE go_memstats_next_gc_bytes gauge
go_memstats_next_gc_bytes 2.033527e+07
# HELP go_memstats_other_sys_bytes Number of bytes used for other system allocations.
# TYPE go_memstats_other_sys_bytes gauge
go_memstats_other_sys_bytes 2.077323e+06
# HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator.
# TYPE go_memstats_stack_inuse_bytes gauge
go_memstats_stack_inuse_bytes 1.6384e+06
# HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
# TYPE go_memstats_stack_sys_bytes gauge
go_memstats_stack_sys_bytes 1.6384e+06
# HELP go_memstats_sys_bytes Number of bytes obtained by system. Sum of all system allocations.
# TYPE go_memstats_sys_bytes gauge
go_memstats_sys_bytes 3.2205048e+07
# HELP http_request_duration_microseconds The HTTP request latencies in microseconds.
# TYPE http_request_duration_microseconds summary
http_request_duration_microseconds{handler="alerts",quantile="0.5"} NaN
http_request_duration_microseconds{handler="alerts",quantile="0.9"} NaN
http_request_duration_microseconds{handler="alerts",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="alerts"} 0
http_request_duration_microseconds_count{handler="alerts"} 0
http_request_duration_microseconds{handler="config",quantile="0.5"} NaN
http_request_duration_microseconds{handler="config",quantile="0.9"} NaN
http_request_duration_microseconds{handler="config",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="config"} 0
http_request_duration_microseconds_count{handler="config"} 0
http_request_duration_microseconds{handler="consoles",quantile="0.5"} NaN
http_request_duration_microseconds{handler="consoles",quantile="0.9"} NaN
http_request_duration_microseconds{handler="consoles",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="consoles"} 0
http_request_duration_microseconds_count{handler="consoles"} 0
http_request_duration_microseconds{handler="drop_series",quantile="0.5"} NaN
http_request_duration_microseconds{handler="drop_series",quantile="0.9"} NaN
http_request_duration_microseconds{handler="drop_series",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="drop_series"} 0
http_request_duration_microseconds_count{handler="drop_series"} 0
http_request_duration_microseconds{handler="federate",quantile="0.5"} NaN
http_request_duration_microseconds{handler="federate",quantile="0.9"} NaN
http_request_duration_microseconds{handler="federate",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="federate"} 0
http_request_duration_microseconds_count{handler="federate"} 0
http_request_duration_microseconds{handler="flags",quantile="0.5"} NaN
http_request_duration_microseconds{handler="flags",quantile="0.9"} NaN
http_request_duration_microseconds{handler="flags",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="flags"} 0
http_request_duration_microseconds_count{handler="flags"} 0
http_request_duration_microseconds{handler="graph",quantile="0.5"} 771.655
http_request_duration_microseconds{handler="graph",quantile="0.9"} 1761.823
http_request_duration_microseconds{handler="graph",quantile="0.99"} 1761.823
http_request_duration_microseconds_sum{handler="graph"} 5803.93
http_request_duration_microseconds_count{handler="graph"} 3
http_request_duration_microseconds{handler="heap",quantile="0.5"} NaN
http_request_duration_microseconds{handler="heap",quantile="0.9"} NaN
http_request_duration_microseconds{handler="heap",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="heap"} 0
http_request_duration_microseconds_count{handler="heap"} 0
http_request_duration_microseconds{handler="label_values",quantile="0.5"} 325.401
http_request_duration_microseconds{handler="label_values",quantile="0.9"} 414.708
http_request_duration_microseconds{handler="label_values",quantile="0.99"} 414.708
http_request_duration_microseconds_sum{handler="label_values"} 3995.574
http_request_duration_microseconds_count{handler="label_values"} 3
http_request_duration_microseconds{handler="options",quantile="0.5"} NaN
http_request_duration_microseconds{handler="options",quantile="0.9"} NaN
http_request_duration_microseconds{handler="options",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="options"} 0
http_request_duration_microseconds_count{handler="options"} 0
http_request_duration_microseconds{handler="prometheus",quantile="0.5"} 1351.859
http_request_duration_microseconds{handler="prometheus",quantile="0.9"} 1714.035
http_request_duration_microseconds{handler="prometheus",quantile="0.99"} 2833.523
http_request_duration_microseconds_sum{handler="prometheus"} 661851.54
http_request_duration_microseconds_count{handler="prometheus"} 462
http_request_duration_microseconds{handler="query",quantile="0.5"} 3885.448
http_request_duration_microseconds{handler="query",quantile="0.9"} 4390.558
http_request_duration_microseconds{handler="query",quantile="0.99"} 4390.558
http_request_duration_microseconds_sum{handler="query"} 26074.11
http_request_duration_microseconds_count{handler="query"} 6
http_request_duration_microseconds{handler="query_range",quantile="0.5"} NaN
http_request_duration_microseconds{handler="query_range",quantile="0.9"} NaN
http_request_duration_microseconds{handler="query_range",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="query_range"} 0
http_request_duration_microseconds_count{handler="query_range"} 0
http_request_duration_microseconds{handler="rules",quantile="0.5"} NaN
http_request_duration_microseconds{handler="rules",quantile="0.9"} NaN
http_request_duration_microseconds{handler="rules",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="rules"} 0
http_request_duration_microseconds_count{handler="rules"} 0
http_request_duration_microseconds{handler="series",quantile="0.5"} NaN
http_request_duration_microseconds{handler="series",quantile="0.9"} NaN
http_request_duration_microseconds{handler="series",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="series"} 0
http_request_duration_microseconds_count{handler="series"} 0
http_request_duration_microseconds{handler="static",quantile="0.5"} 212.311
http_request_duration_microseconds{handler="static",quantile="0.9"} 265.174
http_request_duration_microseconds{handler="static",quantile="0.99"} 265.174
http_request_duration_microseconds_sum{handler="static"} 6458.621
http_request_duration_microseconds_count{handler="static"} 3
http_request_duration_microseconds{handler="status",quantile="0.5"} NaN
http_request_duration_microseconds{handler="status",quantile="0.9"} NaN
http_request_duration_microseconds{handler="status",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="status"} 0
http_request_duration_microseconds_count{handler="status"} 0
http_request_duration_microseconds{handler="targets",quantile="0.5"} NaN
http_request_duration_microseconds{handler="targets",quantile="0.9"} NaN
http_request_duration_microseconds{handler="targets",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="targets"} 0
http_request_duration_microseconds_count{handler="targets"} 0
http_request_duration_microseconds{handler="version",quantile="0.5"} NaN
http_request_duration_microseconds{handler="version",quantile="0.9"} NaN
http_request_duration_microseconds{handler="version",quantile="0.99"} NaN
http_request_duration_microseconds_sum{handler="version"} 0
http_request_duration_microseconds_count{handler="version"} 0
# HELP http_request_size_bytes The HTTP request sizes in bytes.
# TYPE http_request_size_bytes summary
http_request_size_bytes{handler="alerts",quantile="0.5"} NaN
http_request_size_bytes{handler="alerts",quantile="0.9"} NaN
http_request_size_bytes{handler="alerts",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="alerts"} 0
http_request_size_bytes_count{handler="alerts"} 0
http_request_size_bytes{handler="config",quantile="0.5"} NaN
http_request_size_bytes{handler="config",quantile="0.9"} NaN
http_request_size_bytes{handler="config",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="config"} 0
http_request_size_bytes_count{handler="config"} 0
http_request_size_bytes{handler="consoles",quantile="0.5"} NaN
http_request_size_bytes{handler="consoles",quantile="0.9"} NaN
http_request_size_bytes{handler="consoles",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="consoles"} 0
http_request_size_bytes_count{handler="consoles"} 0
http_request_size_bytes{handler="drop_series",quantile="0.5"} NaN
http_request_size_bytes{handler="drop_series",quantile="0.9"} NaN
http_request_size_bytes{handler="drop_series",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="drop_series"} 0
http_request_size_bytes_count{handler="drop_series"} 0
http_request_size_bytes{handler="federate",quantile="0.5"} NaN
http_request_size_bytes{handler="federate",quantile="0.9"} NaN
http_request_size_bytes{handler="federate",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="federate"} 0
http_request_size_bytes_count{handler="federate"} 0
http_request_size_bytes{handler="flags",quantile="0.5"} NaN
http_request_size_bytes{handler="flags",quantile="0.9"} NaN
http_request_size_bytes{handler="flags",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="flags"} 0
http_request_size_bytes_count{handler="flags"} 0
http_request_size_bytes{handler="graph",quantile="0.5"} 367
http_request_size_bytes{handler="graph",quantile="0.9"} 389
http_request_size_bytes{handler="graph",quantile="0.99"} 389
http_request_size_bytes_sum{handler="graph"} 1145
http_request_size_bytes_count{handler="graph"} 3
http_request_size_bytes{handler="heap",quantile="0.5"} NaN
http_request_size_bytes{handler="heap",quantile="0.9"} NaN
http_request_size_bytes{handler="heap",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="heap"} 0
http_request_size_bytes_count{handler="heap"} 0
http_request_size_bytes{handler="label_values",quantile="0.5"} 416
http_request_size_bytes{handler="label_values",quantile="0.9"} 416
http_request_size_bytes{handler="label_values",quantile="0.99"} 416
http_request_size_bytes_sum{handler="label_values"} 1248
http_request_size_bytes_count{handler="label_values"} 3
http_request_size_bytes{handler="options",quantile="0.5"} NaN
http_request_size_bytes{handler="options",quantile="0.9"} NaN
http_request_size_bytes{handler="options",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="options"} 0
http_request_size_bytes_count{handler="options"} 0
http_request_size_bytes{handler="prometheus",quantile="0.5"} 238
http_request_size_bytes{handler="prometheus",quantile="0.9"} 238
http_request_size_bytes{handler="prometheus",quantile="0.99"} 238
http_request_size_bytes_sum{handler="prometheus"} 109956
http_request_size_bytes_count{handler="prometheus"} 462
http_request_size_bytes{handler="query",quantile="0.5"} 531
http_request_size_bytes{handler="query",quantile="0.9"} 531
http_request_size_bytes{handler="query",quantile="0.99"} 531
http_request_size_bytes_sum{handler="query"} 3186
http_request_size_bytes_count{handler="query"} 6
http_request_size_bytes{handler="query_range",quantile="0.5"} NaN
http_request_size_bytes{handler="query_range",quantile="0.9"} NaN
http_request_size_bytes{handler="query_range",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="query_range"} 0
http_request_size_bytes_count{handler="query_range"} 0
http_request_size_bytes{handler="rules",quantile="0.5"} NaN
http_request_size_bytes{handler="rules",quantile="0.9"} NaN
http_request_size_bytes{handler="rules",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="rules"} 0
http_request_size_bytes_count{handler="rules"} 0
http_request_size_bytes{handler="series",quantile="0.5"} NaN
http_request_size_bytes{handler="series",quantile="0.9"} NaN
http_request_size_bytes{handler="series",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="series"} 0
http_request_size_bytes_count{handler="series"} 0
http_request_size_bytes{handler="static",quantile="0.5"} 379
http_request_size_bytes{handler="static",quantile="0.9"} 379
http_request_size_bytes{handler="static",quantile="0.99"} 379
http_request_size_bytes_sum{handler="static"} 1137
http_request_size_bytes_count{handler="static"} 3
http_request_size_bytes{handler="status",quantile="0.5"} NaN
http_request_size_bytes{handler="status",quantile="0.9"} NaN
http_request_size_bytes{handler="status",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="status"} 0
http_request_size_bytes_count{handler="status"} 0
http_request_size_bytes{handler="targets",quantile="0.5"} NaN
http_request_size_bytes{handler="targets",quantile="0.9"} NaN
http_request_size_bytes{handler="targets",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="targets"} 0
http_request_size_bytes_count{handler="targets"} 0
http_request_size_bytes{handler="version",quantile="0.5"} NaN
http_request_size_bytes{handler="version",quantile="0.9"} NaN
http_request_size_bytes{handler="version",quantile="0.99"} NaN
http_request_size_bytes_sum{handler="version"} 0
http_request_size_bytes_count{handler="version"} 0
# HELP http_requests_total Total number of HTTP requests made.
# TYPE http_requests_total counter
http_requests_total{code="200",handler="graph",method="get"} 3
http_requests_total{code="200",handler="label_values",method="get"} 3
http_requests_total{code="200",handler="prometheus",method="get"} 462
http_requests_total{code="200",handler="query",method="get"} 6
http_requests_total{code="200",handler="static",method="get"} 3
# HELP http_response_size_bytes The HTTP response sizes in bytes.
# TYPE http_response_size_bytes summary
http_response_size_bytes{handler="alerts",quantile="0.5"} NaN
http_response_size_bytes{handler="alerts",quantile="0.9"} NaN
http_response_size_bytes{handler="alerts",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="alerts"} 0
http_response_size_bytes_count{handler="alerts"} 0
http_response_size_bytes{handler="config",quantile="0.5"} NaN
http_response_size_bytes{handler="config",quantile="0.9"} NaN
http_response_size_bytes{handler="config",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="config"} 0
http_response_size_bytes_count{handler="config"} 0
http_response_size_bytes{handler="consoles",quantile="0.5"} NaN
http_response_size_bytes{handler="consoles",quantile="0.9"} NaN
http_response_size_bytes{handler="consoles",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="consoles"} 0
http_response_size_bytes_count{handler="consoles"} 0
http_response_size_bytes{handler="drop_series",quantile="0.5"} NaN
http_response_size_bytes{handler="drop_series",quantile="0.9"} NaN
http_response_size_bytes{handler="drop_series",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="drop_series"} 0
http_response_size_bytes_count{handler="drop_series"} 0
http_response_size_bytes{handler="federate",quantile="0.5"} NaN
http_response_size_bytes{handler="federate",quantile="0.9"} NaN
http_response_size_bytes{handler="federate",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="federate"} 0
http_response_size_bytes_count{handler="federate"} 0
http_response_size_bytes{handler="flags",quantile="0.5"} NaN
http_response_size_bytes{handler="flags",quantile="0.9"} NaN
http_response_size_bytes{handler="flags",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="flags"} 0
http_response_size_bytes_count{handler="flags"} 0
http_response_size_bytes{handler="graph",quantile="0.5"} 3619
http_response_size_bytes{handler="graph",quantile="0.9"} 3619
http_response_size_bytes{handler="graph",quantile="0.99"} 3619
http_response_size_bytes_sum{handler="graph"} 10857
http_response_size_bytes_count{handler="graph"} 3
http_response_size_bytes{handler="heap",quantile="0.5"} NaN
http_response_size_bytes{handler="heap",quantile="0.9"} NaN
http_response_size_bytes{handler="heap",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="heap"} 0
http_response_size_bytes_count{handler="heap"} 0
http_response_size_bytes{handler="label_values",quantile="0.5"} 642
http_response_size_bytes{handler="label_values",quantile="0.9"} 642
http_response_size_bytes{handler="label_values",quantile="0.99"} 642
http_response_size_bytes_sum{handler="label_values"} 1926
http_response_size_bytes_count{handler="label_values"} 3
http_response_size_bytes{handler="options",quantile="0.5"} NaN
http_response_size_bytes{handler="options",quantile="0.9"} NaN
http_response_size_bytes{handler="options",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="options"} 0
http_response_size_bytes_count{handler="options"} 0
http_response_size_bytes{handler="prometheus",quantile="0.5"} 3033
http_response_size_bytes{handler="prometheus",quantile="0.9"} 3123
http_response_size_bytes{handler="prometheus",quantile="0.99"} 3128
http_response_size_bytes_sum{handler="prometheus"} 1.374097e+06
http_response_size_bytes_count{handler="prometheus"} 462
http_response_size_bytes{handler="query",quantile="0.5"} 776
http_response_size_bytes{handler="query",quantile="0.9"} 781
http_response_size_bytes{handler="query",quantile="0.99"} 781
http_response_size_bytes_sum{handler="query"} 4656
http_response_size_bytes_count{handler="query"} 6
http_response_size_bytes{handler="query_range",quantile="0.5"} NaN
http_response_size_bytes{handler="query_range",quantile="0.9"} NaN
http_response_size_bytes{handler="query_range",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="query_range"} 0
http_response_size_bytes_count{handler="query_range"} 0
http_response_size_bytes{handler="rules",quantile="0.5"} NaN
http_response_size_bytes{handler="rules",quantile="0.9"} NaN
http_response_size_bytes{handler="rules",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="rules"} 0
http_response_size_bytes_count{handler="rules"} 0
http_response_size_bytes{handler="series",quantile="0.5"} NaN
http_response_size_bytes{handler="series",quantile="0.9"} NaN
http_response_size_bytes{handler="series",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="series"} 0
http_response_size_bytes_count{handler="series"} 0
http_response_size_bytes{handler="static",quantile="0.5"} 6316
http_response_size_bytes{handler="static",quantile="0.9"} 6316
http_response_size_bytes{handler="static",quantile="0.99"} 6316
http_response_size_bytes_sum{handler="static"} 18948
http_response_size_bytes_count{handler="static"} 3
http_response_size_bytes{handler="status",quantile="0.5"} NaN
http_response_size_bytes{handler="status",quantile="0.9"} NaN
http_response_size_bytes{handler="status",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="status"} 0
http_response_size_bytes_count{handler="status"} 0
http_response_size_bytes{handler="targets",quantile="0.5"} NaN
http_response_size_bytes{handler="targets",quantile="0.9"} NaN
http_response_size_bytes{handler="targets",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="targets"} 0
http_response_size_bytes_count{handler="targets"} 0
http_response_size_bytes{handler="version",quantile="0.5"} NaN
http_response_size_bytes{handler="version",quantile="0.9"} NaN
http_response_size_bytes{handler="version",quantile="0.99"} NaN
http_response_size_bytes_sum{handler="version"} 0
http_response_size_bytes_count{handler="version"} 0
# HELP prometheus_build_info A metric with a constant '1' value labeled by version, revision, branch, and goversion from which prometheus was built.
# TYPE prometheus_build_info gauge
prometheus_build_info{branch="",goversion="go1.7.3",revision="",version=""} 1
# HELP prometheus_config_last_reload_success_timestamp_seconds Timestamp of the last successful configuration reload.
# TYPE prometheus_config_last_reload_success_timestamp_seconds gauge
prometheus_config_last_reload_success_timestamp_seconds 1.484395547e+09
# HELP prometheus_config_last_reload_successful Whether the last configuration reload attempt was successful.
# TYPE prometheus_config_last_reload_successful gauge
prometheus_config_last_reload_successful 1
# HELP prometheus_evaluator_duration_seconds The duration of rule group evaluations.
# TYPE prometheus_evaluator_duration_seconds summary
prometheus_evaluator_duration_seconds{quantile="0.01"} 1.7890000000000002e-06
prometheus_evaluator_duration_seconds{quantile="0.05"} 1.7890000000000002e-06
prometheus_evaluator_duration_seconds{quantile="0.5"} 1.7890000000000002e-06
prometheus_evaluator_duration_seconds{quantile="0.9"} 1.7890000000000002e-06
prometheus_evaluator_duration_seconds{quantile="0.99"} 1.7890000000000002e-06
prometheus_evaluator_duration_seconds_sum 1.7890000000000002e-06
prometheus_evaluator_duration_seconds_count 1
# HELP prometheus_evaluator_iterations_skipped_total The total number of rule group evaluations skipped due to throttled metric storage.
# TYPE prometheus_evaluator_iterations_skipped_total counter
prometheus_evaluator_iterations_skipped_total 0
# HELP prometheus_notifications_dropped_total Total number of alerts dropped due to alert manager missing in configuration.
# TYPE prometheus_notifications_dropped_total counter
prometheus_notifications_dropped_total 0
# HELP prometheus_notifications_queue_capacity The capacity of the alert notifications queue.
# TYPE prometheus_notifications_queue_capacity gauge
prometheus_notifications_queue_capacity 10000
# HELP prometheus_notifications_queue_length The number of alert notifications in the queue.
# TYPE prometheus_notifications_queue_length gauge
prometheus_notifications_queue_length 0
# HELP prometheus_rule_evaluation_failures_total The total number of rule evaluation failures.
# TYPE prometheus_rule_evaluation_failures_total counter
prometheus_rule_evaluation_failures_total{rule_type="alerting"} 0
prometheus_rule_evaluation_failures_total{rule_type="recording"} 0
# HELP prometheus_sd_azure_refresh_duration_seconds The duration of a Azure-SD refresh in seconds.
# TYPE prometheus_sd_azure_refresh_duration_seconds summary
prometheus_sd_azure_refresh_duration_seconds{quantile="0.5"} NaN
prometheus_sd_azure_refresh_duration_seconds{quantile="0.9"} NaN
prometheus_sd_azure_refresh_duration_seconds{quantile="0.99"} NaN
prometheus_sd_azure_refresh_duration_seconds_sum 0
prometheus_sd_azure_refresh_duration_seconds_count 0
# HELP prometheus_sd_azure_refresh_failures_total Number of Azure-SD refresh failures.
# TYPE prometheus_sd_azure_refresh_failures_total counter
prometheus_sd_azure_refresh_failures_total 0
# HELP prometheus_sd_consul_rpc_duration_seconds The duration of a Consul RPC call in seconds.
# TYPE prometheus_sd_consul_rpc_duration_seconds summary
prometheus_sd_consul_rpc_duration_seconds{call="service",endpoint="catalog",quantile="0.5"} NaN
prometheus_sd_consul_rpc_duration_seconds{call="service",endpoint="catalog",quantile="0.9"} NaN
prometheus_sd_consul_rpc_duration_seconds{call="service",endpoint="catalog",quantile="0.99"} NaN
prometheus_sd_consul_rpc_duration_seconds_sum{call="service",endpoint="catalog"} 0
prometheus_sd_consul_rpc_duration_seconds_count{call="service",endpoint="catalog"} 0
prometheus_sd_consul_rpc_duration_seconds{call="services",endpoint="catalog",quantile="0.5"} NaN
prometheus_sd_consul_rpc_duration_seconds{call="services",endpoint="catalog",quantile="0.9"} NaN
prometheus_sd_consul_rpc_duration_seconds{call="services",endpoint="catalog",quantile="0.99"} NaN
prometheus_sd_consul_rpc_duration_seconds_sum{call="services",endpoint="catalog"} 0
prometheus_sd_consul_rpc_duration_seconds_count{call="services",endpoint="catalog"} 0
# HELP prometheus_sd_consul_rpc_failures_total The number of Consul RPC call failures.
# TYPE prometheus_sd_consul_rpc_failures_total counter
prometheus_sd_consul_rpc_failures_total 0
# HELP prometheus_sd_dns_lookup_failures_total The number of DNS-SD lookup failures.
# TYPE prometheus_sd_dns_lookup_failures_total counter
prometheus_sd_dns_lookup_failures_total 0
# HELP prometheus_sd_dns_lookups_total The number of DNS-SD lookups.
# TYPE prometheus_sd_dns_lookups_total counter
prometheus_sd_dns_lookups_total 0
# HELP prometheus_sd_ec2_refresh_duration_seconds The duration of a EC2-SD refresh in seconds.
# TYPE prometheus_sd_ec2_refresh_duration_seconds summary
prometheus_sd_ec2_refresh_duration_seconds{quantile="0.5"} NaN
prometheus_sd_ec2_refresh_duration_seconds{quantile="0.9"} NaN
prometheus_sd_ec2_refresh_duration_seconds{quantile="0.99"} NaN
prometheus_sd_ec2_refresh_duration_seconds_sum 0
prometheus_sd_ec2_refresh_duration_seconds_count 0
# HELP prometheus_sd_ec2_refresh_failures_total The number of EC2-SD scrape failures.
# TYPE prometheus_sd_ec2_refresh_failures_total counter
prometheus_sd_ec2_refresh_failures_total 0
# HELP prometheus_sd_file_read_errors_total The number of File-SD read errors.
# TYPE prometheus_sd_file_read_errors_total counter
prometheus_sd_file_read_errors_total 0
# HELP prometheus_sd_file_scan_duration_seconds The duration of the File-SD scan in seconds.
# TYPE prometheus_sd_file_scan_duration_seconds summary
prometheus_sd_file_scan_duration_seconds{quantile="0.5"} NaN
prometheus_sd_file_scan_duration_seconds{quantile="0.9"} NaN
prometheus_sd_file_scan_duration_seconds{quantile="0.99"} NaN
prometheus_sd_file_scan_duration_seconds_sum 0
prometheus_sd_file_scan_duration_seconds_count 0
# HELP prometheus_sd_gce_refresh_duration The duration of a GCE-SD refresh in seconds.
# TYPE prometheus_sd_gce_refresh_duration summary
prometheus_sd_gce_refresh_duration{quantile="0.5"} NaN
prometheus_sd_gce_refresh_duration{quantile="0.9"} NaN
prometheus_sd_gce_refresh_duration{quantile="0.99"} NaN
prometheus_sd_gce_refresh_duration_sum 0
prometheus_sd_gce_refresh_duration_count 0
# HELP prometheus_sd_gce_refresh_failures_total The number of GCE-SD refresh failures.
# TYPE prometheus_sd_gce_refresh_failures_total counter
prometheus_sd_gce_refresh_failures_total 0
# HELP prometheus_sd_kubernetes_events_total The number of Kubernetes events handled.
# TYPE prometheus_sd_kubernetes_events_total counter
prometheus_sd_kubernetes_events_total{event="add",role="endpoints"} 0
prometheus_sd_kubernetes_events_total{event="add",role="node"} 0
prometheus_sd_kubernetes_events_total{event="add",role="pod"} 0
prometheus_sd_kubernetes_events_total{event="add",role="service"} 0
prometheus_sd_kubernetes_events_total{event="delete",role="endpoints"} 0
prometheus_sd_kubernetes_events_total{event="delete",role="node"} 0
prometheus_sd_kubernetes_events_total{event="delete",role="pod"} 0
prometheus_sd_kubernetes_events_total{event="delete",role="service"} 0
prometheus_sd_kubernetes_events_total{event="update",role="endpoints"} 0
prometheus_sd_kubernetes_events_total{event="update",role="node"} 0
prometheus_sd_kubernetes_events_total{event="update",role="pod"} 0
prometheus_sd_kubernetes_events_total{event="update",role="service"} 0
# HELP prometheus_sd_marathon_refresh_duration_seconds The duration of a Marathon-SD refresh in seconds.
# TYPE prometheus_sd_marathon_refresh_duration_seconds summary
prometheus_sd_marathon_refresh_duration_seconds{quantile="0.5"} NaN
prometheus_sd_marathon_refresh_duration_seconds{quantile="0.9"} NaN
prometheus_sd_marathon_refresh_duration_seconds{quantile="0.99"} NaN
prometheus_sd_marathon_refresh_duration_seconds_sum 0
prometheus_sd_marathon_refresh_duration_seconds_count 0
# HELP prometheus_sd_marathon_refresh_failures_total The number of Marathon-SD refresh failures.
# TYPE prometheus_sd_marathon_refresh_failures_total counter
prometheus_sd_marathon_refresh_failures_total 0
# HELP prometheus_target_interval_length_seconds Actual intervals between scrapes.
# TYPE prometheus_target_interval_length_seconds summary
prometheus_target_interval_length_seconds{interval="50ms",quantile="0.01"} 0.046182157
prometheus_target_interval_length_seconds{interval="50ms",quantile="0.05"} 0.047306979000000006
prometheus_target_interval_length_seconds{interval="50ms",quantile="0.5"} 0.050381782
prometheus_target_interval_length_seconds{interval="50ms",quantile="0.9"} 0.052614556
prometheus_target_interval_length_seconds{interval="50ms",quantile="0.99"} 0.054404386000000006
prometheus_target_interval_length_seconds_sum{interval="50ms"} 34.512091221999995
prometheus_target_interval_length_seconds_count{interval="50ms"} 685
# HELP prometheus_target_scrape_pool_sync_total Total number of syncs that were executed on a scrape pool.
# TYPE prometheus_target_scrape_pool_sync_total counter
prometheus_target_scrape_pool_sync_total{scrape_job="prometheus"} 1
# HELP prometheus_target_skipped_scrapes_total Total number of scrapes that were skipped because the metric storage was throttled.
# TYPE prometheus_target_skipped_scrapes_total counter
prometheus_target_skipped_scrapes_total 0
# HELP prometheus_target_sync_length_seconds Actual interval to sync the scrape pool.
# TYPE prometheus_target_sync_length_seconds summary
prometheus_target_sync_length_seconds{scrape_job="prometheus",quantile="0.01"} 0.00020043300000000002
prometheus_target_sync_length_seconds{scrape_job="prometheus",quantile="0.05"} 0.00020043300000000002
prometheus_target_sync_length_seconds{scrape_job="prometheus",quantile="0.5"} 0.00020043300000000002
prometheus_target_sync_length_seconds{scrape_job="prometheus",quantile="0.9"} 0.00020043300000000002
prometheus_target_sync_length_seconds{scrape_job="prometheus",quantile="0.99"} 0.00020043300000000002
prometheus_target_sync_length_seconds_sum{scrape_job="prometheus"} 0.00020043300000000002
prometheus_target_sync_length_seconds_count{scrape_job="prometheus"} 1
# HELP prometheus_treecache_watcher_goroutines The current number of watcher goroutines.
# TYPE prometheus_treecache_watcher_goroutines gauge
prometheus_treecache_watcher_goroutines 0
# HELP prometheus_treecache_zookeeper_failures_total The total number of ZooKeeper failures.
# TYPE prometheus_treecache_zookeeper_failures_total counter
prometheus_treecache_zookeeper_failures_total 0
# EOF
