run:

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 1m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  skip-dirs-use-default: true

  modules-download-mode: readonly

output:
  format: colored-line-number
