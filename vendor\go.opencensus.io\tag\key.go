// Copyright 2017, OpenCensus Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package tag

// Key represents a tag key.
type Key struct {
	name string
}

// <PERSON><PERSON><PERSON> creates or retrieves a string key identified by name.
// Calling NewKey more than once with the same name returns the same key.
func <PERSON>ey(name string) (Key, error) {
	if !checkKeyName(name) {
		return Key{}, errInvalidKeyName
	}
	return Key{name: name}, nil
}

// Must<PERSON>ew<PERSON><PERSON> returns a key with the given name, and panics if name is an invalid key name.
func MustNew<PERSON>ey(name string) Key {
	k, err := <PERSON><PERSON><PERSON>(name)
	if err != nil {
		panic(err)
	}
	return k
}

// Name returns the name of the key.
func (k Key) Name() string {
	return k.name
}
