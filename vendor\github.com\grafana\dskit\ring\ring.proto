syntax = "proto3";

package ring;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;

message Desc {
	map<string,InstanceDesc> ingesters = 1 [(gogoproto.nullable) = false];
	reserved 2;
}

message InstanceDesc {
	reserved 4, 5; // old, deprecated fields

	string addr = 1;

	// Unix timestamp (with seconds precision) of the last heartbeat sent
	// by this instance.
	int64 timestamp = 2;

	InstanceState state = 3;
	repeated uint32 tokens = 6;
	string zone = 7;

	// Unix timestamp (with seconds precision) of when the instance has been registered
	// to the ring. This field has not been called "joined_timestamp" intentionally, in order
	// to not introduce any misunderstanding with the instance's "joining" state.
	//
	// This field is used to find out subset of instances that could have possibly owned a
	// specific token in the past. Because of this, it's important that either this timestamp
	// is set to the real time the instance has been registered to the ring or it's left
	// 0 (which means unknown).
	//
	// When an instance is already registered in the ring with a value of 0 it's NOT safe to
	// update the timestamp to "now" because it would break the contract, given the instance
	// was already registered before "now". If unknown (0), it should be left as is, and the
	// code will properly deal with that.
	int64 registered_timestamp = 8;
}

enum InstanceState {
	ACTIVE = 0;
	LEAVING = 1;

	PENDING = 2;
	JOINING = 3;

	// This state is only used by gossiping code to distribute information about
	// instances that have been removed from the ring. Ring users should not use it directly.
	LEFT = 4;
}
