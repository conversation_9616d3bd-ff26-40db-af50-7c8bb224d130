/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	v1 "k8s.io/api/core/v1"
	resource "k8s.io/apimachinery/pkg/api/resource"
)

// ContainerResourceMetricStatusApplyConfiguration represents an declarative configuration of the ContainerResourceMetricStatus type for use
// with apply.
type ContainerResourceMetricStatusApplyConfiguration struct {
	Name                      *v1.ResourceName   `json:"name,omitempty"`
	CurrentAverageUtilization *int32             `json:"currentAverageUtilization,omitempty"`
	CurrentAverageValue       *resource.Quantity `json:"currentAverageValue,omitempty"`
	Container                 *string            `json:"container,omitempty"`
}

// ContainerResourceMetricStatusApplyConfiguration constructs an declarative configuration of the ContainerResourceMetricStatus type for use with
// apply.
func ContainerResourceMetricStatus() *ContainerResourceMetricStatusApplyConfiguration {
	return &ContainerResourceMetricStatusApplyConfiguration{}
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *ContainerResourceMetricStatusApplyConfiguration) WithName(value v1.ResourceName) *ContainerResourceMetricStatusApplyConfiguration {
	b.Name = &value
	return b
}

// WithCurrentAverageUtilization sets the CurrentAverageUtilization field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CurrentAverageUtilization field is set to the value of the last call.
func (b *ContainerResourceMetricStatusApplyConfiguration) WithCurrentAverageUtilization(value int32) *ContainerResourceMetricStatusApplyConfiguration {
	b.CurrentAverageUtilization = &value
	return b
}

// WithCurrentAverageValue sets the CurrentAverageValue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CurrentAverageValue field is set to the value of the last call.
func (b *ContainerResourceMetricStatusApplyConfiguration) WithCurrentAverageValue(value resource.Quantity) *ContainerResourceMetricStatusApplyConfiguration {
	b.CurrentAverageValue = &value
	return b
}

// WithContainer sets the Container field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Container field is set to the value of the last call.
func (b *ContainerResourceMetricStatusApplyConfiguration) WithContainer(value string) *ContainerResourceMetricStatusApplyConfiguration {
	b.Container = &value
	return b
}
