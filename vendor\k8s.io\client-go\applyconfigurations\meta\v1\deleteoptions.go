/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// DeleteOptionsApplyConfiguration represents an declarative configuration of the DeleteOptions type for use
// with apply.
type DeleteOptionsApplyConfiguration struct {
	TypeMetaApplyConfiguration `json:",inline"`
	GracePeriodSeconds         *int64                           `json:"gracePeriodSeconds,omitempty"`
	Preconditions              *PreconditionsApplyConfiguration `json:"preconditions,omitempty"`
	OrphanDependents           *bool                            `json:"orphanDependents,omitempty"`
	PropagationPolicy          *metav1.DeletionPropagation      `json:"propagationPolicy,omitempty"`
	DryRun                     []string                         `json:"dryRun,omitempty"`
}

// DeleteOptionsApplyConfiguration constructs an declarative configuration of the DeleteOptions type for use with
// apply.
func DeleteOptions() *DeleteOptionsApplyConfiguration {
	return &DeleteOptionsApplyConfiguration{}
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *DeleteOptionsApplyConfiguration) WithKind(value string) *DeleteOptionsApplyConfiguration {
	b.Kind = &value
	return b
}

// WithAPIVersion sets the APIVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the APIVersion field is set to the value of the last call.
func (b *DeleteOptionsApplyConfiguration) WithAPIVersion(value string) *DeleteOptionsApplyConfiguration {
	b.APIVersion = &value
	return b
}

// WithGracePeriodSeconds sets the GracePeriodSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GracePeriodSeconds field is set to the value of the last call.
func (b *DeleteOptionsApplyConfiguration) WithGracePeriodSeconds(value int64) *DeleteOptionsApplyConfiguration {
	b.GracePeriodSeconds = &value
	return b
}

// WithPreconditions sets the Preconditions field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Preconditions field is set to the value of the last call.
func (b *DeleteOptionsApplyConfiguration) WithPreconditions(value *PreconditionsApplyConfiguration) *DeleteOptionsApplyConfiguration {
	b.Preconditions = value
	return b
}

// WithOrphanDependents sets the OrphanDependents field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the OrphanDependents field is set to the value of the last call.
func (b *DeleteOptionsApplyConfiguration) WithOrphanDependents(value bool) *DeleteOptionsApplyConfiguration {
	b.OrphanDependents = &value
	return b
}

// WithPropagationPolicy sets the PropagationPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PropagationPolicy field is set to the value of the last call.
func (b *DeleteOptionsApplyConfiguration) WithPropagationPolicy(value metav1.DeletionPropagation) *DeleteOptionsApplyConfiguration {
	b.PropagationPolicy = &value
	return b
}

// WithDryRun adds the given value to the DryRun field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the DryRun field.
func (b *DeleteOptionsApplyConfiguration) WithDryRun(values ...string) *DeleteOptionsApplyConfiguration {
	for i := range values {
		b.DryRun = append(b.DryRun, values[i])
	}
	return b
}
