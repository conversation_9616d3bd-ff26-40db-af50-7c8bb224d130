## Changelog

### v2.14.3

- fix problem where <PERSON><PERSON> code didn't set the selected DB
- update to redis 6.0.10 (thanks @lazappa)


### v2.14.2

- update LUA dependency
- deal with (p)unsubscribe when there are no channels


### v2.14.1

- mod tidy


### v2.14.0

- support for HELLO and the RESP3 protocol
- KEEPTTL in SET (thanks @johnpena)


### v2.13.3

- support Go 1.14 and 1.15
- update the `Check...()` methods
- support for XREAD (thanks @pieterlexis)


### v2.13.2

- Use SAN instead of CN in self signed cert for testing (thanks @johejo)
- Travis CI now tests against the most recent two versions of Go (thanks @johejo)
- changed unit and integration tests to compare raw payloads, not parsed payloads
- remove "redigo" dependency


### v2.13.1

- added HSTRLEN
- minimal support for ACL users in AUTH


### v2.13.0

- added RunTLS(...)
- added SetError(...)


### v2.12.0

- redis 6
- <PERSON><PERSON> json update (thanks @gsmith85)
- CLUSTER commands (thanks @kratisto)
- fix TOUCH
- fix a shutdown race condition


### v2.11.4

- ZUNIONSTORE now supports standard set types (thanks @wshirey)


### v2.11.3

- support for TOUCH (thanks @cleroux)
- support for cluster and stream commands (thanks @kak-tus)


### v2.11.2

- make sure Lua code is executed concurrently
- add command GEORADIUSBYMEMBER (thanks @kyeett)


### v2.11.1

- globals protection for Lua code (thanks @vk-outreach)
- HSET update (thanks @carlgreen)
- fix BLPOP block on shutdown (thanks @Asalle)


### v2.11.0

- added XRANGE/XREVRANGE, XADD, and XLEN (thanks @skateinmars)
- added GEODIST
- improved precision for geohashes, closer to what real redis does
- use 128bit floats internally for INCRBYFLOAT and related (thanks @timnd)


### v2.10.1

- added m.Server()


### v2.10.0

- added UNLINK
- fix DEL zero-argument case
- cleanup some direct access commands
- added GEOADD, GEOPOS, GEORADIUS, and GEORADIUS_RO


### v2.9.1

- fix issue with ZRANGEBYLEX
- fix issue with BRPOPLPUSH and direct access


### v2.9.0

- proper versioned import of github.com/gomodule/redigo (thanks @yfei1)
- fix messages generated by PSUBSCRIBE
- optional internal seed (thanks @zikaeroh)


### v2.8.0

Proper `v2` in go.mod.


### older

See https://github.com/alicebob/miniredis/releases for the full changelog
