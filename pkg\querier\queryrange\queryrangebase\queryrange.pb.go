// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/querier/queryrange/queryrangebase/queryrange.proto

package queryrangebase

import (
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	types "github.com/gogo/protobuf/types"
	_ "github.com/golang/protobuf/ptypes/duration"
	github_com_grafana_loki_pkg_logproto "github.com/grafana/loki/pkg/logproto"
	logproto "github.com/grafana/loki/pkg/logproto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type PrometheusRequestHeader struct {
	Name   string   `protobuf:"bytes,1,opt,name=Name,proto3" json:"-"`
	Values []string `protobuf:"bytes,2,rep,name=Values,proto3" json:"-"`
}

func (m *PrometheusRequestHeader) Reset()      { *m = PrometheusRequestHeader{} }
func (*PrometheusRequestHeader) ProtoMessage() {}
func (*PrometheusRequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{0}
}
func (m *PrometheusRequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrometheusRequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrometheusRequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrometheusRequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrometheusRequestHeader.Merge(m, src)
}
func (m *PrometheusRequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *PrometheusRequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_PrometheusRequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_PrometheusRequestHeader proto.InternalMessageInfo

func (m *PrometheusRequestHeader) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PrometheusRequestHeader) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

type PrometheusRequest struct {
	Path           string                     `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Start          int64                      `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End            int64                      `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	Step           int64                      `protobuf:"varint,4,opt,name=step,proto3" json:"step,omitempty"`
	Timeout        time.Duration              `protobuf:"bytes,5,opt,name=timeout,proto3,stdduration" json:"timeout"`
	Query          string                     `protobuf:"bytes,6,opt,name=query,proto3" json:"query,omitempty"`
	CachingOptions CachingOptions             `protobuf:"bytes,7,opt,name=cachingOptions,proto3" json:"cachingOptions"`
	Headers        []*PrometheusRequestHeader `protobuf:"bytes,8,rep,name=Headers,proto3" json:"-"`
}

func (m *PrometheusRequest) Reset()      { *m = PrometheusRequest{} }
func (*PrometheusRequest) ProtoMessage() {}
func (*PrometheusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{1}
}
func (m *PrometheusRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrometheusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrometheusRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrometheusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrometheusRequest.Merge(m, src)
}
func (m *PrometheusRequest) XXX_Size() int {
	return m.Size()
}
func (m *PrometheusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PrometheusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PrometheusRequest proto.InternalMessageInfo

func (m *PrometheusRequest) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

func (m *PrometheusRequest) GetStart() int64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *PrometheusRequest) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

func (m *PrometheusRequest) GetStep() int64 {
	if m != nil {
		return m.Step
	}
	return 0
}

func (m *PrometheusRequest) GetTimeout() time.Duration {
	if m != nil {
		return m.Timeout
	}
	return 0
}

func (m *PrometheusRequest) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *PrometheusRequest) GetCachingOptions() CachingOptions {
	if m != nil {
		return m.CachingOptions
	}
	return CachingOptions{}
}

func (m *PrometheusRequest) GetHeaders() []*PrometheusRequestHeader {
	if m != nil {
		return m.Headers
	}
	return nil
}

type PrometheusResponseHeader struct {
	Name   string   `protobuf:"bytes,1,opt,name=Name,proto3" json:"-"`
	Values []string `protobuf:"bytes,2,rep,name=Values,proto3" json:"-"`
}

func (m *PrometheusResponseHeader) Reset()      { *m = PrometheusResponseHeader{} }
func (*PrometheusResponseHeader) ProtoMessage() {}
func (*PrometheusResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{2}
}
func (m *PrometheusResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrometheusResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrometheusResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrometheusResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrometheusResponseHeader.Merge(m, src)
}
func (m *PrometheusResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *PrometheusResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_PrometheusResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_PrometheusResponseHeader proto.InternalMessageInfo

func (m *PrometheusResponseHeader) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PrometheusResponseHeader) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

type PrometheusResponse struct {
	Status    string                      `protobuf:"bytes,1,opt,name=Status,proto3" json:"status"`
	Data      PrometheusData              `protobuf:"bytes,2,opt,name=Data,proto3" json:"data,omitempty"`
	ErrorType string                      `protobuf:"bytes,3,opt,name=ErrorType,proto3" json:"errorType,omitempty"`
	Error     string                      `protobuf:"bytes,4,opt,name=Error,proto3" json:"error,omitempty"`
	Headers   []*PrometheusResponseHeader `protobuf:"bytes,5,rep,name=Headers,proto3" json:"-"`
}

func (m *PrometheusResponse) Reset()      { *m = PrometheusResponse{} }
func (*PrometheusResponse) ProtoMessage() {}
func (*PrometheusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{3}
}
func (m *PrometheusResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrometheusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrometheusResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrometheusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrometheusResponse.Merge(m, src)
}
func (m *PrometheusResponse) XXX_Size() int {
	return m.Size()
}
func (m *PrometheusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PrometheusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PrometheusResponse proto.InternalMessageInfo

func (m *PrometheusResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *PrometheusResponse) GetData() PrometheusData {
	if m != nil {
		return m.Data
	}
	return PrometheusData{}
}

func (m *PrometheusResponse) GetErrorType() string {
	if m != nil {
		return m.ErrorType
	}
	return ""
}

func (m *PrometheusResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *PrometheusResponse) GetHeaders() []*PrometheusResponseHeader {
	if m != nil {
		return m.Headers
	}
	return nil
}

type PrometheusData struct {
	ResultType string         `protobuf:"bytes,1,opt,name=ResultType,proto3" json:"resultType"`
	Result     []SampleStream `protobuf:"bytes,2,rep,name=Result,proto3" json:"result"`
}

func (m *PrometheusData) Reset()      { *m = PrometheusData{} }
func (*PrometheusData) ProtoMessage() {}
func (*PrometheusData) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{4}
}
func (m *PrometheusData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrometheusData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrometheusData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrometheusData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrometheusData.Merge(m, src)
}
func (m *PrometheusData) XXX_Size() int {
	return m.Size()
}
func (m *PrometheusData) XXX_DiscardUnknown() {
	xxx_messageInfo_PrometheusData.DiscardUnknown(m)
}

var xxx_messageInfo_PrometheusData proto.InternalMessageInfo

func (m *PrometheusData) GetResultType() string {
	if m != nil {
		return m.ResultType
	}
	return ""
}

func (m *PrometheusData) GetResult() []SampleStream {
	if m != nil {
		return m.Result
	}
	return nil
}

type SampleStream struct {
	Labels  []github_com_grafana_loki_pkg_logproto.LabelAdapter `protobuf:"bytes,1,rep,name=labels,proto3,customtype=github.com/grafana/loki/pkg/logproto.LabelAdapter" json:"metric"`
	Samples []logproto.LegacySample                             `protobuf:"bytes,2,rep,name=samples,proto3" json:"values"`
}

func (m *SampleStream) Reset()      { *m = SampleStream{} }
func (*SampleStream) ProtoMessage() {}
func (*SampleStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{5}
}
func (m *SampleStream) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SampleStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SampleStream.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SampleStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SampleStream.Merge(m, src)
}
func (m *SampleStream) XXX_Size() int {
	return m.Size()
}
func (m *SampleStream) XXX_DiscardUnknown() {
	xxx_messageInfo_SampleStream.DiscardUnknown(m)
}

var xxx_messageInfo_SampleStream proto.InternalMessageInfo

func (m *SampleStream) GetSamples() []logproto.LegacySample {
	if m != nil {
		return m.Samples
	}
	return nil
}

type CachedResponse struct {
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	// List of cached responses; non-overlapping and in order.
	Extents []Extent `protobuf:"bytes,2,rep,name=extents,proto3" json:"extents"`
}

func (m *CachedResponse) Reset()      { *m = CachedResponse{} }
func (*CachedResponse) ProtoMessage() {}
func (*CachedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{6}
}
func (m *CachedResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CachedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CachedResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CachedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CachedResponse.Merge(m, src)
}
func (m *CachedResponse) XXX_Size() int {
	return m.Size()
}
func (m *CachedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CachedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CachedResponse proto.InternalMessageInfo

func (m *CachedResponse) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CachedResponse) GetExtents() []Extent {
	if m != nil {
		return m.Extents
	}
	return nil
}

type Extent struct {
	Start    int64      `protobuf:"varint,1,opt,name=start,proto3" json:"start"`
	End      int64      `protobuf:"varint,2,opt,name=end,proto3" json:"end"`
	TraceId  string     `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"-"`
	Response *types.Any `protobuf:"bytes,5,opt,name=response,proto3" json:"response"`
}

func (m *Extent) Reset()      { *m = Extent{} }
func (*Extent) ProtoMessage() {}
func (*Extent) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{7}
}
func (m *Extent) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Extent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Extent.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Extent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Extent.Merge(m, src)
}
func (m *Extent) XXX_Size() int {
	return m.Size()
}
func (m *Extent) XXX_DiscardUnknown() {
	xxx_messageInfo_Extent.DiscardUnknown(m)
}

var xxx_messageInfo_Extent proto.InternalMessageInfo

func (m *Extent) GetStart() int64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *Extent) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

func (m *Extent) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *Extent) GetResponse() *types.Any {
	if m != nil {
		return m.Response
	}
	return nil
}

type CachingOptions struct {
	Disabled bool `protobuf:"varint,1,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (m *CachingOptions) Reset()      { *m = CachingOptions{} }
func (*CachingOptions) ProtoMessage() {}
func (*CachingOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_4cc6a0c1d6b614c4, []int{8}
}
func (m *CachingOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CachingOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CachingOptions.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CachingOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CachingOptions.Merge(m, src)
}
func (m *CachingOptions) XXX_Size() int {
	return m.Size()
}
func (m *CachingOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_CachingOptions.DiscardUnknown(m)
}

var xxx_messageInfo_CachingOptions proto.InternalMessageInfo

func (m *CachingOptions) GetDisabled() bool {
	if m != nil {
		return m.Disabled
	}
	return false
}

func init() {
	proto.RegisterType((*PrometheusRequestHeader)(nil), "queryrangebase.PrometheusRequestHeader")
	proto.RegisterType((*PrometheusRequest)(nil), "queryrangebase.PrometheusRequest")
	proto.RegisterType((*PrometheusResponseHeader)(nil), "queryrangebase.PrometheusResponseHeader")
	proto.RegisterType((*PrometheusResponse)(nil), "queryrangebase.PrometheusResponse")
	proto.RegisterType((*PrometheusData)(nil), "queryrangebase.PrometheusData")
	proto.RegisterType((*SampleStream)(nil), "queryrangebase.SampleStream")
	proto.RegisterType((*CachedResponse)(nil), "queryrangebase.CachedResponse")
	proto.RegisterType((*Extent)(nil), "queryrangebase.Extent")
	proto.RegisterType((*CachingOptions)(nil), "queryrangebase.CachingOptions")
}

func init() {
	proto.RegisterFile("pkg/querier/queryrange/queryrangebase/queryrange.proto", fileDescriptor_4cc6a0c1d6b614c4)
}

var fileDescriptor_4cc6a0c1d6b614c4 = []byte{
	// 869 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x55, 0x3f, 0x6f, 0xdb, 0x46,
	0x14, 0x17, 0x4d, 0x89, 0x92, 0xce, 0x81, 0x92, 0x5e, 0x82, 0x84, 0x72, 0x5b, 0x52, 0xe0, 0x52,
	0x15, 0x48, 0x28, 0xd4, 0x45, 0xbb, 0xa5, 0xa8, 0x19, 0x07, 0x48, 0x02, 0xa3, 0x0d, 0xce, 0x41,
	0x87, 0x2e, 0xc5, 0x49, 0x7c, 0xa1, 0x09, 0xf3, 0x5f, 0xee, 0x8e, 0x41, 0xb5, 0x75, 0xea, 0xdc,
	0xb1, 0x1f, 0xa1, 0x43, 0x97, 0x7e, 0x0b, 0x8f, 0x1e, 0x83, 0x0e, 0x6c, 0x2d, 0x2f, 0x05, 0xa7,
	0x7c, 0x84, 0x82, 0x77, 0xa4, 0x44, 0xcb, 0x6d, 0x3a, 0x74, 0xb1, 0xdf, 0x9f, 0xdf, 0xfb, 0x73,
	0xbf, 0xf7, 0xf8, 0x84, 0x3e, 0xcf, 0x4e, 0x83, 0xd9, 0xab, 0x1c, 0x58, 0x08, 0x4c, 0xfe, 0x5f,
	0x32, 0x9a, 0x04, 0xd0, 0x12, 0xe7, 0x94, 0xb7, 0x55, 0x37, 0x63, 0xa9, 0x48, 0xf1, 0xe8, 0x2a,
	0x60, 0xef, 0x41, 0x10, 0x8a, 0x93, 0x7c, 0xee, 0x2e, 0xd2, 0x78, 0x16, 0xa4, 0x41, 0x3a, 0x93,
	0xb0, 0x79, 0xfe, 0x52, 0x6a, 0x52, 0x91, 0x92, 0x0a, 0xdf, 0x1b, 0x07, 0x69, 0x1a, 0x44, 0xb0,
	0x41, 0xd1, 0x64, 0x59, 0xbb, 0xac, 0x6d, 0x97, 0x9f, 0x33, 0x2a, 0xc2, 0x34, 0xa9, 0xfd, 0xef,
	0x57, 0x1d, 0x47, 0x69, 0xa0, 0x72, 0x36, 0x82, 0x72, 0x3a, 0xc7, 0xe8, 0xde, 0x73, 0x96, 0xc6,
	0x20, 0x4e, 0x20, 0xe7, 0x04, 0x5e, 0xe5, 0xc0, 0xc5, 0x13, 0xa0, 0x3e, 0x30, 0x3c, 0x46, 0xdd,
	0xaf, 0x68, 0x0c, 0xa6, 0x36, 0xd1, 0xa6, 0x43, 0xaf, 0x57, 0x16, 0xb6, 0xf6, 0x80, 0x48, 0x13,
	0xfe, 0x10, 0x19, 0xdf, 0xd0, 0x28, 0x07, 0x6e, 0xee, 0x4c, 0xf4, 0x8d, 0xb3, 0x36, 0x3a, 0xe7,
	0x3b, 0xe8, 0xbd, 0x6b, 0x59, 0x31, 0x46, 0xdd, 0x8c, 0x8a, 0x13, 0x95, 0x8f, 0x48, 0x19, 0xdf,
	0x41, 0x3d, 0x2e, 0x28, 0x13, 0xe6, 0xce, 0x44, 0x9b, 0xea, 0x44, 0x29, 0xf8, 0x16, 0xd2, 0x21,
	0xf1, 0x4d, 0x5d, 0xda, 0x2a, 0xb1, 0x8a, 0xe5, 0x02, 0x32, 0xb3, 0x2b, 0x4d, 0x52, 0xc6, 0x0f,
	0x51, 0x5f, 0x84, 0x31, 0xa4, 0xb9, 0x30, 0x7b, 0x13, 0x6d, 0xba, 0xbb, 0x3f, 0x76, 0x15, 0x13,
	0x6e, 0xc3, 0x84, 0x7b, 0x58, 0x33, 0xe1, 0x0d, 0xce, 0x0a, 0xbb, 0xf3, 0xf3, 0x1f, 0xb6, 0x46,
	0x9a, 0x98, 0xaa, 0xb4, 0x1c, 0x89, 0x69, 0xc8, 0x7e, 0x94, 0x82, 0x8f, 0xd0, 0x68, 0x41, 0x17,
	0x27, 0x61, 0x12, 0x7c, 0x9d, 0x55, 0x91, 0xdc, 0xec, 0xcb, 0xdc, 0x96, 0x7b, 0x75, 0x7e, 0xee,
	0xa3, 0x2b, 0x28, 0xaf, 0x5b, 0x15, 0x20, 0x5b, 0xb1, 0xf8, 0x09, 0xea, 0x2b, 0x32, 0xb9, 0x39,
	0x98, 0xe8, 0xd3, 0xdd, 0xfd, 0x8f, 0xb6, 0xd3, 0xfc, 0x0b, 0xf9, 0x0d, 0xa3, 0x4d, 0xb8, 0xf3,
	0x02, 0x99, 0x6d, 0x28, 0xcf, 0xd2, 0x84, 0xc3, 0xff, 0x1e, 0xd4, 0x6f, 0x3b, 0x08, 0x5f, 0x4f,
	0x8b, 0x1d, 0x64, 0x1c, 0x0b, 0x2a, 0x72, 0x5e, 0xa7, 0x44, 0x65, 0x61, 0x1b, 0x5c, 0x5a, 0x48,
	0xed, 0xc1, 0xcf, 0x50, 0xf7, 0x90, 0x0a, 0x2a, 0x07, 0xf7, 0x0f, 0xf4, 0x6c, 0xb2, 0x56, 0x28,
	0xef, 0x6e, 0x45, 0x4f, 0x59, 0xd8, 0x23, 0x9f, 0x0a, 0x7a, 0x3f, 0x8d, 0x43, 0x01, 0x71, 0x26,
	0x96, 0x44, 0xe6, 0xc0, 0x9f, 0xa1, 0xe1, 0x63, 0xc6, 0x52, 0xf6, 0x62, 0x99, 0x81, 0x9c, 0xfa,
	0xd0, 0xbb, 0x57, 0x16, 0xf6, 0x6d, 0x68, 0x8c, 0xad, 0x88, 0x0d, 0x12, 0x7f, 0x8c, 0x7a, 0x52,
	0x91, 0x5b, 0x31, 0xf4, 0x6e, 0x97, 0x85, 0x7d, 0x53, 0x86, 0xb4, 0xe0, 0x0a, 0x81, 0x9f, 0x6e,
	0x06, 0xd1, 0x93, 0x83, 0x98, 0xbe, 0x6b, 0x10, 0x6d, 0x76, 0xaf, 0x4d, 0xe2, 0x47, 0x0d, 0x8d,
	0xae, 0xbe, 0x0e, 0xbb, 0x08, 0x11, 0xe0, 0x79, 0x24, 0xe4, 0x03, 0x14, 0x67, 0xa3, 0xb2, 0xb0,
	0x11, 0x5b, 0x5b, 0x49, 0x0b, 0x81, 0x0f, 0x91, 0xa1, 0x34, 0x39, 0x95, 0xdd, 0xfd, 0x0f, 0xb6,
	0x9b, 0x39, 0xa6, 0x71, 0x16, 0xc1, 0xb1, 0x60, 0x40, 0x63, 0x6f, 0x54, 0x73, 0x67, 0xa8, 0x6c,
	0xa4, 0x8e, 0x75, 0xce, 0x34, 0x74, 0xa3, 0x0d, 0xc4, 0xaf, 0x91, 0x11, 0xd1, 0x39, 0x44, 0xd5,
	0xd8, 0x74, 0xf9, 0x3d, 0xac, 0x3f, 0xf6, 0x23, 0x08, 0xe8, 0x62, 0x79, 0x54, 0x79, 0x9f, 0xd3,
	0x90, 0x79, 0x8f, 0xaa, 0x9c, 0xbf, 0x17, 0xf6, 0x27, 0xed, 0x2b, 0xc4, 0xe8, 0x4b, 0x9a, 0xd0,
	0x59, 0x94, 0x9e, 0x86, 0xb3, 0xf6, 0xcd, 0x70, 0x65, 0xdc, 0x81, 0x4f, 0x33, 0x01, 0xac, 0x6a,
	0x24, 0x06, 0xc1, 0xc2, 0x05, 0xa9, 0xab, 0xe1, 0x2f, 0x51, 0x9f, 0xcb, 0x3e, 0x78, 0xfd, 0x9e,
	0xbb, 0xdb, 0x85, 0x55, 0x9b, 0x9b, 0x97, 0xbc, 0x96, 0xeb, 0x47, 0x9a, 0x30, 0x27, 0x41, 0xa3,
	0xea, 0x7b, 0x02, 0x7f, 0xbd, 0x82, 0x63, 0xa4, 0x9f, 0xc2, 0xb2, 0xe6, 0xb2, 0x5f, 0x16, 0x76,
	0xa5, 0x92, 0xea, 0x0f, 0x3e, 0x40, 0x7d, 0xf8, 0x5e, 0x40, 0x22, 0x36, 0xe5, 0xb6, 0xe8, 0x7b,
	0x2c, 0xdd, 0xde, 0xcd, 0xba, 0x5c, 0x03, 0x27, 0x8d, 0xe0, 0xfc, 0xaa, 0x21, 0x43, 0x81, 0xb0,
	0xdd, 0x5c, 0xa0, 0xaa, 0x94, 0xee, 0x0d, 0xcb, 0xc2, 0x56, 0x86, 0xe6, 0x18, 0x8d, 0xd5, 0x31,
	0x92, 0x07, 0x4a, 0x75, 0x02, 0x89, 0xaf, 0xae, 0xd2, 0x04, 0x0d, 0x04, 0xa3, 0x0b, 0xf8, 0x2e,
	0xf4, 0xeb, 0x1d, 0x6c, 0x96, 0x45, 0x9a, 0x9f, 0xfa, 0xf8, 0x0b, 0x34, 0x60, 0xf5, 0x93, 0xea,
	0x23, 0x75, 0xe7, 0xda, 0x91, 0x3a, 0x48, 0x96, 0xde, 0x8d, 0xb2, 0xb0, 0xd7, 0x48, 0xb2, 0x96,
	0x9e, 0x75, 0x07, 0xfa, 0xad, 0xae, 0x73, 0x5f, 0xd1, 0xd3, 0x3a, 0x2c, 0x7b, 0x68, 0xe0, 0x87,
	0x9c, 0xce, 0x23, 0xf0, 0x65, 0xe3, 0x03, 0xb2, 0xd6, 0x3d, 0x7e, 0x7e, 0x61, 0x75, 0xde, 0x5c,
	0x58, 0x9d, 0xb7, 0x17, 0x96, 0xf6, 0xc3, 0xca, 0xd2, 0x7e, 0x59, 0x59, 0xda, 0xd9, 0xca, 0xd2,
	0xce, 0x57, 0x96, 0xf6, 0xe7, 0xca, 0xd2, 0xfe, 0x5a, 0x59, 0x9d, 0xb7, 0x2b, 0x4b, 0xfb, 0xe9,
	0xd2, 0xea, 0x9c, 0x5f, 0x5a, 0x9d, 0x37, 0x97, 0x56, 0xe7, 0xdb, 0x87, 0xef, 0xda, 0x84, 0xff,
	0xfc, 0xbd, 0x9b, 0x1b, 0xf2, 0x39, 0x9f, 0xfe, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x93, 0x01, 0x6e,
	0x11, 0x1f, 0x07, 0x00, 0x00,
}

func (this *PrometheusRequestHeader) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PrometheusRequestHeader)
	if !ok {
		that2, ok := that.(PrometheusRequestHeader)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if len(this.Values) != len(that1.Values) {
		return false
	}
	for i := range this.Values {
		if this.Values[i] != that1.Values[i] {
			return false
		}
	}
	return true
}
func (this *PrometheusRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PrometheusRequest)
	if !ok {
		that2, ok := that.(PrometheusRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Path != that1.Path {
		return false
	}
	if this.Start != that1.Start {
		return false
	}
	if this.End != that1.End {
		return false
	}
	if this.Step != that1.Step {
		return false
	}
	if this.Timeout != that1.Timeout {
		return false
	}
	if this.Query != that1.Query {
		return false
	}
	if !this.CachingOptions.Equal(&that1.CachingOptions) {
		return false
	}
	if len(this.Headers) != len(that1.Headers) {
		return false
	}
	for i := range this.Headers {
		if !this.Headers[i].Equal(that1.Headers[i]) {
			return false
		}
	}
	return true
}
func (this *PrometheusResponseHeader) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PrometheusResponseHeader)
	if !ok {
		that2, ok := that.(PrometheusResponseHeader)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if len(this.Values) != len(that1.Values) {
		return false
	}
	for i := range this.Values {
		if this.Values[i] != that1.Values[i] {
			return false
		}
	}
	return true
}
func (this *PrometheusResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PrometheusResponse)
	if !ok {
		that2, ok := that.(PrometheusResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Status != that1.Status {
		return false
	}
	if !this.Data.Equal(&that1.Data) {
		return false
	}
	if this.ErrorType != that1.ErrorType {
		return false
	}
	if this.Error != that1.Error {
		return false
	}
	if len(this.Headers) != len(that1.Headers) {
		return false
	}
	for i := range this.Headers {
		if !this.Headers[i].Equal(that1.Headers[i]) {
			return false
		}
	}
	return true
}
func (this *PrometheusData) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PrometheusData)
	if !ok {
		that2, ok := that.(PrometheusData)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ResultType != that1.ResultType {
		return false
	}
	if len(this.Result) != len(that1.Result) {
		return false
	}
	for i := range this.Result {
		if !this.Result[i].Equal(&that1.Result[i]) {
			return false
		}
	}
	return true
}
func (this *SampleStream) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SampleStream)
	if !ok {
		that2, ok := that.(SampleStream)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	if len(this.Samples) != len(that1.Samples) {
		return false
	}
	for i := range this.Samples {
		if !this.Samples[i].Equal(&that1.Samples[i]) {
			return false
		}
	}
	return true
}
func (this *CachedResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CachedResponse)
	if !ok {
		that2, ok := that.(CachedResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Key != that1.Key {
		return false
	}
	if len(this.Extents) != len(that1.Extents) {
		return false
	}
	for i := range this.Extents {
		if !this.Extents[i].Equal(&that1.Extents[i]) {
			return false
		}
	}
	return true
}
func (this *Extent) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Extent)
	if !ok {
		that2, ok := that.(Extent)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Start != that1.Start {
		return false
	}
	if this.End != that1.End {
		return false
	}
	if this.TraceId != that1.TraceId {
		return false
	}
	if !this.Response.Equal(that1.Response) {
		return false
	}
	return true
}
func (this *CachingOptions) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CachingOptions)
	if !ok {
		that2, ok := that.(CachingOptions)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Disabled != that1.Disabled {
		return false
	}
	return true
}
func (this *PrometheusRequestHeader) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrangebase.PrometheusRequestHeader{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Values: "+fmt.Sprintf("%#v", this.Values)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PrometheusRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&queryrangebase.PrometheusRequest{")
	s = append(s, "Path: "+fmt.Sprintf("%#v", this.Path)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "Step: "+fmt.Sprintf("%#v", this.Step)+",\n")
	s = append(s, "Timeout: "+fmt.Sprintf("%#v", this.Timeout)+",\n")
	s = append(s, "Query: "+fmt.Sprintf("%#v", this.Query)+",\n")
	s = append(s, "CachingOptions: "+strings.Replace(this.CachingOptions.GoString(), `&`, ``, 1)+",\n")
	if this.Headers != nil {
		s = append(s, "Headers: "+fmt.Sprintf("%#v", this.Headers)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PrometheusResponseHeader) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrangebase.PrometheusResponseHeader{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Values: "+fmt.Sprintf("%#v", this.Values)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PrometheusResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&queryrangebase.PrometheusResponse{")
	s = append(s, "Status: "+fmt.Sprintf("%#v", this.Status)+",\n")
	s = append(s, "Data: "+strings.Replace(this.Data.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "ErrorType: "+fmt.Sprintf("%#v", this.ErrorType)+",\n")
	s = append(s, "Error: "+fmt.Sprintf("%#v", this.Error)+",\n")
	if this.Headers != nil {
		s = append(s, "Headers: "+fmt.Sprintf("%#v", this.Headers)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PrometheusData) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrangebase.PrometheusData{")
	s = append(s, "ResultType: "+fmt.Sprintf("%#v", this.ResultType)+",\n")
	if this.Result != nil {
		vs := make([]*SampleStream, len(this.Result))
		for i := range vs {
			vs[i] = &this.Result[i]
		}
		s = append(s, "Result: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SampleStream) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrangebase.SampleStream{")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	if this.Samples != nil {
		vs := make([]*logproto.LegacySample, len(this.Samples))
		for i := range vs {
			vs[i] = &this.Samples[i]
		}
		s = append(s, "Samples: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CachedResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&queryrangebase.CachedResponse{")
	s = append(s, "Key: "+fmt.Sprintf("%#v", this.Key)+",\n")
	if this.Extents != nil {
		vs := make([]*Extent, len(this.Extents))
		for i := range vs {
			vs[i] = &this.Extents[i]
		}
		s = append(s, "Extents: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Extent) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&queryrangebase.Extent{")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "TraceId: "+fmt.Sprintf("%#v", this.TraceId)+",\n")
	if this.Response != nil {
		s = append(s, "Response: "+fmt.Sprintf("%#v", this.Response)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CachingOptions) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&queryrangebase.CachingOptions{")
	s = append(s, "Disabled: "+fmt.Sprintf("%#v", this.Disabled)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringQueryrange(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *PrometheusRequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrometheusRequestHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrometheusRequestHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PrometheusRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrometheusRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrometheusRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Headers) > 0 {
		for iNdEx := len(m.Headers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Headers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x42
		}
	}
	{
		size, err := m.CachingOptions.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x3a
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0x32
	}
	n2, err2 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.Timeout, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.Timeout):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintQueryrange(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x2a
	if m.Step != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Step))
		i--
		dAtA[i] = 0x20
	}
	if m.End != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.End))
		i--
		dAtA[i] = 0x18
	}
	if m.Start != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Start))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PrometheusResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrometheusResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrometheusResponseHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PrometheusResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrometheusResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrometheusResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Headers) > 0 {
		for iNdEx := len(m.Headers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Headers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ErrorType) > 0 {
		i -= len(m.ErrorType)
		copy(dAtA[i:], m.ErrorType)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.ErrorType)))
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Data.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintQueryrange(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PrometheusData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrometheusData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrometheusData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Result) > 0 {
		for iNdEx := len(m.Result) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Result[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ResultType) > 0 {
		i -= len(m.ResultType)
		copy(dAtA[i:], m.ResultType)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.ResultType)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SampleStream) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SampleStream) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SampleStream) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Samples) > 0 {
		for iNdEx := len(m.Samples) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Samples[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CachedResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CachedResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CachedResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Extents) > 0 {
		for iNdEx := len(m.Extents) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Extents[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryrange(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Extent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Extent) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Extent) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Response != nil {
		{
			size, err := m.Response.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintQueryrange(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.TraceId) > 0 {
		i -= len(m.TraceId)
		copy(dAtA[i:], m.TraceId)
		i = encodeVarintQueryrange(dAtA, i, uint64(len(m.TraceId)))
		i--
		dAtA[i] = 0x22
	}
	if m.End != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.End))
		i--
		dAtA[i] = 0x10
	}
	if m.Start != 0 {
		i = encodeVarintQueryrange(dAtA, i, uint64(m.Start))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CachingOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CachingOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CachingOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Disabled {
		i--
		if m.Disabled {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintQueryrange(dAtA []byte, offset int, v uint64) int {
	offset -= sovQueryrange(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *PrometheusRequestHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *PrometheusRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if m.Start != 0 {
		n += 1 + sovQueryrange(uint64(m.Start))
	}
	if m.End != 0 {
		n += 1 + sovQueryrange(uint64(m.End))
	}
	if m.Step != 0 {
		n += 1 + sovQueryrange(uint64(m.Step))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.Timeout)
	n += 1 + l + sovQueryrange(uint64(l))
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	l = m.CachingOptions.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	if len(m.Headers) > 0 {
		for _, e := range m.Headers {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *PrometheusResponseHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *PrometheusResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	l = m.Data.Size()
	n += 1 + l + sovQueryrange(uint64(l))
	l = len(m.ErrorType)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Headers) > 0 {
		for _, e := range m.Headers {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *PrometheusData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResultType)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Result) > 0 {
		for _, e := range m.Result {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *SampleStream) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	if len(m.Samples) > 0 {
		for _, e := range m.Samples {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *CachedResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if len(m.Extents) > 0 {
		for _, e := range m.Extents {
			l = e.Size()
			n += 1 + l + sovQueryrange(uint64(l))
		}
	}
	return n
}

func (m *Extent) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Start != 0 {
		n += 1 + sovQueryrange(uint64(m.Start))
	}
	if m.End != 0 {
		n += 1 + sovQueryrange(uint64(m.End))
	}
	l = len(m.TraceId)
	if l > 0 {
		n += 1 + l + sovQueryrange(uint64(l))
	}
	if m.Response != nil {
		l = m.Response.Size()
		n += 1 + l + sovQueryrange(uint64(l))
	}
	return n
}

func (m *CachingOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Disabled {
		n += 2
	}
	return n
}

func sovQueryrange(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozQueryrange(x uint64) (n int) {
	return sovQueryrange(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *PrometheusRequestHeader) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PrometheusRequestHeader{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Values:` + fmt.Sprintf("%v", this.Values) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PrometheusRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForHeaders := "[]*PrometheusRequestHeader{"
	for _, f := range this.Headers {
		repeatedStringForHeaders += strings.Replace(f.String(), "PrometheusRequestHeader", "PrometheusRequestHeader", 1) + ","
	}
	repeatedStringForHeaders += "}"
	s := strings.Join([]string{`&PrometheusRequest{`,
		`Path:` + fmt.Sprintf("%v", this.Path) + `,`,
		`Start:` + fmt.Sprintf("%v", this.Start) + `,`,
		`End:` + fmt.Sprintf("%v", this.End) + `,`,
		`Step:` + fmt.Sprintf("%v", this.Step) + `,`,
		`Timeout:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Timeout), "Duration", "duration.Duration", 1), `&`, ``, 1) + `,`,
		`Query:` + fmt.Sprintf("%v", this.Query) + `,`,
		`CachingOptions:` + strings.Replace(strings.Replace(this.CachingOptions.String(), "CachingOptions", "CachingOptions", 1), `&`, ``, 1) + `,`,
		`Headers:` + repeatedStringForHeaders + `,`,
		`}`,
	}, "")
	return s
}
func (this *PrometheusResponseHeader) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PrometheusResponseHeader{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Values:` + fmt.Sprintf("%v", this.Values) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PrometheusResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForHeaders := "[]*PrometheusResponseHeader{"
	for _, f := range this.Headers {
		repeatedStringForHeaders += strings.Replace(f.String(), "PrometheusResponseHeader", "PrometheusResponseHeader", 1) + ","
	}
	repeatedStringForHeaders += "}"
	s := strings.Join([]string{`&PrometheusResponse{`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`Data:` + strings.Replace(strings.Replace(this.Data.String(), "PrometheusData", "PrometheusData", 1), `&`, ``, 1) + `,`,
		`ErrorType:` + fmt.Sprintf("%v", this.ErrorType) + `,`,
		`Error:` + fmt.Sprintf("%v", this.Error) + `,`,
		`Headers:` + repeatedStringForHeaders + `,`,
		`}`,
	}, "")
	return s
}
func (this *PrometheusData) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForResult := "[]SampleStream{"
	for _, f := range this.Result {
		repeatedStringForResult += strings.Replace(strings.Replace(f.String(), "SampleStream", "SampleStream", 1), `&`, ``, 1) + ","
	}
	repeatedStringForResult += "}"
	s := strings.Join([]string{`&PrometheusData{`,
		`ResultType:` + fmt.Sprintf("%v", this.ResultType) + `,`,
		`Result:` + repeatedStringForResult + `,`,
		`}`,
	}, "")
	return s
}
func (this *SampleStream) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForSamples := "[]LegacySample{"
	for _, f := range this.Samples {
		repeatedStringForSamples += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForSamples += "}"
	s := strings.Join([]string{`&SampleStream{`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Samples:` + repeatedStringForSamples + `,`,
		`}`,
	}, "")
	return s
}
func (this *CachedResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForExtents := "[]Extent{"
	for _, f := range this.Extents {
		repeatedStringForExtents += strings.Replace(strings.Replace(f.String(), "Extent", "Extent", 1), `&`, ``, 1) + ","
	}
	repeatedStringForExtents += "}"
	s := strings.Join([]string{`&CachedResponse{`,
		`Key:` + fmt.Sprintf("%v", this.Key) + `,`,
		`Extents:` + repeatedStringForExtents + `,`,
		`}`,
	}, "")
	return s
}
func (this *Extent) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Extent{`,
		`Start:` + fmt.Sprintf("%v", this.Start) + `,`,
		`End:` + fmt.Sprintf("%v", this.End) + `,`,
		`TraceId:` + fmt.Sprintf("%v", this.TraceId) + `,`,
		`Response:` + strings.Replace(fmt.Sprintf("%v", this.Response), "Any", "types.Any", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CachingOptions) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CachingOptions{`,
		`Disabled:` + fmt.Sprintf("%v", this.Disabled) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringQueryrange(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *PrometheusRequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrometheusRequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrometheusRequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrometheusRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrometheusRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrometheusRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			m.End = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.End |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Step", wireType)
			}
			m.Step = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Step |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.Timeout, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CachingOptions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.CachingOptions.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Headers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Headers = append(m.Headers, &PrometheusRequestHeader{})
			if err := m.Headers[len(m.Headers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrometheusResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrometheusResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrometheusResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrometheusResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrometheusResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrometheusResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Headers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Headers = append(m.Headers, &PrometheusResponseHeader{})
			if err := m.Headers[len(m.Headers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrometheusData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrometheusData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrometheusData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResultType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResultType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Result = append(m.Result, SampleStream{})
			if err := m.Result[len(m.Result)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SampleStream) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SampleStream: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SampleStream: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, github_com_grafana_loki_pkg_logproto.LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Samples", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Samples = append(m.Samples, logproto.LegacySample{})
			if err := m.Samples[len(m.Samples)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CachedResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CachedResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CachedResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Extents", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Extents = append(m.Extents, Extent{})
			if err := m.Extents[len(m.Extents)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Extent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Extent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Extent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			m.End = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.End |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TraceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Response", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryrange
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryrange
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Response == nil {
				m.Response = &types.Any{}
			}
			if err := m.Response.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CachingOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CachingOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CachingOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Disabled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Disabled = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipQueryrange(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthQueryrange
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipQueryrange(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowQueryrange
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowQueryrange
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthQueryrange
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthQueryrange
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowQueryrange
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipQueryrange(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthQueryrange
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthQueryrange = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowQueryrange   = fmt.Errorf("proto: integer overflow")
)
