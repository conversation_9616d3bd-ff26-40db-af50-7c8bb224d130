/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.policy.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";
import "k8s.io/apimachinery/pkg/util/intstr/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1beta1";

// AllowedCSIDriver represents a single inline CSI Driver that is allowed to be used.
message AllowedCSIDriver {
  // Name is the registered name of the CSI driver
  optional string name = 1;
}

// AllowedFlexVolume represents a single Flexvolume that is allowed to be used.
message AllowedFlexVolume {
  // driver is the name of the Flexvolume driver.
  optional string driver = 1;
}

// AllowedHostPath defines the host volume conditions that will be enabled by a policy
// for pods to use. It requires the path prefix to be defined.
message AllowedHostPath {
  // pathPrefix is the path prefix that the host volume must match.
  // It does not support `*`.
  // Trailing slashes are trimmed when validating the path prefix with a host path.
  //
  // Examples:
  // `/foo` would allow `/foo`, `/foo/` and `/foo/bar`
  // `/foo` would not allow `/food` or `/etc/foo`
  optional string pathPrefix = 1;

  // when set to true, will allow host volumes matching the pathPrefix only if all volume mounts are readOnly.
  // +optional
  optional bool readOnly = 2;
}

// Eviction evicts a pod from its node subject to certain policies and safety constraints.
// This is a subresource of Pod.  A request to cause such an eviction is
// created by POSTing to .../pods/<pod name>/evictions.
message Eviction {
  // ObjectMeta describes the pod that is being evicted.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // DeleteOptions may be provided
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.DeleteOptions deleteOptions = 2;
}

// FSGroupStrategyOptions defines the strategy type and options used to create the strategy.
message FSGroupStrategyOptions {
  // rule is the strategy that will dictate what FSGroup is used in the SecurityContext.
  // +optional
  optional string rule = 1;

  // ranges are the allowed ranges of fs groups.  If you would like to force a single
  // fs group then supply a single range with the same start and end. Required for MustRunAs.
  // +optional
  repeated IDRange ranges = 2;
}

// HostPortRange defines a range of host ports that will be enabled by a policy
// for pods to use.  It requires both the start and end to be defined.
message HostPortRange {
  // min is the start of the range, inclusive.
  optional int32 min = 1;

  // max is the end of the range, inclusive.
  optional int32 max = 2;
}

// IDRange provides a min/max of an allowed range of IDs.
message IDRange {
  // min is the start of the range, inclusive.
  optional int64 min = 1;

  // max is the end of the range, inclusive.
  optional int64 max = 2;
}

// PodDisruptionBudget is an object to define the max disruption that can be caused to a collection of pods
message PodDisruptionBudget {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the PodDisruptionBudget.
  // +optional
  optional PodDisruptionBudgetSpec spec = 2;

  // Most recently observed status of the PodDisruptionBudget.
  // +optional
  optional PodDisruptionBudgetStatus status = 3;
}

// PodDisruptionBudgetList is a collection of PodDisruptionBudgets.
message PodDisruptionBudgetList {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items list individual PodDisruptionBudget objects
  repeated PodDisruptionBudget items = 2;
}

// PodDisruptionBudgetSpec is a description of a PodDisruptionBudget.
message PodDisruptionBudgetSpec {
  // An eviction is allowed if at least "minAvailable" pods selected by
  // "selector" will still be available after the eviction, i.e. even in the
  // absence of the evicted pod.  So for example you can prevent all voluntary
  // evictions by specifying "100%".
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString minAvailable = 1;

  // Label query over pods whose evictions are managed by the disruption
  // budget.
  // A null selector selects no pods.
  // An empty selector ({}) also selects no pods, which differs from standard behavior of selecting all pods.
  // In policy/v1, an empty selector will select all pods in the namespace.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // An eviction is allowed if at most "maxUnavailable" pods selected by
  // "selector" are unavailable after the eviction, i.e. even in absence of
  // the evicted pod. For example, one can prevent all voluntary evictions
  // by specifying 0. This is a mutually exclusive setting with "minAvailable".
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnavailable = 3;
}

// PodDisruptionBudgetStatus represents information about the status of a
// PodDisruptionBudget. Status may trail the actual state of a system.
message PodDisruptionBudgetStatus {
  // Most recent generation observed when updating this PDB status. DisruptionsAllowed and other
  // status information is valid only if observedGeneration equals to PDB's object generation.
  // +optional
  optional int64 observedGeneration = 1;

  // DisruptedPods contains information about pods whose eviction was
  // processed by the API server eviction subresource handler but has not
  // yet been observed by the PodDisruptionBudget controller.
  // A pod will be in this map from the time when the API server processed the
  // eviction request to the time when the pod is seen by PDB controller
  // as having been marked for deletion (or after a timeout). The key in the map is the name of the pod
  // and the value is the time when the API server processed the eviction request. If
  // the deletion didn't occur and a pod is still there it will be removed from
  // the list automatically by PodDisruptionBudget controller after some time.
  // If everything goes smooth this map should be empty for the most of the time.
  // Large number of entries in the map may indicate problems with pod deletions.
  // +optional
  map<string, k8s.io.apimachinery.pkg.apis.meta.v1.Time> disruptedPods = 2;

  // Number of pod disruptions that are currently allowed.
  optional int32 disruptionsAllowed = 3;

  // current number of healthy pods
  optional int32 currentHealthy = 4;

  // minimum desired number of healthy pods
  optional int32 desiredHealthy = 5;

  // total number of pods counted by this disruption budget
  optional int32 expectedPods = 6;

  // Conditions contain conditions for PDB. The disruption controller sets the
  // DisruptionAllowed condition. The following are known values for the reason field
  // (additional reasons could be added in the future):
  // - SyncFailed: The controller encountered an error and wasn't able to compute
  //               the number of allowed disruptions. Therefore no disruptions are
  //               allowed and the status of the condition will be False.
  // - InsufficientPods: The number of pods are either at or below the number
  //                     required by the PodDisruptionBudget. No disruptions are
  //                     allowed and the status of the condition will be False.
  // - SufficientPods: There are more pods than required by the PodDisruptionBudget.
  //                   The condition will be True, and the number of allowed
  //                   disruptions are provided by the disruptionsAllowed property.
  //
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=type
  repeated k8s.io.apimachinery.pkg.apis.meta.v1.Condition conditions = 7;
}

// PodSecurityPolicy governs the ability to make requests that affect the Security Context
// that will be applied to a pod and container.
// Deprecated in 1.21.
message PodSecurityPolicy {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec defines the policy enforced.
  // +optional
  optional PodSecurityPolicySpec spec = 2;
}

// PodSecurityPolicyList is a list of PodSecurityPolicy objects.
message PodSecurityPolicyList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is a list of schema objects.
  repeated PodSecurityPolicy items = 2;
}

// PodSecurityPolicySpec defines the policy enforced.
message PodSecurityPolicySpec {
  // privileged determines if a pod can request to be run as privileged.
  // +optional
  optional bool privileged = 1;

  // defaultAddCapabilities is the default set of capabilities that will be added to the container
  // unless the pod spec specifically drops the capability.  You may not list a capability in both
  // defaultAddCapabilities and requiredDropCapabilities. Capabilities added here are implicitly
  // allowed, and need not be included in the allowedCapabilities list.
  // +optional
  repeated string defaultAddCapabilities = 2;

  // requiredDropCapabilities are the capabilities that will be dropped from the container.  These
  // are required to be dropped and cannot be added.
  // +optional
  repeated string requiredDropCapabilities = 3;

  // allowedCapabilities is a list of capabilities that can be requested to add to the container.
  // Capabilities in this field may be added at the pod author's discretion.
  // You must not list a capability in both allowedCapabilities and requiredDropCapabilities.
  // +optional
  repeated string allowedCapabilities = 4;

  // volumes is an allowlist of volume plugins. Empty indicates that
  // no volumes may be used. To allow all volumes you may use '*'.
  // +optional
  repeated string volumes = 5;

  // hostNetwork determines if the policy allows the use of HostNetwork in the pod spec.
  // +optional
  optional bool hostNetwork = 6;

  // hostPorts determines which host port ranges are allowed to be exposed.
  // +optional
  repeated HostPortRange hostPorts = 7;

  // hostPID determines if the policy allows the use of HostPID in the pod spec.
  // +optional
  optional bool hostPID = 8;

  // hostIPC determines if the policy allows the use of HostIPC in the pod spec.
  // +optional
  optional bool hostIPC = 9;

  // seLinux is the strategy that will dictate the allowable labels that may be set.
  optional SELinuxStrategyOptions seLinux = 10;

  // runAsUser is the strategy that will dictate the allowable RunAsUser values that may be set.
  optional RunAsUserStrategyOptions runAsUser = 11;

  // RunAsGroup is the strategy that will dictate the allowable RunAsGroup values that may be set.
  // If this field is omitted, the pod's RunAsGroup can take any value. This field requires the
  // RunAsGroup feature gate to be enabled.
  // +optional
  optional RunAsGroupStrategyOptions runAsGroup = 22;

  // supplementalGroups is the strategy that will dictate what supplemental groups are used by the SecurityContext.
  optional SupplementalGroupsStrategyOptions supplementalGroups = 12;

  // fsGroup is the strategy that will dictate what fs group is used by the SecurityContext.
  optional FSGroupStrategyOptions fsGroup = 13;

  // readOnlyRootFilesystem when set to true will force containers to run with a read only root file
  // system.  If the container specifically requests to run with a non-read only root file system
  // the PSP should deny the pod.
  // If set to false the container may run with a read only root file system if it wishes but it
  // will not be forced to.
  // +optional
  optional bool readOnlyRootFilesystem = 14;

  // defaultAllowPrivilegeEscalation controls the default setting for whether a
  // process can gain more privileges than its parent process.
  // +optional
  optional bool defaultAllowPrivilegeEscalation = 15;

  // allowPrivilegeEscalation determines if a pod can request to allow
  // privilege escalation. If unspecified, defaults to true.
  // +optional
  optional bool allowPrivilegeEscalation = 16;

  // allowedHostPaths is an allowlist of host paths. Empty indicates
  // that all host paths may be used.
  // +optional
  repeated AllowedHostPath allowedHostPaths = 17;

  // allowedFlexVolumes is an allowlist of Flexvolumes.  Empty or nil indicates that all
  // Flexvolumes may be used.  This parameter is effective only when the usage of the Flexvolumes
  // is allowed in the "volumes" field.
  // +optional
  repeated AllowedFlexVolume allowedFlexVolumes = 18;

  // AllowedCSIDrivers is an allowlist of inline CSI drivers that must be explicitly set to be embedded within a pod spec.
  // An empty value indicates that any CSI driver can be used for inline ephemeral volumes.
  // This is a beta field, and is only honored if the API server enables the CSIInlineVolume feature gate.
  // +optional
  repeated AllowedCSIDriver allowedCSIDrivers = 23;

  // allowedUnsafeSysctls is a list of explicitly allowed unsafe sysctls, defaults to none.
  // Each entry is either a plain sysctl name or ends in "*" in which case it is considered
  // as a prefix of allowed sysctls. Single * means all unsafe sysctls are allowed.
  // Kubelet has to allowlist all allowed unsafe sysctls explicitly to avoid rejection.
  //
  // Examples:
  // e.g. "foo/*" allows "foo/bar", "foo/baz", etc.
  // e.g. "foo.*" allows "foo.bar", "foo.baz", etc.
  // +optional
  repeated string allowedUnsafeSysctls = 19;

  // forbiddenSysctls is a list of explicitly forbidden sysctls, defaults to none.
  // Each entry is either a plain sysctl name or ends in "*" in which case it is considered
  // as a prefix of forbidden sysctls. Single * means all sysctls are forbidden.
  //
  // Examples:
  // e.g. "foo/*" forbids "foo/bar", "foo/baz", etc.
  // e.g. "foo.*" forbids "foo.bar", "foo.baz", etc.
  // +optional
  repeated string forbiddenSysctls = 20;

  // AllowedProcMountTypes is an allowlist of allowed ProcMountTypes.
  // Empty or nil indicates that only the DefaultProcMountType may be used.
  // This requires the ProcMountType feature flag to be enabled.
  // +optional
  repeated string allowedProcMountTypes = 21;

  // runtimeClass is the strategy that will dictate the allowable RuntimeClasses for a pod.
  // If this field is omitted, the pod's runtimeClassName field is unrestricted.
  // Enforcement of this field depends on the RuntimeClass feature gate being enabled.
  // +optional
  optional RuntimeClassStrategyOptions runtimeClass = 24;
}

// RunAsGroupStrategyOptions defines the strategy type and any options used to create the strategy.
message RunAsGroupStrategyOptions {
  // rule is the strategy that will dictate the allowable RunAsGroup values that may be set.
  optional string rule = 1;

  // ranges are the allowed ranges of gids that may be used. If you would like to force a single gid
  // then supply a single range with the same start and end. Required for MustRunAs.
  // +optional
  repeated IDRange ranges = 2;
}

// RunAsUserStrategyOptions defines the strategy type and any options used to create the strategy.
message RunAsUserStrategyOptions {
  // rule is the strategy that will dictate the allowable RunAsUser values that may be set.
  optional string rule = 1;

  // ranges are the allowed ranges of uids that may be used. If you would like to force a single uid
  // then supply a single range with the same start and end. Required for MustRunAs.
  // +optional
  repeated IDRange ranges = 2;
}

// RuntimeClassStrategyOptions define the strategy that will dictate the allowable RuntimeClasses
// for a pod.
message RuntimeClassStrategyOptions {
  // allowedRuntimeClassNames is an allowlist of RuntimeClass names that may be specified on a pod.
  // A value of "*" means that any RuntimeClass name is allowed, and must be the only item in the
  // list. An empty list requires the RuntimeClassName field to be unset.
  repeated string allowedRuntimeClassNames = 1;

  // defaultRuntimeClassName is the default RuntimeClassName to set on the pod.
  // The default MUST be allowed by the allowedRuntimeClassNames list.
  // A value of nil does not mutate the Pod.
  // +optional
  optional string defaultRuntimeClassName = 2;
}

// SELinuxStrategyOptions defines the strategy type and any options used to create the strategy.
message SELinuxStrategyOptions {
  // rule is the strategy that will dictate the allowable labels that may be set.
  optional string rule = 1;

  // seLinuxOptions required to run as; required for MustRunAs
  // More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  // +optional
  optional k8s.io.api.core.v1.SELinuxOptions seLinuxOptions = 2;
}

// SupplementalGroupsStrategyOptions defines the strategy type and options used to create the strategy.
message SupplementalGroupsStrategyOptions {
  // rule is the strategy that will dictate what supplemental groups is used in the SecurityContext.
  // +optional
  optional string rule = 1;

  // ranges are the allowed ranges of supplemental groups.  If you would like to force a single
  // supplemental group then supply a single range with the same start and end. Required for MustRunAs.
  // +optional
  repeated IDRange ranges = 2;
}

