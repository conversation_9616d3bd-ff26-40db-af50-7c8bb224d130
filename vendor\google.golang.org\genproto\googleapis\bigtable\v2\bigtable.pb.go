// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/bigtable/v2/bigtable.proto

package bigtable

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	status "google.golang.org/genproto/googleapis/rpc/status"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status1 "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request message for Bigtable.ReadRows.
type ReadRowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the table from which to read.
	// Values are of the form
	// `projects/<project>/instances/<instance>/tables/<table>`.
	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,5,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
	// The row keys and/or ranges to read sequentially. If not specified, reads
	// from all rows.
	Rows *RowSet `protobuf:"bytes,2,opt,name=rows,proto3" json:"rows,omitempty"`
	// The filter to apply to the contents of the specified row(s). If unset,
	// reads the entirety of each row.
	Filter *RowFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// The read will stop after committing to N rows' worth of results. The
	// default (zero) is to return all results.
	RowsLimit int64 `protobuf:"varint,4,opt,name=rows_limit,json=rowsLimit,proto3" json:"rows_limit,omitempty"`
}

func (x *ReadRowsRequest) Reset() {
	*x = ReadRowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadRowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRowsRequest) ProtoMessage() {}

func (x *ReadRowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRowsRequest.ProtoReflect.Descriptor instead.
func (*ReadRowsRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{0}
}

func (x *ReadRowsRequest) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *ReadRowsRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

func (x *ReadRowsRequest) GetRows() *RowSet {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *ReadRowsRequest) GetFilter() *RowFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ReadRowsRequest) GetRowsLimit() int64 {
	if x != nil {
		return x.RowsLimit
	}
	return 0
}

// Response message for Bigtable.ReadRows.
type ReadRowsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A collection of a row's contents as part of the read request.
	Chunks []*ReadRowsResponse_CellChunk `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
	// Optionally the server might return the row key of the last row it
	// has scanned.  The client can use this to construct a more
	// efficient retry request if needed: any row keys or portions of
	// ranges less than this row key can be dropped from the request.
	// This is primarily useful for cases where the server has read a
	// lot of data that was filtered out since the last committed row
	// key, allowing the client to skip that work on a retry.
	LastScannedRowKey []byte `protobuf:"bytes,2,opt,name=last_scanned_row_key,json=lastScannedRowKey,proto3" json:"last_scanned_row_key,omitempty"`
}

func (x *ReadRowsResponse) Reset() {
	*x = ReadRowsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadRowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRowsResponse) ProtoMessage() {}

func (x *ReadRowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRowsResponse.ProtoReflect.Descriptor instead.
func (*ReadRowsResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{1}
}

func (x *ReadRowsResponse) GetChunks() []*ReadRowsResponse_CellChunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

func (x *ReadRowsResponse) GetLastScannedRowKey() []byte {
	if x != nil {
		return x.LastScannedRowKey
	}
	return nil
}

// Request message for Bigtable.SampleRowKeys.
type SampleRowKeysRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the table from which to sample row keys.
	// Values are of the form
	// `projects/<project>/instances/<instance>/tables/<table>`.
	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,2,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
}

func (x *SampleRowKeysRequest) Reset() {
	*x = SampleRowKeysRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SampleRowKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleRowKeysRequest) ProtoMessage() {}

func (x *SampleRowKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleRowKeysRequest.ProtoReflect.Descriptor instead.
func (*SampleRowKeysRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{2}
}

func (x *SampleRowKeysRequest) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *SampleRowKeysRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

// Response message for Bigtable.SampleRowKeys.
type SampleRowKeysResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Sorted streamed sequence of sample row keys in the table. The table might
	// have contents before the first row key in the list and after the last one,
	// but a key containing the empty string indicates "end of table" and will be
	// the last response given, if present.
	// Note that row keys in this list may not have ever been written to or read
	// from, and users should therefore not make any assumptions about the row key
	// structure that are specific to their use case.
	RowKey []byte `protobuf:"bytes,1,opt,name=row_key,json=rowKey,proto3" json:"row_key,omitempty"`
	// Approximate total storage space used by all rows in the table which precede
	// `row_key`. Buffering the contents of all rows between two subsequent
	// samples would require space roughly equal to the difference in their
	// `offset_bytes` fields.
	OffsetBytes int64 `protobuf:"varint,2,opt,name=offset_bytes,json=offsetBytes,proto3" json:"offset_bytes,omitempty"`
}

func (x *SampleRowKeysResponse) Reset() {
	*x = SampleRowKeysResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SampleRowKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleRowKeysResponse) ProtoMessage() {}

func (x *SampleRowKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleRowKeysResponse.ProtoReflect.Descriptor instead.
func (*SampleRowKeysResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{3}
}

func (x *SampleRowKeysResponse) GetRowKey() []byte {
	if x != nil {
		return x.RowKey
	}
	return nil
}

func (x *SampleRowKeysResponse) GetOffsetBytes() int64 {
	if x != nil {
		return x.OffsetBytes
	}
	return 0
}

// Request message for Bigtable.MutateRow.
type MutateRowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the table to which the mutation should be applied.
	// Values are of the form
	// `projects/<project>/instances/<instance>/tables/<table>`.
	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,4,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
	// Required. The key of the row to which the mutation should be applied.
	RowKey []byte `protobuf:"bytes,2,opt,name=row_key,json=rowKey,proto3" json:"row_key,omitempty"`
	// Required. Changes to be atomically applied to the specified row. Entries are applied
	// in order, meaning that earlier mutations can be masked by later ones.
	// Must contain at least one entry and at most 100000.
	Mutations []*Mutation `protobuf:"bytes,3,rep,name=mutations,proto3" json:"mutations,omitempty"`
}

func (x *MutateRowRequest) Reset() {
	*x = MutateRowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutateRowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutateRowRequest) ProtoMessage() {}

func (x *MutateRowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutateRowRequest.ProtoReflect.Descriptor instead.
func (*MutateRowRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{4}
}

func (x *MutateRowRequest) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *MutateRowRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

func (x *MutateRowRequest) GetRowKey() []byte {
	if x != nil {
		return x.RowKey
	}
	return nil
}

func (x *MutateRowRequest) GetMutations() []*Mutation {
	if x != nil {
		return x.Mutations
	}
	return nil
}

// Response message for Bigtable.MutateRow.
type MutateRowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MutateRowResponse) Reset() {
	*x = MutateRowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutateRowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutateRowResponse) ProtoMessage() {}

func (x *MutateRowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutateRowResponse.ProtoReflect.Descriptor instead.
func (*MutateRowResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{5}
}

// Request message for BigtableService.MutateRows.
type MutateRowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the table to which the mutations should be applied.
	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,3,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
	// Required. The row keys and corresponding mutations to be applied in bulk.
	// Each entry is applied as an atomic mutation, but the entries may be
	// applied in arbitrary order (even between entries for the same row).
	// At least one entry must be specified, and in total the entries can
	// contain at most 100000 mutations.
	Entries []*MutateRowsRequest_Entry `protobuf:"bytes,2,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *MutateRowsRequest) Reset() {
	*x = MutateRowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutateRowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutateRowsRequest) ProtoMessage() {}

func (x *MutateRowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutateRowsRequest.ProtoReflect.Descriptor instead.
func (*MutateRowsRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{6}
}

func (x *MutateRowsRequest) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *MutateRowsRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

func (x *MutateRowsRequest) GetEntries() []*MutateRowsRequest_Entry {
	if x != nil {
		return x.Entries
	}
	return nil
}

// Response message for BigtableService.MutateRows.
type MutateRowsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// One or more results for Entries from the batch request.
	Entries []*MutateRowsResponse_Entry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *MutateRowsResponse) Reset() {
	*x = MutateRowsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutateRowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutateRowsResponse) ProtoMessage() {}

func (x *MutateRowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutateRowsResponse.ProtoReflect.Descriptor instead.
func (*MutateRowsResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{7}
}

func (x *MutateRowsResponse) GetEntries() []*MutateRowsResponse_Entry {
	if x != nil {
		return x.Entries
	}
	return nil
}

// Request message for Bigtable.CheckAndMutateRow.
type CheckAndMutateRowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the table to which the conditional mutation should be
	// applied.
	// Values are of the form
	// `projects/<project>/instances/<instance>/tables/<table>`.
	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,7,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
	// Required. The key of the row to which the conditional mutation should be applied.
	RowKey []byte `protobuf:"bytes,2,opt,name=row_key,json=rowKey,proto3" json:"row_key,omitempty"`
	// The filter to be applied to the contents of the specified row. Depending
	// on whether or not any results are yielded, either `true_mutations` or
	// `false_mutations` will be executed. If unset, checks that the row contains
	// any values at all.
	PredicateFilter *RowFilter `protobuf:"bytes,6,opt,name=predicate_filter,json=predicateFilter,proto3" json:"predicate_filter,omitempty"`
	// Changes to be atomically applied to the specified row if `predicate_filter`
	// yields at least one cell when applied to `row_key`. Entries are applied in
	// order, meaning that earlier mutations can be masked by later ones.
	// Must contain at least one entry if `false_mutations` is empty, and at most
	// 100000.
	TrueMutations []*Mutation `protobuf:"bytes,4,rep,name=true_mutations,json=trueMutations,proto3" json:"true_mutations,omitempty"`
	// Changes to be atomically applied to the specified row if `predicate_filter`
	// does not yield any cells when applied to `row_key`. Entries are applied in
	// order, meaning that earlier mutations can be masked by later ones.
	// Must contain at least one entry if `true_mutations` is empty, and at most
	// 100000.
	FalseMutations []*Mutation `protobuf:"bytes,5,rep,name=false_mutations,json=falseMutations,proto3" json:"false_mutations,omitempty"`
}

func (x *CheckAndMutateRowRequest) Reset() {
	*x = CheckAndMutateRowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAndMutateRowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAndMutateRowRequest) ProtoMessage() {}

func (x *CheckAndMutateRowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAndMutateRowRequest.ProtoReflect.Descriptor instead.
func (*CheckAndMutateRowRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{8}
}

func (x *CheckAndMutateRowRequest) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *CheckAndMutateRowRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

func (x *CheckAndMutateRowRequest) GetRowKey() []byte {
	if x != nil {
		return x.RowKey
	}
	return nil
}

func (x *CheckAndMutateRowRequest) GetPredicateFilter() *RowFilter {
	if x != nil {
		return x.PredicateFilter
	}
	return nil
}

func (x *CheckAndMutateRowRequest) GetTrueMutations() []*Mutation {
	if x != nil {
		return x.TrueMutations
	}
	return nil
}

func (x *CheckAndMutateRowRequest) GetFalseMutations() []*Mutation {
	if x != nil {
		return x.FalseMutations
	}
	return nil
}

// Response message for Bigtable.CheckAndMutateRow.
type CheckAndMutateRowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether or not the request's `predicate_filter` yielded any results for
	// the specified row.
	PredicateMatched bool `protobuf:"varint,1,opt,name=predicate_matched,json=predicateMatched,proto3" json:"predicate_matched,omitempty"`
}

func (x *CheckAndMutateRowResponse) Reset() {
	*x = CheckAndMutateRowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAndMutateRowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAndMutateRowResponse) ProtoMessage() {}

func (x *CheckAndMutateRowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAndMutateRowResponse.ProtoReflect.Descriptor instead.
func (*CheckAndMutateRowResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{9}
}

func (x *CheckAndMutateRowResponse) GetPredicateMatched() bool {
	if x != nil {
		return x.PredicateMatched
	}
	return false
}

// Request message for client connection keep-alive and warming.
type PingAndWarmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the instance to check permissions for as well as
	// respond. Values are of the form `projects/<project>/instances/<instance>`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,2,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
}

func (x *PingAndWarmRequest) Reset() {
	*x = PingAndWarmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingAndWarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingAndWarmRequest) ProtoMessage() {}

func (x *PingAndWarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingAndWarmRequest.ProtoReflect.Descriptor instead.
func (*PingAndWarmRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{10}
}

func (x *PingAndWarmRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PingAndWarmRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

// Response message for Bigtable.PingAndWarm connection keepalive and warming.
type PingAndWarmResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PingAndWarmResponse) Reset() {
	*x = PingAndWarmResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingAndWarmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingAndWarmResponse) ProtoMessage() {}

func (x *PingAndWarmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingAndWarmResponse.ProtoReflect.Descriptor instead.
func (*PingAndWarmResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{11}
}

// Request message for Bigtable.ReadModifyWriteRow.
type ReadModifyWriteRowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The unique name of the table to which the read/modify/write rules should be
	// applied.
	// Values are of the form
	// `projects/<project>/instances/<instance>/tables/<table>`.
	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// This value specifies routing for replication. If not specified, the
	// "default" application profile will be used.
	AppProfileId string `protobuf:"bytes,4,opt,name=app_profile_id,json=appProfileId,proto3" json:"app_profile_id,omitempty"`
	// Required. The key of the row to which the read/modify/write rules should be applied.
	RowKey []byte `protobuf:"bytes,2,opt,name=row_key,json=rowKey,proto3" json:"row_key,omitempty"`
	// Required. Rules specifying how the specified row's contents are to be transformed
	// into writes. Entries are applied in order, meaning that earlier rules will
	// affect the results of later ones.
	Rules []*ReadModifyWriteRule `protobuf:"bytes,3,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *ReadModifyWriteRowRequest) Reset() {
	*x = ReadModifyWriteRowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModifyWriteRowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModifyWriteRowRequest) ProtoMessage() {}

func (x *ReadModifyWriteRowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModifyWriteRowRequest.ProtoReflect.Descriptor instead.
func (*ReadModifyWriteRowRequest) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{12}
}

func (x *ReadModifyWriteRowRequest) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *ReadModifyWriteRowRequest) GetAppProfileId() string {
	if x != nil {
		return x.AppProfileId
	}
	return ""
}

func (x *ReadModifyWriteRowRequest) GetRowKey() []byte {
	if x != nil {
		return x.RowKey
	}
	return nil
}

func (x *ReadModifyWriteRowRequest) GetRules() []*ReadModifyWriteRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

// Response message for Bigtable.ReadModifyWriteRow.
type ReadModifyWriteRowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A Row containing the new contents of all cells modified by the request.
	Row *Row `protobuf:"bytes,1,opt,name=row,proto3" json:"row,omitempty"`
}

func (x *ReadModifyWriteRowResponse) Reset() {
	*x = ReadModifyWriteRowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModifyWriteRowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModifyWriteRowResponse) ProtoMessage() {}

func (x *ReadModifyWriteRowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModifyWriteRowResponse.ProtoReflect.Descriptor instead.
func (*ReadModifyWriteRowResponse) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{13}
}

func (x *ReadModifyWriteRowResponse) GetRow() *Row {
	if x != nil {
		return x.Row
	}
	return nil
}

// Specifies a piece of a row's contents returned as part of the read
// response stream.
type ReadRowsResponse_CellChunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The row key for this chunk of data.  If the row key is empty,
	// this CellChunk is a continuation of the same row as the previous
	// CellChunk in the response stream, even if that CellChunk was in a
	// previous ReadRowsResponse message.
	RowKey []byte `protobuf:"bytes,1,opt,name=row_key,json=rowKey,proto3" json:"row_key,omitempty"`
	// The column family name for this chunk of data.  If this message
	// is not present this CellChunk is a continuation of the same column
	// family as the previous CellChunk.  The empty string can occur as a
	// column family name in a response so clients must check
	// explicitly for the presence of this message, not just for
	// `family_name.value` being non-empty.
	FamilyName *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// The column qualifier for this chunk of data.  If this message
	// is not present, this CellChunk is a continuation of the same column
	// as the previous CellChunk.  Column qualifiers may be empty so
	// clients must check for the presence of this message, not just
	// for `qualifier.value` being non-empty.
	Qualifier *wrapperspb.BytesValue `protobuf:"bytes,3,opt,name=qualifier,proto3" json:"qualifier,omitempty"`
	// The cell's stored timestamp, which also uniquely identifies it
	// within its column.  Values are always expressed in
	// microseconds, but individual tables may set a coarser
	// granularity to further restrict the allowed values. For
	// example, a table which specifies millisecond granularity will
	// only allow values of `timestamp_micros` which are multiples of
	// 1000.  Timestamps are only set in the first CellChunk per cell
	// (for cells split into multiple chunks).
	TimestampMicros int64 `protobuf:"varint,4,opt,name=timestamp_micros,json=timestampMicros,proto3" json:"timestamp_micros,omitempty"`
	// Labels applied to the cell by a
	// [RowFilter][google.bigtable.v2.RowFilter].  Labels are only set
	// on the first CellChunk per cell.
	Labels []string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
	// The value stored in the cell.  Cell values can be split across
	// multiple CellChunks.  In that case only the value field will be
	// set in CellChunks after the first: the timestamp and labels
	// will only be present in the first CellChunk, even if the first
	// CellChunk came in a previous ReadRowsResponse.
	Value []byte `protobuf:"bytes,6,opt,name=value,proto3" json:"value,omitempty"`
	// If this CellChunk is part of a chunked cell value and this is
	// not the final chunk of that cell, value_size will be set to the
	// total length of the cell value.  The client can use this size
	// to pre-allocate memory to hold the full cell value.
	ValueSize int32 `protobuf:"varint,7,opt,name=value_size,json=valueSize,proto3" json:"value_size,omitempty"`
	// Signals to the client concerning previous CellChunks received.
	//
	// Types that are assignable to RowStatus:
	//	*ReadRowsResponse_CellChunk_ResetRow
	//	*ReadRowsResponse_CellChunk_CommitRow
	RowStatus isReadRowsResponse_CellChunk_RowStatus `protobuf_oneof:"row_status"`
}

func (x *ReadRowsResponse_CellChunk) Reset() {
	*x = ReadRowsResponse_CellChunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadRowsResponse_CellChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRowsResponse_CellChunk) ProtoMessage() {}

func (x *ReadRowsResponse_CellChunk) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRowsResponse_CellChunk.ProtoReflect.Descriptor instead.
func (*ReadRowsResponse_CellChunk) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ReadRowsResponse_CellChunk) GetRowKey() []byte {
	if x != nil {
		return x.RowKey
	}
	return nil
}

func (x *ReadRowsResponse_CellChunk) GetFamilyName() *wrapperspb.StringValue {
	if x != nil {
		return x.FamilyName
	}
	return nil
}

func (x *ReadRowsResponse_CellChunk) GetQualifier() *wrapperspb.BytesValue {
	if x != nil {
		return x.Qualifier
	}
	return nil
}

func (x *ReadRowsResponse_CellChunk) GetTimestampMicros() int64 {
	if x != nil {
		return x.TimestampMicros
	}
	return 0
}

func (x *ReadRowsResponse_CellChunk) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ReadRowsResponse_CellChunk) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ReadRowsResponse_CellChunk) GetValueSize() int32 {
	if x != nil {
		return x.ValueSize
	}
	return 0
}

func (m *ReadRowsResponse_CellChunk) GetRowStatus() isReadRowsResponse_CellChunk_RowStatus {
	if m != nil {
		return m.RowStatus
	}
	return nil
}

func (x *ReadRowsResponse_CellChunk) GetResetRow() bool {
	if x, ok := x.GetRowStatus().(*ReadRowsResponse_CellChunk_ResetRow); ok {
		return x.ResetRow
	}
	return false
}

func (x *ReadRowsResponse_CellChunk) GetCommitRow() bool {
	if x, ok := x.GetRowStatus().(*ReadRowsResponse_CellChunk_CommitRow); ok {
		return x.CommitRow
	}
	return false
}

type isReadRowsResponse_CellChunk_RowStatus interface {
	isReadRowsResponse_CellChunk_RowStatus()
}

type ReadRowsResponse_CellChunk_ResetRow struct {
	// Indicates that the client should drop all previous chunks for
	// `row_key`, as it will be re-read from the beginning.
	ResetRow bool `protobuf:"varint,8,opt,name=reset_row,json=resetRow,proto3,oneof"`
}

type ReadRowsResponse_CellChunk_CommitRow struct {
	// Indicates that the client can safely process all previous chunks for
	// `row_key`, as its data has been fully read.
	CommitRow bool `protobuf:"varint,9,opt,name=commit_row,json=commitRow,proto3,oneof"`
}

func (*ReadRowsResponse_CellChunk_ResetRow) isReadRowsResponse_CellChunk_RowStatus() {}

func (*ReadRowsResponse_CellChunk_CommitRow) isReadRowsResponse_CellChunk_RowStatus() {}

// A mutation for a given row.
type MutateRowsRequest_Entry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The key of the row to which the `mutations` should be applied.
	RowKey []byte `protobuf:"bytes,1,opt,name=row_key,json=rowKey,proto3" json:"row_key,omitempty"`
	// Required. Changes to be atomically applied to the specified row. Mutations are
	// applied in order, meaning that earlier mutations can be masked by
	// later ones.
	// You must specify at least one mutation.
	Mutations []*Mutation `protobuf:"bytes,2,rep,name=mutations,proto3" json:"mutations,omitempty"`
}

func (x *MutateRowsRequest_Entry) Reset() {
	*x = MutateRowsRequest_Entry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutateRowsRequest_Entry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutateRowsRequest_Entry) ProtoMessage() {}

func (x *MutateRowsRequest_Entry) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutateRowsRequest_Entry.ProtoReflect.Descriptor instead.
func (*MutateRowsRequest_Entry) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{6, 0}
}

func (x *MutateRowsRequest_Entry) GetRowKey() []byte {
	if x != nil {
		return x.RowKey
	}
	return nil
}

func (x *MutateRowsRequest_Entry) GetMutations() []*Mutation {
	if x != nil {
		return x.Mutations
	}
	return nil
}

// The result of applying a passed mutation in the original request.
type MutateRowsResponse_Entry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The index into the original request's `entries` list of the Entry
	// for which a result is being reported.
	Index int64 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// The result of the request Entry identified by `index`.
	// Depending on how requests are batched during execution, it is possible
	// for one Entry to fail due to an error with another Entry. In the event
	// that this occurs, the same error will be reported for both entries.
	Status *status.Status `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MutateRowsResponse_Entry) Reset() {
	*x = MutateRowsResponse_Entry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutateRowsResponse_Entry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutateRowsResponse_Entry) ProtoMessage() {}

func (x *MutateRowsResponse_Entry) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_bigtable_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutateRowsResponse_Entry.ProtoReflect.Descriptor instead.
func (*MutateRowsResponse_Entry) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_bigtable_proto_rawDescGZIP(), []int{7, 0}
}

func (x *MutateRowsResponse_Entry) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *MutateRowsResponse_Entry) GetStatus() *status.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_google_bigtable_v2_bigtable_proto protoreflect.FileDescriptor

var file_google_bigtable_v2_bigtable_proto_rawDesc = []byte{
	0x0a, 0x21, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2f, 0x76, 0x32, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x12, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x02, 0x0a,
	0x0f, 0x52, 0x65, 0x61, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x49, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x24, 0x0a, 0x22, 0x62, 0x69,
	0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x52, 0x04, 0x72, 0x6f, 0x77,
	0x73, 0x12, 0x35, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x73,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x6f,
	0x77, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xf2, 0x03, 0x0a, 0x10, 0x52, 0x65, 0x61, 0x64,
	0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x06,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x65, 0x6c, 0x6c, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x06, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x64, 0x52,
	0x6f, 0x77, 0x4b, 0x65, 0x79, 0x1a, 0xe4, 0x02, 0x0a, 0x09, 0x43, 0x65, 0x6c, 0x6c, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x0b,
	0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x71,
	0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x69, 0x63, 0x72, 0x6f,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x09, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x74, 0x52, 0x6f, 0x77, 0x12, 0x1f, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x5f, 0x72, 0x6f, 0x77, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x6f, 0x77, 0x42, 0x0c,
	0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x87, 0x01, 0x0a,
	0x14, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41,
	0x24, 0x0a, 0x22, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x15, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x52, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x06, 0x72, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x22, 0xe2, 0x01, 0x0a, 0x10,
	0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x49, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x24, 0x0a, 0x22, 0x62, 0x69,
	0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x12,
	0x3f, 0x0a, 0x09, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x13, 0x0a, 0x11, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb3, 0x02, 0x0a, 0x11, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x24, 0x0a, 0x22, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x09, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x07,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x1a, 0x61, 0x0a, 0x05, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x12, 0x3f, 0x0a, 0x09, 0x6d, 0x75,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x09, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x12,
	0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x1a, 0x49, 0x0a, 0x05, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xff, 0x02, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6e, 0x64, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x24, 0x0a, 0x22,
	0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x4b, 0x65,
	0x79, 0x12, 0x48, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0e, 0x74,
	0x72, 0x75, 0x65, 0x5f, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x74, 0x72, 0x75, 0x65, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x45, 0x0a, 0x0f, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d,
	0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x4d, 0x75,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x48, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x41, 0x6e, 0x64, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x22, 0x7d, 0x0a, 0x12, 0x50, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x64, 0x57, 0x61, 0x72, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x27, 0x0a, 0x25, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64,
	0x22, 0x15, 0x0a, 0x13, 0x50, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x64, 0x57, 0x61, 0x72, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xee, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x61, 0x64,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41,
	0x24, 0x0a, 0x22, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x72, 0x6f,
	0x77, 0x4b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x1a, 0x52, 0x65, 0x61, 0x64,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x52, 0x03, 0x72, 0x6f,
	0x77, 0x32, 0xb0, 0x14, 0x0a, 0x08, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x9b,
	0x02, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x12, 0x23, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x61, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xc1, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3e, 0x22,
	0x39, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a,
	0x7d, 0x3a, 0x72, 0x65, 0x61, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4,
	0x93, 0x02, 0x4e, 0x12, 0x3a, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2c, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x12,
	0x10, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0xda, 0x41, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0xda, 0x41,
	0x19, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x61, 0x70, 0x70, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x30, 0x01, 0x12, 0xac, 0x02, 0x0a,
	0x0d, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x28,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x4b, 0x65, 0x79,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0xc3, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x12, 0x3e, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x73, 0x8a, 0xd3, 0xe4, 0x93,
	0x02, 0x4e, 0x12, 0x3a, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2c, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x10,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0xda, 0x41, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0xda, 0x41, 0x19,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x30, 0x01, 0x12, 0xc1, 0x02, 0x0a, 0x09,
	0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d,
	0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe6, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x22,
	0x3a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a,
	0x7d, 0x3a, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x3a, 0x01, 0x2a, 0x8a, 0xd3,
	0xe4, 0x93, 0x02, 0x4e, 0x12, 0x3a, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x2c, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d,
	0x12, 0x10, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0xda, 0x41, 0x1c, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c,
	0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x2c, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0xda, 0x41, 0x2b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72,
	0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x2c, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x12,
	0xb3, 0x02, 0x0a, 0x0a, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x73, 0x12, 0x25,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd3, 0x01,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x22, 0x3b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x77, 0x73, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x4e, 0x12, 0x3a, 0x0a, 0x0a,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x7b, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x10, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0xda, 0x41, 0x12, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0xda,
	0x41, 0x21, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x30, 0x01, 0x12, 0xad, 0x03, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6e, 0x64, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x2c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6e, 0x64, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x41, 0x6e, 0x64, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xba, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x47, 0x22, 0x42, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x2f, 0x2a, 0x7d, 0x3a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6e, 0x64, 0x4d, 0x75, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x77, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x4e, 0x12, 0x3a,
	0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x7b, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a,
	0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x10, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0xda, 0x41, 0x42, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65,
	0x79, 0x2c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x2c, 0x74, 0x72, 0x75, 0x65, 0x5f, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2c, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0xda, 0x41, 0x51, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72,
	0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x2c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2c, 0x74, 0x72, 0x75, 0x65, 0x5f, 0x6d, 0x75, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2c, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x6d, 0x75, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xee, 0x01, 0x0a, 0x0b, 0x50, 0x69, 0x6e, 0x67, 0x41, 0x6e,
	0x64, 0x57, 0x61, 0x72, 0x6d, 0x12, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x41,
	0x6e, 0x64, 0x57, 0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x64, 0x57, 0x61, 0x72, 0x6d, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8d, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x22,
	0x26, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f,
	0x2a, 0x7d, 0x3a, 0x70, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x39,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x10, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xda, 0x41, 0x13, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xdd, 0x02, 0x0a, 0x12, 0x52, 0x65, 0x61, 0x64, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x2d, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe7, 0x01, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x48, 0x22, 0x43, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4,
	0x93, 0x02, 0x4e, 0x12, 0x3a, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2c, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x12,
	0x10, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0xda, 0x41, 0x18, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72,
	0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x2c, 0x72, 0x75, 0x6c, 0x65, 0x73, 0xda, 0x41, 0x27, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65,
	0x79, 0x2c, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x1a, 0xdb, 0x02, 0xca, 0x41, 0x17, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0xd2, 0x41, 0xbd, 0x02, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77,
	0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x2c, 0x68, 0x74, 0x74, 0x70,
	0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2d, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2c,
	0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2d, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x2c, 0x68, 0x74, 0x74,
	0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2c, 0x68, 0x74, 0x74, 0x70,
	0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x72, 0x65, 0x61, 0x64, 0x2d,
	0x6f, 0x6e, 0x6c, 0x79, 0x42, 0xeb, 0x02, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x42,
	0x0d, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e,
	0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2f, 0x76, 0x32, 0x3b, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0xaa, 0x02, 0x18, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x42, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x56, 0x32, 0xca, 0x02, 0x18, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5c,
	0x56, 0x32, 0xea, 0x02, 0x1b, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x3a, 0x3a, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x3a, 0x3a, 0x56, 0x32,
	0xea, 0x41, 0x5c, 0x0a, 0x22, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x36, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x7d,
	0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x7d, 0xea,
	0x41, 0x50, 0x0a, 0x25, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x27, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x7d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_bigtable_v2_bigtable_proto_rawDescOnce sync.Once
	file_google_bigtable_v2_bigtable_proto_rawDescData = file_google_bigtable_v2_bigtable_proto_rawDesc
)

func file_google_bigtable_v2_bigtable_proto_rawDescGZIP() []byte {
	file_google_bigtable_v2_bigtable_proto_rawDescOnce.Do(func() {
		file_google_bigtable_v2_bigtable_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_bigtable_v2_bigtable_proto_rawDescData)
	})
	return file_google_bigtable_v2_bigtable_proto_rawDescData
}

var file_google_bigtable_v2_bigtable_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_google_bigtable_v2_bigtable_proto_goTypes = []interface{}{
	(*ReadRowsRequest)(nil),            // 0: google.bigtable.v2.ReadRowsRequest
	(*ReadRowsResponse)(nil),           // 1: google.bigtable.v2.ReadRowsResponse
	(*SampleRowKeysRequest)(nil),       // 2: google.bigtable.v2.SampleRowKeysRequest
	(*SampleRowKeysResponse)(nil),      // 3: google.bigtable.v2.SampleRowKeysResponse
	(*MutateRowRequest)(nil),           // 4: google.bigtable.v2.MutateRowRequest
	(*MutateRowResponse)(nil),          // 5: google.bigtable.v2.MutateRowResponse
	(*MutateRowsRequest)(nil),          // 6: google.bigtable.v2.MutateRowsRequest
	(*MutateRowsResponse)(nil),         // 7: google.bigtable.v2.MutateRowsResponse
	(*CheckAndMutateRowRequest)(nil),   // 8: google.bigtable.v2.CheckAndMutateRowRequest
	(*CheckAndMutateRowResponse)(nil),  // 9: google.bigtable.v2.CheckAndMutateRowResponse
	(*PingAndWarmRequest)(nil),         // 10: google.bigtable.v2.PingAndWarmRequest
	(*PingAndWarmResponse)(nil),        // 11: google.bigtable.v2.PingAndWarmResponse
	(*ReadModifyWriteRowRequest)(nil),  // 12: google.bigtable.v2.ReadModifyWriteRowRequest
	(*ReadModifyWriteRowResponse)(nil), // 13: google.bigtable.v2.ReadModifyWriteRowResponse
	(*ReadRowsResponse_CellChunk)(nil), // 14: google.bigtable.v2.ReadRowsResponse.CellChunk
	(*MutateRowsRequest_Entry)(nil),    // 15: google.bigtable.v2.MutateRowsRequest.Entry
	(*MutateRowsResponse_Entry)(nil),   // 16: google.bigtable.v2.MutateRowsResponse.Entry
	(*RowSet)(nil),                     // 17: google.bigtable.v2.RowSet
	(*RowFilter)(nil),                  // 18: google.bigtable.v2.RowFilter
	(*Mutation)(nil),                   // 19: google.bigtable.v2.Mutation
	(*ReadModifyWriteRule)(nil),        // 20: google.bigtable.v2.ReadModifyWriteRule
	(*Row)(nil),                        // 21: google.bigtable.v2.Row
	(*wrapperspb.StringValue)(nil),     // 22: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),      // 23: google.protobuf.BytesValue
	(*status.Status)(nil),              // 24: google.rpc.Status
}
var file_google_bigtable_v2_bigtable_proto_depIdxs = []int32{
	17, // 0: google.bigtable.v2.ReadRowsRequest.rows:type_name -> google.bigtable.v2.RowSet
	18, // 1: google.bigtable.v2.ReadRowsRequest.filter:type_name -> google.bigtable.v2.RowFilter
	14, // 2: google.bigtable.v2.ReadRowsResponse.chunks:type_name -> google.bigtable.v2.ReadRowsResponse.CellChunk
	19, // 3: google.bigtable.v2.MutateRowRequest.mutations:type_name -> google.bigtable.v2.Mutation
	15, // 4: google.bigtable.v2.MutateRowsRequest.entries:type_name -> google.bigtable.v2.MutateRowsRequest.Entry
	16, // 5: google.bigtable.v2.MutateRowsResponse.entries:type_name -> google.bigtable.v2.MutateRowsResponse.Entry
	18, // 6: google.bigtable.v2.CheckAndMutateRowRequest.predicate_filter:type_name -> google.bigtable.v2.RowFilter
	19, // 7: google.bigtable.v2.CheckAndMutateRowRequest.true_mutations:type_name -> google.bigtable.v2.Mutation
	19, // 8: google.bigtable.v2.CheckAndMutateRowRequest.false_mutations:type_name -> google.bigtable.v2.Mutation
	20, // 9: google.bigtable.v2.ReadModifyWriteRowRequest.rules:type_name -> google.bigtable.v2.ReadModifyWriteRule
	21, // 10: google.bigtable.v2.ReadModifyWriteRowResponse.row:type_name -> google.bigtable.v2.Row
	22, // 11: google.bigtable.v2.ReadRowsResponse.CellChunk.family_name:type_name -> google.protobuf.StringValue
	23, // 12: google.bigtable.v2.ReadRowsResponse.CellChunk.qualifier:type_name -> google.protobuf.BytesValue
	19, // 13: google.bigtable.v2.MutateRowsRequest.Entry.mutations:type_name -> google.bigtable.v2.Mutation
	24, // 14: google.bigtable.v2.MutateRowsResponse.Entry.status:type_name -> google.rpc.Status
	0,  // 15: google.bigtable.v2.Bigtable.ReadRows:input_type -> google.bigtable.v2.ReadRowsRequest
	2,  // 16: google.bigtable.v2.Bigtable.SampleRowKeys:input_type -> google.bigtable.v2.SampleRowKeysRequest
	4,  // 17: google.bigtable.v2.Bigtable.MutateRow:input_type -> google.bigtable.v2.MutateRowRequest
	6,  // 18: google.bigtable.v2.Bigtable.MutateRows:input_type -> google.bigtable.v2.MutateRowsRequest
	8,  // 19: google.bigtable.v2.Bigtable.CheckAndMutateRow:input_type -> google.bigtable.v2.CheckAndMutateRowRequest
	10, // 20: google.bigtable.v2.Bigtable.PingAndWarm:input_type -> google.bigtable.v2.PingAndWarmRequest
	12, // 21: google.bigtable.v2.Bigtable.ReadModifyWriteRow:input_type -> google.bigtable.v2.ReadModifyWriteRowRequest
	1,  // 22: google.bigtable.v2.Bigtable.ReadRows:output_type -> google.bigtable.v2.ReadRowsResponse
	3,  // 23: google.bigtable.v2.Bigtable.SampleRowKeys:output_type -> google.bigtable.v2.SampleRowKeysResponse
	5,  // 24: google.bigtable.v2.Bigtable.MutateRow:output_type -> google.bigtable.v2.MutateRowResponse
	7,  // 25: google.bigtable.v2.Bigtable.MutateRows:output_type -> google.bigtable.v2.MutateRowsResponse
	9,  // 26: google.bigtable.v2.Bigtable.CheckAndMutateRow:output_type -> google.bigtable.v2.CheckAndMutateRowResponse
	11, // 27: google.bigtable.v2.Bigtable.PingAndWarm:output_type -> google.bigtable.v2.PingAndWarmResponse
	13, // 28: google.bigtable.v2.Bigtable.ReadModifyWriteRow:output_type -> google.bigtable.v2.ReadModifyWriteRowResponse
	22, // [22:29] is the sub-list for method output_type
	15, // [15:22] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_google_bigtable_v2_bigtable_proto_init() }
func file_google_bigtable_v2_bigtable_proto_init() {
	if File_google_bigtable_v2_bigtable_proto != nil {
		return
	}
	file_google_bigtable_v2_data_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_bigtable_v2_bigtable_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadRowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadRowsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SampleRowKeysRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SampleRowKeysResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutateRowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutateRowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutateRowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutateRowsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAndMutateRowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAndMutateRowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingAndWarmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingAndWarmResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModifyWriteRowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModifyWriteRowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadRowsResponse_CellChunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutateRowsRequest_Entry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_bigtable_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutateRowsResponse_Entry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_bigtable_v2_bigtable_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*ReadRowsResponse_CellChunk_ResetRow)(nil),
		(*ReadRowsResponse_CellChunk_CommitRow)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_bigtable_v2_bigtable_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_bigtable_v2_bigtable_proto_goTypes,
		DependencyIndexes: file_google_bigtable_v2_bigtable_proto_depIdxs,
		MessageInfos:      file_google_bigtable_v2_bigtable_proto_msgTypes,
	}.Build()
	File_google_bigtable_v2_bigtable_proto = out.File
	file_google_bigtable_v2_bigtable_proto_rawDesc = nil
	file_google_bigtable_v2_bigtable_proto_goTypes = nil
	file_google_bigtable_v2_bigtable_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// BigtableClient is the client API for Bigtable service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BigtableClient interface {
	// Streams back the contents of all requested rows in key order, optionally
	// applying the same Reader filter to each. Depending on their size,
	// rows and cells may be broken up across multiple responses, but
	// atomicity of each row will still be preserved. See the
	// ReadRowsResponse documentation for details.
	ReadRows(ctx context.Context, in *ReadRowsRequest, opts ...grpc.CallOption) (Bigtable_ReadRowsClient, error)
	// Returns a sample of row keys in the table. The returned row keys will
	// delimit contiguous sections of the table of approximately equal size,
	// which can be used to break up the data for distributed tasks like
	// mapreduces.
	SampleRowKeys(ctx context.Context, in *SampleRowKeysRequest, opts ...grpc.CallOption) (Bigtable_SampleRowKeysClient, error)
	// Mutates a row atomically. Cells already present in the row are left
	// unchanged unless explicitly changed by `mutation`.
	MutateRow(ctx context.Context, in *MutateRowRequest, opts ...grpc.CallOption) (*MutateRowResponse, error)
	// Mutates multiple rows in a batch. Each individual row is mutated
	// atomically as in MutateRow, but the entire batch is not executed
	// atomically.
	MutateRows(ctx context.Context, in *MutateRowsRequest, opts ...grpc.CallOption) (Bigtable_MutateRowsClient, error)
	// Mutates a row atomically based on the output of a predicate Reader filter.
	CheckAndMutateRow(ctx context.Context, in *CheckAndMutateRowRequest, opts ...grpc.CallOption) (*CheckAndMutateRowResponse, error)
	// Warm up associated instance metadata for this connection.
	// This call is not required but may be useful for connection keep-alive.
	PingAndWarm(ctx context.Context, in *PingAndWarmRequest, opts ...grpc.CallOption) (*PingAndWarmResponse, error)
	// Modifies a row atomically on the server. The method reads the latest
	// existing timestamp and value from the specified columns and writes a new
	// entry based on pre-defined read/modify/write rules. The new value for the
	// timestamp is the greater of the existing timestamp or the current server
	// time. The method returns the new contents of all modified cells.
	ReadModifyWriteRow(ctx context.Context, in *ReadModifyWriteRowRequest, opts ...grpc.CallOption) (*ReadModifyWriteRowResponse, error)
}

type bigtableClient struct {
	cc grpc.ClientConnInterface
}

func NewBigtableClient(cc grpc.ClientConnInterface) BigtableClient {
	return &bigtableClient{cc}
}

func (c *bigtableClient) ReadRows(ctx context.Context, in *ReadRowsRequest, opts ...grpc.CallOption) (Bigtable_ReadRowsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Bigtable_serviceDesc.Streams[0], "/google.bigtable.v2.Bigtable/ReadRows", opts...)
	if err != nil {
		return nil, err
	}
	x := &bigtableReadRowsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Bigtable_ReadRowsClient interface {
	Recv() (*ReadRowsResponse, error)
	grpc.ClientStream
}

type bigtableReadRowsClient struct {
	grpc.ClientStream
}

func (x *bigtableReadRowsClient) Recv() (*ReadRowsResponse, error) {
	m := new(ReadRowsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *bigtableClient) SampleRowKeys(ctx context.Context, in *SampleRowKeysRequest, opts ...grpc.CallOption) (Bigtable_SampleRowKeysClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Bigtable_serviceDesc.Streams[1], "/google.bigtable.v2.Bigtable/SampleRowKeys", opts...)
	if err != nil {
		return nil, err
	}
	x := &bigtableSampleRowKeysClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Bigtable_SampleRowKeysClient interface {
	Recv() (*SampleRowKeysResponse, error)
	grpc.ClientStream
}

type bigtableSampleRowKeysClient struct {
	grpc.ClientStream
}

func (x *bigtableSampleRowKeysClient) Recv() (*SampleRowKeysResponse, error) {
	m := new(SampleRowKeysResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *bigtableClient) MutateRow(ctx context.Context, in *MutateRowRequest, opts ...grpc.CallOption) (*MutateRowResponse, error) {
	out := new(MutateRowResponse)
	err := c.cc.Invoke(ctx, "/google.bigtable.v2.Bigtable/MutateRow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bigtableClient) MutateRows(ctx context.Context, in *MutateRowsRequest, opts ...grpc.CallOption) (Bigtable_MutateRowsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Bigtable_serviceDesc.Streams[2], "/google.bigtable.v2.Bigtable/MutateRows", opts...)
	if err != nil {
		return nil, err
	}
	x := &bigtableMutateRowsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Bigtable_MutateRowsClient interface {
	Recv() (*MutateRowsResponse, error)
	grpc.ClientStream
}

type bigtableMutateRowsClient struct {
	grpc.ClientStream
}

func (x *bigtableMutateRowsClient) Recv() (*MutateRowsResponse, error) {
	m := new(MutateRowsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *bigtableClient) CheckAndMutateRow(ctx context.Context, in *CheckAndMutateRowRequest, opts ...grpc.CallOption) (*CheckAndMutateRowResponse, error) {
	out := new(CheckAndMutateRowResponse)
	err := c.cc.Invoke(ctx, "/google.bigtable.v2.Bigtable/CheckAndMutateRow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bigtableClient) PingAndWarm(ctx context.Context, in *PingAndWarmRequest, opts ...grpc.CallOption) (*PingAndWarmResponse, error) {
	out := new(PingAndWarmResponse)
	err := c.cc.Invoke(ctx, "/google.bigtable.v2.Bigtable/PingAndWarm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bigtableClient) ReadModifyWriteRow(ctx context.Context, in *ReadModifyWriteRowRequest, opts ...grpc.CallOption) (*ReadModifyWriteRowResponse, error) {
	out := new(ReadModifyWriteRowResponse)
	err := c.cc.Invoke(ctx, "/google.bigtable.v2.Bigtable/ReadModifyWriteRow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BigtableServer is the server API for Bigtable service.
type BigtableServer interface {
	// Streams back the contents of all requested rows in key order, optionally
	// applying the same Reader filter to each. Depending on their size,
	// rows and cells may be broken up across multiple responses, but
	// atomicity of each row will still be preserved. See the
	// ReadRowsResponse documentation for details.
	ReadRows(*ReadRowsRequest, Bigtable_ReadRowsServer) error
	// Returns a sample of row keys in the table. The returned row keys will
	// delimit contiguous sections of the table of approximately equal size,
	// which can be used to break up the data for distributed tasks like
	// mapreduces.
	SampleRowKeys(*SampleRowKeysRequest, Bigtable_SampleRowKeysServer) error
	// Mutates a row atomically. Cells already present in the row are left
	// unchanged unless explicitly changed by `mutation`.
	MutateRow(context.Context, *MutateRowRequest) (*MutateRowResponse, error)
	// Mutates multiple rows in a batch. Each individual row is mutated
	// atomically as in MutateRow, but the entire batch is not executed
	// atomically.
	MutateRows(*MutateRowsRequest, Bigtable_MutateRowsServer) error
	// Mutates a row atomically based on the output of a predicate Reader filter.
	CheckAndMutateRow(context.Context, *CheckAndMutateRowRequest) (*CheckAndMutateRowResponse, error)
	// Warm up associated instance metadata for this connection.
	// This call is not required but may be useful for connection keep-alive.
	PingAndWarm(context.Context, *PingAndWarmRequest) (*PingAndWarmResponse, error)
	// Modifies a row atomically on the server. The method reads the latest
	// existing timestamp and value from the specified columns and writes a new
	// entry based on pre-defined read/modify/write rules. The new value for the
	// timestamp is the greater of the existing timestamp or the current server
	// time. The method returns the new contents of all modified cells.
	ReadModifyWriteRow(context.Context, *ReadModifyWriteRowRequest) (*ReadModifyWriteRowResponse, error)
}

// UnimplementedBigtableServer can be embedded to have forward compatible implementations.
type UnimplementedBigtableServer struct {
}

func (*UnimplementedBigtableServer) ReadRows(*ReadRowsRequest, Bigtable_ReadRowsServer) error {
	return status1.Errorf(codes.Unimplemented, "method ReadRows not implemented")
}
func (*UnimplementedBigtableServer) SampleRowKeys(*SampleRowKeysRequest, Bigtable_SampleRowKeysServer) error {
	return status1.Errorf(codes.Unimplemented, "method SampleRowKeys not implemented")
}
func (*UnimplementedBigtableServer) MutateRow(context.Context, *MutateRowRequest) (*MutateRowResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method MutateRow not implemented")
}
func (*UnimplementedBigtableServer) MutateRows(*MutateRowsRequest, Bigtable_MutateRowsServer) error {
	return status1.Errorf(codes.Unimplemented, "method MutateRows not implemented")
}
func (*UnimplementedBigtableServer) CheckAndMutateRow(context.Context, *CheckAndMutateRowRequest) (*CheckAndMutateRowResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method CheckAndMutateRow not implemented")
}
func (*UnimplementedBigtableServer) PingAndWarm(context.Context, *PingAndWarmRequest) (*PingAndWarmResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method PingAndWarm not implemented")
}
func (*UnimplementedBigtableServer) ReadModifyWriteRow(context.Context, *ReadModifyWriteRowRequest) (*ReadModifyWriteRowResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method ReadModifyWriteRow not implemented")
}

func RegisterBigtableServer(s *grpc.Server, srv BigtableServer) {
	s.RegisterService(&_Bigtable_serviceDesc, srv)
}

func _Bigtable_ReadRows_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ReadRowsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(BigtableServer).ReadRows(m, &bigtableReadRowsServer{stream})
}

type Bigtable_ReadRowsServer interface {
	Send(*ReadRowsResponse) error
	grpc.ServerStream
}

type bigtableReadRowsServer struct {
	grpc.ServerStream
}

func (x *bigtableReadRowsServer) Send(m *ReadRowsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Bigtable_SampleRowKeys_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SampleRowKeysRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(BigtableServer).SampleRowKeys(m, &bigtableSampleRowKeysServer{stream})
}

type Bigtable_SampleRowKeysServer interface {
	Send(*SampleRowKeysResponse) error
	grpc.ServerStream
}

type bigtableSampleRowKeysServer struct {
	grpc.ServerStream
}

func (x *bigtableSampleRowKeysServer) Send(m *SampleRowKeysResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Bigtable_MutateRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MutateRowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BigtableServer).MutateRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.bigtable.v2.Bigtable/MutateRow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BigtableServer).MutateRow(ctx, req.(*MutateRowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bigtable_MutateRows_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(MutateRowsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(BigtableServer).MutateRows(m, &bigtableMutateRowsServer{stream})
}

type Bigtable_MutateRowsServer interface {
	Send(*MutateRowsResponse) error
	grpc.ServerStream
}

type bigtableMutateRowsServer struct {
	grpc.ServerStream
}

func (x *bigtableMutateRowsServer) Send(m *MutateRowsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Bigtable_CheckAndMutateRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAndMutateRowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BigtableServer).CheckAndMutateRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.bigtable.v2.Bigtable/CheckAndMutateRow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BigtableServer).CheckAndMutateRow(ctx, req.(*CheckAndMutateRowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bigtable_PingAndWarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingAndWarmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BigtableServer).PingAndWarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.bigtable.v2.Bigtable/PingAndWarm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BigtableServer).PingAndWarm(ctx, req.(*PingAndWarmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bigtable_ReadModifyWriteRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadModifyWriteRowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BigtableServer).ReadModifyWriteRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.bigtable.v2.Bigtable/ReadModifyWriteRow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BigtableServer).ReadModifyWriteRow(ctx, req.(*ReadModifyWriteRowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Bigtable_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.bigtable.v2.Bigtable",
	HandlerType: (*BigtableServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MutateRow",
			Handler:    _Bigtable_MutateRow_Handler,
		},
		{
			MethodName: "CheckAndMutateRow",
			Handler:    _Bigtable_CheckAndMutateRow_Handler,
		},
		{
			MethodName: "PingAndWarm",
			Handler:    _Bigtable_PingAndWarm_Handler,
		},
		{
			MethodName: "ReadModifyWriteRow",
			Handler:    _Bigtable_ReadModifyWriteRow_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ReadRows",
			Handler:       _Bigtable_ReadRows_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "SampleRowKeys",
			Handler:       _Bigtable_SampleRowKeys_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "MutateRows",
			Handler:       _Bigtable_MutateRows_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "google/bigtable/v2/bigtable.proto",
}
