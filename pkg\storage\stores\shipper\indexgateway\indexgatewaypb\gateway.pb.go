// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/storage/stores/shipper/indexgateway/indexgatewaypb/gateway.proto

package indexgatewaypb

import (
	bytes "bytes"
	context "context"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	github_com_grafana_loki_pkg_logproto "github.com/grafana/loki/pkg/logproto"
	logproto "github.com/grafana/loki/pkg/logproto"
	github_com_prometheus_common_model "github.com/prometheus/common/model"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type LabelValuesForMetricNameRequest struct {
	MetricName string                                  `protobuf:"bytes,1,opt,name=metric_name,json=metricName,proto3" json:"metric_name,omitempty"`
	LabelName  string                                  `protobuf:"bytes,2,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	From       github_com_prometheus_common_model.Time `protobuf:"varint,3,opt,name=from,proto3,customtype=github.com/prometheus/common/model.Time" json:"from"`
	Through    github_com_prometheus_common_model.Time `protobuf:"varint,4,opt,name=through,proto3,customtype=github.com/prometheus/common/model.Time" json:"through"`
	Matchers   string                                  `protobuf:"bytes,5,opt,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *LabelValuesForMetricNameRequest) Reset()      { *m = LabelValuesForMetricNameRequest{} }
func (*LabelValuesForMetricNameRequest) ProtoMessage() {}
func (*LabelValuesForMetricNameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{0}
}
func (m *LabelValuesForMetricNameRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelValuesForMetricNameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelValuesForMetricNameRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelValuesForMetricNameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelValuesForMetricNameRequest.Merge(m, src)
}
func (m *LabelValuesForMetricNameRequest) XXX_Size() int {
	return m.Size()
}
func (m *LabelValuesForMetricNameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelValuesForMetricNameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LabelValuesForMetricNameRequest proto.InternalMessageInfo

func (m *LabelValuesForMetricNameRequest) GetMetricName() string {
	if m != nil {
		return m.MetricName
	}
	return ""
}

func (m *LabelValuesForMetricNameRequest) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *LabelValuesForMetricNameRequest) GetMatchers() string {
	if m != nil {
		return m.Matchers
	}
	return ""
}

type LabelNamesForMetricNameRequest struct {
	MetricName string                                  `protobuf:"bytes,1,opt,name=metric_name,json=metricName,proto3" json:"metric_name,omitempty"`
	From       github_com_prometheus_common_model.Time `protobuf:"varint,2,opt,name=from,proto3,customtype=github.com/prometheus/common/model.Time" json:"from"`
	Through    github_com_prometheus_common_model.Time `protobuf:"varint,3,opt,name=through,proto3,customtype=github.com/prometheus/common/model.Time" json:"through"`
}

func (m *LabelNamesForMetricNameRequest) Reset()      { *m = LabelNamesForMetricNameRequest{} }
func (*LabelNamesForMetricNameRequest) ProtoMessage() {}
func (*LabelNamesForMetricNameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{1}
}
func (m *LabelNamesForMetricNameRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelNamesForMetricNameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelNamesForMetricNameRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelNamesForMetricNameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelNamesForMetricNameRequest.Merge(m, src)
}
func (m *LabelNamesForMetricNameRequest) XXX_Size() int {
	return m.Size()
}
func (m *LabelNamesForMetricNameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelNamesForMetricNameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LabelNamesForMetricNameRequest proto.InternalMessageInfo

func (m *LabelNamesForMetricNameRequest) GetMetricName() string {
	if m != nil {
		return m.MetricName
	}
	return ""
}

type LabelResponse struct {
	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (m *LabelResponse) Reset()      { *m = LabelResponse{} }
func (*LabelResponse) ProtoMessage() {}
func (*LabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{2}
}
func (m *LabelResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelResponse.Merge(m, src)
}
func (m *LabelResponse) XXX_Size() int {
	return m.Size()
}
func (m *LabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LabelResponse proto.InternalMessageInfo

func (m *LabelResponse) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

type GetChunkRefRequest struct {
	From     github_com_prometheus_common_model.Time `protobuf:"varint,1,opt,name=from,proto3,customtype=github.com/prometheus/common/model.Time" json:"from"`
	Through  github_com_prometheus_common_model.Time `protobuf:"varint,2,opt,name=through,proto3,customtype=github.com/prometheus/common/model.Time" json:"through"`
	Matchers string                                  `protobuf:"bytes,3,opt,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *GetChunkRefRequest) Reset()      { *m = GetChunkRefRequest{} }
func (*GetChunkRefRequest) ProtoMessage() {}
func (*GetChunkRefRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{3}
}
func (m *GetChunkRefRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetChunkRefRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetChunkRefRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetChunkRefRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChunkRefRequest.Merge(m, src)
}
func (m *GetChunkRefRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetChunkRefRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChunkRefRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChunkRefRequest proto.InternalMessageInfo

func (m *GetChunkRefRequest) GetMatchers() string {
	if m != nil {
		return m.Matchers
	}
	return ""
}

type GetChunkRefResponse struct {
	Refs []*logproto.ChunkRef `protobuf:"bytes,1,rep,name=refs,proto3" json:"refs,omitempty"`
}

func (m *GetChunkRefResponse) Reset()      { *m = GetChunkRefResponse{} }
func (*GetChunkRefResponse) ProtoMessage() {}
func (*GetChunkRefResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{4}
}
func (m *GetChunkRefResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetChunkRefResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetChunkRefResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetChunkRefResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChunkRefResponse.Merge(m, src)
}
func (m *GetChunkRefResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetChunkRefResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChunkRefResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChunkRefResponse proto.InternalMessageInfo

func (m *GetChunkRefResponse) GetRefs() []*logproto.ChunkRef {
	if m != nil {
		return m.Refs
	}
	return nil
}

type GetSeriesRequest struct {
	From     github_com_prometheus_common_model.Time `protobuf:"varint,1,opt,name=from,proto3,customtype=github.com/prometheus/common/model.Time" json:"from"`
	Through  github_com_prometheus_common_model.Time `protobuf:"varint,2,opt,name=through,proto3,customtype=github.com/prometheus/common/model.Time" json:"through"`
	Matchers string                                  `protobuf:"bytes,3,opt,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *GetSeriesRequest) Reset()      { *m = GetSeriesRequest{} }
func (*GetSeriesRequest) ProtoMessage() {}
func (*GetSeriesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{5}
}
func (m *GetSeriesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetSeriesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetSeriesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetSeriesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeriesRequest.Merge(m, src)
}
func (m *GetSeriesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetSeriesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeriesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeriesRequest proto.InternalMessageInfo

func (m *GetSeriesRequest) GetMatchers() string {
	if m != nil {
		return m.Matchers
	}
	return ""
}

type GetSeriesResponse struct {
	Series []Series `protobuf:"bytes,1,rep,name=series,proto3" json:"series"`
}

func (m *GetSeriesResponse) Reset()      { *m = GetSeriesResponse{} }
func (*GetSeriesResponse) ProtoMessage() {}
func (*GetSeriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{6}
}
func (m *GetSeriesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetSeriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetSeriesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetSeriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeriesResponse.Merge(m, src)
}
func (m *GetSeriesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetSeriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeriesResponse proto.InternalMessageInfo

func (m *GetSeriesResponse) GetSeries() []Series {
	if m != nil {
		return m.Series
	}
	return nil
}

type Series struct {
	Labels []github_com_grafana_loki_pkg_logproto.LabelAdapter `protobuf:"bytes,1,rep,name=labels,proto3,customtype=github.com/grafana/loki/pkg/logproto.LabelAdapter" json:"labels"`
}

func (m *Series) Reset()      { *m = Series{} }
func (*Series) ProtoMessage() {}
func (*Series) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{7}
}
func (m *Series) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Series) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Series.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Series) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Series.Merge(m, src)
}
func (m *Series) XXX_Size() int {
	return m.Size()
}
func (m *Series) XXX_DiscardUnknown() {
	xxx_messageInfo_Series.DiscardUnknown(m)
}

var xxx_messageInfo_Series proto.InternalMessageInfo

type QueryIndexResponse struct {
	QueryKey string `protobuf:"bytes,1,opt,name=QueryKey,proto3" json:"QueryKey,omitempty"`
	Rows     []*Row `protobuf:"bytes,2,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *QueryIndexResponse) Reset()      { *m = QueryIndexResponse{} }
func (*QueryIndexResponse) ProtoMessage() {}
func (*QueryIndexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{8}
}
func (m *QueryIndexResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryIndexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryIndexResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryIndexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryIndexResponse.Merge(m, src)
}
func (m *QueryIndexResponse) XXX_Size() int {
	return m.Size()
}
func (m *QueryIndexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryIndexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryIndexResponse proto.InternalMessageInfo

func (m *QueryIndexResponse) GetQueryKey() string {
	if m != nil {
		return m.QueryKey
	}
	return ""
}

func (m *QueryIndexResponse) GetRows() []*Row {
	if m != nil {
		return m.Rows
	}
	return nil
}

type Row struct {
	RangeValue []byte `protobuf:"bytes,1,opt,name=rangeValue,proto3" json:"rangeValue,omitempty"`
	Value      []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Row) Reset()      { *m = Row{} }
func (*Row) ProtoMessage() {}
func (*Row) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{9}
}
func (m *Row) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Row) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Row.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Row) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Row.Merge(m, src)
}
func (m *Row) XXX_Size() int {
	return m.Size()
}
func (m *Row) XXX_DiscardUnknown() {
	xxx_messageInfo_Row.DiscardUnknown(m)
}

var xxx_messageInfo_Row proto.InternalMessageInfo

func (m *Row) GetRangeValue() []byte {
	if m != nil {
		return m.RangeValue
	}
	return nil
}

func (m *Row) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type QueryIndexRequest struct {
	Queries []*IndexQuery `protobuf:"bytes,1,rep,name=Queries,proto3" json:"Queries,omitempty"`
}

func (m *QueryIndexRequest) Reset()      { *m = QueryIndexRequest{} }
func (*QueryIndexRequest) ProtoMessage() {}
func (*QueryIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{10}
}
func (m *QueryIndexRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryIndexRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryIndexRequest.Merge(m, src)
}
func (m *QueryIndexRequest) XXX_Size() int {
	return m.Size()
}
func (m *QueryIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryIndexRequest proto.InternalMessageInfo

func (m *QueryIndexRequest) GetQueries() []*IndexQuery {
	if m != nil {
		return m.Queries
	}
	return nil
}

type IndexQuery struct {
	TableName        string `protobuf:"bytes,1,opt,name=tableName,proto3" json:"tableName,omitempty"`
	HashValue        string `protobuf:"bytes,2,opt,name=hashValue,proto3" json:"hashValue,omitempty"`
	RangeValuePrefix []byte `protobuf:"bytes,3,opt,name=rangeValuePrefix,proto3" json:"rangeValuePrefix,omitempty"`
	RangeValueStart  []byte `protobuf:"bytes,4,opt,name=rangeValueStart,proto3" json:"rangeValueStart,omitempty"`
	ValueEqual       []byte `protobuf:"bytes,5,opt,name=valueEqual,proto3" json:"valueEqual,omitempty"`
}

func (m *IndexQuery) Reset()      { *m = IndexQuery{} }
func (*IndexQuery) ProtoMessage() {}
func (*IndexQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{11}
}
func (m *IndexQuery) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IndexQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IndexQuery.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IndexQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexQuery.Merge(m, src)
}
func (m *IndexQuery) XXX_Size() int {
	return m.Size()
}
func (m *IndexQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexQuery.DiscardUnknown(m)
}

var xxx_messageInfo_IndexQuery proto.InternalMessageInfo

func (m *IndexQuery) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

func (m *IndexQuery) GetHashValue() string {
	if m != nil {
		return m.HashValue
	}
	return ""
}

func (m *IndexQuery) GetRangeValuePrefix() []byte {
	if m != nil {
		return m.RangeValuePrefix
	}
	return nil
}

func (m *IndexQuery) GetRangeValueStart() []byte {
	if m != nil {
		return m.RangeValueStart
	}
	return nil
}

func (m *IndexQuery) GetValueEqual() []byte {
	if m != nil {
		return m.ValueEqual
	}
	return nil
}

type IndexStatsRequest struct {
	From     github_com_prometheus_common_model.Time `protobuf:"varint,1,opt,name=from,proto3,customtype=github.com/prometheus/common/model.Time" json:"from"`
	Through  github_com_prometheus_common_model.Time `protobuf:"varint,2,opt,name=through,proto3,customtype=github.com/prometheus/common/model.Time" json:"through"`
	Matchers string                                  `protobuf:"bytes,3,opt,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *IndexStatsRequest) Reset()      { *m = IndexStatsRequest{} }
func (*IndexStatsRequest) ProtoMessage() {}
func (*IndexStatsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{12}
}
func (m *IndexStatsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IndexStatsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IndexStatsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IndexStatsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexStatsRequest.Merge(m, src)
}
func (m *IndexStatsRequest) XXX_Size() int {
	return m.Size()
}
func (m *IndexStatsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexStatsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IndexStatsRequest proto.InternalMessageInfo

func (m *IndexStatsRequest) GetMatchers() string {
	if m != nil {
		return m.Matchers
	}
	return ""
}

type IndexStatsResponse struct {
	Streams uint64 `protobuf:"varint,1,opt,name=streams,proto3" json:"streams"`
	Chunks  uint64 `protobuf:"varint,2,opt,name=chunks,proto3" json:"chunks"`
	Bytes   uint64 `protobuf:"varint,3,opt,name=bytes,proto3" json:"bytes"`
	Entries uint64 `protobuf:"varint,4,opt,name=entries,proto3" json:"entries"`
}

func (m *IndexStatsResponse) Reset()      { *m = IndexStatsResponse{} }
func (*IndexStatsResponse) ProtoMessage() {}
func (*IndexStatsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33a7bd4603d312b2, []int{13}
}
func (m *IndexStatsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IndexStatsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IndexStatsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IndexStatsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexStatsResponse.Merge(m, src)
}
func (m *IndexStatsResponse) XXX_Size() int {
	return m.Size()
}
func (m *IndexStatsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexStatsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IndexStatsResponse proto.InternalMessageInfo

func (m *IndexStatsResponse) GetStreams() uint64 {
	if m != nil {
		return m.Streams
	}
	return 0
}

func (m *IndexStatsResponse) GetChunks() uint64 {
	if m != nil {
		return m.Chunks
	}
	return 0
}

func (m *IndexStatsResponse) GetBytes() uint64 {
	if m != nil {
		return m.Bytes
	}
	return 0
}

func (m *IndexStatsResponse) GetEntries() uint64 {
	if m != nil {
		return m.Entries
	}
	return 0
}

func init() {
	proto.RegisterType((*LabelValuesForMetricNameRequest)(nil), "indexgatewaypb.LabelValuesForMetricNameRequest")
	proto.RegisterType((*LabelNamesForMetricNameRequest)(nil), "indexgatewaypb.LabelNamesForMetricNameRequest")
	proto.RegisterType((*LabelResponse)(nil), "indexgatewaypb.LabelResponse")
	proto.RegisterType((*GetChunkRefRequest)(nil), "indexgatewaypb.GetChunkRefRequest")
	proto.RegisterType((*GetChunkRefResponse)(nil), "indexgatewaypb.GetChunkRefResponse")
	proto.RegisterType((*GetSeriesRequest)(nil), "indexgatewaypb.GetSeriesRequest")
	proto.RegisterType((*GetSeriesResponse)(nil), "indexgatewaypb.GetSeriesResponse")
	proto.RegisterType((*Series)(nil), "indexgatewaypb.Series")
	proto.RegisterType((*QueryIndexResponse)(nil), "indexgatewaypb.QueryIndexResponse")
	proto.RegisterType((*Row)(nil), "indexgatewaypb.Row")
	proto.RegisterType((*QueryIndexRequest)(nil), "indexgatewaypb.QueryIndexRequest")
	proto.RegisterType((*IndexQuery)(nil), "indexgatewaypb.IndexQuery")
	proto.RegisterType((*IndexStatsRequest)(nil), "indexgatewaypb.IndexStatsRequest")
	proto.RegisterType((*IndexStatsResponse)(nil), "indexgatewaypb.IndexStatsResponse")
}

func init() {
	proto.RegisterFile("pkg/storage/stores/shipper/indexgateway/indexgatewaypb/gateway.proto", fileDescriptor_33a7bd4603d312b2)
}

var fileDescriptor_33a7bd4603d312b2 = []byte{
	// 931 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x56, 0x4f, 0x6f, 0x1b, 0x45,
	0x14, 0xdf, 0xb1, 0x37, 0x4e, 0xfc, 0x62, 0xa0, 0x9d, 0xa2, 0x62, 0x2d, 0x74, 0x9d, 0x2e, 0x82,
	0x44, 0x48, 0x78, 0xa1, 0xf4, 0x82, 0x10, 0x07, 0x5c, 0x20, 0x8a, 0x28, 0x51, 0x99, 0x40, 0x05,
	0x5c, 0xd0, 0xd8, 0x19, 0xef, 0x2e, 0xd9, 0xf5, 0x6c, 0x67, 0x67, 0x49, 0x7d, 0xe3, 0xc0, 0x15,
	0x89, 0x6f, 0x01, 0x9f, 0x02, 0x10, 0xa7, 0x1e, 0xc3, 0xad, 0xe2, 0x60, 0x11, 0xe7, 0x82, 0x72,
	0xea, 0x47, 0x40, 0x3b, 0xfb, 0xd7, 0x76, 0x62, 0x44, 0x9b, 0x4b, 0x4e, 0x3b, 0xef, 0xf7, 0xde,
	0x9b, 0x79, 0xbf, 0x99, 0x37, 0xbf, 0x1d, 0xf8, 0x30, 0x3c, 0x70, 0xec, 0x48, 0x72, 0x41, 0x1d,
	0xa6, 0xbe, 0x2c, 0xb2, 0x23, 0xd7, 0x0b, 0x43, 0x26, 0x6c, 0x6f, 0xb4, 0xcf, 0x1e, 0x3a, 0x54,
	0xb2, 0x43, 0x3a, 0x9e, 0x31, 0xc2, 0xbe, 0x9d, 0x8d, 0xba, 0xa1, 0xe0, 0x92, 0xe3, 0xe7, 0x67,
	0xbd, 0xc6, 0x9b, 0x8e, 0x27, 0xdd, 0xb8, 0xdf, 0x1d, 0xf0, 0xc0, 0x76, 0xb8, 0xc3, 0x6d, 0x15,
	0xd6, 0x8f, 0x87, 0xca, 0x52, 0x86, 0x1a, 0xa5, 0xe9, 0xc6, 0xcb, 0x49, 0x11, 0x3e, 0x77, 0x52,
	0x47, 0x3e, 0x48, 0x9d, 0xd6, 0x8f, 0x35, 0xe8, 0xdc, 0xa5, 0x7d, 0xe6, 0xdf, 0xa7, 0x7e, 0xcc,
	0xa2, 0x8f, 0xb9, 0xf8, 0x94, 0x49, 0xe1, 0x0d, 0x76, 0x69, 0xc0, 0x08, 0x7b, 0x10, 0xb3, 0x48,
	0xe2, 0x0e, 0xac, 0x07, 0x0a, 0xfc, 0x66, 0x44, 0x03, 0xd6, 0x46, 0x1b, 0x68, 0xab, 0x49, 0x20,
	0x28, 0xe2, 0xf0, 0x0d, 0x00, 0x3f, 0x99, 0x23, 0xf5, 0xd7, 0x94, 0xbf, 0xa9, 0x10, 0xe5, 0xbe,
	0x03, 0xfa, 0x50, 0xf0, 0xa0, 0x5d, 0xdf, 0x40, 0x5b, 0xf5, 0x9e, 0xfd, 0x68, 0xd2, 0xd1, 0xfe,
	0x9a, 0x74, 0x36, 0x2b, 0x2c, 0x42, 0xc1, 0x03, 0x26, 0x5d, 0x16, 0x47, 0xf6, 0x80, 0x07, 0x01,
	0x1f, 0xd9, 0x01, 0xdf, 0x67, 0x7e, 0xf7, 0x73, 0x2f, 0x60, 0x44, 0x25, 0xe3, 0x1d, 0x58, 0x95,
	0xae, 0xe0, 0xb1, 0xe3, 0xb6, 0xf5, 0xa7, 0x9b, 0x27, 0xcf, 0xc7, 0x06, 0xac, 0x05, 0x54, 0x0e,
	0x5c, 0x26, 0xa2, 0xf6, 0x8a, 0x2a, 0xb6, 0xb0, 0xad, 0x3f, 0x11, 0x98, 0x77, 0xf3, 0xca, 0x9f,
	0x72, 0x3b, 0x72, 0xbe, 0xb5, 0x0b, 0xe2, 0x5b, 0x7f, 0x36, 0xbe, 0xd6, 0x26, 0x3c, 0xa7, 0x28,
	0x11, 0x16, 0x85, 0x7c, 0x14, 0x31, 0x7c, 0x1d, 0x1a, 0xdf, 0xa9, 0xe3, 0x6e, 0xa3, 0x8d, 0xfa,
	0x56, 0x93, 0x64, 0x96, 0xf5, 0x3b, 0x02, 0xbc, 0xcd, 0xe4, 0x1d, 0x37, 0x1e, 0x1d, 0x10, 0x36,
	0xcc, 0x09, 0xe7, 0x7c, 0xd0, 0x05, 0xf1, 0xa9, 0x5d, 0xe0, 0xf9, 0xd5, 0xe7, 0xce, 0xef, 0x7d,
	0xb8, 0x36, 0xc3, 0x20, 0x63, 0xfc, 0x3a, 0xe8, 0x82, 0x0d, 0x53, 0xbe, 0xeb, 0xb7, 0x70, 0xb7,
	0xb8, 0x05, 0x45, 0xa4, 0xf2, 0x5b, 0xbf, 0x22, 0xb8, 0xb2, 0xcd, 0xe4, 0x1e, 0x13, 0x1e, 0x8b,
	0x2e, 0x23, 0xff, 0x1d, 0xb8, 0x5a, 0xa9, 0x3f, 0x63, 0x7f, 0x1b, 0x1a, 0x91, 0x42, 0x32, 0xfe,
	0xd7, 0xbb, 0xb3, 0x8a, 0xd2, 0x4d, 0xe3, 0x7b, 0x7a, 0x52, 0x12, 0xc9, 0x62, 0xad, 0x10, 0x1a,
	0x29, 0x8e, 0x87, 0xd0, 0x50, 0xb7, 0x39, 0xcf, 0xbf, 0x56, 0xee, 0x9f, 0x6a, 0xac, 0x7b, 0xd4,
	0x13, 0xbd, 0x77, 0x33, 0x3e, 0x6f, 0x57, 0xd5, 0x49, 0xd0, 0x21, 0x1d, 0x51, 0xdb, 0xe7, 0x07,
	0x9e, 0x5d, 0x95, 0xa1, 0x34, 0xef, 0x83, 0x7d, 0x1a, 0x4a, 0x26, 0x48, 0x36, 0xbb, 0xf5, 0x15,
	0xe0, 0xcf, 0x62, 0x26, 0xc6, 0x3b, 0x49, 0x75, 0x45, 0xf5, 0x06, 0xac, 0x29, 0xf4, 0x13, 0x36,
	0xce, 0x2e, 0x5b, 0x61, 0xe3, 0x4d, 0xd0, 0x05, 0x3f, 0x8c, 0xda, 0xb5, 0xac, 0xae, 0x39, 0x5e,
	0x84, 0x1f, 0x12, 0x15, 0x60, 0xbd, 0x07, 0x75, 0xc2, 0x0f, 0xb1, 0x09, 0x20, 0xe8, 0xc8, 0x61,
	0x4a, 0xed, 0xd4, 0x6c, 0x2d, 0x52, 0x41, 0xf0, 0x8b, 0xb0, 0xa2, 0xee, 0x82, 0x3a, 0xa3, 0x16,
	0x49, 0x8d, 0x64, 0x53, 0xab, 0x75, 0xa5, 0x5d, 0x71, 0x1b, 0x56, 0x13, 0xb0, 0xdc, 0x55, 0x63,
	0x7e, 0x75, 0x15, 0xae, 0x12, 0x49, 0x1e, 0x9a, 0x34, 0x18, 0x94, 0x38, 0x7e, 0x05, 0x9a, 0x92,
	0xf6, 0x7d, 0xb6, 0x5b, 0x2a, 0x49, 0x09, 0x24, 0x5e, 0x97, 0x46, 0xee, 0xfd, 0xa2, 0xa2, 0x26,
	0x29, 0x01, 0xfc, 0x06, 0x5c, 0x29, 0x2b, 0xbf, 0x27, 0xd8, 0xd0, 0x7b, 0xa8, 0xda, 0xa1, 0x45,
	0x16, 0x70, 0xbc, 0x05, 0x2f, 0x94, 0xd8, 0x9e, 0xa4, 0x42, 0x2a, 0x15, 0x6d, 0x91, 0x79, 0x38,
	0xd9, 0x21, 0x45, 0xfa, 0xa3, 0x07, 0x31, 0xf5, 0x95, 0x3c, 0xb6, 0x48, 0x05, 0xb1, 0x7e, 0x43,
	0x70, 0x55, 0x11, 0xd8, 0x93, 0x54, 0x5e, 0xca, 0x2b, 0xf2, 0x33, 0x02, 0x5c, 0x65, 0x90, 0xb5,
	0xd9, 0x6b, 0xb0, 0x1a, 0x49, 0xc1, 0x68, 0x10, 0x29, 0x16, 0x7a, 0x6f, 0xfd, 0x74, 0xd2, 0xc9,
	0x21, 0x92, 0x0f, 0xb0, 0x05, 0x8d, 0x41, 0xa2, 0x19, 0x91, 0xaa, 0x51, 0xef, 0xc1, 0xe9, 0xa4,
	0x93, 0x21, 0x24, 0xfb, 0xe2, 0x0e, 0xac, 0xf4, 0xc7, 0x92, 0xa5, 0x4b, 0xeb, 0xbd, 0xe6, 0xe9,
	0xa4, 0x93, 0x02, 0x24, 0xfd, 0x24, 0x6b, 0xb1, 0x91, 0x54, 0xbd, 0xa3, 0x97, 0x6b, 0x65, 0x10,
	0xc9, 0x07, 0xb7, 0xfe, 0xd0, 0xa1, 0xa5, 0x2a, 0xdd, 0x4e, 0x7b, 0x0a, 0x7f, 0x01, 0x50, 0x36,
	0x22, 0xbe, 0x39, 0xdf, 0x70, 0x0b, 0x4d, 0x6a, 0x58, 0xcb, 0x42, 0x52, 0xe2, 0x6f, 0x21, 0xfc,
	0x25, 0xac, 0x57, 0x44, 0x13, 0x2f, 0x24, 0x2d, 0xfe, 0x13, 0x8c, 0x57, 0x97, 0xc6, 0xa4, 0x33,
	0x5b, 0x1a, 0x26, 0xd0, 0x2c, 0xe4, 0x08, 0x6f, 0x9c, 0x91, 0x33, 0xa3, 0xb4, 0xc6, 0xcd, 0x25,
	0x11, 0xc5, 0x9c, 0xdf, 0xc2, 0x4b, 0xe7, 0xfc, 0xa1, 0x71, 0x77, 0x3e, 0x7f, 0xf9, 0xaf, 0xdc,
	0xb8, 0x71, 0x66, 0x7c, 0x65, 0x2d, 0x1f, 0xda, 0xe7, 0xbd, 0x8e, 0xb0, 0x7d, 0x66, 0xf2, 0xf9,
	0xef, 0xa8, 0xff, 0x5e, 0x6d, 0x0f, 0xd6, 0x12, 0xc2, 0x49, 0x5b, 0x2e, 0x1e, 0xee, 0xc2, 0xa5,
	0x5b, 0x3c, 0xdc, 0xc5, 0xae, 0xb6, 0xb4, 0xde, 0x0f, 0xe8, 0xe8, 0xd8, 0xd4, 0x1e, 0x1f, 0x9b,
	0xda, 0x93, 0x63, 0x13, 0x7d, 0x3f, 0x35, 0xd1, 0x2f, 0x53, 0x13, 0x3d, 0x9a, 0x9a, 0xe8, 0x68,
	0x6a, 0xa2, 0xbf, 0xa7, 0x26, 0xfa, 0x67, 0x6a, 0x6a, 0x4f, 0xa6, 0x26, 0xfa, 0xe9, 0xc4, 0xd4,
	0x8e, 0x4e, 0x4c, 0xed, 0xf1, 0x89, 0xa9, 0x7d, 0xbd, 0xbb, 0x4c, 0xc6, 0xff, 0xff, 0x93, 0xb6,
	0xdf, 0x50, 0xba, 0xff, 0xce, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x32, 0x10, 0x4c, 0x62, 0x13,
	0x0b, 0x00, 0x00,
}

func (this *LabelValuesForMetricNameRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelValuesForMetricNameRequest)
	if !ok {
		that2, ok := that.(LabelValuesForMetricNameRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.MetricName != that1.MetricName {
		return false
	}
	if this.LabelName != that1.LabelName {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.Through.Equal(that1.Through) {
		return false
	}
	if this.Matchers != that1.Matchers {
		return false
	}
	return true
}
func (this *LabelNamesForMetricNameRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelNamesForMetricNameRequest)
	if !ok {
		that2, ok := that.(LabelNamesForMetricNameRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.MetricName != that1.MetricName {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.Through.Equal(that1.Through) {
		return false
	}
	return true
}
func (this *LabelResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelResponse)
	if !ok {
		that2, ok := that.(LabelResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Values) != len(that1.Values) {
		return false
	}
	for i := range this.Values {
		if this.Values[i] != that1.Values[i] {
			return false
		}
	}
	return true
}
func (this *GetChunkRefRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetChunkRefRequest)
	if !ok {
		that2, ok := that.(GetChunkRefRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.Through.Equal(that1.Through) {
		return false
	}
	if this.Matchers != that1.Matchers {
		return false
	}
	return true
}
func (this *GetChunkRefResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetChunkRefResponse)
	if !ok {
		that2, ok := that.(GetChunkRefResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Refs) != len(that1.Refs) {
		return false
	}
	for i := range this.Refs {
		if !this.Refs[i].Equal(that1.Refs[i]) {
			return false
		}
	}
	return true
}
func (this *GetSeriesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetSeriesRequest)
	if !ok {
		that2, ok := that.(GetSeriesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.Through.Equal(that1.Through) {
		return false
	}
	if this.Matchers != that1.Matchers {
		return false
	}
	return true
}
func (this *GetSeriesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetSeriesResponse)
	if !ok {
		that2, ok := that.(GetSeriesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Series) != len(that1.Series) {
		return false
	}
	for i := range this.Series {
		if !this.Series[i].Equal(&that1.Series[i]) {
			return false
		}
	}
	return true
}
func (this *Series) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Series)
	if !ok {
		that2, ok := that.(Series)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	return true
}
func (this *QueryIndexResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryIndexResponse)
	if !ok {
		that2, ok := that.(QueryIndexResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.QueryKey != that1.QueryKey {
		return false
	}
	if len(this.Rows) != len(that1.Rows) {
		return false
	}
	for i := range this.Rows {
		if !this.Rows[i].Equal(that1.Rows[i]) {
			return false
		}
	}
	return true
}
func (this *Row) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Row)
	if !ok {
		that2, ok := that.(Row)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !bytes.Equal(this.RangeValue, that1.RangeValue) {
		return false
	}
	if !bytes.Equal(this.Value, that1.Value) {
		return false
	}
	return true
}
func (this *QueryIndexRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryIndexRequest)
	if !ok {
		that2, ok := that.(QueryIndexRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Queries) != len(that1.Queries) {
		return false
	}
	for i := range this.Queries {
		if !this.Queries[i].Equal(that1.Queries[i]) {
			return false
		}
	}
	return true
}
func (this *IndexQuery) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*IndexQuery)
	if !ok {
		that2, ok := that.(IndexQuery)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	if this.HashValue != that1.HashValue {
		return false
	}
	if !bytes.Equal(this.RangeValuePrefix, that1.RangeValuePrefix) {
		return false
	}
	if !bytes.Equal(this.RangeValueStart, that1.RangeValueStart) {
		return false
	}
	if !bytes.Equal(this.ValueEqual, that1.ValueEqual) {
		return false
	}
	return true
}
func (this *IndexStatsRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*IndexStatsRequest)
	if !ok {
		that2, ok := that.(IndexStatsRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.Through.Equal(that1.Through) {
		return false
	}
	if this.Matchers != that1.Matchers {
		return false
	}
	return true
}
func (this *IndexStatsResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*IndexStatsResponse)
	if !ok {
		that2, ok := that.(IndexStatsResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Streams != that1.Streams {
		return false
	}
	if this.Chunks != that1.Chunks {
		return false
	}
	if this.Bytes != that1.Bytes {
		return false
	}
	if this.Entries != that1.Entries {
		return false
	}
	return true
}
func (this *LabelValuesForMetricNameRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&indexgatewaypb.LabelValuesForMetricNameRequest{")
	s = append(s, "MetricName: "+fmt.Sprintf("%#v", this.MetricName)+",\n")
	s = append(s, "LabelName: "+fmt.Sprintf("%#v", this.LabelName)+",\n")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "Through: "+fmt.Sprintf("%#v", this.Through)+",\n")
	s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelNamesForMetricNameRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&indexgatewaypb.LabelNamesForMetricNameRequest{")
	s = append(s, "MetricName: "+fmt.Sprintf("%#v", this.MetricName)+",\n")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "Through: "+fmt.Sprintf("%#v", this.Through)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&indexgatewaypb.LabelResponse{")
	s = append(s, "Values: "+fmt.Sprintf("%#v", this.Values)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetChunkRefRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&indexgatewaypb.GetChunkRefRequest{")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "Through: "+fmt.Sprintf("%#v", this.Through)+",\n")
	s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetChunkRefResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&indexgatewaypb.GetChunkRefResponse{")
	if this.Refs != nil {
		s = append(s, "Refs: "+fmt.Sprintf("%#v", this.Refs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetSeriesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&indexgatewaypb.GetSeriesRequest{")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "Through: "+fmt.Sprintf("%#v", this.Through)+",\n")
	s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetSeriesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&indexgatewaypb.GetSeriesResponse{")
	if this.Series != nil {
		vs := make([]*Series, len(this.Series))
		for i := range vs {
			vs[i] = &this.Series[i]
		}
		s = append(s, "Series: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Series) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&indexgatewaypb.Series{")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryIndexResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&indexgatewaypb.QueryIndexResponse{")
	s = append(s, "QueryKey: "+fmt.Sprintf("%#v", this.QueryKey)+",\n")
	if this.Rows != nil {
		s = append(s, "Rows: "+fmt.Sprintf("%#v", this.Rows)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Row) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&indexgatewaypb.Row{")
	s = append(s, "RangeValue: "+fmt.Sprintf("%#v", this.RangeValue)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryIndexRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&indexgatewaypb.QueryIndexRequest{")
	if this.Queries != nil {
		s = append(s, "Queries: "+fmt.Sprintf("%#v", this.Queries)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *IndexQuery) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&indexgatewaypb.IndexQuery{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "HashValue: "+fmt.Sprintf("%#v", this.HashValue)+",\n")
	s = append(s, "RangeValuePrefix: "+fmt.Sprintf("%#v", this.RangeValuePrefix)+",\n")
	s = append(s, "RangeValueStart: "+fmt.Sprintf("%#v", this.RangeValueStart)+",\n")
	s = append(s, "ValueEqual: "+fmt.Sprintf("%#v", this.ValueEqual)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *IndexStatsRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&indexgatewaypb.IndexStatsRequest{")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "Through: "+fmt.Sprintf("%#v", this.Through)+",\n")
	s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *IndexStatsResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&indexgatewaypb.IndexStatsResponse{")
	s = append(s, "Streams: "+fmt.Sprintf("%#v", this.Streams)+",\n")
	s = append(s, "Chunks: "+fmt.Sprintf("%#v", this.Chunks)+",\n")
	s = append(s, "Bytes: "+fmt.Sprintf("%#v", this.Bytes)+",\n")
	s = append(s, "Entries: "+fmt.Sprintf("%#v", this.Entries)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringGateway(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IndexGatewayClient is the client API for IndexGateway service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IndexGatewayClient interface {
	/// QueryIndex reads the indexes required for given query & sends back the batch of rows
	/// in rpc streams
	QueryIndex(ctx context.Context, in *QueryIndexRequest, opts ...grpc.CallOption) (IndexGateway_QueryIndexClient, error)
	/// GetChunkRef returns chunk reference that match the provided label matchers
	GetChunkRef(ctx context.Context, in *GetChunkRefRequest, opts ...grpc.CallOption) (*GetChunkRefResponse, error)
	GetSeries(ctx context.Context, in *GetSeriesRequest, opts ...grpc.CallOption) (*GetSeriesResponse, error)
	LabelNamesForMetricName(ctx context.Context, in *LabelNamesForMetricNameRequest, opts ...grpc.CallOption) (*LabelResponse, error)
	LabelValuesForMetricName(ctx context.Context, in *LabelValuesForMetricNameRequest, opts ...grpc.CallOption) (*LabelResponse, error)
	GetStats(ctx context.Context, in *IndexStatsRequest, opts ...grpc.CallOption) (*IndexStatsResponse, error)
}

type indexGatewayClient struct {
	cc *grpc.ClientConn
}

func NewIndexGatewayClient(cc *grpc.ClientConn) IndexGatewayClient {
	return &indexGatewayClient{cc}
}

func (c *indexGatewayClient) QueryIndex(ctx context.Context, in *QueryIndexRequest, opts ...grpc.CallOption) (IndexGateway_QueryIndexClient, error) {
	stream, err := c.cc.NewStream(ctx, &_IndexGateway_serviceDesc.Streams[0], "/indexgatewaypb.IndexGateway/QueryIndex", opts...)
	if err != nil {
		return nil, err
	}
	x := &indexGatewayQueryIndexClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type IndexGateway_QueryIndexClient interface {
	Recv() (*QueryIndexResponse, error)
	grpc.ClientStream
}

type indexGatewayQueryIndexClient struct {
	grpc.ClientStream
}

func (x *indexGatewayQueryIndexClient) Recv() (*QueryIndexResponse, error) {
	m := new(QueryIndexResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *indexGatewayClient) GetChunkRef(ctx context.Context, in *GetChunkRefRequest, opts ...grpc.CallOption) (*GetChunkRefResponse, error) {
	out := new(GetChunkRefResponse)
	err := c.cc.Invoke(ctx, "/indexgatewaypb.IndexGateway/GetChunkRef", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indexGatewayClient) GetSeries(ctx context.Context, in *GetSeriesRequest, opts ...grpc.CallOption) (*GetSeriesResponse, error) {
	out := new(GetSeriesResponse)
	err := c.cc.Invoke(ctx, "/indexgatewaypb.IndexGateway/GetSeries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indexGatewayClient) LabelNamesForMetricName(ctx context.Context, in *LabelNamesForMetricNameRequest, opts ...grpc.CallOption) (*LabelResponse, error) {
	out := new(LabelResponse)
	err := c.cc.Invoke(ctx, "/indexgatewaypb.IndexGateway/LabelNamesForMetricName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indexGatewayClient) LabelValuesForMetricName(ctx context.Context, in *LabelValuesForMetricNameRequest, opts ...grpc.CallOption) (*LabelResponse, error) {
	out := new(LabelResponse)
	err := c.cc.Invoke(ctx, "/indexgatewaypb.IndexGateway/LabelValuesForMetricName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indexGatewayClient) GetStats(ctx context.Context, in *IndexStatsRequest, opts ...grpc.CallOption) (*IndexStatsResponse, error) {
	out := new(IndexStatsResponse)
	err := c.cc.Invoke(ctx, "/indexgatewaypb.IndexGateway/GetStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IndexGatewayServer is the server API for IndexGateway service.
type IndexGatewayServer interface {
	/// QueryIndex reads the indexes required for given query & sends back the batch of rows
	/// in rpc streams
	QueryIndex(*QueryIndexRequest, IndexGateway_QueryIndexServer) error
	/// GetChunkRef returns chunk reference that match the provided label matchers
	GetChunkRef(context.Context, *GetChunkRefRequest) (*GetChunkRefResponse, error)
	GetSeries(context.Context, *GetSeriesRequest) (*GetSeriesResponse, error)
	LabelNamesForMetricName(context.Context, *LabelNamesForMetricNameRequest) (*LabelResponse, error)
	LabelValuesForMetricName(context.Context, *LabelValuesForMetricNameRequest) (*LabelResponse, error)
	GetStats(context.Context, *IndexStatsRequest) (*IndexStatsResponse, error)
}

// UnimplementedIndexGatewayServer can be embedded to have forward compatible implementations.
type UnimplementedIndexGatewayServer struct {
}

func (*UnimplementedIndexGatewayServer) QueryIndex(req *QueryIndexRequest, srv IndexGateway_QueryIndexServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryIndex not implemented")
}
func (*UnimplementedIndexGatewayServer) GetChunkRef(ctx context.Context, req *GetChunkRefRequest) (*GetChunkRefResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChunkRef not implemented")
}
func (*UnimplementedIndexGatewayServer) GetSeries(ctx context.Context, req *GetSeriesRequest) (*GetSeriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeries not implemented")
}
func (*UnimplementedIndexGatewayServer) LabelNamesForMetricName(ctx context.Context, req *LabelNamesForMetricNameRequest) (*LabelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelNamesForMetricName not implemented")
}
func (*UnimplementedIndexGatewayServer) LabelValuesForMetricName(ctx context.Context, req *LabelValuesForMetricNameRequest) (*LabelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelValuesForMetricName not implemented")
}
func (*UnimplementedIndexGatewayServer) GetStats(ctx context.Context, req *IndexStatsRequest) (*IndexStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStats not implemented")
}

func RegisterIndexGatewayServer(s *grpc.Server, srv IndexGatewayServer) {
	s.RegisterService(&_IndexGateway_serviceDesc, srv)
}

func _IndexGateway_QueryIndex_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(QueryIndexRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(IndexGatewayServer).QueryIndex(m, &indexGatewayQueryIndexServer{stream})
}

type IndexGateway_QueryIndexServer interface {
	Send(*QueryIndexResponse) error
	grpc.ServerStream
}

type indexGatewayQueryIndexServer struct {
	grpc.ServerStream
}

func (x *indexGatewayQueryIndexServer) Send(m *QueryIndexResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _IndexGateway_GetChunkRef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChunkRefRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndexGatewayServer).GetChunkRef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/indexgatewaypb.IndexGateway/GetChunkRef",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndexGatewayServer).GetChunkRef(ctx, req.(*GetChunkRefRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndexGateway_GetSeries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndexGatewayServer).GetSeries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/indexgatewaypb.IndexGateway/GetSeries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndexGatewayServer).GetSeries(ctx, req.(*GetSeriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndexGateway_LabelNamesForMetricName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelNamesForMetricNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndexGatewayServer).LabelNamesForMetricName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/indexgatewaypb.IndexGateway/LabelNamesForMetricName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndexGatewayServer).LabelNamesForMetricName(ctx, req.(*LabelNamesForMetricNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndexGateway_LabelValuesForMetricName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelValuesForMetricNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndexGatewayServer).LabelValuesForMetricName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/indexgatewaypb.IndexGateway/LabelValuesForMetricName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndexGatewayServer).LabelValuesForMetricName(ctx, req.(*LabelValuesForMetricNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndexGateway_GetStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndexGatewayServer).GetStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/indexgatewaypb.IndexGateway/GetStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndexGatewayServer).GetStats(ctx, req.(*IndexStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _IndexGateway_serviceDesc = grpc.ServiceDesc{
	ServiceName: "indexgatewaypb.IndexGateway",
	HandlerType: (*IndexGatewayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChunkRef",
			Handler:    _IndexGateway_GetChunkRef_Handler,
		},
		{
			MethodName: "GetSeries",
			Handler:    _IndexGateway_GetSeries_Handler,
		},
		{
			MethodName: "LabelNamesForMetricName",
			Handler:    _IndexGateway_LabelNamesForMetricName_Handler,
		},
		{
			MethodName: "LabelValuesForMetricName",
			Handler:    _IndexGateway_LabelValuesForMetricName_Handler,
		},
		{
			MethodName: "GetStats",
			Handler:    _IndexGateway_GetStats_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "QueryIndex",
			Handler:       _IndexGateway_QueryIndex_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "pkg/storage/stores/shipper/indexgateway/indexgatewaypb/gateway.proto",
}

func (m *LabelValuesForMetricNameRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelValuesForMetricNameRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelValuesForMetricNameRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		i -= len(m.Matchers)
		copy(dAtA[i:], m.Matchers)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.Matchers)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Through != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Through))
		i--
		dAtA[i] = 0x20
	}
	if m.From != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x18
	}
	if len(m.LabelName) > 0 {
		i -= len(m.LabelName)
		copy(dAtA[i:], m.LabelName)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.LabelName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.MetricName) > 0 {
		i -= len(m.MetricName)
		copy(dAtA[i:], m.MetricName)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.MetricName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LabelNamesForMetricNameRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelNamesForMetricNameRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelNamesForMetricNameRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Through != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Through))
		i--
		dAtA[i] = 0x18
	}
	if m.From != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x10
	}
	if len(m.MetricName) > 0 {
		i -= len(m.MetricName)
		copy(dAtA[i:], m.MetricName)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.MetricName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LabelResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintGateway(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetChunkRefRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChunkRefRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetChunkRefRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		i -= len(m.Matchers)
		copy(dAtA[i:], m.Matchers)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.Matchers)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Through != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Through))
		i--
		dAtA[i] = 0x10
	}
	if m.From != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetChunkRefResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChunkRefResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetChunkRefResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Refs) > 0 {
		for iNdEx := len(m.Refs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Refs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGateway(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetSeriesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSeriesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetSeriesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		i -= len(m.Matchers)
		copy(dAtA[i:], m.Matchers)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.Matchers)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Through != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Through))
		i--
		dAtA[i] = 0x10
	}
	if m.From != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetSeriesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSeriesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetSeriesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Series) > 0 {
		for iNdEx := len(m.Series) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Series[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGateway(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Series) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Series) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Series) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintGateway(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *QueryIndexResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryIndexResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryIndexResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGateway(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.QueryKey) > 0 {
		i -= len(m.QueryKey)
		copy(dAtA[i:], m.QueryKey)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.QueryKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Row) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Row) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Row) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RangeValue) > 0 {
		i -= len(m.RangeValue)
		copy(dAtA[i:], m.RangeValue)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.RangeValue)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *QueryIndexRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryIndexRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryIndexRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Queries) > 0 {
		for iNdEx := len(m.Queries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Queries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGateway(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *IndexQuery) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexQuery) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IndexQuery) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ValueEqual) > 0 {
		i -= len(m.ValueEqual)
		copy(dAtA[i:], m.ValueEqual)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.ValueEqual)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.RangeValueStart) > 0 {
		i -= len(m.RangeValueStart)
		copy(dAtA[i:], m.RangeValueStart)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.RangeValueStart)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RangeValuePrefix) > 0 {
		i -= len(m.RangeValuePrefix)
		copy(dAtA[i:], m.RangeValuePrefix)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.RangeValuePrefix)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.HashValue) > 0 {
		i -= len(m.HashValue)
		copy(dAtA[i:], m.HashValue)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.HashValue)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IndexStatsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexStatsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IndexStatsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		i -= len(m.Matchers)
		copy(dAtA[i:], m.Matchers)
		i = encodeVarintGateway(dAtA, i, uint64(len(m.Matchers)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Through != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Through))
		i--
		dAtA[i] = 0x10
	}
	if m.From != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *IndexStatsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexStatsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IndexStatsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Entries != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Entries))
		i--
		dAtA[i] = 0x20
	}
	if m.Bytes != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Bytes))
		i--
		dAtA[i] = 0x18
	}
	if m.Chunks != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Chunks))
		i--
		dAtA[i] = 0x10
	}
	if m.Streams != 0 {
		i = encodeVarintGateway(dAtA, i, uint64(m.Streams))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintGateway(dAtA []byte, offset int, v uint64) int {
	offset -= sovGateway(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *LabelValuesForMetricNameRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.MetricName)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	l = len(m.LabelName)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	if m.From != 0 {
		n += 1 + sovGateway(uint64(m.From))
	}
	if m.Through != 0 {
		n += 1 + sovGateway(uint64(m.Through))
	}
	l = len(m.Matchers)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	return n
}

func (m *LabelNamesForMetricNameRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.MetricName)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	if m.From != 0 {
		n += 1 + sovGateway(uint64(m.From))
	}
	if m.Through != 0 {
		n += 1 + sovGateway(uint64(m.Through))
	}
	return n
}

func (m *LabelResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovGateway(uint64(l))
		}
	}
	return n
}

func (m *GetChunkRefRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.From != 0 {
		n += 1 + sovGateway(uint64(m.From))
	}
	if m.Through != 0 {
		n += 1 + sovGateway(uint64(m.Through))
	}
	l = len(m.Matchers)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	return n
}

func (m *GetChunkRefResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Refs) > 0 {
		for _, e := range m.Refs {
			l = e.Size()
			n += 1 + l + sovGateway(uint64(l))
		}
	}
	return n
}

func (m *GetSeriesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.From != 0 {
		n += 1 + sovGateway(uint64(m.From))
	}
	if m.Through != 0 {
		n += 1 + sovGateway(uint64(m.Through))
	}
	l = len(m.Matchers)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	return n
}

func (m *GetSeriesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Series) > 0 {
		for _, e := range m.Series {
			l = e.Size()
			n += 1 + l + sovGateway(uint64(l))
		}
	}
	return n
}

func (m *Series) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovGateway(uint64(l))
		}
	}
	return n
}

func (m *QueryIndexResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.QueryKey)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovGateway(uint64(l))
		}
	}
	return n
}

func (m *Row) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RangeValue)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	return n
}

func (m *QueryIndexRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Queries) > 0 {
		for _, e := range m.Queries {
			l = e.Size()
			n += 1 + l + sovGateway(uint64(l))
		}
	}
	return n
}

func (m *IndexQuery) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	l = len(m.HashValue)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	l = len(m.RangeValuePrefix)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	l = len(m.RangeValueStart)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	l = len(m.ValueEqual)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	return n
}

func (m *IndexStatsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.From != 0 {
		n += 1 + sovGateway(uint64(m.From))
	}
	if m.Through != 0 {
		n += 1 + sovGateway(uint64(m.Through))
	}
	l = len(m.Matchers)
	if l > 0 {
		n += 1 + l + sovGateway(uint64(l))
	}
	return n
}

func (m *IndexStatsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Streams != 0 {
		n += 1 + sovGateway(uint64(m.Streams))
	}
	if m.Chunks != 0 {
		n += 1 + sovGateway(uint64(m.Chunks))
	}
	if m.Bytes != 0 {
		n += 1 + sovGateway(uint64(m.Bytes))
	}
	if m.Entries != 0 {
		n += 1 + sovGateway(uint64(m.Entries))
	}
	return n
}

func sovGateway(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGateway(x uint64) (n int) {
	return sovGateway(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *LabelValuesForMetricNameRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelValuesForMetricNameRequest{`,
		`MetricName:` + fmt.Sprintf("%v", this.MetricName) + `,`,
		`LabelName:` + fmt.Sprintf("%v", this.LabelName) + `,`,
		`From:` + fmt.Sprintf("%v", this.From) + `,`,
		`Through:` + fmt.Sprintf("%v", this.Through) + `,`,
		`Matchers:` + fmt.Sprintf("%v", this.Matchers) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelNamesForMetricNameRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelNamesForMetricNameRequest{`,
		`MetricName:` + fmt.Sprintf("%v", this.MetricName) + `,`,
		`From:` + fmt.Sprintf("%v", this.From) + `,`,
		`Through:` + fmt.Sprintf("%v", this.Through) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelResponse{`,
		`Values:` + fmt.Sprintf("%v", this.Values) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetChunkRefRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetChunkRefRequest{`,
		`From:` + fmt.Sprintf("%v", this.From) + `,`,
		`Through:` + fmt.Sprintf("%v", this.Through) + `,`,
		`Matchers:` + fmt.Sprintf("%v", this.Matchers) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetChunkRefResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRefs := "[]*ChunkRef{"
	for _, f := range this.Refs {
		repeatedStringForRefs += strings.Replace(fmt.Sprintf("%v", f), "ChunkRef", "logproto.ChunkRef", 1) + ","
	}
	repeatedStringForRefs += "}"
	s := strings.Join([]string{`&GetChunkRefResponse{`,
		`Refs:` + repeatedStringForRefs + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetSeriesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetSeriesRequest{`,
		`From:` + fmt.Sprintf("%v", this.From) + `,`,
		`Through:` + fmt.Sprintf("%v", this.Through) + `,`,
		`Matchers:` + fmt.Sprintf("%v", this.Matchers) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetSeriesResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForSeries := "[]Series{"
	for _, f := range this.Series {
		repeatedStringForSeries += strings.Replace(strings.Replace(f.String(), "Series", "Series", 1), `&`, ``, 1) + ","
	}
	repeatedStringForSeries += "}"
	s := strings.Join([]string{`&GetSeriesResponse{`,
		`Series:` + repeatedStringForSeries + `,`,
		`}`,
	}, "")
	return s
}
func (this *Series) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Series{`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryIndexResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRows := "[]*Row{"
	for _, f := range this.Rows {
		repeatedStringForRows += strings.Replace(f.String(), "Row", "Row", 1) + ","
	}
	repeatedStringForRows += "}"
	s := strings.Join([]string{`&QueryIndexResponse{`,
		`QueryKey:` + fmt.Sprintf("%v", this.QueryKey) + `,`,
		`Rows:` + repeatedStringForRows + `,`,
		`}`,
	}, "")
	return s
}
func (this *Row) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Row{`,
		`RangeValue:` + fmt.Sprintf("%v", this.RangeValue) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryIndexRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForQueries := "[]*IndexQuery{"
	for _, f := range this.Queries {
		repeatedStringForQueries += strings.Replace(f.String(), "IndexQuery", "IndexQuery", 1) + ","
	}
	repeatedStringForQueries += "}"
	s := strings.Join([]string{`&QueryIndexRequest{`,
		`Queries:` + repeatedStringForQueries + `,`,
		`}`,
	}, "")
	return s
}
func (this *IndexQuery) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IndexQuery{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`HashValue:` + fmt.Sprintf("%v", this.HashValue) + `,`,
		`RangeValuePrefix:` + fmt.Sprintf("%v", this.RangeValuePrefix) + `,`,
		`RangeValueStart:` + fmt.Sprintf("%v", this.RangeValueStart) + `,`,
		`ValueEqual:` + fmt.Sprintf("%v", this.ValueEqual) + `,`,
		`}`,
	}, "")
	return s
}
func (this *IndexStatsRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IndexStatsRequest{`,
		`From:` + fmt.Sprintf("%v", this.From) + `,`,
		`Through:` + fmt.Sprintf("%v", this.Through) + `,`,
		`Matchers:` + fmt.Sprintf("%v", this.Matchers) + `,`,
		`}`,
	}, "")
	return s
}
func (this *IndexStatsResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IndexStatsResponse{`,
		`Streams:` + fmt.Sprintf("%v", this.Streams) + `,`,
		`Chunks:` + fmt.Sprintf("%v", this.Chunks) + `,`,
		`Bytes:` + fmt.Sprintf("%v", this.Bytes) + `,`,
		`Entries:` + fmt.Sprintf("%v", this.Entries) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGateway(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *LabelValuesForMetricNameRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelValuesForMetricNameRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelValuesForMetricNameRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetricName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MetricName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LabelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Through", wireType)
			}
			m.Through = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Through |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelNamesForMetricNameRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelNamesForMetricNameRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelNamesForMetricNameRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetricName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MetricName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Through", wireType)
			}
			m.Through = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Through |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChunkRefRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetChunkRefRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetChunkRefRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Through", wireType)
			}
			m.Through = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Through |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChunkRefResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetChunkRefResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetChunkRefResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Refs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Refs = append(m.Refs, &logproto.ChunkRef{})
			if err := m.Refs[len(m.Refs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSeriesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetSeriesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetSeriesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Through", wireType)
			}
			m.Through = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Through |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSeriesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetSeriesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetSeriesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Series", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Series = append(m.Series, Series{})
			if err := m.Series[len(m.Series)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Series) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Series: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Series: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, github_com_grafana_loki_pkg_logproto.LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryIndexResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryIndexResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryIndexResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.QueryKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &Row{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Row) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Row: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Row: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValue", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValue = append(m.RangeValue[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValue == nil {
				m.RangeValue = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryIndexRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryIndexRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryIndexRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Queries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Queries = append(m.Queries, &IndexQuery{})
			if err := m.Queries[len(m.Queries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IndexQuery) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IndexQuery: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IndexQuery: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HashValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HashValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValuePrefix", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValuePrefix = append(m.RangeValuePrefix[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValuePrefix == nil {
				m.RangeValuePrefix = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValueStart", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValueStart = append(m.RangeValueStart[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValueStart == nil {
				m.RangeValueStart = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValueEqual", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ValueEqual = append(m.ValueEqual[:0], dAtA[iNdEx:postIndex]...)
			if m.ValueEqual == nil {
				m.ValueEqual = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IndexStatsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IndexStatsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IndexStatsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Through", wireType)
			}
			m.Through = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Through |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGateway
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGateway
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IndexStatsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IndexStatsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IndexStatsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Streams", wireType)
			}
			m.Streams = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Streams |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			m.Chunks = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chunks |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bytes", wireType)
			}
			m.Bytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Bytes |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			m.Entries = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Entries |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGateway(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGateway
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGateway(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGateway
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGateway
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGateway
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthGateway
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGateway
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGateway(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthGateway
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGateway = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGateway   = fmt.Errorf("proto: integer overflow")
)
