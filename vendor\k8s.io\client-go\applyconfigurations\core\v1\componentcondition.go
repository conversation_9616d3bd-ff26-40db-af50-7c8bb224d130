/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	v1 "k8s.io/api/core/v1"
)

// ComponentConditionApplyConfiguration represents an declarative configuration of the ComponentCondition type for use
// with apply.
type ComponentConditionApplyConfiguration struct {
	Type    *v1.ComponentConditionType `json:"type,omitempty"`
	Status  *v1.ConditionStatus        `json:"status,omitempty"`
	Message *string                    `json:"message,omitempty"`
	Error   *string                    `json:"error,omitempty"`
}

// ComponentConditionApplyConfiguration constructs an declarative configuration of the ComponentCondition type for use with
// apply.
func ComponentCondition() *ComponentConditionApplyConfiguration {
	return &ComponentConditionApplyConfiguration{}
}

// WithType sets the Type field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Type field is set to the value of the last call.
func (b *ComponentConditionApplyConfiguration) WithType(value v1.ComponentConditionType) *ComponentConditionApplyConfiguration {
	b.Type = &value
	return b
}

// WithStatus sets the Status field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Status field is set to the value of the last call.
func (b *ComponentConditionApplyConfiguration) WithStatus(value v1.ConditionStatus) *ComponentConditionApplyConfiguration {
	b.Status = &value
	return b
}

// WithMessage sets the Message field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Message field is set to the value of the last call.
func (b *ComponentConditionApplyConfiguration) WithMessage(value string) *ComponentConditionApplyConfiguration {
	b.Message = &value
	return b
}

// WithError sets the Error field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Error field is set to the value of the last call.
func (b *ComponentConditionApplyConfiguration) WithError(value string) *ComponentConditionApplyConfiguration {
	b.Error = &value
	return b
}
