// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/bigtable/v2/data.proto

package bigtable

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Specifies the complete (requested) contents of a single row of a table.
// Rows which exceed 256MiB in size cannot be read in full.
type Row struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique key which identifies this row within its table. This is the same
	// key that's used to identify the row in, for example, a MutateRowRequest.
	// May contain any non-empty byte string up to 4KiB in length.
	Key []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// May be empty, but only if the entire row is empty.
	// The mutual ordering of column families is not specified.
	Families []*Family `protobuf:"bytes,2,rep,name=families,proto3" json:"families,omitempty"`
}

func (x *Row) Reset() {
	*x = Row{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Row) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Row) ProtoMessage() {}

func (x *Row) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Row.ProtoReflect.Descriptor instead.
func (*Row) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{0}
}

func (x *Row) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Row) GetFamilies() []*Family {
	if x != nil {
		return x.Families
	}
	return nil
}

// Specifies (some of) the contents of a single row/column family intersection
// of a table.
type Family struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique key which identifies this family within its row. This is the
	// same key that's used to identify the family in, for example, a RowFilter
	// which sets its "family_name_regex_filter" field.
	// Must match `[-_.a-zA-Z0-9]+`, except that AggregatingRowProcessors may
	// produce cells in a sentinel family with an empty name.
	// Must be no greater than 64 characters in length.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Must not be empty. Sorted in order of increasing "qualifier".
	Columns []*Column `protobuf:"bytes,2,rep,name=columns,proto3" json:"columns,omitempty"`
}

func (x *Family) Reset() {
	*x = Family{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Family) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Family) ProtoMessage() {}

func (x *Family) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Family.ProtoReflect.Descriptor instead.
func (*Family) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{1}
}

func (x *Family) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Family) GetColumns() []*Column {
	if x != nil {
		return x.Columns
	}
	return nil
}

// Specifies (some of) the contents of a single row/column intersection of a
// table.
type Column struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique key which identifies this column within its family. This is the
	// same key that's used to identify the column in, for example, a RowFilter
	// which sets its `column_qualifier_regex_filter` field.
	// May contain any byte string, including the empty string, up to 16kiB in
	// length.
	Qualifier []byte `protobuf:"bytes,1,opt,name=qualifier,proto3" json:"qualifier,omitempty"`
	// Must not be empty. Sorted in order of decreasing "timestamp_micros".
	Cells []*Cell `protobuf:"bytes,2,rep,name=cells,proto3" json:"cells,omitempty"`
}

func (x *Column) Reset() {
	*x = Column{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Column) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Column) ProtoMessage() {}

func (x *Column) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Column.ProtoReflect.Descriptor instead.
func (*Column) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{2}
}

func (x *Column) GetQualifier() []byte {
	if x != nil {
		return x.Qualifier
	}
	return nil
}

func (x *Column) GetCells() []*Cell {
	if x != nil {
		return x.Cells
	}
	return nil
}

// Specifies (some of) the contents of a single row/column/timestamp of a table.
type Cell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The cell's stored timestamp, which also uniquely identifies it within
	// its column.
	// Values are always expressed in microseconds, but individual tables may set
	// a coarser granularity to further restrict the allowed values. For
	// example, a table which specifies millisecond granularity will only allow
	// values of `timestamp_micros` which are multiples of 1000.
	TimestampMicros int64 `protobuf:"varint,1,opt,name=timestamp_micros,json=timestampMicros,proto3" json:"timestamp_micros,omitempty"`
	// The value stored in the cell.
	// May contain any byte string, including the empty string, up to 100MiB in
	// length.
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Labels applied to the cell by a [RowFilter][google.bigtable.v2.RowFilter].
	Labels []string `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *Cell) Reset() {
	*x = Cell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cell) ProtoMessage() {}

func (x *Cell) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cell.ProtoReflect.Descriptor instead.
func (*Cell) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{3}
}

func (x *Cell) GetTimestampMicros() int64 {
	if x != nil {
		return x.TimestampMicros
	}
	return 0
}

func (x *Cell) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Cell) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Specifies a contiguous range of rows.
type RowRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The row key at which to start the range.
	// If neither field is set, interpreted as the empty string, inclusive.
	//
	// Types that are assignable to StartKey:
	//	*RowRange_StartKeyClosed
	//	*RowRange_StartKeyOpen
	StartKey isRowRange_StartKey `protobuf_oneof:"start_key"`
	// The row key at which to end the range.
	// If neither field is set, interpreted as the infinite row key, exclusive.
	//
	// Types that are assignable to EndKey:
	//	*RowRange_EndKeyOpen
	//	*RowRange_EndKeyClosed
	EndKey isRowRange_EndKey `protobuf_oneof:"end_key"`
}

func (x *RowRange) Reset() {
	*x = RowRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowRange) ProtoMessage() {}

func (x *RowRange) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowRange.ProtoReflect.Descriptor instead.
func (*RowRange) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{4}
}

func (m *RowRange) GetStartKey() isRowRange_StartKey {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (x *RowRange) GetStartKeyClosed() []byte {
	if x, ok := x.GetStartKey().(*RowRange_StartKeyClosed); ok {
		return x.StartKeyClosed
	}
	return nil
}

func (x *RowRange) GetStartKeyOpen() []byte {
	if x, ok := x.GetStartKey().(*RowRange_StartKeyOpen); ok {
		return x.StartKeyOpen
	}
	return nil
}

func (m *RowRange) GetEndKey() isRowRange_EndKey {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (x *RowRange) GetEndKeyOpen() []byte {
	if x, ok := x.GetEndKey().(*RowRange_EndKeyOpen); ok {
		return x.EndKeyOpen
	}
	return nil
}

func (x *RowRange) GetEndKeyClosed() []byte {
	if x, ok := x.GetEndKey().(*RowRange_EndKeyClosed); ok {
		return x.EndKeyClosed
	}
	return nil
}

type isRowRange_StartKey interface {
	isRowRange_StartKey()
}

type RowRange_StartKeyClosed struct {
	// Used when giving an inclusive lower bound for the range.
	StartKeyClosed []byte `protobuf:"bytes,1,opt,name=start_key_closed,json=startKeyClosed,proto3,oneof"`
}

type RowRange_StartKeyOpen struct {
	// Used when giving an exclusive lower bound for the range.
	StartKeyOpen []byte `protobuf:"bytes,2,opt,name=start_key_open,json=startKeyOpen,proto3,oneof"`
}

func (*RowRange_StartKeyClosed) isRowRange_StartKey() {}

func (*RowRange_StartKeyOpen) isRowRange_StartKey() {}

type isRowRange_EndKey interface {
	isRowRange_EndKey()
}

type RowRange_EndKeyOpen struct {
	// Used when giving an exclusive upper bound for the range.
	EndKeyOpen []byte `protobuf:"bytes,3,opt,name=end_key_open,json=endKeyOpen,proto3,oneof"`
}

type RowRange_EndKeyClosed struct {
	// Used when giving an inclusive upper bound for the range.
	EndKeyClosed []byte `protobuf:"bytes,4,opt,name=end_key_closed,json=endKeyClosed,proto3,oneof"`
}

func (*RowRange_EndKeyOpen) isRowRange_EndKey() {}

func (*RowRange_EndKeyClosed) isRowRange_EndKey() {}

// Specifies a non-contiguous set of rows.
type RowSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Single rows included in the set.
	RowKeys [][]byte `protobuf:"bytes,1,rep,name=row_keys,json=rowKeys,proto3" json:"row_keys,omitempty"`
	// Contiguous row ranges included in the set.
	RowRanges []*RowRange `protobuf:"bytes,2,rep,name=row_ranges,json=rowRanges,proto3" json:"row_ranges,omitempty"`
}

func (x *RowSet) Reset() {
	*x = RowSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowSet) ProtoMessage() {}

func (x *RowSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowSet.ProtoReflect.Descriptor instead.
func (*RowSet) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{5}
}

func (x *RowSet) GetRowKeys() [][]byte {
	if x != nil {
		return x.RowKeys
	}
	return nil
}

func (x *RowSet) GetRowRanges() []*RowRange {
	if x != nil {
		return x.RowRanges
	}
	return nil
}

// Specifies a contiguous range of columns within a single column family.
// The range spans from &lt;column_family&gt;:&lt;start_qualifier&gt; to
// &lt;column_family&gt;:&lt;end_qualifier&gt;, where both bounds can be either
// inclusive or exclusive.
type ColumnRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the column family within which this range falls.
	FamilyName string `protobuf:"bytes,1,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// The column qualifier at which to start the range (within `column_family`).
	// If neither field is set, interpreted as the empty string, inclusive.
	//
	// Types that are assignable to StartQualifier:
	//	*ColumnRange_StartQualifierClosed
	//	*ColumnRange_StartQualifierOpen
	StartQualifier isColumnRange_StartQualifier `protobuf_oneof:"start_qualifier"`
	// The column qualifier at which to end the range (within `column_family`).
	// If neither field is set, interpreted as the infinite string, exclusive.
	//
	// Types that are assignable to EndQualifier:
	//	*ColumnRange_EndQualifierClosed
	//	*ColumnRange_EndQualifierOpen
	EndQualifier isColumnRange_EndQualifier `protobuf_oneof:"end_qualifier"`
}

func (x *ColumnRange) Reset() {
	*x = ColumnRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ColumnRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ColumnRange) ProtoMessage() {}

func (x *ColumnRange) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ColumnRange.ProtoReflect.Descriptor instead.
func (*ColumnRange) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{6}
}

func (x *ColumnRange) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (m *ColumnRange) GetStartQualifier() isColumnRange_StartQualifier {
	if m != nil {
		return m.StartQualifier
	}
	return nil
}

func (x *ColumnRange) GetStartQualifierClosed() []byte {
	if x, ok := x.GetStartQualifier().(*ColumnRange_StartQualifierClosed); ok {
		return x.StartQualifierClosed
	}
	return nil
}

func (x *ColumnRange) GetStartQualifierOpen() []byte {
	if x, ok := x.GetStartQualifier().(*ColumnRange_StartQualifierOpen); ok {
		return x.StartQualifierOpen
	}
	return nil
}

func (m *ColumnRange) GetEndQualifier() isColumnRange_EndQualifier {
	if m != nil {
		return m.EndQualifier
	}
	return nil
}

func (x *ColumnRange) GetEndQualifierClosed() []byte {
	if x, ok := x.GetEndQualifier().(*ColumnRange_EndQualifierClosed); ok {
		return x.EndQualifierClosed
	}
	return nil
}

func (x *ColumnRange) GetEndQualifierOpen() []byte {
	if x, ok := x.GetEndQualifier().(*ColumnRange_EndQualifierOpen); ok {
		return x.EndQualifierOpen
	}
	return nil
}

type isColumnRange_StartQualifier interface {
	isColumnRange_StartQualifier()
}

type ColumnRange_StartQualifierClosed struct {
	// Used when giving an inclusive lower bound for the range.
	StartQualifierClosed []byte `protobuf:"bytes,2,opt,name=start_qualifier_closed,json=startQualifierClosed,proto3,oneof"`
}

type ColumnRange_StartQualifierOpen struct {
	// Used when giving an exclusive lower bound for the range.
	StartQualifierOpen []byte `protobuf:"bytes,3,opt,name=start_qualifier_open,json=startQualifierOpen,proto3,oneof"`
}

func (*ColumnRange_StartQualifierClosed) isColumnRange_StartQualifier() {}

func (*ColumnRange_StartQualifierOpen) isColumnRange_StartQualifier() {}

type isColumnRange_EndQualifier interface {
	isColumnRange_EndQualifier()
}

type ColumnRange_EndQualifierClosed struct {
	// Used when giving an inclusive upper bound for the range.
	EndQualifierClosed []byte `protobuf:"bytes,4,opt,name=end_qualifier_closed,json=endQualifierClosed,proto3,oneof"`
}

type ColumnRange_EndQualifierOpen struct {
	// Used when giving an exclusive upper bound for the range.
	EndQualifierOpen []byte `protobuf:"bytes,5,opt,name=end_qualifier_open,json=endQualifierOpen,proto3,oneof"`
}

func (*ColumnRange_EndQualifierClosed) isColumnRange_EndQualifier() {}

func (*ColumnRange_EndQualifierOpen) isColumnRange_EndQualifier() {}

// Specified a contiguous range of microsecond timestamps.
type TimestampRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Inclusive lower bound. If left empty, interpreted as 0.
	StartTimestampMicros int64 `protobuf:"varint,1,opt,name=start_timestamp_micros,json=startTimestampMicros,proto3" json:"start_timestamp_micros,omitempty"`
	// Exclusive upper bound. If left empty, interpreted as infinity.
	EndTimestampMicros int64 `protobuf:"varint,2,opt,name=end_timestamp_micros,json=endTimestampMicros,proto3" json:"end_timestamp_micros,omitempty"`
}

func (x *TimestampRange) Reset() {
	*x = TimestampRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimestampRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimestampRange) ProtoMessage() {}

func (x *TimestampRange) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimestampRange.ProtoReflect.Descriptor instead.
func (*TimestampRange) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{7}
}

func (x *TimestampRange) GetStartTimestampMicros() int64 {
	if x != nil {
		return x.StartTimestampMicros
	}
	return 0
}

func (x *TimestampRange) GetEndTimestampMicros() int64 {
	if x != nil {
		return x.EndTimestampMicros
	}
	return 0
}

// Specifies a contiguous range of raw byte values.
type ValueRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The value at which to start the range.
	// If neither field is set, interpreted as the empty string, inclusive.
	//
	// Types that are assignable to StartValue:
	//	*ValueRange_StartValueClosed
	//	*ValueRange_StartValueOpen
	StartValue isValueRange_StartValue `protobuf_oneof:"start_value"`
	// The value at which to end the range.
	// If neither field is set, interpreted as the infinite string, exclusive.
	//
	// Types that are assignable to EndValue:
	//	*ValueRange_EndValueClosed
	//	*ValueRange_EndValueOpen
	EndValue isValueRange_EndValue `protobuf_oneof:"end_value"`
}

func (x *ValueRange) Reset() {
	*x = ValueRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueRange) ProtoMessage() {}

func (x *ValueRange) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueRange.ProtoReflect.Descriptor instead.
func (*ValueRange) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{8}
}

func (m *ValueRange) GetStartValue() isValueRange_StartValue {
	if m != nil {
		return m.StartValue
	}
	return nil
}

func (x *ValueRange) GetStartValueClosed() []byte {
	if x, ok := x.GetStartValue().(*ValueRange_StartValueClosed); ok {
		return x.StartValueClosed
	}
	return nil
}

func (x *ValueRange) GetStartValueOpen() []byte {
	if x, ok := x.GetStartValue().(*ValueRange_StartValueOpen); ok {
		return x.StartValueOpen
	}
	return nil
}

func (m *ValueRange) GetEndValue() isValueRange_EndValue {
	if m != nil {
		return m.EndValue
	}
	return nil
}

func (x *ValueRange) GetEndValueClosed() []byte {
	if x, ok := x.GetEndValue().(*ValueRange_EndValueClosed); ok {
		return x.EndValueClosed
	}
	return nil
}

func (x *ValueRange) GetEndValueOpen() []byte {
	if x, ok := x.GetEndValue().(*ValueRange_EndValueOpen); ok {
		return x.EndValueOpen
	}
	return nil
}

type isValueRange_StartValue interface {
	isValueRange_StartValue()
}

type ValueRange_StartValueClosed struct {
	// Used when giving an inclusive lower bound for the range.
	StartValueClosed []byte `protobuf:"bytes,1,opt,name=start_value_closed,json=startValueClosed,proto3,oneof"`
}

type ValueRange_StartValueOpen struct {
	// Used when giving an exclusive lower bound for the range.
	StartValueOpen []byte `protobuf:"bytes,2,opt,name=start_value_open,json=startValueOpen,proto3,oneof"`
}

func (*ValueRange_StartValueClosed) isValueRange_StartValue() {}

func (*ValueRange_StartValueOpen) isValueRange_StartValue() {}

type isValueRange_EndValue interface {
	isValueRange_EndValue()
}

type ValueRange_EndValueClosed struct {
	// Used when giving an inclusive upper bound for the range.
	EndValueClosed []byte `protobuf:"bytes,3,opt,name=end_value_closed,json=endValueClosed,proto3,oneof"`
}

type ValueRange_EndValueOpen struct {
	// Used when giving an exclusive upper bound for the range.
	EndValueOpen []byte `protobuf:"bytes,4,opt,name=end_value_open,json=endValueOpen,proto3,oneof"`
}

func (*ValueRange_EndValueClosed) isValueRange_EndValue() {}

func (*ValueRange_EndValueOpen) isValueRange_EndValue() {}

// Takes a row as input and produces an alternate view of the row based on
// specified rules. For example, a RowFilter might trim down a row to include
// just the cells from columns matching a given regular expression, or might
// return all the cells of a row but not their values. More complicated filters
// can be composed out of these components to express requests such as, "within
// every column of a particular family, give just the two most recent cells
// which are older than timestamp X."
//
// There are two broad categories of RowFilters (true filters and transformers),
// as well as two ways to compose simple filters into more complex ones
// (chains and interleaves). They work as follows:
//
// * True filters alter the input row by excluding some of its cells wholesale
// from the output row. An example of a true filter is the `value_regex_filter`,
// which excludes cells whose values don't match the specified pattern. All
// regex true filters use RE2 syntax (https://github.com/google/re2/wiki/Syntax)
// in raw byte mode (RE2::Latin1), and are evaluated as full matches. An
// important point to keep in mind is that `RE2(.)` is equivalent by default to
// `RE2([^\n])`, meaning that it does not match newlines. When attempting to
// match an arbitrary byte, you should therefore use the escape sequence `\C`,
// which may need to be further escaped as `\\C` in your client language.
//
// * Transformers alter the input row by changing the values of some of its
// cells in the output, without excluding them completely. Currently, the only
// supported transformer is the `strip_value_transformer`, which replaces every
// cell's value with the empty string.
//
// * Chains and interleaves are described in more detail in the
// RowFilter.Chain and RowFilter.Interleave documentation.
//
// The total serialized size of a RowFilter message must not
// exceed 20480 bytes, and RowFilters may not be nested within each other
// (in Chains or Interleaves) to a depth of more than 20.
type RowFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Which of the possible RowFilter types to apply. If none are set, this
	// RowFilter returns all cells in the input row.
	//
	// Types that are assignable to Filter:
	//	*RowFilter_Chain_
	//	*RowFilter_Interleave_
	//	*RowFilter_Condition_
	//	*RowFilter_Sink
	//	*RowFilter_PassAllFilter
	//	*RowFilter_BlockAllFilter
	//	*RowFilter_RowKeyRegexFilter
	//	*RowFilter_RowSampleFilter
	//	*RowFilter_FamilyNameRegexFilter
	//	*RowFilter_ColumnQualifierRegexFilter
	//	*RowFilter_ColumnRangeFilter
	//	*RowFilter_TimestampRangeFilter
	//	*RowFilter_ValueRegexFilter
	//	*RowFilter_ValueRangeFilter
	//	*RowFilter_CellsPerRowOffsetFilter
	//	*RowFilter_CellsPerRowLimitFilter
	//	*RowFilter_CellsPerColumnLimitFilter
	//	*RowFilter_StripValueTransformer
	//	*RowFilter_ApplyLabelTransformer
	Filter isRowFilter_Filter `protobuf_oneof:"filter"`
}

func (x *RowFilter) Reset() {
	*x = RowFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowFilter) ProtoMessage() {}

func (x *RowFilter) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowFilter.ProtoReflect.Descriptor instead.
func (*RowFilter) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{9}
}

func (m *RowFilter) GetFilter() isRowFilter_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (x *RowFilter) GetChain() *RowFilter_Chain {
	if x, ok := x.GetFilter().(*RowFilter_Chain_); ok {
		return x.Chain
	}
	return nil
}

func (x *RowFilter) GetInterleave() *RowFilter_Interleave {
	if x, ok := x.GetFilter().(*RowFilter_Interleave_); ok {
		return x.Interleave
	}
	return nil
}

func (x *RowFilter) GetCondition() *RowFilter_Condition {
	if x, ok := x.GetFilter().(*RowFilter_Condition_); ok {
		return x.Condition
	}
	return nil
}

func (x *RowFilter) GetSink() bool {
	if x, ok := x.GetFilter().(*RowFilter_Sink); ok {
		return x.Sink
	}
	return false
}

func (x *RowFilter) GetPassAllFilter() bool {
	if x, ok := x.GetFilter().(*RowFilter_PassAllFilter); ok {
		return x.PassAllFilter
	}
	return false
}

func (x *RowFilter) GetBlockAllFilter() bool {
	if x, ok := x.GetFilter().(*RowFilter_BlockAllFilter); ok {
		return x.BlockAllFilter
	}
	return false
}

func (x *RowFilter) GetRowKeyRegexFilter() []byte {
	if x, ok := x.GetFilter().(*RowFilter_RowKeyRegexFilter); ok {
		return x.RowKeyRegexFilter
	}
	return nil
}

func (x *RowFilter) GetRowSampleFilter() float64 {
	if x, ok := x.GetFilter().(*RowFilter_RowSampleFilter); ok {
		return x.RowSampleFilter
	}
	return 0
}

func (x *RowFilter) GetFamilyNameRegexFilter() string {
	if x, ok := x.GetFilter().(*RowFilter_FamilyNameRegexFilter); ok {
		return x.FamilyNameRegexFilter
	}
	return ""
}

func (x *RowFilter) GetColumnQualifierRegexFilter() []byte {
	if x, ok := x.GetFilter().(*RowFilter_ColumnQualifierRegexFilter); ok {
		return x.ColumnQualifierRegexFilter
	}
	return nil
}

func (x *RowFilter) GetColumnRangeFilter() *ColumnRange {
	if x, ok := x.GetFilter().(*RowFilter_ColumnRangeFilter); ok {
		return x.ColumnRangeFilter
	}
	return nil
}

func (x *RowFilter) GetTimestampRangeFilter() *TimestampRange {
	if x, ok := x.GetFilter().(*RowFilter_TimestampRangeFilter); ok {
		return x.TimestampRangeFilter
	}
	return nil
}

func (x *RowFilter) GetValueRegexFilter() []byte {
	if x, ok := x.GetFilter().(*RowFilter_ValueRegexFilter); ok {
		return x.ValueRegexFilter
	}
	return nil
}

func (x *RowFilter) GetValueRangeFilter() *ValueRange {
	if x, ok := x.GetFilter().(*RowFilter_ValueRangeFilter); ok {
		return x.ValueRangeFilter
	}
	return nil
}

func (x *RowFilter) GetCellsPerRowOffsetFilter() int32 {
	if x, ok := x.GetFilter().(*RowFilter_CellsPerRowOffsetFilter); ok {
		return x.CellsPerRowOffsetFilter
	}
	return 0
}

func (x *RowFilter) GetCellsPerRowLimitFilter() int32 {
	if x, ok := x.GetFilter().(*RowFilter_CellsPerRowLimitFilter); ok {
		return x.CellsPerRowLimitFilter
	}
	return 0
}

func (x *RowFilter) GetCellsPerColumnLimitFilter() int32 {
	if x, ok := x.GetFilter().(*RowFilter_CellsPerColumnLimitFilter); ok {
		return x.CellsPerColumnLimitFilter
	}
	return 0
}

func (x *RowFilter) GetStripValueTransformer() bool {
	if x, ok := x.GetFilter().(*RowFilter_StripValueTransformer); ok {
		return x.StripValueTransformer
	}
	return false
}

func (x *RowFilter) GetApplyLabelTransformer() string {
	if x, ok := x.GetFilter().(*RowFilter_ApplyLabelTransformer); ok {
		return x.ApplyLabelTransformer
	}
	return ""
}

type isRowFilter_Filter interface {
	isRowFilter_Filter()
}

type RowFilter_Chain_ struct {
	// Applies several RowFilters to the data in sequence, progressively
	// narrowing the results.
	Chain *RowFilter_Chain `protobuf:"bytes,1,opt,name=chain,proto3,oneof"`
}

type RowFilter_Interleave_ struct {
	// Applies several RowFilters to the data in parallel and combines the
	// results.
	Interleave *RowFilter_Interleave `protobuf:"bytes,2,opt,name=interleave,proto3,oneof"`
}

type RowFilter_Condition_ struct {
	// Applies one of two possible RowFilters to the data based on the output of
	// a predicate RowFilter.
	Condition *RowFilter_Condition `protobuf:"bytes,3,opt,name=condition,proto3,oneof"`
}

type RowFilter_Sink struct {
	// ADVANCED USE ONLY.
	// Hook for introspection into the RowFilter. Outputs all cells directly to
	// the output of the read rather than to any parent filter. Consider the
	// following example:
	//
	//     Chain(
	//       FamilyRegex("A"),
	//       Interleave(
	//         All(),
	//         Chain(Label("foo"), Sink())
	//       ),
	//       QualifierRegex("B")
	//     )
	//
	//                         A,A,1,w
	//                         A,B,2,x
	//                         B,B,4,z
	//                            |
	//                     FamilyRegex("A")
	//                            |
	//                         A,A,1,w
	//                         A,B,2,x
	//                            |
	//               +------------+-------------+
	//               |                          |
	//             All()                    Label(foo)
	//               |                          |
	//            A,A,1,w              A,A,1,w,labels:[foo]
	//            A,B,2,x              A,B,2,x,labels:[foo]
	//               |                          |
	//               |                        Sink() --------------+
	//               |                          |                  |
	//               +------------+      x------+          A,A,1,w,labels:[foo]
	//                            |                        A,B,2,x,labels:[foo]
	//                         A,A,1,w                             |
	//                         A,B,2,x                             |
	//                            |                                |
	//                    QualifierRegex("B")                      |
	//                            |                                |
	//                         A,B,2,x                             |
	//                            |                                |
	//                            +--------------------------------+
	//                            |
	//                         A,A,1,w,labels:[foo]
	//                         A,B,2,x,labels:[foo]  // could be switched
	//                         A,B,2,x               // could be switched
	//
	// Despite being excluded by the qualifier filter, a copy of every cell
	// that reaches the sink is present in the final result.
	//
	// As with an [Interleave][google.bigtable.v2.RowFilter.Interleave],
	// duplicate cells are possible, and appear in an unspecified mutual order.
	// In this case we have a duplicate with column "A:B" and timestamp 2,
	// because one copy passed through the all filter while the other was
	// passed through the label and sink. Note that one copy has label "foo",
	// while the other does not.
	//
	// Cannot be used within the `predicate_filter`, `true_filter`, or
	// `false_filter` of a [Condition][google.bigtable.v2.RowFilter.Condition].
	Sink bool `protobuf:"varint,16,opt,name=sink,proto3,oneof"`
}

type RowFilter_PassAllFilter struct {
	// Matches all cells, regardless of input. Functionally equivalent to
	// leaving `filter` unset, but included for completeness.
	PassAllFilter bool `protobuf:"varint,17,opt,name=pass_all_filter,json=passAllFilter,proto3,oneof"`
}

type RowFilter_BlockAllFilter struct {
	// Does not match any cells, regardless of input. Useful for temporarily
	// disabling just part of a filter.
	BlockAllFilter bool `protobuf:"varint,18,opt,name=block_all_filter,json=blockAllFilter,proto3,oneof"`
}

type RowFilter_RowKeyRegexFilter struct {
	// Matches only cells from rows whose keys satisfy the given RE2 regex. In
	// other words, passes through the entire row when the key matches, and
	// otherwise produces an empty row.
	// Note that, since row keys can contain arbitrary bytes, the `\C` escape
	// sequence must be used if a true wildcard is desired. The `.` character
	// will not match the new line character `\n`, which may be present in a
	// binary key.
	RowKeyRegexFilter []byte `protobuf:"bytes,4,opt,name=row_key_regex_filter,json=rowKeyRegexFilter,proto3,oneof"`
}

type RowFilter_RowSampleFilter struct {
	// Matches all cells from a row with probability p, and matches no cells
	// from the row with probability 1-p.
	RowSampleFilter float64 `protobuf:"fixed64,14,opt,name=row_sample_filter,json=rowSampleFilter,proto3,oneof"`
}

type RowFilter_FamilyNameRegexFilter struct {
	// Matches only cells from columns whose families satisfy the given RE2
	// regex. For technical reasons, the regex must not contain the `:`
	// character, even if it is not being used as a literal.
	// Note that, since column families cannot contain the new line character
	// `\n`, it is sufficient to use `.` as a full wildcard when matching
	// column family names.
	FamilyNameRegexFilter string `protobuf:"bytes,5,opt,name=family_name_regex_filter,json=familyNameRegexFilter,proto3,oneof"`
}

type RowFilter_ColumnQualifierRegexFilter struct {
	// Matches only cells from columns whose qualifiers satisfy the given RE2
	// regex.
	// Note that, since column qualifiers can contain arbitrary bytes, the `\C`
	// escape sequence must be used if a true wildcard is desired. The `.`
	// character will not match the new line character `\n`, which may be
	// present in a binary qualifier.
	ColumnQualifierRegexFilter []byte `protobuf:"bytes,6,opt,name=column_qualifier_regex_filter,json=columnQualifierRegexFilter,proto3,oneof"`
}

type RowFilter_ColumnRangeFilter struct {
	// Matches only cells from columns within the given range.
	ColumnRangeFilter *ColumnRange `protobuf:"bytes,7,opt,name=column_range_filter,json=columnRangeFilter,proto3,oneof"`
}

type RowFilter_TimestampRangeFilter struct {
	// Matches only cells with timestamps within the given range.
	TimestampRangeFilter *TimestampRange `protobuf:"bytes,8,opt,name=timestamp_range_filter,json=timestampRangeFilter,proto3,oneof"`
}

type RowFilter_ValueRegexFilter struct {
	// Matches only cells with values that satisfy the given regular expression.
	// Note that, since cell values can contain arbitrary bytes, the `\C` escape
	// sequence must be used if a true wildcard is desired. The `.` character
	// will not match the new line character `\n`, which may be present in a
	// binary value.
	ValueRegexFilter []byte `protobuf:"bytes,9,opt,name=value_regex_filter,json=valueRegexFilter,proto3,oneof"`
}

type RowFilter_ValueRangeFilter struct {
	// Matches only cells with values that fall within the given range.
	ValueRangeFilter *ValueRange `protobuf:"bytes,15,opt,name=value_range_filter,json=valueRangeFilter,proto3,oneof"`
}

type RowFilter_CellsPerRowOffsetFilter struct {
	// Skips the first N cells of each row, matching all subsequent cells.
	// If duplicate cells are present, as is possible when using an Interleave,
	// each copy of the cell is counted separately.
	CellsPerRowOffsetFilter int32 `protobuf:"varint,10,opt,name=cells_per_row_offset_filter,json=cellsPerRowOffsetFilter,proto3,oneof"`
}

type RowFilter_CellsPerRowLimitFilter struct {
	// Matches only the first N cells of each row.
	// If duplicate cells are present, as is possible when using an Interleave,
	// each copy of the cell is counted separately.
	CellsPerRowLimitFilter int32 `protobuf:"varint,11,opt,name=cells_per_row_limit_filter,json=cellsPerRowLimitFilter,proto3,oneof"`
}

type RowFilter_CellsPerColumnLimitFilter struct {
	// Matches only the most recent N cells within each column. For example,
	// if N=2, this filter would match column `foo:bar` at timestamps 10 and 9,
	// skip all earlier cells in `foo:bar`, and then begin matching again in
	// column `foo:bar2`.
	// If duplicate cells are present, as is possible when using an Interleave,
	// each copy of the cell is counted separately.
	CellsPerColumnLimitFilter int32 `protobuf:"varint,12,opt,name=cells_per_column_limit_filter,json=cellsPerColumnLimitFilter,proto3,oneof"`
}

type RowFilter_StripValueTransformer struct {
	// Replaces each cell's value with the empty string.
	StripValueTransformer bool `protobuf:"varint,13,opt,name=strip_value_transformer,json=stripValueTransformer,proto3,oneof"`
}

type RowFilter_ApplyLabelTransformer struct {
	// Applies the given label to all cells in the output row. This allows
	// the client to determine which results were produced from which part of
	// the filter.
	//
	// Values must be at most 15 characters in length, and match the RE2
	// pattern `[a-z0-9\\-]+`
	//
	// Due to a technical limitation, it is not currently possible to apply
	// multiple labels to a cell. As a result, a Chain may have no more than
	// one sub-filter which contains a `apply_label_transformer`. It is okay for
	// an Interleave to contain multiple `apply_label_transformers`, as they
	// will be applied to separate copies of the input. This may be relaxed in
	// the future.
	ApplyLabelTransformer string `protobuf:"bytes,19,opt,name=apply_label_transformer,json=applyLabelTransformer,proto3,oneof"`
}

func (*RowFilter_Chain_) isRowFilter_Filter() {}

func (*RowFilter_Interleave_) isRowFilter_Filter() {}

func (*RowFilter_Condition_) isRowFilter_Filter() {}

func (*RowFilter_Sink) isRowFilter_Filter() {}

func (*RowFilter_PassAllFilter) isRowFilter_Filter() {}

func (*RowFilter_BlockAllFilter) isRowFilter_Filter() {}

func (*RowFilter_RowKeyRegexFilter) isRowFilter_Filter() {}

func (*RowFilter_RowSampleFilter) isRowFilter_Filter() {}

func (*RowFilter_FamilyNameRegexFilter) isRowFilter_Filter() {}

func (*RowFilter_ColumnQualifierRegexFilter) isRowFilter_Filter() {}

func (*RowFilter_ColumnRangeFilter) isRowFilter_Filter() {}

func (*RowFilter_TimestampRangeFilter) isRowFilter_Filter() {}

func (*RowFilter_ValueRegexFilter) isRowFilter_Filter() {}

func (*RowFilter_ValueRangeFilter) isRowFilter_Filter() {}

func (*RowFilter_CellsPerRowOffsetFilter) isRowFilter_Filter() {}

func (*RowFilter_CellsPerRowLimitFilter) isRowFilter_Filter() {}

func (*RowFilter_CellsPerColumnLimitFilter) isRowFilter_Filter() {}

func (*RowFilter_StripValueTransformer) isRowFilter_Filter() {}

func (*RowFilter_ApplyLabelTransformer) isRowFilter_Filter() {}

// Specifies a particular change to be made to the contents of a row.
type Mutation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Which of the possible Mutation types to apply.
	//
	// Types that are assignable to Mutation:
	//	*Mutation_SetCell_
	//	*Mutation_DeleteFromColumn_
	//	*Mutation_DeleteFromFamily_
	//	*Mutation_DeleteFromRow_
	Mutation isMutation_Mutation `protobuf_oneof:"mutation"`
}

func (x *Mutation) Reset() {
	*x = Mutation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mutation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mutation) ProtoMessage() {}

func (x *Mutation) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mutation.ProtoReflect.Descriptor instead.
func (*Mutation) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{10}
}

func (m *Mutation) GetMutation() isMutation_Mutation {
	if m != nil {
		return m.Mutation
	}
	return nil
}

func (x *Mutation) GetSetCell() *Mutation_SetCell {
	if x, ok := x.GetMutation().(*Mutation_SetCell_); ok {
		return x.SetCell
	}
	return nil
}

func (x *Mutation) GetDeleteFromColumn() *Mutation_DeleteFromColumn {
	if x, ok := x.GetMutation().(*Mutation_DeleteFromColumn_); ok {
		return x.DeleteFromColumn
	}
	return nil
}

func (x *Mutation) GetDeleteFromFamily() *Mutation_DeleteFromFamily {
	if x, ok := x.GetMutation().(*Mutation_DeleteFromFamily_); ok {
		return x.DeleteFromFamily
	}
	return nil
}

func (x *Mutation) GetDeleteFromRow() *Mutation_DeleteFromRow {
	if x, ok := x.GetMutation().(*Mutation_DeleteFromRow_); ok {
		return x.DeleteFromRow
	}
	return nil
}

type isMutation_Mutation interface {
	isMutation_Mutation()
}

type Mutation_SetCell_ struct {
	// Set a cell's value.
	SetCell *Mutation_SetCell `protobuf:"bytes,1,opt,name=set_cell,json=setCell,proto3,oneof"`
}

type Mutation_DeleteFromColumn_ struct {
	// Deletes cells from a column.
	DeleteFromColumn *Mutation_DeleteFromColumn `protobuf:"bytes,2,opt,name=delete_from_column,json=deleteFromColumn,proto3,oneof"`
}

type Mutation_DeleteFromFamily_ struct {
	// Deletes cells from a column family.
	DeleteFromFamily *Mutation_DeleteFromFamily `protobuf:"bytes,3,opt,name=delete_from_family,json=deleteFromFamily,proto3,oneof"`
}

type Mutation_DeleteFromRow_ struct {
	// Deletes cells from the entire row.
	DeleteFromRow *Mutation_DeleteFromRow `protobuf:"bytes,4,opt,name=delete_from_row,json=deleteFromRow,proto3,oneof"`
}

func (*Mutation_SetCell_) isMutation_Mutation() {}

func (*Mutation_DeleteFromColumn_) isMutation_Mutation() {}

func (*Mutation_DeleteFromFamily_) isMutation_Mutation() {}

func (*Mutation_DeleteFromRow_) isMutation_Mutation() {}

// Specifies an atomic read/modify/write operation on the latest value of the
// specified column.
type ReadModifyWriteRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the family to which the read/modify/write should be applied.
	// Must match `[-_.a-zA-Z0-9]+`
	FamilyName string `protobuf:"bytes,1,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// The qualifier of the column to which the read/modify/write should be
	// applied.
	// Can be any byte string, including the empty string.
	ColumnQualifier []byte `protobuf:"bytes,2,opt,name=column_qualifier,json=columnQualifier,proto3" json:"column_qualifier,omitempty"`
	// The rule used to determine the column's new latest value from its current
	// latest value.
	//
	// Types that are assignable to Rule:
	//	*ReadModifyWriteRule_AppendValue
	//	*ReadModifyWriteRule_IncrementAmount
	Rule isReadModifyWriteRule_Rule `protobuf_oneof:"rule"`
}

func (x *ReadModifyWriteRule) Reset() {
	*x = ReadModifyWriteRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModifyWriteRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModifyWriteRule) ProtoMessage() {}

func (x *ReadModifyWriteRule) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModifyWriteRule.ProtoReflect.Descriptor instead.
func (*ReadModifyWriteRule) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{11}
}

func (x *ReadModifyWriteRule) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *ReadModifyWriteRule) GetColumnQualifier() []byte {
	if x != nil {
		return x.ColumnQualifier
	}
	return nil
}

func (m *ReadModifyWriteRule) GetRule() isReadModifyWriteRule_Rule {
	if m != nil {
		return m.Rule
	}
	return nil
}

func (x *ReadModifyWriteRule) GetAppendValue() []byte {
	if x, ok := x.GetRule().(*ReadModifyWriteRule_AppendValue); ok {
		return x.AppendValue
	}
	return nil
}

func (x *ReadModifyWriteRule) GetIncrementAmount() int64 {
	if x, ok := x.GetRule().(*ReadModifyWriteRule_IncrementAmount); ok {
		return x.IncrementAmount
	}
	return 0
}

type isReadModifyWriteRule_Rule interface {
	isReadModifyWriteRule_Rule()
}

type ReadModifyWriteRule_AppendValue struct {
	// Rule specifying that `append_value` be appended to the existing value.
	// If the targeted cell is unset, it will be treated as containing the
	// empty string.
	AppendValue []byte `protobuf:"bytes,3,opt,name=append_value,json=appendValue,proto3,oneof"`
}

type ReadModifyWriteRule_IncrementAmount struct {
	// Rule specifying that `increment_amount` be added to the existing value.
	// If the targeted cell is unset, it will be treated as containing a zero.
	// Otherwise, the targeted cell must contain an 8-byte value (interpreted
	// as a 64-bit big-endian signed integer), or the entire request will fail.
	IncrementAmount int64 `protobuf:"varint,4,opt,name=increment_amount,json=incrementAmount,proto3,oneof"`
}

func (*ReadModifyWriteRule_AppendValue) isReadModifyWriteRule_Rule() {}

func (*ReadModifyWriteRule_IncrementAmount) isReadModifyWriteRule_Rule() {}

// A RowFilter which sends rows through several RowFilters in sequence.
type RowFilter_Chain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The elements of "filters" are chained together to process the input row:
	// in row -> f(0) -> intermediate row -> f(1) -> ... -> f(N) -> out row
	// The full chain is executed atomically.
	Filters []*RowFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *RowFilter_Chain) Reset() {
	*x = RowFilter_Chain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowFilter_Chain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowFilter_Chain) ProtoMessage() {}

func (x *RowFilter_Chain) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowFilter_Chain.ProtoReflect.Descriptor instead.
func (*RowFilter_Chain) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{9, 0}
}

func (x *RowFilter_Chain) GetFilters() []*RowFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

// A RowFilter which sends each row to each of several component
// RowFilters and interleaves the results.
type RowFilter_Interleave struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The elements of "filters" all process a copy of the input row, and the
	// results are pooled, sorted, and combined into a single output row.
	// If multiple cells are produced with the same column and timestamp,
	// they will all appear in the output row in an unspecified mutual order.
	// Consider the following example, with three filters:
	//
	//                                  input row
	//                                      |
	//            -----------------------------------------------------
	//            |                         |                         |
	//           f(0)                      f(1)                      f(2)
	//            |                         |                         |
	//     1: foo,bar,10,x             foo,bar,10,z              far,bar,7,a
	//     2: foo,blah,11,z            far,blah,5,x              far,blah,5,x
	//            |                         |                         |
	//            -----------------------------------------------------
	//                                      |
	//     1:                      foo,bar,10,z   // could have switched with #2
	//     2:                      foo,bar,10,x   // could have switched with #1
	//     3:                      foo,blah,11,z
	//     4:                      far,bar,7,a
	//     5:                      far,blah,5,x   // identical to #6
	//     6:                      far,blah,5,x   // identical to #5
	//
	// All interleaved filters are executed atomically.
	Filters []*RowFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *RowFilter_Interleave) Reset() {
	*x = RowFilter_Interleave{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowFilter_Interleave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowFilter_Interleave) ProtoMessage() {}

func (x *RowFilter_Interleave) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowFilter_Interleave.ProtoReflect.Descriptor instead.
func (*RowFilter_Interleave) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{9, 1}
}

func (x *RowFilter_Interleave) GetFilters() []*RowFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

// A RowFilter which evaluates one of two possible RowFilters, depending on
// whether or not a predicate RowFilter outputs any cells from the input row.
//
// IMPORTANT NOTE: The predicate filter does not execute atomically with the
// true and false filters, which may lead to inconsistent or unexpected
// results. Additionally, Condition filters have poor performance, especially
// when filters are set for the false condition.
type RowFilter_Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If `predicate_filter` outputs any cells, then `true_filter` will be
	// evaluated on the input row. Otherwise, `false_filter` will be evaluated.
	PredicateFilter *RowFilter `protobuf:"bytes,1,opt,name=predicate_filter,json=predicateFilter,proto3" json:"predicate_filter,omitempty"`
	// The filter to apply to the input row if `predicate_filter` returns any
	// results. If not provided, no results will be returned in the true case.
	TrueFilter *RowFilter `protobuf:"bytes,2,opt,name=true_filter,json=trueFilter,proto3" json:"true_filter,omitempty"`
	// The filter to apply to the input row if `predicate_filter` does not
	// return any results. If not provided, no results will be returned in the
	// false case.
	FalseFilter *RowFilter `protobuf:"bytes,3,opt,name=false_filter,json=falseFilter,proto3" json:"false_filter,omitempty"`
}

func (x *RowFilter_Condition) Reset() {
	*x = RowFilter_Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowFilter_Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowFilter_Condition) ProtoMessage() {}

func (x *RowFilter_Condition) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowFilter_Condition.ProtoReflect.Descriptor instead.
func (*RowFilter_Condition) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{9, 2}
}

func (x *RowFilter_Condition) GetPredicateFilter() *RowFilter {
	if x != nil {
		return x.PredicateFilter
	}
	return nil
}

func (x *RowFilter_Condition) GetTrueFilter() *RowFilter {
	if x != nil {
		return x.TrueFilter
	}
	return nil
}

func (x *RowFilter_Condition) GetFalseFilter() *RowFilter {
	if x != nil {
		return x.FalseFilter
	}
	return nil
}

// A Mutation which sets the value of the specified cell.
type Mutation_SetCell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the family into which new data should be written.
	// Must match `[-_.a-zA-Z0-9]+`
	FamilyName string `protobuf:"bytes,1,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// The qualifier of the column into which new data should be written.
	// Can be any byte string, including the empty string.
	ColumnQualifier []byte `protobuf:"bytes,2,opt,name=column_qualifier,json=columnQualifier,proto3" json:"column_qualifier,omitempty"`
	// The timestamp of the cell into which new data should be written.
	// Use -1 for current Bigtable server time.
	// Otherwise, the client should set this value itself, noting that the
	// default value is a timestamp of zero if the field is left unspecified.
	// Values must match the granularity of the table (e.g. micros, millis).
	TimestampMicros int64 `protobuf:"varint,3,opt,name=timestamp_micros,json=timestampMicros,proto3" json:"timestamp_micros,omitempty"`
	// The value to be written into the specified cell.
	Value []byte `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Mutation_SetCell) Reset() {
	*x = Mutation_SetCell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mutation_SetCell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mutation_SetCell) ProtoMessage() {}

func (x *Mutation_SetCell) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mutation_SetCell.ProtoReflect.Descriptor instead.
func (*Mutation_SetCell) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{10, 0}
}

func (x *Mutation_SetCell) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Mutation_SetCell) GetColumnQualifier() []byte {
	if x != nil {
		return x.ColumnQualifier
	}
	return nil
}

func (x *Mutation_SetCell) GetTimestampMicros() int64 {
	if x != nil {
		return x.TimestampMicros
	}
	return 0
}

func (x *Mutation_SetCell) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

// A Mutation which deletes cells from the specified column, optionally
// restricting the deletions to a given timestamp range.
type Mutation_DeleteFromColumn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the family from which cells should be deleted.
	// Must match `[-_.a-zA-Z0-9]+`
	FamilyName string `protobuf:"bytes,1,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// The qualifier of the column from which cells should be deleted.
	// Can be any byte string, including the empty string.
	ColumnQualifier []byte `protobuf:"bytes,2,opt,name=column_qualifier,json=columnQualifier,proto3" json:"column_qualifier,omitempty"`
	// The range of timestamps within which cells should be deleted.
	TimeRange *TimestampRange `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *Mutation_DeleteFromColumn) Reset() {
	*x = Mutation_DeleteFromColumn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mutation_DeleteFromColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mutation_DeleteFromColumn) ProtoMessage() {}

func (x *Mutation_DeleteFromColumn) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mutation_DeleteFromColumn.ProtoReflect.Descriptor instead.
func (*Mutation_DeleteFromColumn) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{10, 1}
}

func (x *Mutation_DeleteFromColumn) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Mutation_DeleteFromColumn) GetColumnQualifier() []byte {
	if x != nil {
		return x.ColumnQualifier
	}
	return nil
}

func (x *Mutation_DeleteFromColumn) GetTimeRange() *TimestampRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

// A Mutation which deletes all cells from the specified column family.
type Mutation_DeleteFromFamily struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the family from which cells should be deleted.
	// Must match `[-_.a-zA-Z0-9]+`
	FamilyName string `protobuf:"bytes,1,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
}

func (x *Mutation_DeleteFromFamily) Reset() {
	*x = Mutation_DeleteFromFamily{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mutation_DeleteFromFamily) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mutation_DeleteFromFamily) ProtoMessage() {}

func (x *Mutation_DeleteFromFamily) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mutation_DeleteFromFamily.ProtoReflect.Descriptor instead.
func (*Mutation_DeleteFromFamily) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{10, 2}
}

func (x *Mutation_DeleteFromFamily) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

// A Mutation which deletes all cells from the containing row.
type Mutation_DeleteFromRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Mutation_DeleteFromRow) Reset() {
	*x = Mutation_DeleteFromRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_v2_data_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mutation_DeleteFromRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mutation_DeleteFromRow) ProtoMessage() {}

func (x *Mutation_DeleteFromRow) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_v2_data_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mutation_DeleteFromRow.ProtoReflect.Descriptor instead.
func (*Mutation_DeleteFromRow) Descriptor() ([]byte, []int) {
	return file_google_bigtable_v2_data_proto_rawDescGZIP(), []int{10, 3}
}

var File_google_bigtable_v2_data_proto protoreflect.FileDescriptor

var file_google_bigtable_v2_data_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x12, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x22, 0x4f, 0x0a, 0x03, 0x52, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x08,
	0x66, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x52, 0x08, 0x66, 0x61, 0x6d, 0x69,
	0x6c, 0x69, 0x65, 0x73, 0x22, 0x52, 0x0a, 0x06, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52,
	0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x22, 0x56, 0x0a, 0x06, 0x43, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x12, 0x2e, 0x0a, 0x05, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x65, 0x6c, 0x6c, 0x52, 0x05, 0x63, 0x65, 0x6c, 0x6c, 0x73,
	0x22, 0x5f, 0x0a, 0x04, 0x43, 0x65, 0x6c, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x69, 0x63,
	0x72, 0x6f, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x22, 0xc2, 0x01, 0x0a, 0x08, 0x52, 0x6f, 0x77, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2a,
	0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x4b, 0x65, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4b, 0x65, 0x79, 0x4f, 0x70,
	0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x4b,
	0x65, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x6b, 0x65,
	0x79, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x01,
	0x52, 0x0c, 0x65, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x42, 0x0b,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x22, 0x60, 0x0a, 0x06, 0x52, 0x6f, 0x77, 0x53, 0x65, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x07, 0x72, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x72,
	0x6f, 0x77, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x72,
	0x6f, 0x77, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x6d, 0x69,
	0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x14, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x12, 0x32, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x48,
	0x00, 0x52, 0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x14, 0x65, 0x6e, 0x64, 0x5f, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x12, 0x65, 0x6e, 0x64, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x65, 0x6e, 0x64,
	0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x10, 0x65, 0x6e, 0x64, 0x51, 0x75, 0x61, 0x6c,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x42, 0x0f, 0x0a, 0x0d,
	0x65, 0x6e, 0x64, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x78, 0x0a,
	0x0e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x34, 0x0a, 0x16, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x14, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d,
	0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x12, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x22, 0xd8, 0x01, 0x0a, 0x0a, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x48, 0x00, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70,
	0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x6e, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x0e,
	0x65, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x12, 0x26,
	0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0xfc, 0x0b, 0x0a, 0x09, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x3b, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x48, 0x00, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x4a, 0x0a,
	0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73,
	0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x41, 0x6c, 0x6c, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x2a, 0x0a, 0x10, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0e,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x6c, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x31,
	0x0a, 0x14, 0x72, 0x6f, 0x77, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x72, 0x65, 0x67, 0x65, 0x78, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x11,
	0x72, 0x6f, 0x77, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x67, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0f,
	0x72, 0x6f, 0x77, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x39, 0x0a, 0x18, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x72,
	0x65, 0x67, 0x65, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x15, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x67, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x1d, 0x63, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x67, 0x65, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0c, 0x48, 0x00, 0x52, 0x1a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x67, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x51, 0x0a, 0x13, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52,
	0x11, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x5a, 0x0a, 0x16, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2e,
	0x0a, 0x12, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x65, 0x78, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x10, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x65, 0x67, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x4e,
	0x0a, 0x12, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x10, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3e,
	0x0a, 0x1b, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x5f,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x17, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x50, 0x65, 0x72, 0x52,
	0x6f, 0x77, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3c,
	0x0a, 0x1a, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x00, 0x52, 0x16, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x50, 0x65, 0x72, 0x52, 0x6f,
	0x77, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x1d,
	0x63, 0x65, 0x6c, 0x6c, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x19, 0x63, 0x65, 0x6c, 0x6c, 0x73, 0x50, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x38, 0x0a, 0x17, 0x73, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x15, 0x73, 0x74, 0x72, 0x69, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x17, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x6f, 0x72, 0x6d, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x15, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f,
	0x72, 0x6d, 0x65, 0x72, 0x1a, 0x40, 0x0a, 0x05, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x37, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x45, 0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6c,
	0x65, 0x61, 0x76, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0xd7, 0x01,
	0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x10, 0x70,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x74, 0x72, 0x75, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0a, 0x74, 0x72, 0x75, 0x65, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x22, 0xf0, 0x05, 0x0a, 0x08, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41,
	0x0a, 0x08, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x65, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x65, 0x74, 0x43, 0x65, 0x6c, 0x6c, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x74, 0x43, 0x65, 0x6c,
	0x6c, 0x12, 0x5d, 0x0a, 0x12, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x48, 0x00, 0x52, 0x10,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x12, 0x5d, 0x0a, 0x12, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x46, 0x72, 0x6f, 0x6d, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x10, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12,
	0x54, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x72,
	0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x75,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f,
	0x6d, 0x52, 0x6f, 0x77, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72,
	0x6f, 0x6d, 0x52, 0x6f, 0x77, 0x1a, 0x96, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x43, 0x65, 0x6c,
	0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x63, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x29, 0x0a,
	0x10, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0xa1,
	0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x71,
	0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f,
	0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x41, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x1a, 0x33, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d,
	0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x0f, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x6f, 0x77, 0x42, 0x0a, 0x0a, 0x08, 0x6d, 0x75, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xbb, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x57, 0x72, 0x69, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x51,
	0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x65,
	0x6e, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2b, 0x0a,
	0x10, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x72, 0x75,
	0x6c, 0x65, 0x42, 0xb5, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x42, 0x09, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3a, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x3b, 0x62, 0x69,
	0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0xaa, 0x02, 0x18, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x56,
	0x32, 0xca, 0x02, 0x18, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x5c, 0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5c, 0x56, 0x32, 0xea, 0x02, 0x1b, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x42, 0x69,
	0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x3a, 0x3a, 0x56, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_google_bigtable_v2_data_proto_rawDescOnce sync.Once
	file_google_bigtable_v2_data_proto_rawDescData = file_google_bigtable_v2_data_proto_rawDesc
)

func file_google_bigtable_v2_data_proto_rawDescGZIP() []byte {
	file_google_bigtable_v2_data_proto_rawDescOnce.Do(func() {
		file_google_bigtable_v2_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_bigtable_v2_data_proto_rawDescData)
	})
	return file_google_bigtable_v2_data_proto_rawDescData
}

var file_google_bigtable_v2_data_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_google_bigtable_v2_data_proto_goTypes = []interface{}{
	(*Row)(nil),                       // 0: google.bigtable.v2.Row
	(*Family)(nil),                    // 1: google.bigtable.v2.Family
	(*Column)(nil),                    // 2: google.bigtable.v2.Column
	(*Cell)(nil),                      // 3: google.bigtable.v2.Cell
	(*RowRange)(nil),                  // 4: google.bigtable.v2.RowRange
	(*RowSet)(nil),                    // 5: google.bigtable.v2.RowSet
	(*ColumnRange)(nil),               // 6: google.bigtable.v2.ColumnRange
	(*TimestampRange)(nil),            // 7: google.bigtable.v2.TimestampRange
	(*ValueRange)(nil),                // 8: google.bigtable.v2.ValueRange
	(*RowFilter)(nil),                 // 9: google.bigtable.v2.RowFilter
	(*Mutation)(nil),                  // 10: google.bigtable.v2.Mutation
	(*ReadModifyWriteRule)(nil),       // 11: google.bigtable.v2.ReadModifyWriteRule
	(*RowFilter_Chain)(nil),           // 12: google.bigtable.v2.RowFilter.Chain
	(*RowFilter_Interleave)(nil),      // 13: google.bigtable.v2.RowFilter.Interleave
	(*RowFilter_Condition)(nil),       // 14: google.bigtable.v2.RowFilter.Condition
	(*Mutation_SetCell)(nil),          // 15: google.bigtable.v2.Mutation.SetCell
	(*Mutation_DeleteFromColumn)(nil), // 16: google.bigtable.v2.Mutation.DeleteFromColumn
	(*Mutation_DeleteFromFamily)(nil), // 17: google.bigtable.v2.Mutation.DeleteFromFamily
	(*Mutation_DeleteFromRow)(nil),    // 18: google.bigtable.v2.Mutation.DeleteFromRow
}
var file_google_bigtable_v2_data_proto_depIdxs = []int32{
	1,  // 0: google.bigtable.v2.Row.families:type_name -> google.bigtable.v2.Family
	2,  // 1: google.bigtable.v2.Family.columns:type_name -> google.bigtable.v2.Column
	3,  // 2: google.bigtable.v2.Column.cells:type_name -> google.bigtable.v2.Cell
	4,  // 3: google.bigtable.v2.RowSet.row_ranges:type_name -> google.bigtable.v2.RowRange
	12, // 4: google.bigtable.v2.RowFilter.chain:type_name -> google.bigtable.v2.RowFilter.Chain
	13, // 5: google.bigtable.v2.RowFilter.interleave:type_name -> google.bigtable.v2.RowFilter.Interleave
	14, // 6: google.bigtable.v2.RowFilter.condition:type_name -> google.bigtable.v2.RowFilter.Condition
	6,  // 7: google.bigtable.v2.RowFilter.column_range_filter:type_name -> google.bigtable.v2.ColumnRange
	7,  // 8: google.bigtable.v2.RowFilter.timestamp_range_filter:type_name -> google.bigtable.v2.TimestampRange
	8,  // 9: google.bigtable.v2.RowFilter.value_range_filter:type_name -> google.bigtable.v2.ValueRange
	15, // 10: google.bigtable.v2.Mutation.set_cell:type_name -> google.bigtable.v2.Mutation.SetCell
	16, // 11: google.bigtable.v2.Mutation.delete_from_column:type_name -> google.bigtable.v2.Mutation.DeleteFromColumn
	17, // 12: google.bigtable.v2.Mutation.delete_from_family:type_name -> google.bigtable.v2.Mutation.DeleteFromFamily
	18, // 13: google.bigtable.v2.Mutation.delete_from_row:type_name -> google.bigtable.v2.Mutation.DeleteFromRow
	9,  // 14: google.bigtable.v2.RowFilter.Chain.filters:type_name -> google.bigtable.v2.RowFilter
	9,  // 15: google.bigtable.v2.RowFilter.Interleave.filters:type_name -> google.bigtable.v2.RowFilter
	9,  // 16: google.bigtable.v2.RowFilter.Condition.predicate_filter:type_name -> google.bigtable.v2.RowFilter
	9,  // 17: google.bigtable.v2.RowFilter.Condition.true_filter:type_name -> google.bigtable.v2.RowFilter
	9,  // 18: google.bigtable.v2.RowFilter.Condition.false_filter:type_name -> google.bigtable.v2.RowFilter
	7,  // 19: google.bigtable.v2.Mutation.DeleteFromColumn.time_range:type_name -> google.bigtable.v2.TimestampRange
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_google_bigtable_v2_data_proto_init() }
func file_google_bigtable_v2_data_proto_init() {
	if File_google_bigtable_v2_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_bigtable_v2_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Row); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Family); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Column); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ColumnRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimestampRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mutation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModifyWriteRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowFilter_Chain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowFilter_Interleave); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowFilter_Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mutation_SetCell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mutation_DeleteFromColumn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mutation_DeleteFromFamily); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_v2_data_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mutation_DeleteFromRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_bigtable_v2_data_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*RowRange_StartKeyClosed)(nil),
		(*RowRange_StartKeyOpen)(nil),
		(*RowRange_EndKeyOpen)(nil),
		(*RowRange_EndKeyClosed)(nil),
	}
	file_google_bigtable_v2_data_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ColumnRange_StartQualifierClosed)(nil),
		(*ColumnRange_StartQualifierOpen)(nil),
		(*ColumnRange_EndQualifierClosed)(nil),
		(*ColumnRange_EndQualifierOpen)(nil),
	}
	file_google_bigtable_v2_data_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*ValueRange_StartValueClosed)(nil),
		(*ValueRange_StartValueOpen)(nil),
		(*ValueRange_EndValueClosed)(nil),
		(*ValueRange_EndValueOpen)(nil),
	}
	file_google_bigtable_v2_data_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*RowFilter_Chain_)(nil),
		(*RowFilter_Interleave_)(nil),
		(*RowFilter_Condition_)(nil),
		(*RowFilter_Sink)(nil),
		(*RowFilter_PassAllFilter)(nil),
		(*RowFilter_BlockAllFilter)(nil),
		(*RowFilter_RowKeyRegexFilter)(nil),
		(*RowFilter_RowSampleFilter)(nil),
		(*RowFilter_FamilyNameRegexFilter)(nil),
		(*RowFilter_ColumnQualifierRegexFilter)(nil),
		(*RowFilter_ColumnRangeFilter)(nil),
		(*RowFilter_TimestampRangeFilter)(nil),
		(*RowFilter_ValueRegexFilter)(nil),
		(*RowFilter_ValueRangeFilter)(nil),
		(*RowFilter_CellsPerRowOffsetFilter)(nil),
		(*RowFilter_CellsPerRowLimitFilter)(nil),
		(*RowFilter_CellsPerColumnLimitFilter)(nil),
		(*RowFilter_StripValueTransformer)(nil),
		(*RowFilter_ApplyLabelTransformer)(nil),
	}
	file_google_bigtable_v2_data_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*Mutation_SetCell_)(nil),
		(*Mutation_DeleteFromColumn_)(nil),
		(*Mutation_DeleteFromFamily_)(nil),
		(*Mutation_DeleteFromRow_)(nil),
	}
	file_google_bigtable_v2_data_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*ReadModifyWriteRule_AppendValue)(nil),
		(*ReadModifyWriteRule_IncrementAmount)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_bigtable_v2_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_bigtable_v2_data_proto_goTypes,
		DependencyIndexes: file_google_bigtable_v2_data_proto_depIdxs,
		MessageInfos:      file_google_bigtable_v2_data_proto_msgTypes,
	}.Build()
	File_google_bigtable_v2_data_proto = out.File
	file_google_bigtable_v2_data_proto_rawDesc = nil
	file_google_bigtable_v2_data_proto_goTypes = nil
	file_google_bigtable_v2_data_proto_depIdxs = nil
}
