/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// GitRepoVolumeSourceApplyConfiguration represents an declarative configuration of the GitRepoVolumeSource type for use
// with apply.
type GitRepoVolumeSourceApplyConfiguration struct {
	Repository *string `json:"repository,omitempty"`
	Revision   *string `json:"revision,omitempty"`
	Directory  *string `json:"directory,omitempty"`
}

// GitRepoVolumeSourceApplyConfiguration constructs an declarative configuration of the GitRepoVolumeSource type for use with
// apply.
func GitRepoVolumeSource() *GitRepoVolumeSourceApplyConfiguration {
	return &GitRepoVolumeSourceApplyConfiguration{}
}

// WithRepository sets the Repository field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Repository field is set to the value of the last call.
func (b *GitRepoVolumeSourceApplyConfiguration) WithRepository(value string) *GitRepoVolumeSourceApplyConfiguration {
	b.Repository = &value
	return b
}

// WithRevision sets the Revision field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Revision field is set to the value of the last call.
func (b *GitRepoVolumeSourceApplyConfiguration) WithRevision(value string) *GitRepoVolumeSourceApplyConfiguration {
	b.Revision = &value
	return b
}

// WithDirectory sets the Directory field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Directory field is set to the value of the last call.
func (b *GitRepoVolumeSourceApplyConfiguration) WithDirectory(value string) *GitRepoVolumeSourceApplyConfiguration {
	b.Directory = &value
	return b
}
