apiVersion: v1
kind: Service
metadata:
  name: {{ template "loki.fullname" . }}-headless
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "loki.name" . }}
    chart: {{ template "loki.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    variant: headless
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.service.port }}
      protocol: TCP
      name: http-metrics
      targetPort: {{ .Values.service.targetPort }}
{{- if .Values.extraPorts }}
{{ toYaml .Values.extraPorts | indent 4}}
{{- end }}
  selector:
    app: {{ template "loki.name" . }}
    release: {{ .Release.Name }}
