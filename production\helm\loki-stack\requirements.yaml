dependencies:
- name: "loki"
  condition: loki.enabled
  repository: "file://../loki"
  version: "^2.0.0"
- name: "promtail"
  condition: promtail.enabled
  repository: "file://../promtail"
  version: "^2.0.0"
- name: "fluent-bit"
  condition: fluent-bit.enabled
  repository: "file://../fluent-bit"
  version: "^2.0.0"
- name: "grafana"
  condition: grafana.enabled
  version: "~5.7.0"
  repository:  "https://grafana.github.io/helm-charts"
- name: "prometheus"
  condition: prometheus.enabled
  version: "~11.16.0"
  repository:  "https://prometheus-community.github.io/helm-charts"
- name: "filebeat"
  condition: filebeat.enabled
  version: "~7.8.0"
  repository:  "https://helm.elastic.co"
- name: "logstash"
  condition: logstash.enabled
  version: "~7.8.0"
  repository:  "https://helm.elastic.co"
