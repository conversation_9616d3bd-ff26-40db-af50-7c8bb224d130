// Copyright 2020 Google LLC. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// THIS FILE IS AUTOMATICALLY GENERATED.

package openapi_v2

import (
	"fmt"
	"github.com/googleapis/gnostic/compiler"
	"gopkg.in/yaml.v3"
	"regexp"
	"strings"
)

// Version returns the package name (and OpenAPI version).
func Version() string {
	return "openapi_v2"
}

// NewAdditionalPropertiesItem creates an object of type AdditionalPropertiesItem if possible, returning an error if not.
func NewAdditionalPropertiesItem(in *yaml.Node, context *compiler.Context) (*AdditionalPropertiesItem, error) {
	errors := make([]error, 0)
	x := &AdditionalPropertiesItem{}
	matched := false
	// Schema schema = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewSchema(m, compiler.NewContext("schema", m, context))
			if matchingError == nil {
				x.Oneof = &AdditionalPropertiesItem_Schema{Schema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// bool boolean = 2;
	boolValue, ok := compiler.BoolForScalarNode(in)
	if ok {
		x.Oneof = &AdditionalPropertiesItem_Boolean{Boolean: boolValue}
		matched = true
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid AdditionalPropertiesItem")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewAny creates an object of type Any if possible, returning an error if not.
func NewAny(in *yaml.Node, context *compiler.Context) (*Any, error) {
	errors := make([]error, 0)
	x := &Any{}
	bytes := compiler.Marshal(in)
	x.Yaml = string(bytes)
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewApiKeySecurity creates an object of type ApiKeySecurity if possible, returning an error if not.
func NewApiKeySecurity(in *yaml.Node, context *compiler.Context) (*ApiKeySecurity, error) {
	errors := make([]error, 0)
	x := &ApiKeySecurity{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"in", "name", "type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "in", "name", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [apiKey]
			if ok && !compiler.StringArrayContainsValue([]string{"apiKey"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 2;
		v2 := compiler.MapValueForKey(m, "name")
		if v2 != nil {
			x.Name, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 3;
		v3 := compiler.MapValueForKey(m, "in")
		if v3 != nil {
			x.In, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [header query]
			if ok && !compiler.StringArrayContainsValue([]string{"header", "query"}, x.In) {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 4;
		v4 := compiler.MapValueForKey(m, "description")
		if v4 != nil {
			x.Description, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 5;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewBasicAuthenticationSecurity creates an object of type BasicAuthenticationSecurity if possible, returning an error if not.
func NewBasicAuthenticationSecurity(in *yaml.Node, context *compiler.Context) (*BasicAuthenticationSecurity, error) {
	errors := make([]error, 0)
	x := &BasicAuthenticationSecurity{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [basic]
			if ok && !compiler.StringArrayContainsValue([]string{"basic"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 3;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewBodyParameter creates an object of type BodyParameter if possible, returning an error if not.
func NewBodyParameter(in *yaml.Node, context *compiler.Context) (*BodyParameter, error) {
	errors := make([]error, 0)
	x := &BodyParameter{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"in", "name", "schema"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "in", "name", "required", "schema"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 2;
		v2 := compiler.MapValueForKey(m, "name")
		if v2 != nil {
			x.Name, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 3;
		v3 := compiler.MapValueForKey(m, "in")
		if v3 != nil {
			x.In, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [body]
			if ok && !compiler.StringArrayContainsValue([]string{"body"}, x.In) {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool required = 4;
		v4 := compiler.MapValueForKey(m, "required")
		if v4 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Schema schema = 5;
		v5 := compiler.MapValueForKey(m, "schema")
		if v5 != nil {
			var err error
			x.Schema, err = NewSchema(v5, compiler.NewContext("schema", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 6;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewContact creates an object of type Contact if possible, returning an error if not.
func NewContact(in *yaml.Node, context *compiler.Context) (*Contact, error) {
	errors := make([]error, 0)
	x := &Contact{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"email", "name", "url"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string url = 2;
		v2 := compiler.MapValueForKey(m, "url")
		if v2 != nil {
			x.Url, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string email = 3;
		v3 := compiler.MapValueForKey(m, "email")
		if v3 != nil {
			x.Email, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for email: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 4;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewDefault creates an object of type Default if possible, returning an error if not.
func NewDefault(in *yaml.Node, context *compiler.Context) (*Default, error) {
	errors := make([]error, 0)
	x := &Default{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedAny additional_properties = 1;
		// MAP: Any
		x.AdditionalProperties = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedAny{}
				pair.Name = k
				result := &Any{}
				handled, resultFromExt, err := compiler.CallExtension(context, v, k)
				if handled {
					if err != nil {
						errors = append(errors, err)
					} else {
						bytes := compiler.Marshal(v)
						result.Yaml = string(bytes)
						result.Value = resultFromExt
						pair.Value = result
					}
				} else {
					pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewDefinitions creates an object of type Definitions if possible, returning an error if not.
func NewDefinitions(in *yaml.Node, context *compiler.Context) (*Definitions, error) {
	errors := make([]error, 0)
	x := &Definitions{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedSchema additional_properties = 1;
		// MAP: Schema
		x.AdditionalProperties = make([]*NamedSchema, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedSchema{}
				pair.Name = k
				var err error
				pair.Value, err = NewSchema(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewDocument creates an object of type Document if possible, returning an error if not.
func NewDocument(in *yaml.Node, context *compiler.Context) (*Document, error) {
	errors := make([]error, 0)
	x := &Document{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"info", "paths", "swagger"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"basePath", "consumes", "definitions", "externalDocs", "host", "info", "parameters", "paths", "produces", "responses", "schemes", "security", "securityDefinitions", "swagger", "tags"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string swagger = 1;
		v1 := compiler.MapValueForKey(m, "swagger")
		if v1 != nil {
			x.Swagger, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for swagger: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [2.0]
			if ok && !compiler.StringArrayContainsValue([]string{"2.0"}, x.Swagger) {
				message := fmt.Sprintf("has unexpected value for swagger: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Info info = 2;
		v2 := compiler.MapValueForKey(m, "info")
		if v2 != nil {
			var err error
			x.Info, err = NewInfo(v2, compiler.NewContext("info", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string host = 3;
		v3 := compiler.MapValueForKey(m, "host")
		if v3 != nil {
			x.Host, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for host: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string base_path = 4;
		v4 := compiler.MapValueForKey(m, "basePath")
		if v4 != nil {
			x.BasePath, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for basePath: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string schemes = 5;
		v5 := compiler.MapValueForKey(m, "schemes")
		if v5 != nil {
			v, ok := compiler.SequenceNodeForNode(v5)
			if ok {
				x.Schemes = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for schemes: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [http https ws wss]
			if ok && !compiler.StringArrayContainsValues([]string{"http", "https", "ws", "wss"}, x.Schemes) {
				message := fmt.Sprintf("has unexpected value for schemes: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string consumes = 6;
		v6 := compiler.MapValueForKey(m, "consumes")
		if v6 != nil {
			v, ok := compiler.SequenceNodeForNode(v6)
			if ok {
				x.Consumes = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for consumes: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string produces = 7;
		v7 := compiler.MapValueForKey(m, "produces")
		if v7 != nil {
			v, ok := compiler.SequenceNodeForNode(v7)
			if ok {
				x.Produces = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for produces: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Paths paths = 8;
		v8 := compiler.MapValueForKey(m, "paths")
		if v8 != nil {
			var err error
			x.Paths, err = NewPaths(v8, compiler.NewContext("paths", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Definitions definitions = 9;
		v9 := compiler.MapValueForKey(m, "definitions")
		if v9 != nil {
			var err error
			x.Definitions, err = NewDefinitions(v9, compiler.NewContext("definitions", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ParameterDefinitions parameters = 10;
		v10 := compiler.MapValueForKey(m, "parameters")
		if v10 != nil {
			var err error
			x.Parameters, err = NewParameterDefinitions(v10, compiler.NewContext("parameters", v10, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ResponseDefinitions responses = 11;
		v11 := compiler.MapValueForKey(m, "responses")
		if v11 != nil {
			var err error
			x.Responses, err = NewResponseDefinitions(v11, compiler.NewContext("responses", v11, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated SecurityRequirement security = 12;
		v12 := compiler.MapValueForKey(m, "security")
		if v12 != nil {
			// repeated SecurityRequirement
			x.Security = make([]*SecurityRequirement, 0)
			a, ok := compiler.SequenceNodeForNode(v12)
			if ok {
				for _, item := range a.Content {
					y, err := NewSecurityRequirement(item, compiler.NewContext("security", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Security = append(x.Security, y)
				}
			}
		}
		// SecurityDefinitions security_definitions = 13;
		v13 := compiler.MapValueForKey(m, "securityDefinitions")
		if v13 != nil {
			var err error
			x.SecurityDefinitions, err = NewSecurityDefinitions(v13, compiler.NewContext("securityDefinitions", v13, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated Tag tags = 14;
		v14 := compiler.MapValueForKey(m, "tags")
		if v14 != nil {
			// repeated Tag
			x.Tags = make([]*Tag, 0)
			a, ok := compiler.SequenceNodeForNode(v14)
			if ok {
				for _, item := range a.Content {
					y, err := NewTag(item, compiler.NewContext("tags", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Tags = append(x.Tags, y)
				}
			}
		}
		// ExternalDocs external_docs = 15;
		v15 := compiler.MapValueForKey(m, "externalDocs")
		if v15 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v15, compiler.NewContext("externalDocs", v15, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 16;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExamples creates an object of type Examples if possible, returning an error if not.
func NewExamples(in *yaml.Node, context *compiler.Context) (*Examples, error) {
	errors := make([]error, 0)
	x := &Examples{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedAny additional_properties = 1;
		// MAP: Any
		x.AdditionalProperties = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedAny{}
				pair.Name = k
				result := &Any{}
				handled, resultFromExt, err := compiler.CallExtension(context, v, k)
				if handled {
					if err != nil {
						errors = append(errors, err)
					} else {
						bytes := compiler.Marshal(v)
						result.Yaml = string(bytes)
						result.Value = resultFromExt
						pair.Value = result
					}
				} else {
					pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExternalDocs creates an object of type ExternalDocs if possible, returning an error if not.
func NewExternalDocs(in *yaml.Node, context *compiler.Context) (*ExternalDocs, error) {
	errors := make([]error, 0)
	x := &ExternalDocs{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"url"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "url"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string url = 2;
		v2 := compiler.MapValueForKey(m, "url")
		if v2 != nil {
			x.Url, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 3;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewFileSchema creates an object of type FileSchema if possible, returning an error if not.
func NewFileSchema(in *yaml.Node, context *compiler.Context) (*FileSchema, error) {
	errors := make([]error, 0)
	x := &FileSchema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"default", "description", "example", "externalDocs", "format", "readOnly", "required", "title", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string format = 1;
		v1 := compiler.MapValueForKey(m, "format")
		if v1 != nil {
			x.Format, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string title = 2;
		v2 := compiler.MapValueForKey(m, "title")
		if v2 != nil {
			x.Title, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for title: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 4;
		v4 := compiler.MapValueForKey(m, "default")
		if v4 != nil {
			var err error
			x.Default, err = NewAny(v4, compiler.NewContext("default", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated string required = 5;
		v5 := compiler.MapValueForKey(m, "required")
		if v5 != nil {
			v, ok := compiler.SequenceNodeForNode(v5)
			if ok {
				x.Required = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string type = 6;
		v6 := compiler.MapValueForKey(m, "type")
		if v6 != nil {
			x.Type, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [file]
			if ok && !compiler.StringArrayContainsValue([]string{"file"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool read_only = 7;
		v7 := compiler.MapValueForKey(m, "readOnly")
		if v7 != nil {
			x.ReadOnly, ok = compiler.BoolForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for readOnly: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ExternalDocs external_docs = 8;
		v8 := compiler.MapValueForKey(m, "externalDocs")
		if v8 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v8, compiler.NewContext("externalDocs", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Any example = 9;
		v9 := compiler.MapValueForKey(m, "example")
		if v9 != nil {
			var err error
			x.Example, err = NewAny(v9, compiler.NewContext("example", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 10;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewFormDataParameterSubSchema creates an object of type FormDataParameterSubSchema if possible, returning an error if not.
func NewFormDataParameterSubSchema(in *yaml.Node, context *compiler.Context) (*FormDataParameterSubSchema, error) {
	errors := make([]error, 0)
	x := &FormDataParameterSubSchema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"allowEmptyValue", "collectionFormat", "default", "description", "enum", "exclusiveMaximum", "exclusiveMinimum", "format", "in", "items", "maxItems", "maxLength", "maximum", "minItems", "minLength", "minimum", "multipleOf", "name", "pattern", "required", "type", "uniqueItems"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// bool required = 1;
		v1 := compiler.MapValueForKey(m, "required")
		if v1 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 2;
		v2 := compiler.MapValueForKey(m, "in")
		if v2 != nil {
			x.In, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [formData]
			if ok && !compiler.StringArrayContainsValue([]string{"formData"}, x.In) {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 4;
		v4 := compiler.MapValueForKey(m, "name")
		if v4 != nil {
			x.Name, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_empty_value = 5;
		v5 := compiler.MapValueForKey(m, "allowEmptyValue")
		if v5 != nil {
			x.AllowEmptyValue, ok = compiler.BoolForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowEmptyValue: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string type = 6;
		v6 := compiler.MapValueForKey(m, "type")
		if v6 != nil {
			x.Type, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [string number boolean integer array file]
			if ok && !compiler.StringArrayContainsValue([]string{"string", "number", "boolean", "integer", "array", "file"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 7;
		v7 := compiler.MapValueForKey(m, "format")
		if v7 != nil {
			x.Format, ok = compiler.StringForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PrimitivesItems items = 8;
		v8 := compiler.MapValueForKey(m, "items")
		if v8 != nil {
			var err error
			x.Items, err = NewPrimitivesItems(v8, compiler.NewContext("items", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string collection_format = 9;
		v9 := compiler.MapValueForKey(m, "collectionFormat")
		if v9 != nil {
			x.CollectionFormat, ok = compiler.StringForScalarNode(v9)
			if !ok {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [csv ssv tsv pipes multi]
			if ok && !compiler.StringArrayContainsValue([]string{"csv", "ssv", "tsv", "pipes", "multi"}, x.CollectionFormat) {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 10;
		v10 := compiler.MapValueForKey(m, "default")
		if v10 != nil {
			var err error
			x.Default, err = NewAny(v10, compiler.NewContext("default", v10, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float maximum = 11;
		v11 := compiler.MapValueForKey(m, "maximum")
		if v11 != nil {
			v, ok := compiler.FloatForScalarNode(v11)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 12;
		v12 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v12 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v12)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 13;
		v13 := compiler.MapValueForKey(m, "minimum")
		if v13 != nil {
			v, ok := compiler.FloatForScalarNode(v13)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 14;
		v14 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v14 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v14)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 15;
		v15 := compiler.MapValueForKey(m, "maxLength")
		if v15 != nil {
			t, ok := compiler.IntForScalarNode(v15)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 16;
		v16 := compiler.MapValueForKey(m, "minLength")
		if v16 != nil {
			t, ok := compiler.IntForScalarNode(v16)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v16))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 17;
		v17 := compiler.MapValueForKey(m, "pattern")
		if v17 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v17)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 18;
		v18 := compiler.MapValueForKey(m, "maxItems")
		if v18 != nil {
			t, ok := compiler.IntForScalarNode(v18)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 19;
		v19 := compiler.MapValueForKey(m, "minItems")
		if v19 != nil {
			t, ok := compiler.IntForScalarNode(v19)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v19))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 20;
		v20 := compiler.MapValueForKey(m, "uniqueItems")
		if v20 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v20)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v20))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 21;
		v21 := compiler.MapValueForKey(m, "enum")
		if v21 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v21)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// float multiple_of = 22;
		v22 := compiler.MapValueForKey(m, "multipleOf")
		if v22 != nil {
			v, ok := compiler.FloatForScalarNode(v22)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v22))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 23;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewHeader creates an object of type Header if possible, returning an error if not.
func NewHeader(in *yaml.Node, context *compiler.Context) (*Header, error) {
	errors := make([]error, 0)
	x := &Header{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"collectionFormat", "default", "description", "enum", "exclusiveMaximum", "exclusiveMinimum", "format", "items", "maxItems", "maxLength", "maximum", "minItems", "minLength", "minimum", "multipleOf", "pattern", "type", "uniqueItems"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [string number integer boolean array]
			if ok && !compiler.StringArrayContainsValue([]string{"string", "number", "integer", "boolean", "array"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 2;
		v2 := compiler.MapValueForKey(m, "format")
		if v2 != nil {
			x.Format, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PrimitivesItems items = 3;
		v3 := compiler.MapValueForKey(m, "items")
		if v3 != nil {
			var err error
			x.Items, err = NewPrimitivesItems(v3, compiler.NewContext("items", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string collection_format = 4;
		v4 := compiler.MapValueForKey(m, "collectionFormat")
		if v4 != nil {
			x.CollectionFormat, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [csv ssv tsv pipes]
			if ok && !compiler.StringArrayContainsValue([]string{"csv", "ssv", "tsv", "pipes"}, x.CollectionFormat) {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 5;
		v5 := compiler.MapValueForKey(m, "default")
		if v5 != nil {
			var err error
			x.Default, err = NewAny(v5, compiler.NewContext("default", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float maximum = 6;
		v6 := compiler.MapValueForKey(m, "maximum")
		if v6 != nil {
			v, ok := compiler.FloatForScalarNode(v6)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 7;
		v7 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v7 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 8;
		v8 := compiler.MapValueForKey(m, "minimum")
		if v8 != nil {
			v, ok := compiler.FloatForScalarNode(v8)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 9;
		v9 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v9 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v9)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 10;
		v10 := compiler.MapValueForKey(m, "maxLength")
		if v10 != nil {
			t, ok := compiler.IntForScalarNode(v10)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 11;
		v11 := compiler.MapValueForKey(m, "minLength")
		if v11 != nil {
			t, ok := compiler.IntForScalarNode(v11)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 12;
		v12 := compiler.MapValueForKey(m, "pattern")
		if v12 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v12)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 13;
		v13 := compiler.MapValueForKey(m, "maxItems")
		if v13 != nil {
			t, ok := compiler.IntForScalarNode(v13)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 14;
		v14 := compiler.MapValueForKey(m, "minItems")
		if v14 != nil {
			t, ok := compiler.IntForScalarNode(v14)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 15;
		v15 := compiler.MapValueForKey(m, "uniqueItems")
		if v15 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v15)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 16;
		v16 := compiler.MapValueForKey(m, "enum")
		if v16 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v16)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// float multiple_of = 17;
		v17 := compiler.MapValueForKey(m, "multipleOf")
		if v17 != nil {
			v, ok := compiler.FloatForScalarNode(v17)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 18;
		v18 := compiler.MapValueForKey(m, "description")
		if v18 != nil {
			x.Description, ok = compiler.StringForScalarNode(v18)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 19;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewHeaderParameterSubSchema creates an object of type HeaderParameterSubSchema if possible, returning an error if not.
func NewHeaderParameterSubSchema(in *yaml.Node, context *compiler.Context) (*HeaderParameterSubSchema, error) {
	errors := make([]error, 0)
	x := &HeaderParameterSubSchema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"collectionFormat", "default", "description", "enum", "exclusiveMaximum", "exclusiveMinimum", "format", "in", "items", "maxItems", "maxLength", "maximum", "minItems", "minLength", "minimum", "multipleOf", "name", "pattern", "required", "type", "uniqueItems"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// bool required = 1;
		v1 := compiler.MapValueForKey(m, "required")
		if v1 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 2;
		v2 := compiler.MapValueForKey(m, "in")
		if v2 != nil {
			x.In, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [header]
			if ok && !compiler.StringArrayContainsValue([]string{"header"}, x.In) {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 4;
		v4 := compiler.MapValueForKey(m, "name")
		if v4 != nil {
			x.Name, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string type = 5;
		v5 := compiler.MapValueForKey(m, "type")
		if v5 != nil {
			x.Type, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [string number boolean integer array]
			if ok && !compiler.StringArrayContainsValue([]string{"string", "number", "boolean", "integer", "array"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 6;
		v6 := compiler.MapValueForKey(m, "format")
		if v6 != nil {
			x.Format, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PrimitivesItems items = 7;
		v7 := compiler.MapValueForKey(m, "items")
		if v7 != nil {
			var err error
			x.Items, err = NewPrimitivesItems(v7, compiler.NewContext("items", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string collection_format = 8;
		v8 := compiler.MapValueForKey(m, "collectionFormat")
		if v8 != nil {
			x.CollectionFormat, ok = compiler.StringForScalarNode(v8)
			if !ok {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [csv ssv tsv pipes]
			if ok && !compiler.StringArrayContainsValue([]string{"csv", "ssv", "tsv", "pipes"}, x.CollectionFormat) {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 9;
		v9 := compiler.MapValueForKey(m, "default")
		if v9 != nil {
			var err error
			x.Default, err = NewAny(v9, compiler.NewContext("default", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float maximum = 10;
		v10 := compiler.MapValueForKey(m, "maximum")
		if v10 != nil {
			v, ok := compiler.FloatForScalarNode(v10)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 11;
		v11 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v11 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v11)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 12;
		v12 := compiler.MapValueForKey(m, "minimum")
		if v12 != nil {
			v, ok := compiler.FloatForScalarNode(v12)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 13;
		v13 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v13 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v13)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 14;
		v14 := compiler.MapValueForKey(m, "maxLength")
		if v14 != nil {
			t, ok := compiler.IntForScalarNode(v14)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 15;
		v15 := compiler.MapValueForKey(m, "minLength")
		if v15 != nil {
			t, ok := compiler.IntForScalarNode(v15)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 16;
		v16 := compiler.MapValueForKey(m, "pattern")
		if v16 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v16)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v16))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 17;
		v17 := compiler.MapValueForKey(m, "maxItems")
		if v17 != nil {
			t, ok := compiler.IntForScalarNode(v17)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 18;
		v18 := compiler.MapValueForKey(m, "minItems")
		if v18 != nil {
			t, ok := compiler.IntForScalarNode(v18)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 19;
		v19 := compiler.MapValueForKey(m, "uniqueItems")
		if v19 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v19)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v19))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 20;
		v20 := compiler.MapValueForKey(m, "enum")
		if v20 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v20)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// float multiple_of = 21;
		v21 := compiler.MapValueForKey(m, "multipleOf")
		if v21 != nil {
			v, ok := compiler.FloatForScalarNode(v21)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v21))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 22;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewHeaders creates an object of type Headers if possible, returning an error if not.
func NewHeaders(in *yaml.Node, context *compiler.Context) (*Headers, error) {
	errors := make([]error, 0)
	x := &Headers{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedHeader additional_properties = 1;
		// MAP: Header
		x.AdditionalProperties = make([]*NamedHeader, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedHeader{}
				pair.Name = k
				var err error
				pair.Value, err = NewHeader(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewInfo creates an object of type Info if possible, returning an error if not.
func NewInfo(in *yaml.Node, context *compiler.Context) (*Info, error) {
	errors := make([]error, 0)
	x := &Info{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"title", "version"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"contact", "description", "license", "termsOfService", "title", "version"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string title = 1;
		v1 := compiler.MapValueForKey(m, "title")
		if v1 != nil {
			x.Title, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for title: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string version = 2;
		v2 := compiler.MapValueForKey(m, "version")
		if v2 != nil {
			x.Version, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for version: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string terms_of_service = 4;
		v4 := compiler.MapValueForKey(m, "termsOfService")
		if v4 != nil {
			x.TermsOfService, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for termsOfService: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Contact contact = 5;
		v5 := compiler.MapValueForKey(m, "contact")
		if v5 != nil {
			var err error
			x.Contact, err = NewContact(v5, compiler.NewContext("contact", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// License license = 6;
		v6 := compiler.MapValueForKey(m, "license")
		if v6 != nil {
			var err error
			x.License, err = NewLicense(v6, compiler.NewContext("license", v6, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 7;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewItemsItem creates an object of type ItemsItem if possible, returning an error if not.
func NewItemsItem(in *yaml.Node, context *compiler.Context) (*ItemsItem, error) {
	errors := make([]error, 0)
	x := &ItemsItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value for item array: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		x.Schema = make([]*Schema, 0)
		y, err := NewSchema(m, compiler.NewContext("<array>", m, context))
		if err != nil {
			return nil, err
		}
		x.Schema = append(x.Schema, y)
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewJsonReference creates an object of type JsonReference if possible, returning an error if not.
func NewJsonReference(in *yaml.Node, context *compiler.Context) (*JsonReference, error) {
	errors := make([]error, 0)
	x := &JsonReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"$ref"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string _ref = 1;
		v1 := compiler.MapValueForKey(m, "$ref")
		if v1 != nil {
			x.XRef, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for $ref: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewLicense creates an object of type License if possible, returning an error if not.
func NewLicense(in *yaml.Node, context *compiler.Context) (*License, error) {
	errors := make([]error, 0)
	x := &License{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"name"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"name", "url"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string url = 2;
		v2 := compiler.MapValueForKey(m, "url")
		if v2 != nil {
			x.Url, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 3;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedAny creates an object of type NamedAny if possible, returning an error if not.
func NewNamedAny(in *yaml.Node, context *compiler.Context) (*NamedAny, error) {
	errors := make([]error, 0)
	x := &NamedAny{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewAny(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedHeader creates an object of type NamedHeader if possible, returning an error if not.
func NewNamedHeader(in *yaml.Node, context *compiler.Context) (*NamedHeader, error) {
	errors := make([]error, 0)
	x := &NamedHeader{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Header value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewHeader(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedParameter creates an object of type NamedParameter if possible, returning an error if not.
func NewNamedParameter(in *yaml.Node, context *compiler.Context) (*NamedParameter, error) {
	errors := make([]error, 0)
	x := &NamedParameter{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Parameter value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewParameter(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedPathItem creates an object of type NamedPathItem if possible, returning an error if not.
func NewNamedPathItem(in *yaml.Node, context *compiler.Context) (*NamedPathItem, error) {
	errors := make([]error, 0)
	x := &NamedPathItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PathItem value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewPathItem(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedResponse creates an object of type NamedResponse if possible, returning an error if not.
func NewNamedResponse(in *yaml.Node, context *compiler.Context) (*NamedResponse, error) {
	errors := make([]error, 0)
	x := &NamedResponse{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Response value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewResponse(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedResponseValue creates an object of type NamedResponseValue if possible, returning an error if not.
func NewNamedResponseValue(in *yaml.Node, context *compiler.Context) (*NamedResponseValue, error) {
	errors := make([]error, 0)
	x := &NamedResponseValue{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ResponseValue value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewResponseValue(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedSchema creates an object of type NamedSchema if possible, returning an error if not.
func NewNamedSchema(in *yaml.Node, context *compiler.Context) (*NamedSchema, error) {
	errors := make([]error, 0)
	x := &NamedSchema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Schema value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewSchema(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedSecurityDefinitionsItem creates an object of type NamedSecurityDefinitionsItem if possible, returning an error if not.
func NewNamedSecurityDefinitionsItem(in *yaml.Node, context *compiler.Context) (*NamedSecurityDefinitionsItem, error) {
	errors := make([]error, 0)
	x := &NamedSecurityDefinitionsItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// SecurityDefinitionsItem value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewSecurityDefinitionsItem(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedString creates an object of type NamedString if possible, returning an error if not.
func NewNamedString(in *yaml.Node, context *compiler.Context) (*NamedString, error) {
	errors := make([]error, 0)
	x := &NamedString{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			x.Value, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for value: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedStringArray creates an object of type NamedStringArray if possible, returning an error if not.
func NewNamedStringArray(in *yaml.Node, context *compiler.Context) (*NamedStringArray, error) {
	errors := make([]error, 0)
	x := &NamedStringArray{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// StringArray value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewStringArray(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNonBodyParameter creates an object of type NonBodyParameter if possible, returning an error if not.
func NewNonBodyParameter(in *yaml.Node, context *compiler.Context) (*NonBodyParameter, error) {
	errors := make([]error, 0)
	x := &NonBodyParameter{}
	matched := false
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"in", "name", "type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// HeaderParameterSubSchema header_parameter_sub_schema = 1;
		{
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewHeaderParameterSubSchema(m, compiler.NewContext("headerParameterSubSchema", m, context))
			if matchingError == nil {
				x.Oneof = &NonBodyParameter_HeaderParameterSubSchema{HeaderParameterSubSchema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
		// FormDataParameterSubSchema form_data_parameter_sub_schema = 2;
		{
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewFormDataParameterSubSchema(m, compiler.NewContext("formDataParameterSubSchema", m, context))
			if matchingError == nil {
				x.Oneof = &NonBodyParameter_FormDataParameterSubSchema{FormDataParameterSubSchema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
		// QueryParameterSubSchema query_parameter_sub_schema = 3;
		{
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewQueryParameterSubSchema(m, compiler.NewContext("queryParameterSubSchema", m, context))
			if matchingError == nil {
				x.Oneof = &NonBodyParameter_QueryParameterSubSchema{QueryParameterSubSchema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
		// PathParameterSubSchema path_parameter_sub_schema = 4;
		{
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewPathParameterSubSchema(m, compiler.NewContext("pathParameterSubSchema", m, context))
			if matchingError == nil {
				x.Oneof = &NonBodyParameter_PathParameterSubSchema{PathParameterSubSchema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid NonBodyParameter")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauth2AccessCodeSecurity creates an object of type Oauth2AccessCodeSecurity if possible, returning an error if not.
func NewOauth2AccessCodeSecurity(in *yaml.Node, context *compiler.Context) (*Oauth2AccessCodeSecurity, error) {
	errors := make([]error, 0)
	x := &Oauth2AccessCodeSecurity{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"authorizationUrl", "flow", "tokenUrl", "type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"authorizationUrl", "description", "flow", "scopes", "tokenUrl", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [oauth2]
			if ok && !compiler.StringArrayContainsValue([]string{"oauth2"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string flow = 2;
		v2 := compiler.MapValueForKey(m, "flow")
		if v2 != nil {
			x.Flow, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [accessCode]
			if ok && !compiler.StringArrayContainsValue([]string{"accessCode"}, x.Flow) {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Oauth2Scopes scopes = 3;
		v3 := compiler.MapValueForKey(m, "scopes")
		if v3 != nil {
			var err error
			x.Scopes, err = NewOauth2Scopes(v3, compiler.NewContext("scopes", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string authorization_url = 4;
		v4 := compiler.MapValueForKey(m, "authorizationUrl")
		if v4 != nil {
			x.AuthorizationUrl, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for authorizationUrl: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string token_url = 5;
		v5 := compiler.MapValueForKey(m, "tokenUrl")
		if v5 != nil {
			x.TokenUrl, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for tokenUrl: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 6;
		v6 := compiler.MapValueForKey(m, "description")
		if v6 != nil {
			x.Description, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 7;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauth2ApplicationSecurity creates an object of type Oauth2ApplicationSecurity if possible, returning an error if not.
func NewOauth2ApplicationSecurity(in *yaml.Node, context *compiler.Context) (*Oauth2ApplicationSecurity, error) {
	errors := make([]error, 0)
	x := &Oauth2ApplicationSecurity{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"flow", "tokenUrl", "type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "flow", "scopes", "tokenUrl", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [oauth2]
			if ok && !compiler.StringArrayContainsValue([]string{"oauth2"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string flow = 2;
		v2 := compiler.MapValueForKey(m, "flow")
		if v2 != nil {
			x.Flow, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [application]
			if ok && !compiler.StringArrayContainsValue([]string{"application"}, x.Flow) {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Oauth2Scopes scopes = 3;
		v3 := compiler.MapValueForKey(m, "scopes")
		if v3 != nil {
			var err error
			x.Scopes, err = NewOauth2Scopes(v3, compiler.NewContext("scopes", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string token_url = 4;
		v4 := compiler.MapValueForKey(m, "tokenUrl")
		if v4 != nil {
			x.TokenUrl, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for tokenUrl: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 5;
		v5 := compiler.MapValueForKey(m, "description")
		if v5 != nil {
			x.Description, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 6;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauth2ImplicitSecurity creates an object of type Oauth2ImplicitSecurity if possible, returning an error if not.
func NewOauth2ImplicitSecurity(in *yaml.Node, context *compiler.Context) (*Oauth2ImplicitSecurity, error) {
	errors := make([]error, 0)
	x := &Oauth2ImplicitSecurity{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"authorizationUrl", "flow", "type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"authorizationUrl", "description", "flow", "scopes", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [oauth2]
			if ok && !compiler.StringArrayContainsValue([]string{"oauth2"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string flow = 2;
		v2 := compiler.MapValueForKey(m, "flow")
		if v2 != nil {
			x.Flow, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [implicit]
			if ok && !compiler.StringArrayContainsValue([]string{"implicit"}, x.Flow) {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Oauth2Scopes scopes = 3;
		v3 := compiler.MapValueForKey(m, "scopes")
		if v3 != nil {
			var err error
			x.Scopes, err = NewOauth2Scopes(v3, compiler.NewContext("scopes", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string authorization_url = 4;
		v4 := compiler.MapValueForKey(m, "authorizationUrl")
		if v4 != nil {
			x.AuthorizationUrl, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for authorizationUrl: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 5;
		v5 := compiler.MapValueForKey(m, "description")
		if v5 != nil {
			x.Description, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 6;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauth2PasswordSecurity creates an object of type Oauth2PasswordSecurity if possible, returning an error if not.
func NewOauth2PasswordSecurity(in *yaml.Node, context *compiler.Context) (*Oauth2PasswordSecurity, error) {
	errors := make([]error, 0)
	x := &Oauth2PasswordSecurity{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"flow", "tokenUrl", "type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "flow", "scopes", "tokenUrl", "type"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [oauth2]
			if ok && !compiler.StringArrayContainsValue([]string{"oauth2"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string flow = 2;
		v2 := compiler.MapValueForKey(m, "flow")
		if v2 != nil {
			x.Flow, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [password]
			if ok && !compiler.StringArrayContainsValue([]string{"password"}, x.Flow) {
				message := fmt.Sprintf("has unexpected value for flow: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Oauth2Scopes scopes = 3;
		v3 := compiler.MapValueForKey(m, "scopes")
		if v3 != nil {
			var err error
			x.Scopes, err = NewOauth2Scopes(v3, compiler.NewContext("scopes", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string token_url = 4;
		v4 := compiler.MapValueForKey(m, "tokenUrl")
		if v4 != nil {
			x.TokenUrl, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for tokenUrl: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 5;
		v5 := compiler.MapValueForKey(m, "description")
		if v5 != nil {
			x.Description, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 6;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauth2Scopes creates an object of type Oauth2Scopes if possible, returning an error if not.
func NewOauth2Scopes(in *yaml.Node, context *compiler.Context) (*Oauth2Scopes, error) {
	errors := make([]error, 0)
	x := &Oauth2Scopes{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedString additional_properties = 1;
		// MAP: string
		x.AdditionalProperties = make([]*NamedString, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedString{}
				pair.Name = k
				pair.Value, _ = compiler.StringForScalarNode(v)
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOperation creates an object of type Operation if possible, returning an error if not.
func NewOperation(in *yaml.Node, context *compiler.Context) (*Operation, error) {
	errors := make([]error, 0)
	x := &Operation{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"responses"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"consumes", "deprecated", "description", "externalDocs", "operationId", "parameters", "produces", "responses", "schemes", "security", "summary", "tags"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated string tags = 1;
		v1 := compiler.MapValueForKey(m, "tags")
		if v1 != nil {
			v, ok := compiler.SequenceNodeForNode(v1)
			if ok {
				x.Tags = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for tags: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string summary = 2;
		v2 := compiler.MapValueForKey(m, "summary")
		if v2 != nil {
			x.Summary, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for summary: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ExternalDocs external_docs = 4;
		v4 := compiler.MapValueForKey(m, "externalDocs")
		if v4 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v4, compiler.NewContext("externalDocs", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string operation_id = 5;
		v5 := compiler.MapValueForKey(m, "operationId")
		if v5 != nil {
			x.OperationId, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for operationId: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string produces = 6;
		v6 := compiler.MapValueForKey(m, "produces")
		if v6 != nil {
			v, ok := compiler.SequenceNodeForNode(v6)
			if ok {
				x.Produces = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for produces: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string consumes = 7;
		v7 := compiler.MapValueForKey(m, "consumes")
		if v7 != nil {
			v, ok := compiler.SequenceNodeForNode(v7)
			if ok {
				x.Consumes = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for consumes: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated ParametersItem parameters = 8;
		v8 := compiler.MapValueForKey(m, "parameters")
		if v8 != nil {
			// repeated ParametersItem
			x.Parameters = make([]*ParametersItem, 0)
			a, ok := compiler.SequenceNodeForNode(v8)
			if ok {
				for _, item := range a.Content {
					y, err := NewParametersItem(item, compiler.NewContext("parameters", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Parameters = append(x.Parameters, y)
				}
			}
		}
		// Responses responses = 9;
		v9 := compiler.MapValueForKey(m, "responses")
		if v9 != nil {
			var err error
			x.Responses, err = NewResponses(v9, compiler.NewContext("responses", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated string schemes = 10;
		v10 := compiler.MapValueForKey(m, "schemes")
		if v10 != nil {
			v, ok := compiler.SequenceNodeForNode(v10)
			if ok {
				x.Schemes = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for schemes: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [http https ws wss]
			if ok && !compiler.StringArrayContainsValues([]string{"http", "https", "ws", "wss"}, x.Schemes) {
				message := fmt.Sprintf("has unexpected value for schemes: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool deprecated = 11;
		v11 := compiler.MapValueForKey(m, "deprecated")
		if v11 != nil {
			x.Deprecated, ok = compiler.BoolForScalarNode(v11)
			if !ok {
				message := fmt.Sprintf("has unexpected value for deprecated: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated SecurityRequirement security = 12;
		v12 := compiler.MapValueForKey(m, "security")
		if v12 != nil {
			// repeated SecurityRequirement
			x.Security = make([]*SecurityRequirement, 0)
			a, ok := compiler.SequenceNodeForNode(v12)
			if ok {
				for _, item := range a.Content {
					y, err := NewSecurityRequirement(item, compiler.NewContext("security", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Security = append(x.Security, y)
				}
			}
		}
		// repeated NamedAny vendor_extension = 13;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewParameter creates an object of type Parameter if possible, returning an error if not.
func NewParameter(in *yaml.Node, context *compiler.Context) (*Parameter, error) {
	errors := make([]error, 0)
	x := &Parameter{}
	matched := false
	// BodyParameter body_parameter = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewBodyParameter(m, compiler.NewContext("bodyParameter", m, context))
			if matchingError == nil {
				x.Oneof = &Parameter_BodyParameter{BodyParameter: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// NonBodyParameter non_body_parameter = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewNonBodyParameter(m, compiler.NewContext("nonBodyParameter", m, context))
			if matchingError == nil {
				x.Oneof = &Parameter_NonBodyParameter{NonBodyParameter: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid Parameter")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewParameterDefinitions creates an object of type ParameterDefinitions if possible, returning an error if not.
func NewParameterDefinitions(in *yaml.Node, context *compiler.Context) (*ParameterDefinitions, error) {
	errors := make([]error, 0)
	x := &ParameterDefinitions{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedParameter additional_properties = 1;
		// MAP: Parameter
		x.AdditionalProperties = make([]*NamedParameter, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedParameter{}
				pair.Name = k
				var err error
				pair.Value, err = NewParameter(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewParametersItem creates an object of type ParametersItem if possible, returning an error if not.
func NewParametersItem(in *yaml.Node, context *compiler.Context) (*ParametersItem, error) {
	errors := make([]error, 0)
	x := &ParametersItem{}
	matched := false
	// Parameter parameter = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewParameter(m, compiler.NewContext("parameter", m, context))
			if matchingError == nil {
				x.Oneof = &ParametersItem_Parameter{Parameter: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// JsonReference json_reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewJsonReference(m, compiler.NewContext("jsonReference", m, context))
			if matchingError == nil {
				x.Oneof = &ParametersItem_JsonReference{JsonReference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid ParametersItem")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewPathItem creates an object of type PathItem if possible, returning an error if not.
func NewPathItem(in *yaml.Node, context *compiler.Context) (*PathItem, error) {
	errors := make([]error, 0)
	x := &PathItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"$ref", "delete", "get", "head", "options", "parameters", "patch", "post", "put"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string _ref = 1;
		v1 := compiler.MapValueForKey(m, "$ref")
		if v1 != nil {
			x.XRef, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for $ref: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Operation get = 2;
		v2 := compiler.MapValueForKey(m, "get")
		if v2 != nil {
			var err error
			x.Get, err = NewOperation(v2, compiler.NewContext("get", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation put = 3;
		v3 := compiler.MapValueForKey(m, "put")
		if v3 != nil {
			var err error
			x.Put, err = NewOperation(v3, compiler.NewContext("put", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation post = 4;
		v4 := compiler.MapValueForKey(m, "post")
		if v4 != nil {
			var err error
			x.Post, err = NewOperation(v4, compiler.NewContext("post", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation delete = 5;
		v5 := compiler.MapValueForKey(m, "delete")
		if v5 != nil {
			var err error
			x.Delete, err = NewOperation(v5, compiler.NewContext("delete", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation options = 6;
		v6 := compiler.MapValueForKey(m, "options")
		if v6 != nil {
			var err error
			x.Options, err = NewOperation(v6, compiler.NewContext("options", v6, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation head = 7;
		v7 := compiler.MapValueForKey(m, "head")
		if v7 != nil {
			var err error
			x.Head, err = NewOperation(v7, compiler.NewContext("head", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation patch = 8;
		v8 := compiler.MapValueForKey(m, "patch")
		if v8 != nil {
			var err error
			x.Patch, err = NewOperation(v8, compiler.NewContext("patch", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated ParametersItem parameters = 9;
		v9 := compiler.MapValueForKey(m, "parameters")
		if v9 != nil {
			// repeated ParametersItem
			x.Parameters = make([]*ParametersItem, 0)
			a, ok := compiler.SequenceNodeForNode(v9)
			if ok {
				for _, item := range a.Content {
					y, err := NewParametersItem(item, compiler.NewContext("parameters", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Parameters = append(x.Parameters, y)
				}
			}
		}
		// repeated NamedAny vendor_extension = 10;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewPathParameterSubSchema creates an object of type PathParameterSubSchema if possible, returning an error if not.
func NewPathParameterSubSchema(in *yaml.Node, context *compiler.Context) (*PathParameterSubSchema, error) {
	errors := make([]error, 0)
	x := &PathParameterSubSchema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"required"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"collectionFormat", "default", "description", "enum", "exclusiveMaximum", "exclusiveMinimum", "format", "in", "items", "maxItems", "maxLength", "maximum", "minItems", "minLength", "minimum", "multipleOf", "name", "pattern", "required", "type", "uniqueItems"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// bool required = 1;
		v1 := compiler.MapValueForKey(m, "required")
		if v1 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 2;
		v2 := compiler.MapValueForKey(m, "in")
		if v2 != nil {
			x.In, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [path]
			if ok && !compiler.StringArrayContainsValue([]string{"path"}, x.In) {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 4;
		v4 := compiler.MapValueForKey(m, "name")
		if v4 != nil {
			x.Name, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string type = 5;
		v5 := compiler.MapValueForKey(m, "type")
		if v5 != nil {
			x.Type, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [string number boolean integer array]
			if ok && !compiler.StringArrayContainsValue([]string{"string", "number", "boolean", "integer", "array"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 6;
		v6 := compiler.MapValueForKey(m, "format")
		if v6 != nil {
			x.Format, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PrimitivesItems items = 7;
		v7 := compiler.MapValueForKey(m, "items")
		if v7 != nil {
			var err error
			x.Items, err = NewPrimitivesItems(v7, compiler.NewContext("items", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string collection_format = 8;
		v8 := compiler.MapValueForKey(m, "collectionFormat")
		if v8 != nil {
			x.CollectionFormat, ok = compiler.StringForScalarNode(v8)
			if !ok {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [csv ssv tsv pipes]
			if ok && !compiler.StringArrayContainsValue([]string{"csv", "ssv", "tsv", "pipes"}, x.CollectionFormat) {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 9;
		v9 := compiler.MapValueForKey(m, "default")
		if v9 != nil {
			var err error
			x.Default, err = NewAny(v9, compiler.NewContext("default", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float maximum = 10;
		v10 := compiler.MapValueForKey(m, "maximum")
		if v10 != nil {
			v, ok := compiler.FloatForScalarNode(v10)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 11;
		v11 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v11 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v11)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 12;
		v12 := compiler.MapValueForKey(m, "minimum")
		if v12 != nil {
			v, ok := compiler.FloatForScalarNode(v12)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 13;
		v13 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v13 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v13)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 14;
		v14 := compiler.MapValueForKey(m, "maxLength")
		if v14 != nil {
			t, ok := compiler.IntForScalarNode(v14)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 15;
		v15 := compiler.MapValueForKey(m, "minLength")
		if v15 != nil {
			t, ok := compiler.IntForScalarNode(v15)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 16;
		v16 := compiler.MapValueForKey(m, "pattern")
		if v16 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v16)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v16))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 17;
		v17 := compiler.MapValueForKey(m, "maxItems")
		if v17 != nil {
			t, ok := compiler.IntForScalarNode(v17)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 18;
		v18 := compiler.MapValueForKey(m, "minItems")
		if v18 != nil {
			t, ok := compiler.IntForScalarNode(v18)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 19;
		v19 := compiler.MapValueForKey(m, "uniqueItems")
		if v19 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v19)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v19))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 20;
		v20 := compiler.MapValueForKey(m, "enum")
		if v20 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v20)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// float multiple_of = 21;
		v21 := compiler.MapValueForKey(m, "multipleOf")
		if v21 != nil {
			v, ok := compiler.FloatForScalarNode(v21)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v21))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 22;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewPaths creates an object of type Paths if possible, returning an error if not.
func NewPaths(in *yaml.Node, context *compiler.Context) (*Paths, error) {
	errors := make([]error, 0)
	x := &Paths{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{}
		allowedPatterns := []*regexp.Regexp{pattern0, pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated NamedAny vendor_extension = 1;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
		// repeated NamedPathItem path = 2;
		// MAP: PathItem ^/
		x.Path = make([]*NamedPathItem, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "/") {
					pair := &NamedPathItem{}
					pair.Name = k
					var err error
					pair.Value, err = NewPathItem(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Path = append(x.Path, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewPrimitivesItems creates an object of type PrimitivesItems if possible, returning an error if not.
func NewPrimitivesItems(in *yaml.Node, context *compiler.Context) (*PrimitivesItems, error) {
	errors := make([]error, 0)
	x := &PrimitivesItems{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"collectionFormat", "default", "enum", "exclusiveMaximum", "exclusiveMinimum", "format", "items", "maxItems", "maxLength", "maximum", "minItems", "minLength", "minimum", "multipleOf", "pattern", "type", "uniqueItems"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [string number integer boolean array]
			if ok && !compiler.StringArrayContainsValue([]string{"string", "number", "integer", "boolean", "array"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 2;
		v2 := compiler.MapValueForKey(m, "format")
		if v2 != nil {
			x.Format, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PrimitivesItems items = 3;
		v3 := compiler.MapValueForKey(m, "items")
		if v3 != nil {
			var err error
			x.Items, err = NewPrimitivesItems(v3, compiler.NewContext("items", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string collection_format = 4;
		v4 := compiler.MapValueForKey(m, "collectionFormat")
		if v4 != nil {
			x.CollectionFormat, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [csv ssv tsv pipes]
			if ok && !compiler.StringArrayContainsValue([]string{"csv", "ssv", "tsv", "pipes"}, x.CollectionFormat) {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 5;
		v5 := compiler.MapValueForKey(m, "default")
		if v5 != nil {
			var err error
			x.Default, err = NewAny(v5, compiler.NewContext("default", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float maximum = 6;
		v6 := compiler.MapValueForKey(m, "maximum")
		if v6 != nil {
			v, ok := compiler.FloatForScalarNode(v6)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 7;
		v7 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v7 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 8;
		v8 := compiler.MapValueForKey(m, "minimum")
		if v8 != nil {
			v, ok := compiler.FloatForScalarNode(v8)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 9;
		v9 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v9 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v9)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 10;
		v10 := compiler.MapValueForKey(m, "maxLength")
		if v10 != nil {
			t, ok := compiler.IntForScalarNode(v10)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 11;
		v11 := compiler.MapValueForKey(m, "minLength")
		if v11 != nil {
			t, ok := compiler.IntForScalarNode(v11)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 12;
		v12 := compiler.MapValueForKey(m, "pattern")
		if v12 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v12)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 13;
		v13 := compiler.MapValueForKey(m, "maxItems")
		if v13 != nil {
			t, ok := compiler.IntForScalarNode(v13)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 14;
		v14 := compiler.MapValueForKey(m, "minItems")
		if v14 != nil {
			t, ok := compiler.IntForScalarNode(v14)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 15;
		v15 := compiler.MapValueForKey(m, "uniqueItems")
		if v15 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v15)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 16;
		v16 := compiler.MapValueForKey(m, "enum")
		if v16 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v16)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// float multiple_of = 17;
		v17 := compiler.MapValueForKey(m, "multipleOf")
		if v17 != nil {
			v, ok := compiler.FloatForScalarNode(v17)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 18;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewProperties creates an object of type Properties if possible, returning an error if not.
func NewProperties(in *yaml.Node, context *compiler.Context) (*Properties, error) {
	errors := make([]error, 0)
	x := &Properties{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedSchema additional_properties = 1;
		// MAP: Schema
		x.AdditionalProperties = make([]*NamedSchema, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedSchema{}
				pair.Name = k
				var err error
				pair.Value, err = NewSchema(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewQueryParameterSubSchema creates an object of type QueryParameterSubSchema if possible, returning an error if not.
func NewQueryParameterSubSchema(in *yaml.Node, context *compiler.Context) (*QueryParameterSubSchema, error) {
	errors := make([]error, 0)
	x := &QueryParameterSubSchema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"allowEmptyValue", "collectionFormat", "default", "description", "enum", "exclusiveMaximum", "exclusiveMinimum", "format", "in", "items", "maxItems", "maxLength", "maximum", "minItems", "minLength", "minimum", "multipleOf", "name", "pattern", "required", "type", "uniqueItems"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// bool required = 1;
		v1 := compiler.MapValueForKey(m, "required")
		if v1 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 2;
		v2 := compiler.MapValueForKey(m, "in")
		if v2 != nil {
			x.In, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [query]
			if ok && !compiler.StringArrayContainsValue([]string{"query"}, x.In) {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 4;
		v4 := compiler.MapValueForKey(m, "name")
		if v4 != nil {
			x.Name, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_empty_value = 5;
		v5 := compiler.MapValueForKey(m, "allowEmptyValue")
		if v5 != nil {
			x.AllowEmptyValue, ok = compiler.BoolForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowEmptyValue: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string type = 6;
		v6 := compiler.MapValueForKey(m, "type")
		if v6 != nil {
			x.Type, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [string number boolean integer array]
			if ok && !compiler.StringArrayContainsValue([]string{"string", "number", "boolean", "integer", "array"}, x.Type) {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 7;
		v7 := compiler.MapValueForKey(m, "format")
		if v7 != nil {
			x.Format, ok = compiler.StringForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PrimitivesItems items = 8;
		v8 := compiler.MapValueForKey(m, "items")
		if v8 != nil {
			var err error
			x.Items, err = NewPrimitivesItems(v8, compiler.NewContext("items", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string collection_format = 9;
		v9 := compiler.MapValueForKey(m, "collectionFormat")
		if v9 != nil {
			x.CollectionFormat, ok = compiler.StringForScalarNode(v9)
			if !ok {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
			// check for valid enum values
			// [csv ssv tsv pipes multi]
			if ok && !compiler.StringArrayContainsValue([]string{"csv", "ssv", "tsv", "pipes", "multi"}, x.CollectionFormat) {
				message := fmt.Sprintf("has unexpected value for collectionFormat: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 10;
		v10 := compiler.MapValueForKey(m, "default")
		if v10 != nil {
			var err error
			x.Default, err = NewAny(v10, compiler.NewContext("default", v10, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float maximum = 11;
		v11 := compiler.MapValueForKey(m, "maximum")
		if v11 != nil {
			v, ok := compiler.FloatForScalarNode(v11)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 12;
		v12 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v12 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v12)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 13;
		v13 := compiler.MapValueForKey(m, "minimum")
		if v13 != nil {
			v, ok := compiler.FloatForScalarNode(v13)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 14;
		v14 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v14 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v14)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 15;
		v15 := compiler.MapValueForKey(m, "maxLength")
		if v15 != nil {
			t, ok := compiler.IntForScalarNode(v15)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 16;
		v16 := compiler.MapValueForKey(m, "minLength")
		if v16 != nil {
			t, ok := compiler.IntForScalarNode(v16)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v16))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 17;
		v17 := compiler.MapValueForKey(m, "pattern")
		if v17 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v17)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 18;
		v18 := compiler.MapValueForKey(m, "maxItems")
		if v18 != nil {
			t, ok := compiler.IntForScalarNode(v18)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 19;
		v19 := compiler.MapValueForKey(m, "minItems")
		if v19 != nil {
			t, ok := compiler.IntForScalarNode(v19)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v19))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 20;
		v20 := compiler.MapValueForKey(m, "uniqueItems")
		if v20 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v20)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v20))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 21;
		v21 := compiler.MapValueForKey(m, "enum")
		if v21 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v21)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// float multiple_of = 22;
		v22 := compiler.MapValueForKey(m, "multipleOf")
		if v22 != nil {
			v, ok := compiler.FloatForScalarNode(v22)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v22))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 23;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponse creates an object of type Response if possible, returning an error if not.
func NewResponse(in *yaml.Node, context *compiler.Context) (*Response, error) {
	errors := make([]error, 0)
	x := &Response{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"description"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "examples", "headers", "schema"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// SchemaItem schema = 2;
		v2 := compiler.MapValueForKey(m, "schema")
		if v2 != nil {
			var err error
			x.Schema, err = NewSchemaItem(v2, compiler.NewContext("schema", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Headers headers = 3;
		v3 := compiler.MapValueForKey(m, "headers")
		if v3 != nil {
			var err error
			x.Headers, err = NewHeaders(v3, compiler.NewContext("headers", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Examples examples = 4;
		v4 := compiler.MapValueForKey(m, "examples")
		if v4 != nil {
			var err error
			x.Examples, err = NewExamples(v4, compiler.NewContext("examples", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 5;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponseDefinitions creates an object of type ResponseDefinitions if possible, returning an error if not.
func NewResponseDefinitions(in *yaml.Node, context *compiler.Context) (*ResponseDefinitions, error) {
	errors := make([]error, 0)
	x := &ResponseDefinitions{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedResponse additional_properties = 1;
		// MAP: Response
		x.AdditionalProperties = make([]*NamedResponse, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedResponse{}
				pair.Name = k
				var err error
				pair.Value, err = NewResponse(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponseValue creates an object of type ResponseValue if possible, returning an error if not.
func NewResponseValue(in *yaml.Node, context *compiler.Context) (*ResponseValue, error) {
	errors := make([]error, 0)
	x := &ResponseValue{}
	matched := false
	// Response response = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewResponse(m, compiler.NewContext("response", m, context))
			if matchingError == nil {
				x.Oneof = &ResponseValue_Response{Response: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// JsonReference json_reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewJsonReference(m, compiler.NewContext("jsonReference", m, context))
			if matchingError == nil {
				x.Oneof = &ResponseValue_JsonReference{JsonReference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid ResponseValue")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponses creates an object of type Responses if possible, returning an error if not.
func NewResponses(in *yaml.Node, context *compiler.Context) (*Responses, error) {
	errors := make([]error, 0)
	x := &Responses{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{}
		allowedPatterns := []*regexp.Regexp{pattern2, pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated NamedResponseValue response_code = 1;
		// MAP: ResponseValue ^([0-9]{3})$|^(default)$
		x.ResponseCode = make([]*NamedResponseValue, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if pattern2.MatchString(k) {
					pair := &NamedResponseValue{}
					pair.Name = k
					var err error
					pair.Value, err = NewResponseValue(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.ResponseCode = append(x.ResponseCode, pair)
				}
			}
		}
		// repeated NamedAny vendor_extension = 2;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSchema creates an object of type Schema if possible, returning an error if not.
func NewSchema(in *yaml.Node, context *compiler.Context) (*Schema, error) {
	errors := make([]error, 0)
	x := &Schema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"$ref", "additionalProperties", "allOf", "default", "description", "discriminator", "enum", "example", "exclusiveMaximum", "exclusiveMinimum", "externalDocs", "format", "items", "maxItems", "maxLength", "maxProperties", "maximum", "minItems", "minLength", "minProperties", "minimum", "multipleOf", "pattern", "properties", "readOnly", "required", "title", "type", "uniqueItems", "xml"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string _ref = 1;
		v1 := compiler.MapValueForKey(m, "$ref")
		if v1 != nil {
			x.XRef, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for $ref: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 2;
		v2 := compiler.MapValueForKey(m, "format")
		if v2 != nil {
			x.Format, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string title = 3;
		v3 := compiler.MapValueForKey(m, "title")
		if v3 != nil {
			x.Title, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for title: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 4;
		v4 := compiler.MapValueForKey(m, "description")
		if v4 != nil {
			x.Description, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any default = 5;
		v5 := compiler.MapValueForKey(m, "default")
		if v5 != nil {
			var err error
			x.Default, err = NewAny(v5, compiler.NewContext("default", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// float multiple_of = 6;
		v6 := compiler.MapValueForKey(m, "multipleOf")
		if v6 != nil {
			v, ok := compiler.FloatForScalarNode(v6)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float maximum = 7;
		v7 := compiler.MapValueForKey(m, "maximum")
		if v7 != nil {
			v, ok := compiler.FloatForScalarNode(v7)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 8;
		v8 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v8 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v8)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 9;
		v9 := compiler.MapValueForKey(m, "minimum")
		if v9 != nil {
			v, ok := compiler.FloatForScalarNode(v9)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 10;
		v10 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v10 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v10)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 11;
		v11 := compiler.MapValueForKey(m, "maxLength")
		if v11 != nil {
			t, ok := compiler.IntForScalarNode(v11)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 12;
		v12 := compiler.MapValueForKey(m, "minLength")
		if v12 != nil {
			t, ok := compiler.IntForScalarNode(v12)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 13;
		v13 := compiler.MapValueForKey(m, "pattern")
		if v13 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v13)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 14;
		v14 := compiler.MapValueForKey(m, "maxItems")
		if v14 != nil {
			t, ok := compiler.IntForScalarNode(v14)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 15;
		v15 := compiler.MapValueForKey(m, "minItems")
		if v15 != nil {
			t, ok := compiler.IntForScalarNode(v15)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 16;
		v16 := compiler.MapValueForKey(m, "uniqueItems")
		if v16 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v16)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v16))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_properties = 17;
		v17 := compiler.MapValueForKey(m, "maxProperties")
		if v17 != nil {
			t, ok := compiler.IntForScalarNode(v17)
			if ok {
				x.MaxProperties = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxProperties: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_properties = 18;
		v18 := compiler.MapValueForKey(m, "minProperties")
		if v18 != nil {
			t, ok := compiler.IntForScalarNode(v18)
			if ok {
				x.MinProperties = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minProperties: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string required = 19;
		v19 := compiler.MapValueForKey(m, "required")
		if v19 != nil {
			v, ok := compiler.SequenceNodeForNode(v19)
			if ok {
				x.Required = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v19))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 20;
		v20 := compiler.MapValueForKey(m, "enum")
		if v20 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v20)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// AdditionalPropertiesItem additional_properties = 21;
		v21 := compiler.MapValueForKey(m, "additionalProperties")
		if v21 != nil {
			var err error
			x.AdditionalProperties, err = NewAdditionalPropertiesItem(v21, compiler.NewContext("additionalProperties", v21, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// TypeItem type = 22;
		v22 := compiler.MapValueForKey(m, "type")
		if v22 != nil {
			var err error
			x.Type, err = NewTypeItem(v22, compiler.NewContext("type", v22, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ItemsItem items = 23;
		v23 := compiler.MapValueForKey(m, "items")
		if v23 != nil {
			var err error
			x.Items, err = NewItemsItem(v23, compiler.NewContext("items", v23, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated Schema all_of = 24;
		v24 := compiler.MapValueForKey(m, "allOf")
		if v24 != nil {
			// repeated Schema
			x.AllOf = make([]*Schema, 0)
			a, ok := compiler.SequenceNodeForNode(v24)
			if ok {
				for _, item := range a.Content {
					y, err := NewSchema(item, compiler.NewContext("allOf", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.AllOf = append(x.AllOf, y)
				}
			}
		}
		// Properties properties = 25;
		v25 := compiler.MapValueForKey(m, "properties")
		if v25 != nil {
			var err error
			x.Properties, err = NewProperties(v25, compiler.NewContext("properties", v25, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string discriminator = 26;
		v26 := compiler.MapValueForKey(m, "discriminator")
		if v26 != nil {
			x.Discriminator, ok = compiler.StringForScalarNode(v26)
			if !ok {
				message := fmt.Sprintf("has unexpected value for discriminator: %s", compiler.Display(v26))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool read_only = 27;
		v27 := compiler.MapValueForKey(m, "readOnly")
		if v27 != nil {
			x.ReadOnly, ok = compiler.BoolForScalarNode(v27)
			if !ok {
				message := fmt.Sprintf("has unexpected value for readOnly: %s", compiler.Display(v27))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Xml xml = 28;
		v28 := compiler.MapValueForKey(m, "xml")
		if v28 != nil {
			var err error
			x.Xml, err = NewXml(v28, compiler.NewContext("xml", v28, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ExternalDocs external_docs = 29;
		v29 := compiler.MapValueForKey(m, "externalDocs")
		if v29 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v29, compiler.NewContext("externalDocs", v29, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Any example = 30;
		v30 := compiler.MapValueForKey(m, "example")
		if v30 != nil {
			var err error
			x.Example, err = NewAny(v30, compiler.NewContext("example", v30, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 31;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSchemaItem creates an object of type SchemaItem if possible, returning an error if not.
func NewSchemaItem(in *yaml.Node, context *compiler.Context) (*SchemaItem, error) {
	errors := make([]error, 0)
	x := &SchemaItem{}
	matched := false
	// Schema schema = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewSchema(m, compiler.NewContext("schema", m, context))
			if matchingError == nil {
				x.Oneof = &SchemaItem_Schema{Schema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// FileSchema file_schema = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewFileSchema(m, compiler.NewContext("fileSchema", m, context))
			if matchingError == nil {
				x.Oneof = &SchemaItem_FileSchema{FileSchema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid SchemaItem")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecurityDefinitions creates an object of type SecurityDefinitions if possible, returning an error if not.
func NewSecurityDefinitions(in *yaml.Node, context *compiler.Context) (*SecurityDefinitions, error) {
	errors := make([]error, 0)
	x := &SecurityDefinitions{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedSecurityDefinitionsItem additional_properties = 1;
		// MAP: SecurityDefinitionsItem
		x.AdditionalProperties = make([]*NamedSecurityDefinitionsItem, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedSecurityDefinitionsItem{}
				pair.Name = k
				var err error
				pair.Value, err = NewSecurityDefinitionsItem(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecurityDefinitionsItem creates an object of type SecurityDefinitionsItem if possible, returning an error if not.
func NewSecurityDefinitionsItem(in *yaml.Node, context *compiler.Context) (*SecurityDefinitionsItem, error) {
	errors := make([]error, 0)
	x := &SecurityDefinitionsItem{}
	matched := false
	// BasicAuthenticationSecurity basic_authentication_security = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewBasicAuthenticationSecurity(m, compiler.NewContext("basicAuthenticationSecurity", m, context))
			if matchingError == nil {
				x.Oneof = &SecurityDefinitionsItem_BasicAuthenticationSecurity{BasicAuthenticationSecurity: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// ApiKeySecurity api_key_security = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewApiKeySecurity(m, compiler.NewContext("apiKeySecurity", m, context))
			if matchingError == nil {
				x.Oneof = &SecurityDefinitionsItem_ApiKeySecurity{ApiKeySecurity: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Oauth2ImplicitSecurity oauth2_implicit_security = 3;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewOauth2ImplicitSecurity(m, compiler.NewContext("oauth2ImplicitSecurity", m, context))
			if matchingError == nil {
				x.Oneof = &SecurityDefinitionsItem_Oauth2ImplicitSecurity{Oauth2ImplicitSecurity: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Oauth2PasswordSecurity oauth2_password_security = 4;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewOauth2PasswordSecurity(m, compiler.NewContext("oauth2PasswordSecurity", m, context))
			if matchingError == nil {
				x.Oneof = &SecurityDefinitionsItem_Oauth2PasswordSecurity{Oauth2PasswordSecurity: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Oauth2ApplicationSecurity oauth2_application_security = 5;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewOauth2ApplicationSecurity(m, compiler.NewContext("oauth2ApplicationSecurity", m, context))
			if matchingError == nil {
				x.Oneof = &SecurityDefinitionsItem_Oauth2ApplicationSecurity{Oauth2ApplicationSecurity: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Oauth2AccessCodeSecurity oauth2_access_code_security = 6;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewOauth2AccessCodeSecurity(m, compiler.NewContext("oauth2AccessCodeSecurity", m, context))
			if matchingError == nil {
				x.Oneof = &SecurityDefinitionsItem_Oauth2AccessCodeSecurity{Oauth2AccessCodeSecurity: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid SecurityDefinitionsItem")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecurityRequirement creates an object of type SecurityRequirement if possible, returning an error if not.
func NewSecurityRequirement(in *yaml.Node, context *compiler.Context) (*SecurityRequirement, error) {
	errors := make([]error, 0)
	x := &SecurityRequirement{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedStringArray additional_properties = 1;
		// MAP: StringArray
		x.AdditionalProperties = make([]*NamedStringArray, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedStringArray{}
				pair.Name = k
				var err error
				pair.Value, err = NewStringArray(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewStringArray creates an object of type StringArray if possible, returning an error if not.
func NewStringArray(in *yaml.Node, context *compiler.Context) (*StringArray, error) {
	errors := make([]error, 0)
	x := &StringArray{}
	x.Value = make([]string, 0)
	for _, node := range in.Content {
		s, _ := compiler.StringForScalarNode(node)
		x.Value = append(x.Value, s)
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewTag creates an object of type Tag if possible, returning an error if not.
func NewTag(in *yaml.Node, context *compiler.Context) (*Tag, error) {
	errors := make([]error, 0)
	x := &Tag{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"name"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "externalDocs", "name"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ExternalDocs external_docs = 3;
		v3 := compiler.MapValueForKey(m, "externalDocs")
		if v3 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v3, compiler.NewContext("externalDocs", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny vendor_extension = 4;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewTypeItem creates an object of type TypeItem if possible, returning an error if not.
func NewTypeItem(in *yaml.Node, context *compiler.Context) (*TypeItem, error) {
	errors := make([]error, 0)
	x := &TypeItem{}
	v1 := in
	switch v1.Kind {
	case yaml.ScalarNode:
		x.Value = make([]string, 0)
		x.Value = append(x.Value, v1.Value)
	case yaml.SequenceNode:
		x.Value = make([]string, 0)
		for _, v := range v1.Content {
			value := v.Value
			ok := v.Kind == yaml.ScalarNode
			if ok {
				x.Value = append(x.Value, value)
			} else {
				message := fmt.Sprintf("has unexpected value for string array element: %+v (%T)", value, value)
				errors = append(errors, compiler.NewError(context, message))
			}
		}
	default:
		message := fmt.Sprintf("has unexpected value for string array: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewVendorExtension creates an object of type VendorExtension if possible, returning an error if not.
func NewVendorExtension(in *yaml.Node, context *compiler.Context) (*VendorExtension, error) {
	errors := make([]error, 0)
	x := &VendorExtension{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedAny additional_properties = 1;
		// MAP: Any
		x.AdditionalProperties = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedAny{}
				pair.Name = k
				result := &Any{}
				handled, resultFromExt, err := compiler.CallExtension(context, v, k)
				if handled {
					if err != nil {
						errors = append(errors, err)
					} else {
						bytes := compiler.Marshal(v)
						result.Yaml = string(bytes)
						result.Value = resultFromExt
						pair.Value = result
					}
				} else {
					pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewXml creates an object of type Xml if possible, returning an error if not.
func NewXml(in *yaml.Node, context *compiler.Context) (*Xml, error) {
	errors := make([]error, 0)
	x := &Xml{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"attribute", "name", "namespace", "prefix", "wrapped"}
		allowedPatterns := []*regexp.Regexp{pattern0}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string namespace = 2;
		v2 := compiler.MapValueForKey(m, "namespace")
		if v2 != nil {
			x.Namespace, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for namespace: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string prefix = 3;
		v3 := compiler.MapValueForKey(m, "prefix")
		if v3 != nil {
			x.Prefix, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for prefix: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool attribute = 4;
		v4 := compiler.MapValueForKey(m, "attribute")
		if v4 != nil {
			x.Attribute, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for attribute: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool wrapped = 5;
		v5 := compiler.MapValueForKey(m, "wrapped")
		if v5 != nil {
			x.Wrapped, ok = compiler.BoolForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for wrapped: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny vendor_extension = 6;
		// MAP: Any ^x-
		x.VendorExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.VendorExtension = append(x.VendorExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside AdditionalPropertiesItem objects.
func (m *AdditionalPropertiesItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*AdditionalPropertiesItem_Schema)
		if ok {
			_, err := p.Schema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Any objects.
func (m *Any) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ApiKeySecurity objects.
func (m *ApiKeySecurity) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside BasicAuthenticationSecurity objects.
func (m *BasicAuthenticationSecurity) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside BodyParameter objects.
func (m *BodyParameter) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Schema != nil {
		_, err := m.Schema.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Contact objects.
func (m *Contact) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Default objects.
func (m *Default) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Definitions objects.
func (m *Definitions) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Document objects.
func (m *Document) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Info != nil {
		_, err := m.Info.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Paths != nil {
		_, err := m.Paths.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Definitions != nil {
		_, err := m.Definitions.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Parameters != nil {
		_, err := m.Parameters.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Responses != nil {
		_, err := m.Responses.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Security {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.SecurityDefinitions != nil {
		_, err := m.SecurityDefinitions.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Tags {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Examples objects.
func (m *Examples) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ExternalDocs objects.
func (m *ExternalDocs) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside FileSchema objects.
func (m *FileSchema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Example != nil {
		_, err := m.Example.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside FormDataParameterSubSchema objects.
func (m *FormDataParameterSubSchema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Header objects.
func (m *Header) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside HeaderParameterSubSchema objects.
func (m *HeaderParameterSubSchema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Headers objects.
func (m *Headers) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Info objects.
func (m *Info) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Contact != nil {
		_, err := m.Contact.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.License != nil {
		_, err := m.License.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ItemsItem objects.
func (m *ItemsItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.Schema {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside JsonReference objects.
func (m *JsonReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.XRef != "" {
		info, err := compiler.ReadInfoForRef(root, m.XRef)
		if err != nil {
			return nil, err
		}
		if info != nil {
			replacement, err := NewJsonReference(info, nil)
			if err == nil {
				*m = *replacement
				return m.ResolveReferences(root)
			}
		}
		return info, nil
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside License objects.
func (m *License) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedAny objects.
func (m *NamedAny) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedHeader objects.
func (m *NamedHeader) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedParameter objects.
func (m *NamedParameter) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedPathItem objects.
func (m *NamedPathItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedResponse objects.
func (m *NamedResponse) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedResponseValue objects.
func (m *NamedResponseValue) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedSchema objects.
func (m *NamedSchema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedSecurityDefinitionsItem objects.
func (m *NamedSecurityDefinitionsItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedString objects.
func (m *NamedString) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedStringArray objects.
func (m *NamedStringArray) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NonBodyParameter objects.
func (m *NonBodyParameter) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*NonBodyParameter_HeaderParameterSubSchema)
		if ok {
			_, err := p.HeaderParameterSubSchema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*NonBodyParameter_FormDataParameterSubSchema)
		if ok {
			_, err := p.FormDataParameterSubSchema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*NonBodyParameter_QueryParameterSubSchema)
		if ok {
			_, err := p.QueryParameterSubSchema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*NonBodyParameter_PathParameterSubSchema)
		if ok {
			_, err := p.PathParameterSubSchema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Oauth2AccessCodeSecurity objects.
func (m *Oauth2AccessCodeSecurity) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Scopes != nil {
		_, err := m.Scopes.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Oauth2ApplicationSecurity objects.
func (m *Oauth2ApplicationSecurity) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Scopes != nil {
		_, err := m.Scopes.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Oauth2ImplicitSecurity objects.
func (m *Oauth2ImplicitSecurity) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Scopes != nil {
		_, err := m.Scopes.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Oauth2PasswordSecurity objects.
func (m *Oauth2PasswordSecurity) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Scopes != nil {
		_, err := m.Scopes.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Oauth2Scopes objects.
func (m *Oauth2Scopes) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Operation objects.
func (m *Operation) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Parameters {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.Responses != nil {
		_, err := m.Responses.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Security {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Parameter objects.
func (m *Parameter) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*Parameter_BodyParameter)
		if ok {
			_, err := p.BodyParameter.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*Parameter_NonBodyParameter)
		if ok {
			_, err := p.NonBodyParameter.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ParameterDefinitions objects.
func (m *ParameterDefinitions) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ParametersItem objects.
func (m *ParametersItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*ParametersItem_Parameter)
		if ok {
			_, err := p.Parameter.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*ParametersItem_JsonReference)
		if ok {
			info, err := p.JsonReference.ResolveReferences(root)
			if err != nil {
				return nil, err
			} else if info != nil {
				n, err := NewParametersItem(info, nil)
				if err != nil {
					return nil, err
				} else if n != nil {
					*m = *n
					return nil, nil
				}
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside PathItem objects.
func (m *PathItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.XRef != "" {
		info, err := compiler.ReadInfoForRef(root, m.XRef)
		if err != nil {
			return nil, err
		}
		if info != nil {
			replacement, err := NewPathItem(info, nil)
			if err == nil {
				*m = *replacement
				return m.ResolveReferences(root)
			}
		}
		return info, nil
	}
	if m.Get != nil {
		_, err := m.Get.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Put != nil {
		_, err := m.Put.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Post != nil {
		_, err := m.Post.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Delete != nil {
		_, err := m.Delete.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Options != nil {
		_, err := m.Options.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Head != nil {
		_, err := m.Head.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Patch != nil {
		_, err := m.Patch.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Parameters {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside PathParameterSubSchema objects.
func (m *PathParameterSubSchema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Paths objects.
func (m *Paths) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.Path {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside PrimitivesItems objects.
func (m *PrimitivesItems) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Properties objects.
func (m *Properties) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside QueryParameterSubSchema objects.
func (m *QueryParameterSubSchema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Response objects.
func (m *Response) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Schema != nil {
		_, err := m.Schema.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Headers != nil {
		_, err := m.Headers.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Examples != nil {
		_, err := m.Examples.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ResponseDefinitions objects.
func (m *ResponseDefinitions) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ResponseValue objects.
func (m *ResponseValue) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*ResponseValue_Response)
		if ok {
			_, err := p.Response.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*ResponseValue_JsonReference)
		if ok {
			info, err := p.JsonReference.ResolveReferences(root)
			if err != nil {
				return nil, err
			} else if info != nil {
				n, err := NewResponseValue(info, nil)
				if err != nil {
					return nil, err
				} else if n != nil {
					*m = *n
					return nil, nil
				}
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Responses objects.
func (m *Responses) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.ResponseCode {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Schema objects.
func (m *Schema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.XRef != "" {
		info, err := compiler.ReadInfoForRef(root, m.XRef)
		if err != nil {
			return nil, err
		}
		if info != nil {
			replacement, err := NewSchema(info, nil)
			if err == nil {
				*m = *replacement
				return m.ResolveReferences(root)
			}
		}
		return info, nil
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.AdditionalProperties != nil {
		_, err := m.AdditionalProperties.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Type != nil {
		_, err := m.Type.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.AllOf {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.Properties != nil {
		_, err := m.Properties.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Xml != nil {
		_, err := m.Xml.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Example != nil {
		_, err := m.Example.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SchemaItem objects.
func (m *SchemaItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*SchemaItem_Schema)
		if ok {
			_, err := p.Schema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SchemaItem_FileSchema)
		if ok {
			_, err := p.FileSchema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecurityDefinitions objects.
func (m *SecurityDefinitions) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecurityDefinitionsItem objects.
func (m *SecurityDefinitionsItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*SecurityDefinitionsItem_BasicAuthenticationSecurity)
		if ok {
			_, err := p.BasicAuthenticationSecurity.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SecurityDefinitionsItem_ApiKeySecurity)
		if ok {
			_, err := p.ApiKeySecurity.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SecurityDefinitionsItem_Oauth2ImplicitSecurity)
		if ok {
			_, err := p.Oauth2ImplicitSecurity.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SecurityDefinitionsItem_Oauth2PasswordSecurity)
		if ok {
			_, err := p.Oauth2PasswordSecurity.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SecurityDefinitionsItem_Oauth2ApplicationSecurity)
		if ok {
			_, err := p.Oauth2ApplicationSecurity.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SecurityDefinitionsItem_Oauth2AccessCodeSecurity)
		if ok {
			_, err := p.Oauth2AccessCodeSecurity.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecurityRequirement objects.
func (m *SecurityRequirement) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside StringArray objects.
func (m *StringArray) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Tag objects.
func (m *Tag) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside TypeItem objects.
func (m *TypeItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside VendorExtension objects.
func (m *VendorExtension) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Xml objects.
func (m *Xml) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.VendorExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ToRawInfo returns a description of AdditionalPropertiesItem suitable for JSON or YAML export.
func (m *AdditionalPropertiesItem) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// AdditionalPropertiesItem
	// {Name:schema Type:Schema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetSchema()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:boolean Type:bool StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v1, ok := m.GetOneof().(*AdditionalPropertiesItem_Boolean); ok {
		return compiler.NewScalarNodeForBool(v1.Boolean)
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Any suitable for JSON or YAML export.
func (m *Any) ToRawInfo() *yaml.Node {
	var err error
	var node yaml.Node
	err = yaml.Unmarshal([]byte(m.Yaml), &node)
	if err == nil {
		if node.Kind == yaml.DocumentNode {
			return node.Content[0]
		}
		return &node
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of ApiKeySecurity suitable for JSON or YAML export.
func (m *ApiKeySecurity) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of BasicAuthenticationSecurity suitable for JSON or YAML export.
func (m *BasicAuthenticationSecurity) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of BodyParameter suitable for JSON or YAML export.
func (m *BodyParameter) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("schema"))
	info.Content = append(info.Content, m.Schema.ToRawInfo())
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Contact suitable for JSON or YAML export.
func (m *Contact) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Url != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	}
	if m.Email != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("email"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Email))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Default suitable for JSON or YAML export.
func (m *Default) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Definitions suitable for JSON or YAML export.
func (m *Definitions) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Document suitable for JSON or YAML export.
func (m *Document) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("swagger"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Swagger))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("info"))
	info.Content = append(info.Content, m.Info.ToRawInfo())
	if m.Host != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("host"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Host))
	}
	if m.BasePath != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("basePath"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.BasePath))
	}
	if len(m.Schemes) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schemes"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Schemes))
	}
	if len(m.Consumes) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("consumes"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Consumes))
	}
	if len(m.Produces) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("produces"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Produces))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("paths"))
	info.Content = append(info.Content, m.Paths.ToRawInfo())
	if m.Definitions != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("definitions"))
		info.Content = append(info.Content, m.Definitions.ToRawInfo())
	}
	if m.Parameters != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, m.Parameters.ToRawInfo())
	}
	if m.Responses != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("responses"))
		info.Content = append(info.Content, m.Responses.ToRawInfo())
	}
	if len(m.Security) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Security {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("security"))
		info.Content = append(info.Content, items)
	}
	if m.SecurityDefinitions != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("securityDefinitions"))
		info.Content = append(info.Content, m.SecurityDefinitions.ToRawInfo())
	}
	if len(m.Tags) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Tags {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("tags"))
		info.Content = append(info.Content, items)
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Examples suitable for JSON or YAML export.
func (m *Examples) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ExternalDocs suitable for JSON or YAML export.
func (m *ExternalDocs) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of FileSchema suitable for JSON or YAML export.
func (m *FileSchema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Title != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("title"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Title))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if len(m.Required) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Required))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	if m.ReadOnly != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("readOnly"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ReadOnly))
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.Example != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("example"))
		info.Content = append(info.Content, m.Example.ToRawInfo())
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of FormDataParameterSubSchema suitable for JSON or YAML export.
func (m *FormDataParameterSubSchema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	if m.In != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.AllowEmptyValue != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowEmptyValue"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowEmptyValue))
	}
	if m.Type != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Items != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, m.Items.ToRawInfo())
	}
	if m.CollectionFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("collectionFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.CollectionFormat))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Header suitable for JSON or YAML export.
func (m *Header) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Items != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, m.Items.ToRawInfo())
	}
	if m.CollectionFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("collectionFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.CollectionFormat))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of HeaderParameterSubSchema suitable for JSON or YAML export.
func (m *HeaderParameterSubSchema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	if m.In != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Type != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Items != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, m.Items.ToRawInfo())
	}
	if m.CollectionFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("collectionFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.CollectionFormat))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Headers suitable for JSON or YAML export.
func (m *Headers) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Info suitable for JSON or YAML export.
func (m *Info) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("title"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Title))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("version"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Version))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.TermsOfService != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("termsOfService"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.TermsOfService))
	}
	if m.Contact != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("contact"))
		info.Content = append(info.Content, m.Contact.ToRawInfo())
	}
	if m.License != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("license"))
		info.Content = append(info.Content, m.License.ToRawInfo())
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ItemsItem suitable for JSON or YAML export.
func (m *ItemsItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if len(m.Schema) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Schema {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schema"))
		info.Content = append(info.Content, items)
	}
	return info
}

// ToRawInfo returns a description of JsonReference suitable for JSON or YAML export.
func (m *JsonReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("$ref"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.XRef))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	return info
}

// ToRawInfo returns a description of License suitable for JSON or YAML export.
func (m *License) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	if m.Url != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of NamedAny suitable for JSON or YAML export.
func (m *NamedAny) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Value != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("value"))
		info.Content = append(info.Content, m.Value.ToRawInfo())
	}
	return info
}

// ToRawInfo returns a description of NamedHeader suitable for JSON or YAML export.
func (m *NamedHeader) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:Header StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedParameter suitable for JSON or YAML export.
func (m *NamedParameter) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:Parameter StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedPathItem suitable for JSON or YAML export.
func (m *NamedPathItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:PathItem StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedResponse suitable for JSON or YAML export.
func (m *NamedResponse) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:Response StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedResponseValue suitable for JSON or YAML export.
func (m *NamedResponseValue) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:ResponseValue StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedSchema suitable for JSON or YAML export.
func (m *NamedSchema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:Schema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedSecurityDefinitionsItem suitable for JSON or YAML export.
func (m *NamedSecurityDefinitionsItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:SecurityDefinitionsItem StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedString suitable for JSON or YAML export.
func (m *NamedString) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Value != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("value"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Value))
	}
	return info
}

// ToRawInfo returns a description of NamedStringArray suitable for JSON or YAML export.
func (m *NamedStringArray) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:StringArray StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NonBodyParameter suitable for JSON or YAML export.
func (m *NonBodyParameter) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// NonBodyParameter
	// {Name:headerParameterSubSchema Type:HeaderParameterSubSchema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetHeaderParameterSubSchema()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:formDataParameterSubSchema Type:FormDataParameterSubSchema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetFormDataParameterSubSchema()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	// {Name:queryParameterSubSchema Type:QueryParameterSubSchema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v2 := m.GetQueryParameterSubSchema()
	if v2 != nil {
		return v2.ToRawInfo()
	}
	// {Name:pathParameterSubSchema Type:PathParameterSubSchema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v3 := m.GetPathParameterSubSchema()
	if v3 != nil {
		return v3.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Oauth2AccessCodeSecurity suitable for JSON or YAML export.
func (m *Oauth2AccessCodeSecurity) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("flow"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Flow))
	if m.Scopes != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("scopes"))
		info.Content = append(info.Content, m.Scopes.ToRawInfo())
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("authorizationUrl"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.AuthorizationUrl))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("tokenUrl"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.TokenUrl))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Oauth2ApplicationSecurity suitable for JSON or YAML export.
func (m *Oauth2ApplicationSecurity) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("flow"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Flow))
	if m.Scopes != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("scopes"))
		info.Content = append(info.Content, m.Scopes.ToRawInfo())
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("tokenUrl"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.TokenUrl))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Oauth2ImplicitSecurity suitable for JSON or YAML export.
func (m *Oauth2ImplicitSecurity) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("flow"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Flow))
	if m.Scopes != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("scopes"))
		info.Content = append(info.Content, m.Scopes.ToRawInfo())
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("authorizationUrl"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.AuthorizationUrl))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Oauth2PasswordSecurity suitable for JSON or YAML export.
func (m *Oauth2PasswordSecurity) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("flow"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Flow))
	if m.Scopes != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("scopes"))
		info.Content = append(info.Content, m.Scopes.ToRawInfo())
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("tokenUrl"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.TokenUrl))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Oauth2Scopes suitable for JSON or YAML export.
func (m *Oauth2Scopes) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// &{Name:additionalProperties Type:NamedString StringEnumValues:[] MapType:string Repeated:true Pattern: Implicit:true Description:}
	return info
}

// ToRawInfo returns a description of Operation suitable for JSON or YAML export.
func (m *Operation) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if len(m.Tags) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("tags"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Tags))
	}
	if m.Summary != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("summary"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Summary))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.OperationId != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("operationId"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.OperationId))
	}
	if len(m.Produces) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("produces"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Produces))
	}
	if len(m.Consumes) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("consumes"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Consumes))
	}
	if len(m.Parameters) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Parameters {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, items)
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("responses"))
	info.Content = append(info.Content, m.Responses.ToRawInfo())
	if len(m.Schemes) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schemes"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Schemes))
	}
	if m.Deprecated != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("deprecated"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Deprecated))
	}
	if len(m.Security) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Security {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("security"))
		info.Content = append(info.Content, items)
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Parameter suitable for JSON or YAML export.
func (m *Parameter) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// Parameter
	// {Name:bodyParameter Type:BodyParameter StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetBodyParameter()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:nonBodyParameter Type:NonBodyParameter StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetNonBodyParameter()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of ParameterDefinitions suitable for JSON or YAML export.
func (m *ParameterDefinitions) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ParametersItem suitable for JSON or YAML export.
func (m *ParametersItem) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// ParametersItem
	// {Name:parameter Type:Parameter StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetParameter()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:jsonReference Type:JsonReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetJsonReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of PathItem suitable for JSON or YAML export.
func (m *PathItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.XRef != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("$ref"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.XRef))
	}
	if m.Get != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("get"))
		info.Content = append(info.Content, m.Get.ToRawInfo())
	}
	if m.Put != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("put"))
		info.Content = append(info.Content, m.Put.ToRawInfo())
	}
	if m.Post != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("post"))
		info.Content = append(info.Content, m.Post.ToRawInfo())
	}
	if m.Delete != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("delete"))
		info.Content = append(info.Content, m.Delete.ToRawInfo())
	}
	if m.Options != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("options"))
		info.Content = append(info.Content, m.Options.ToRawInfo())
	}
	if m.Head != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("head"))
		info.Content = append(info.Content, m.Head.ToRawInfo())
	}
	if m.Patch != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("patch"))
		info.Content = append(info.Content, m.Patch.ToRawInfo())
	}
	if len(m.Parameters) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Parameters {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, items)
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of PathParameterSubSchema suitable for JSON or YAML export.
func (m *PathParameterSubSchema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
	info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	if m.In != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Type != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Items != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, m.Items.ToRawInfo())
	}
	if m.CollectionFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("collectionFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.CollectionFormat))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Paths suitable for JSON or YAML export.
func (m *Paths) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	if m.Path != nil {
		for _, item := range m.Path {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of PrimitivesItems suitable for JSON or YAML export.
func (m *PrimitivesItems) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Type != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Items != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, m.Items.ToRawInfo())
	}
	if m.CollectionFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("collectionFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.CollectionFormat))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Properties suitable for JSON or YAML export.
func (m *Properties) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of QueryParameterSubSchema suitable for JSON or YAML export.
func (m *QueryParameterSubSchema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	if m.In != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.AllowEmptyValue != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowEmptyValue"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowEmptyValue))
	}
	if m.Type != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Items != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, m.Items.ToRawInfo())
	}
	if m.CollectionFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("collectionFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.CollectionFormat))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Response suitable for JSON or YAML export.
func (m *Response) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	if m.Schema != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schema"))
		info.Content = append(info.Content, m.Schema.ToRawInfo())
	}
	if m.Headers != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("headers"))
		info.Content = append(info.Content, m.Headers.ToRawInfo())
	}
	if m.Examples != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("examples"))
		info.Content = append(info.Content, m.Examples.ToRawInfo())
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ResponseDefinitions suitable for JSON or YAML export.
func (m *ResponseDefinitions) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ResponseValue suitable for JSON or YAML export.
func (m *ResponseValue) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// ResponseValue
	// {Name:response Type:Response StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetResponse()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:jsonReference Type:JsonReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetJsonReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Responses suitable for JSON or YAML export.
func (m *Responses) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.ResponseCode != nil {
		for _, item := range m.ResponseCode {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Schema suitable for JSON or YAML export.
func (m *Schema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.XRef != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("$ref"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.XRef))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.Title != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("title"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Title))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if m.MaxProperties != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxProperties"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxProperties))
	}
	if m.MinProperties != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minProperties"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinProperties))
	}
	if len(m.Required) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Required))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.AdditionalProperties != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("additionalProperties"))
		info.Content = append(info.Content, m.AdditionalProperties.ToRawInfo())
	}
	if m.Type != nil {
		if len(m.Type.Value) == 1 {
			info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
			info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type.Value[0]))
		} else {
			info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
			info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Type.Value))
		}
	}
	if m.Items != nil {
		items := compiler.NewSequenceNode()
		for _, item := range m.Items.Schema {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		if len(items.Content) == 1 {
			items = items.Content[0]
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, items)
	}
	if len(m.AllOf) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.AllOf {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allOf"))
		info.Content = append(info.Content, items)
	}
	if m.Properties != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("properties"))
		info.Content = append(info.Content, m.Properties.ToRawInfo())
	}
	if m.Discriminator != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("discriminator"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Discriminator))
	}
	if m.ReadOnly != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("readOnly"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ReadOnly))
	}
	if m.Xml != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("xml"))
		info.Content = append(info.Content, m.Xml.ToRawInfo())
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.Example != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("example"))
		info.Content = append(info.Content, m.Example.ToRawInfo())
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SchemaItem suitable for JSON or YAML export.
func (m *SchemaItem) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// SchemaItem
	// {Name:schema Type:Schema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetSchema()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:fileSchema Type:FileSchema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetFileSchema()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of SecurityDefinitions suitable for JSON or YAML export.
func (m *SecurityDefinitions) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SecurityDefinitionsItem suitable for JSON or YAML export.
func (m *SecurityDefinitionsItem) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// SecurityDefinitionsItem
	// {Name:basicAuthenticationSecurity Type:BasicAuthenticationSecurity StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetBasicAuthenticationSecurity()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:apiKeySecurity Type:ApiKeySecurity StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetApiKeySecurity()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	// {Name:oauth2ImplicitSecurity Type:Oauth2ImplicitSecurity StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v2 := m.GetOauth2ImplicitSecurity()
	if v2 != nil {
		return v2.ToRawInfo()
	}
	// {Name:oauth2PasswordSecurity Type:Oauth2PasswordSecurity StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v3 := m.GetOauth2PasswordSecurity()
	if v3 != nil {
		return v3.ToRawInfo()
	}
	// {Name:oauth2ApplicationSecurity Type:Oauth2ApplicationSecurity StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v4 := m.GetOauth2ApplicationSecurity()
	if v4 != nil {
		return v4.ToRawInfo()
	}
	// {Name:oauth2AccessCodeSecurity Type:Oauth2AccessCodeSecurity StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v5 := m.GetOauth2AccessCodeSecurity()
	if v5 != nil {
		return v5.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of SecurityRequirement suitable for JSON or YAML export.
func (m *SecurityRequirement) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of StringArray suitable for JSON or YAML export.
func (m *StringArray) ToRawInfo() *yaml.Node {
	return compiler.NewSequenceNodeForStringArray(m.Value)
}

// ToRawInfo returns a description of Tag suitable for JSON or YAML export.
func (m *Tag) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of TypeItem suitable for JSON or YAML export.
func (m *TypeItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if len(m.Value) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("value"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Value))
	}
	return info
}

// ToRawInfo returns a description of VendorExtension suitable for JSON or YAML export.
func (m *VendorExtension) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Xml suitable for JSON or YAML export.
func (m *Xml) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Namespace != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("namespace"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Namespace))
	}
	if m.Prefix != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("prefix"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Prefix))
	}
	if m.Attribute != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("attribute"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Attribute))
	}
	if m.Wrapped != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("wrapped"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Wrapped))
	}
	if m.VendorExtension != nil {
		for _, item := range m.VendorExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

var (
	pattern0 = regexp.MustCompile("^x-")
	pattern1 = regexp.MustCompile("^/")
	pattern2 = regexp.MustCompile("^([0-9]{3})$|^(default)$")
)
