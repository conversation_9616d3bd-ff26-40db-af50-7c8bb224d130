/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: k8s.io/kubernetes/vendor/k8s.io/api/policy/v1beta1/generated.proto

package v1beta1

import (
	fmt "fmt"

	io "io"

	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"
	k8s_io_api_core_v1 "k8s.io/api/core/v1"
	v11 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"

	intstr "k8s.io/apimachinery/pkg/util/intstr"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func (m *AllowedCSIDriver) Reset()      { *m = AllowedCSIDriver{} }
func (*AllowedCSIDriver) ProtoMessage() {}
func (*AllowedCSIDriver) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{0}
}
func (m *AllowedCSIDriver) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllowedCSIDriver) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AllowedCSIDriver) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllowedCSIDriver.Merge(m, src)
}
func (m *AllowedCSIDriver) XXX_Size() int {
	return m.Size()
}
func (m *AllowedCSIDriver) XXX_DiscardUnknown() {
	xxx_messageInfo_AllowedCSIDriver.DiscardUnknown(m)
}

var xxx_messageInfo_AllowedCSIDriver proto.InternalMessageInfo

func (m *AllowedFlexVolume) Reset()      { *m = AllowedFlexVolume{} }
func (*AllowedFlexVolume) ProtoMessage() {}
func (*AllowedFlexVolume) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{1}
}
func (m *AllowedFlexVolume) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllowedFlexVolume) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AllowedFlexVolume) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllowedFlexVolume.Merge(m, src)
}
func (m *AllowedFlexVolume) XXX_Size() int {
	return m.Size()
}
func (m *AllowedFlexVolume) XXX_DiscardUnknown() {
	xxx_messageInfo_AllowedFlexVolume.DiscardUnknown(m)
}

var xxx_messageInfo_AllowedFlexVolume proto.InternalMessageInfo

func (m *AllowedHostPath) Reset()      { *m = AllowedHostPath{} }
func (*AllowedHostPath) ProtoMessage() {}
func (*AllowedHostPath) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{2}
}
func (m *AllowedHostPath) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllowedHostPath) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AllowedHostPath) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllowedHostPath.Merge(m, src)
}
func (m *AllowedHostPath) XXX_Size() int {
	return m.Size()
}
func (m *AllowedHostPath) XXX_DiscardUnknown() {
	xxx_messageInfo_AllowedHostPath.DiscardUnknown(m)
}

var xxx_messageInfo_AllowedHostPath proto.InternalMessageInfo

func (m *Eviction) Reset()      { *m = Eviction{} }
func (*Eviction) ProtoMessage() {}
func (*Eviction) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{3}
}
func (m *Eviction) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Eviction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *Eviction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Eviction.Merge(m, src)
}
func (m *Eviction) XXX_Size() int {
	return m.Size()
}
func (m *Eviction) XXX_DiscardUnknown() {
	xxx_messageInfo_Eviction.DiscardUnknown(m)
}

var xxx_messageInfo_Eviction proto.InternalMessageInfo

func (m *FSGroupStrategyOptions) Reset()      { *m = FSGroupStrategyOptions{} }
func (*FSGroupStrategyOptions) ProtoMessage() {}
func (*FSGroupStrategyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{4}
}
func (m *FSGroupStrategyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FSGroupStrategyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *FSGroupStrategyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FSGroupStrategyOptions.Merge(m, src)
}
func (m *FSGroupStrategyOptions) XXX_Size() int {
	return m.Size()
}
func (m *FSGroupStrategyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_FSGroupStrategyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_FSGroupStrategyOptions proto.InternalMessageInfo

func (m *HostPortRange) Reset()      { *m = HostPortRange{} }
func (*HostPortRange) ProtoMessage() {}
func (*HostPortRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{5}
}
func (m *HostPortRange) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *HostPortRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *HostPortRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HostPortRange.Merge(m, src)
}
func (m *HostPortRange) XXX_Size() int {
	return m.Size()
}
func (m *HostPortRange) XXX_DiscardUnknown() {
	xxx_messageInfo_HostPortRange.DiscardUnknown(m)
}

var xxx_messageInfo_HostPortRange proto.InternalMessageInfo

func (m *IDRange) Reset()      { *m = IDRange{} }
func (*IDRange) ProtoMessage() {}
func (*IDRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{6}
}
func (m *IDRange) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IDRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *IDRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IDRange.Merge(m, src)
}
func (m *IDRange) XXX_Size() int {
	return m.Size()
}
func (m *IDRange) XXX_DiscardUnknown() {
	xxx_messageInfo_IDRange.DiscardUnknown(m)
}

var xxx_messageInfo_IDRange proto.InternalMessageInfo

func (m *PodDisruptionBudget) Reset()      { *m = PodDisruptionBudget{} }
func (*PodDisruptionBudget) ProtoMessage() {}
func (*PodDisruptionBudget) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{7}
}
func (m *PodDisruptionBudget) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodDisruptionBudget) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodDisruptionBudget) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodDisruptionBudget.Merge(m, src)
}
func (m *PodDisruptionBudget) XXX_Size() int {
	return m.Size()
}
func (m *PodDisruptionBudget) XXX_DiscardUnknown() {
	xxx_messageInfo_PodDisruptionBudget.DiscardUnknown(m)
}

var xxx_messageInfo_PodDisruptionBudget proto.InternalMessageInfo

func (m *PodDisruptionBudgetList) Reset()      { *m = PodDisruptionBudgetList{} }
func (*PodDisruptionBudgetList) ProtoMessage() {}
func (*PodDisruptionBudgetList) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{8}
}
func (m *PodDisruptionBudgetList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodDisruptionBudgetList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodDisruptionBudgetList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodDisruptionBudgetList.Merge(m, src)
}
func (m *PodDisruptionBudgetList) XXX_Size() int {
	return m.Size()
}
func (m *PodDisruptionBudgetList) XXX_DiscardUnknown() {
	xxx_messageInfo_PodDisruptionBudgetList.DiscardUnknown(m)
}

var xxx_messageInfo_PodDisruptionBudgetList proto.InternalMessageInfo

func (m *PodDisruptionBudgetSpec) Reset()      { *m = PodDisruptionBudgetSpec{} }
func (*PodDisruptionBudgetSpec) ProtoMessage() {}
func (*PodDisruptionBudgetSpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{9}
}
func (m *PodDisruptionBudgetSpec) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodDisruptionBudgetSpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodDisruptionBudgetSpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodDisruptionBudgetSpec.Merge(m, src)
}
func (m *PodDisruptionBudgetSpec) XXX_Size() int {
	return m.Size()
}
func (m *PodDisruptionBudgetSpec) XXX_DiscardUnknown() {
	xxx_messageInfo_PodDisruptionBudgetSpec.DiscardUnknown(m)
}

var xxx_messageInfo_PodDisruptionBudgetSpec proto.InternalMessageInfo

func (m *PodDisruptionBudgetStatus) Reset()      { *m = PodDisruptionBudgetStatus{} }
func (*PodDisruptionBudgetStatus) ProtoMessage() {}
func (*PodDisruptionBudgetStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{10}
}
func (m *PodDisruptionBudgetStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodDisruptionBudgetStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodDisruptionBudgetStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodDisruptionBudgetStatus.Merge(m, src)
}
func (m *PodDisruptionBudgetStatus) XXX_Size() int {
	return m.Size()
}
func (m *PodDisruptionBudgetStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_PodDisruptionBudgetStatus.DiscardUnknown(m)
}

var xxx_messageInfo_PodDisruptionBudgetStatus proto.InternalMessageInfo

func (m *PodSecurityPolicy) Reset()      { *m = PodSecurityPolicy{} }
func (*PodSecurityPolicy) ProtoMessage() {}
func (*PodSecurityPolicy) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{11}
}
func (m *PodSecurityPolicy) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodSecurityPolicy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodSecurityPolicy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodSecurityPolicy.Merge(m, src)
}
func (m *PodSecurityPolicy) XXX_Size() int {
	return m.Size()
}
func (m *PodSecurityPolicy) XXX_DiscardUnknown() {
	xxx_messageInfo_PodSecurityPolicy.DiscardUnknown(m)
}

var xxx_messageInfo_PodSecurityPolicy proto.InternalMessageInfo

func (m *PodSecurityPolicyList) Reset()      { *m = PodSecurityPolicyList{} }
func (*PodSecurityPolicyList) ProtoMessage() {}
func (*PodSecurityPolicyList) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{12}
}
func (m *PodSecurityPolicyList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodSecurityPolicyList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodSecurityPolicyList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodSecurityPolicyList.Merge(m, src)
}
func (m *PodSecurityPolicyList) XXX_Size() int {
	return m.Size()
}
func (m *PodSecurityPolicyList) XXX_DiscardUnknown() {
	xxx_messageInfo_PodSecurityPolicyList.DiscardUnknown(m)
}

var xxx_messageInfo_PodSecurityPolicyList proto.InternalMessageInfo

func (m *PodSecurityPolicySpec) Reset()      { *m = PodSecurityPolicySpec{} }
func (*PodSecurityPolicySpec) ProtoMessage() {}
func (*PodSecurityPolicySpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{13}
}
func (m *PodSecurityPolicySpec) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PodSecurityPolicySpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *PodSecurityPolicySpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodSecurityPolicySpec.Merge(m, src)
}
func (m *PodSecurityPolicySpec) XXX_Size() int {
	return m.Size()
}
func (m *PodSecurityPolicySpec) XXX_DiscardUnknown() {
	xxx_messageInfo_PodSecurityPolicySpec.DiscardUnknown(m)
}

var xxx_messageInfo_PodSecurityPolicySpec proto.InternalMessageInfo

func (m *RunAsGroupStrategyOptions) Reset()      { *m = RunAsGroupStrategyOptions{} }
func (*RunAsGroupStrategyOptions) ProtoMessage() {}
func (*RunAsGroupStrategyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{14}
}
func (m *RunAsGroupStrategyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RunAsGroupStrategyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *RunAsGroupStrategyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunAsGroupStrategyOptions.Merge(m, src)
}
func (m *RunAsGroupStrategyOptions) XXX_Size() int {
	return m.Size()
}
func (m *RunAsGroupStrategyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_RunAsGroupStrategyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_RunAsGroupStrategyOptions proto.InternalMessageInfo

func (m *RunAsUserStrategyOptions) Reset()      { *m = RunAsUserStrategyOptions{} }
func (*RunAsUserStrategyOptions) ProtoMessage() {}
func (*RunAsUserStrategyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{15}
}
func (m *RunAsUserStrategyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RunAsUserStrategyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *RunAsUserStrategyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunAsUserStrategyOptions.Merge(m, src)
}
func (m *RunAsUserStrategyOptions) XXX_Size() int {
	return m.Size()
}
func (m *RunAsUserStrategyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_RunAsUserStrategyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_RunAsUserStrategyOptions proto.InternalMessageInfo

func (m *RuntimeClassStrategyOptions) Reset()      { *m = RuntimeClassStrategyOptions{} }
func (*RuntimeClassStrategyOptions) ProtoMessage() {}
func (*RuntimeClassStrategyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{16}
}
func (m *RuntimeClassStrategyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RuntimeClassStrategyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *RuntimeClassStrategyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuntimeClassStrategyOptions.Merge(m, src)
}
func (m *RuntimeClassStrategyOptions) XXX_Size() int {
	return m.Size()
}
func (m *RuntimeClassStrategyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_RuntimeClassStrategyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_RuntimeClassStrategyOptions proto.InternalMessageInfo

func (m *SELinuxStrategyOptions) Reset()      { *m = SELinuxStrategyOptions{} }
func (*SELinuxStrategyOptions) ProtoMessage() {}
func (*SELinuxStrategyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{17}
}
func (m *SELinuxStrategyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SELinuxStrategyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *SELinuxStrategyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SELinuxStrategyOptions.Merge(m, src)
}
func (m *SELinuxStrategyOptions) XXX_Size() int {
	return m.Size()
}
func (m *SELinuxStrategyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_SELinuxStrategyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_SELinuxStrategyOptions proto.InternalMessageInfo

func (m *SupplementalGroupsStrategyOptions) Reset()      { *m = SupplementalGroupsStrategyOptions{} }
func (*SupplementalGroupsStrategyOptions) ProtoMessage() {}
func (*SupplementalGroupsStrategyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_014060e454a820dc, []int{18}
}
func (m *SupplementalGroupsStrategyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SupplementalGroupsStrategyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *SupplementalGroupsStrategyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SupplementalGroupsStrategyOptions.Merge(m, src)
}
func (m *SupplementalGroupsStrategyOptions) XXX_Size() int {
	return m.Size()
}
func (m *SupplementalGroupsStrategyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_SupplementalGroupsStrategyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_SupplementalGroupsStrategyOptions proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AllowedCSIDriver)(nil), "k8s.io.api.policy.v1beta1.AllowedCSIDriver")
	proto.RegisterType((*AllowedFlexVolume)(nil), "k8s.io.api.policy.v1beta1.AllowedFlexVolume")
	proto.RegisterType((*AllowedHostPath)(nil), "k8s.io.api.policy.v1beta1.AllowedHostPath")
	proto.RegisterType((*Eviction)(nil), "k8s.io.api.policy.v1beta1.Eviction")
	proto.RegisterType((*FSGroupStrategyOptions)(nil), "k8s.io.api.policy.v1beta1.FSGroupStrategyOptions")
	proto.RegisterType((*HostPortRange)(nil), "k8s.io.api.policy.v1beta1.HostPortRange")
	proto.RegisterType((*IDRange)(nil), "k8s.io.api.policy.v1beta1.IDRange")
	proto.RegisterType((*PodDisruptionBudget)(nil), "k8s.io.api.policy.v1beta1.PodDisruptionBudget")
	proto.RegisterType((*PodDisruptionBudgetList)(nil), "k8s.io.api.policy.v1beta1.PodDisruptionBudgetList")
	proto.RegisterType((*PodDisruptionBudgetSpec)(nil), "k8s.io.api.policy.v1beta1.PodDisruptionBudgetSpec")
	proto.RegisterType((*PodDisruptionBudgetStatus)(nil), "k8s.io.api.policy.v1beta1.PodDisruptionBudgetStatus")
	proto.RegisterMapType((map[string]v1.Time)(nil), "k8s.io.api.policy.v1beta1.PodDisruptionBudgetStatus.DisruptedPodsEntry")
	proto.RegisterType((*PodSecurityPolicy)(nil), "k8s.io.api.policy.v1beta1.PodSecurityPolicy")
	proto.RegisterType((*PodSecurityPolicyList)(nil), "k8s.io.api.policy.v1beta1.PodSecurityPolicyList")
	proto.RegisterType((*PodSecurityPolicySpec)(nil), "k8s.io.api.policy.v1beta1.PodSecurityPolicySpec")
	proto.RegisterType((*RunAsGroupStrategyOptions)(nil), "k8s.io.api.policy.v1beta1.RunAsGroupStrategyOptions")
	proto.RegisterType((*RunAsUserStrategyOptions)(nil), "k8s.io.api.policy.v1beta1.RunAsUserStrategyOptions")
	proto.RegisterType((*RuntimeClassStrategyOptions)(nil), "k8s.io.api.policy.v1beta1.RuntimeClassStrategyOptions")
	proto.RegisterType((*SELinuxStrategyOptions)(nil), "k8s.io.api.policy.v1beta1.SELinuxStrategyOptions")
	proto.RegisterType((*SupplementalGroupsStrategyOptions)(nil), "k8s.io.api.policy.v1beta1.SupplementalGroupsStrategyOptions")
}

func init() {
	proto.RegisterFile("k8s.io/kubernetes/vendor/k8s.io/api/policy/v1beta1/generated.proto", fileDescriptor_014060e454a820dc)
}

var fileDescriptor_014060e454a820dc = []byte{
	// 1904 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0x5b, 0x6f, 0xdc, 0xc6,
	0x15, 0x16, 0xbd, 0xba, 0xac, 0x46, 0x17, 0x6b, 0x47, 0x17, 0xd3, 0x4a, 0xb3, 0x74, 0x18, 0xa0,
	0x70, 0xd3, 0x84, 0x1b, 0xcb, 0x8e, 0x6b, 0x34, 0x6d, 0x11, 0x51, 0x2b, 0xd9, 0x0a, 0x2c, 0x6b,
	0x3b, 0x6b, 0x07, 0x6d, 0xe1, 0x16, 0x9d, 0x25, 0x47, 0x2b, 0x46, 0x5c, 0x92, 0xe5, 0x0c, 0x37,
	0xda, 0xb7, 0x3e, 0xf4, 0xa1, 0x8f, 0xfd, 0x03, 0x41, 0x7f, 0x40, 0xd1, 0xa7, 0xfe, 0x88, 0x3a,
	0x40, 0x11, 0xe4, 0x31, 0xe8, 0xc3, 0xa2, 0xde, 0xfe, 0x0b, 0x3f, 0x05, 0x9c, 0x1d, 0x72, 0x79,
	0xdd, 0xb5, 0x0d, 0xd8, 0x6f, 0xe4, 0x9c, 0xef, 0xfb, 0xce, 0xcc, 0x99, 0x33, 0x67, 0x2e, 0x40,
	0xbf, 0xb8, 0x47, 0x35, 0xcb, 0x6d, 0x5c, 0x04, 0x1d, 0xe2, 0x3b, 0x84, 0x11, 0xda, 0xe8, 0x13,
	0xc7, 0x74, 0xfd, 0x86, 0x30, 0x60, 0xcf, 0x6a, 0x78, 0xae, 0x6d, 0x19, 0x83, 0x46, 0xff, 0x56,
	0x87, 0x30, 0x7c, 0xab, 0xd1, 0x25, 0x0e, 0xf1, 0x31, 0x23, 0xa6, 0xe6, 0xf9, 0x2e, 0x73, 0xe1,
	0xf5, 0x31, 0x54, 0xc3, 0x9e, 0xa5, 0x8d, 0xa1, 0x9a, 0x80, 0xee, 0x7e, 0xd4, 0xb5, 0xd8, 0x79,
	0xd0, 0xd1, 0x0c, 0xb7, 0xd7, 0xe8, 0xba, 0x5d, 0xb7, 0xc1, 0x19, 0x9d, 0xe0, 0x8c, 0xff, 0xf1,
	0x1f, 0xfe, 0x35, 0x56, 0xda, 0x55, 0x13, 0x4e, 0x0d, 0xd7, 0x27, 0x8d, 0x7e, 0xce, 0xdb, 0xee,
	0x9d, 0x09, 0xa6, 0x87, 0x8d, 0x73, 0xcb, 0x21, 0xfe, 0xa0, 0xe1, 0x5d, 0x74, 0xc3, 0x06, 0xda,
	0xe8, 0x11, 0x86, 0x8b, 0x58, 0x8d, 0x32, 0x96, 0x1f, 0x38, 0xcc, 0xea, 0x91, 0x1c, 0xe1, 0xee,
	0x2c, 0x02, 0x35, 0xce, 0x49, 0x0f, 0xe7, 0x78, 0xb7, 0xcb, 0x78, 0x01, 0xb3, 0xec, 0x86, 0xe5,
	0x30, 0xca, 0xfc, 0x2c, 0x49, 0xbd, 0x03, 0x36, 0xf6, 0x6d, 0xdb, 0xfd, 0x8a, 0x98, 0x07, 0xed,
	0xe3, 0xa6, 0x6f, 0xf5, 0x89, 0x0f, 0x6f, 0x80, 0x79, 0x07, 0xf7, 0x88, 0x2c, 0xdd, 0x90, 0x6e,
	0x2e, 0xeb, 0xab, 0xcf, 0x86, 0xca, 0xdc, 0x68, 0xa8, 0xcc, 0x3f, 0xc2, 0x3d, 0x82, 0xb8, 0x45,
	0xfd, 0x14, 0xd4, 0x04, 0xeb, 0xc8, 0x26, 0x97, 0x5f, 0xb8, 0x76, 0xd0, 0x23, 0xf0, 0xc7, 0x60,
	0xd1, 0xe4, 0x02, 0x82, 0xb8, 0x2e, 0x88, 0x8b, 0x63, 0x59, 0x24, 0xac, 0x2a, 0x05, 0x57, 0x05,
	0xf9, 0x81, 0x4b, 0x59, 0x0b, 0xb3, 0x73, 0xb8, 0x07, 0x80, 0x87, 0xd9, 0x79, 0xcb, 0x27, 0x67,
	0xd6, 0xa5, 0xa0, 0x43, 0x41, 0x07, 0xad, 0xd8, 0x82, 0x12, 0x28, 0xf8, 0x21, 0xa8, 0xfa, 0x04,
	0x9b, 0xa7, 0x8e, 0x3d, 0x90, 0xaf, 0xdc, 0x90, 0x6e, 0x56, 0xf5, 0x0d, 0xc1, 0xa8, 0x22, 0xd1,
	0x8e, 0x62, 0x84, 0xfa, 0x5f, 0x09, 0x54, 0x0f, 0xfb, 0x96, 0xc1, 0x2c, 0xd7, 0x81, 0x7f, 0x04,
	0xd5, 0x70, 0xb6, 0x4c, 0xcc, 0x30, 0x77, 0xb6, 0xb2, 0xf7, 0xb1, 0x36, 0xc9, 0xa4, 0x38, 0x78,
	0x9a, 0x77, 0xd1, 0x0d, 0x1b, 0xa8, 0x16, 0xa2, 0xb5, 0xfe, 0x2d, 0xed, 0xb4, 0xf3, 0x25, 0x31,
	0xd8, 0x09, 0x61, 0x78, 0xd2, 0xbd, 0x49, 0x1b, 0x8a, 0x55, 0xa1, 0x0d, 0xd6, 0x4c, 0x62, 0x13,
	0x46, 0x4e, 0xbd, 0xd0, 0x23, 0xe5, 0x3d, 0x5c, 0xd9, 0xbb, 0xfd, 0x72, 0x6e, 0x9a, 0x49, 0xaa,
	0x5e, 0x1b, 0x0d, 0x95, 0xb5, 0x54, 0x13, 0x4a, 0x8b, 0xab, 0x5f, 0x4b, 0x60, 0xe7, 0xa8, 0x7d,
	0xdf, 0x77, 0x03, 0xaf, 0xcd, 0xc2, 0xd9, 0xed, 0x0e, 0x84, 0x09, 0xfe, 0x0c, 0xcc, 0xfb, 0x81,
	0x1d, 0xcd, 0xe5, 0xfb, 0xd1, 0x5c, 0xa2, 0xc0, 0x26, 0x2f, 0x86, 0xca, 0x66, 0x86, 0xf5, 0x78,
	0xe0, 0x11, 0xc4, 0x09, 0xf0, 0x73, 0xb0, 0xe8, 0x63, 0xa7, 0x4b, 0xc2, 0xae, 0x57, 0x6e, 0xae,
	0xec, 0xa9, 0x5a, 0xe9, 0x5a, 0xd3, 0x8e, 0x9b, 0x28, 0x84, 0x4e, 0x66, 0x9c, 0xff, 0x52, 0x24,
	0x14, 0xd4, 0x13, 0xb0, 0xc6, 0xa7, 0xda, 0xf5, 0x19, 0xb7, 0xc0, 0x77, 0x41, 0xa5, 0x67, 0x39,
	0xbc, 0x53, 0x0b, 0xfa, 0x8a, 0x60, 0x55, 0x4e, 0x2c, 0x07, 0x85, 0xed, 0xdc, 0x8c, 0x2f, 0x79,
	0xcc, 0x92, 0x66, 0x7c, 0x89, 0xc2, 0x76, 0xf5, 0x3e, 0x58, 0x12, 0x1e, 0x93, 0x42, 0x95, 0xe9,
	0x42, 0x95, 0x02, 0xa1, 0x7f, 0x5c, 0x01, 0x9b, 0x2d, 0xd7, 0x6c, 0x5a, 0xd4, 0x0f, 0x78, 0xbc,
	0xf4, 0xc0, 0xec, 0x12, 0xf6, 0x16, 0xf2, 0xe3, 0x31, 0x98, 0xa7, 0x1e, 0x31, 0x44, 0x5a, 0xec,
	0x4d, 0x89, 0x6d, 0x41, 0xff, 0xda, 0x1e, 0x31, 0x26, 0xcb, 0x32, 0xfc, 0x43, 0x5c, 0x0d, 0x3e,
	0x05, 0x8b, 0x94, 0x61, 0x16, 0x50, 0xb9, 0xc2, 0x75, 0xef, 0xbc, 0xa2, 0x2e, 0xe7, 0x4e, 0x66,
	0x71, 0xfc, 0x8f, 0x84, 0xa6, 0xfa, 0x1f, 0x09, 0x5c, 0x2b, 0x60, 0x3d, 0xb4, 0x28, 0x83, 0x4f,
	0x73, 0x11, 0xd3, 0x5e, 0x2e, 0x62, 0x21, 0x9b, 0xc7, 0x2b, 0x5e, 0xbc, 0x51, 0x4b, 0x22, 0x5a,
	0x6d, 0xb0, 0x60, 0x31, 0xd2, 0x8b, 0x52, 0x51, 0x7b, 0xb5, 0x61, 0xe9, 0x6b, 0x42, 0x7a, 0xe1,
	0x38, 0x14, 0x41, 0x63, 0x2d, 0xf5, 0xdb, 0x2b, 0x85, 0xc3, 0x09, 0xc3, 0x09, 0xcf, 0xc0, 0x6a,
	0xcf, 0x72, 0xf6, 0xfb, 0xd8, 0xb2, 0x71, 0x47, 0xac, 0x9e, 0x69, 0x49, 0x10, 0x56, 0x58, 0x6d,
	0x5c, 0x61, 0xb5, 0x63, 0x87, 0x9d, 0xfa, 0x6d, 0xe6, 0x5b, 0x4e, 0x57, 0xdf, 0x18, 0x0d, 0x95,
	0xd5, 0x93, 0x84, 0x12, 0x4a, 0xe9, 0xc2, 0xdf, 0x83, 0x2a, 0x25, 0x36, 0x31, 0x98, 0xeb, 0xbf,
	0x5a, 0x85, 0x78, 0x88, 0x3b, 0xc4, 0x6e, 0x0b, 0xaa, 0xbe, 0x1a, 0xc6, 0x2d, 0xfa, 0x43, 0xb1,
	0x24, 0xb4, 0xc1, 0x7a, 0x0f, 0x5f, 0x3e, 0x71, 0x70, 0x3c, 0x90, 0xca, 0x6b, 0x0e, 0x04, 0x8e,
	0x86, 0xca, 0xfa, 0x49, 0x4a, 0x0b, 0x65, 0xb4, 0xd5, 0x7f, 0x2f, 0x80, 0xeb, 0xa5, 0x59, 0x05,
	0x3f, 0x07, 0xd0, 0xed, 0x50, 0xe2, 0xf7, 0x89, 0x79, 0x7f, 0xbc, 0x07, 0x59, 0x6e, 0xb4, 0x70,
	0x77, 0xc5, 0x04, 0xc1, 0xd3, 0x1c, 0x02, 0x15, 0xb0, 0xe0, 0x5f, 0x24, 0xb0, 0x66, 0x8e, 0xdd,
	0x10, 0xb3, 0xe5, 0x9a, 0x51, 0x62, 0xdc, 0x7f, 0x9d, 0x7c, 0xd7, 0x9a, 0x49, 0xa5, 0x43, 0x87,
	0xf9, 0x03, 0x7d, 0x5b, 0x74, 0x68, 0x2d, 0x65, 0x43, 0x69, 0xa7, 0xe1, 0x90, 0xcc, 0x58, 0x92,
	0x8a, 0x3d, 0x8d, 0x87, 0x78, 0x61, 0x32, 0xa4, 0x66, 0x0e, 0x81, 0x0a, 0x58, 0xf0, 0x57, 0x60,
	0xdd, 0x08, 0x7c, 0x9f, 0x38, 0xec, 0x01, 0xc1, 0x36, 0x3b, 0x1f, 0xc8, 0xf3, 0x5c, 0x67, 0x47,
	0xe8, 0xac, 0x1f, 0xa4, 0xac, 0x28, 0x83, 0x0e, 0xf9, 0x26, 0xa1, 0x96, 0x4f, 0xcc, 0x88, 0xbf,
	0x90, 0xe6, 0x37, 0x53, 0x56, 0x94, 0x41, 0xc3, 0x7b, 0x60, 0x95, 0x5c, 0x7a, 0xc4, 0x88, 0x02,
	0xba, 0xc8, 0xd9, 0x5b, 0x82, 0xbd, 0x7a, 0x98, 0xb0, 0xa1, 0x14, 0x12, 0x1a, 0x00, 0x18, 0xae,
	0x63, 0x5a, 0xe3, 0x7d, 0x6e, 0x89, 0x4f, 0x44, 0xe3, 0xe5, 0xb2, 0xf8, 0x20, 0xe2, 0x4d, 0xaa,
	0x65, 0xdc, 0x44, 0x51, 0x42, 0x76, 0xd7, 0x06, 0x30, 0x3f, 0x4d, 0x70, 0x03, 0x54, 0x2e, 0xc8,
	0x60, 0xbc, 0xb7, 0xa1, 0xf0, 0x13, 0x7e, 0x06, 0x16, 0xfa, 0xd8, 0x0e, 0x88, 0x58, 0x4d, 0x1f,
	0xbc, 0x5c, 0x3f, 0x1e, 0x5b, 0x3d, 0x82, 0xc6, 0xc4, 0x9f, 0x5f, 0xb9, 0x27, 0xa9, 0xdf, 0x48,
	0xa0, 0xd6, 0x72, 0xcd, 0x36, 0x31, 0x02, 0xdf, 0x62, 0x83, 0x16, 0xcf, 0xa4, 0xb7, 0xb0, 0x2b,
	0xa0, 0xd4, 0xae, 0xf0, 0xf1, 0xf4, 0x6c, 0x4e, 0xf7, 0xae, 0x6c, 0x4f, 0x50, 0x9f, 0x49, 0x60,
	0x3b, 0x87, 0x7e, 0x0b, 0x35, 0xfb, 0xd7, 0xe9, 0x9a, 0xfd, 0xe1, 0xab, 0x0c, 0xa6, 0xa4, 0x62,
	0x7f, 0x53, 0x2b, 0x18, 0x0a, 0xaf, 0xd7, 0xe1, 0xf9, 0xd1, 0xb7, 0xfa, 0x96, 0x4d, 0xba, 0xc4,
	0xe4, 0x83, 0xa9, 0x26, 0xce, 0x8f, 0xb1, 0x05, 0x25, 0x50, 0x90, 0x82, 0x1d, 0x93, 0x9c, 0xe1,
	0xc0, 0x66, 0xfb, 0xa6, 0x79, 0x80, 0x3d, 0xdc, 0xb1, 0x6c, 0x8b, 0x59, 0xe2, 0xc0, 0xb3, 0xac,
	0x7f, 0x3a, 0x1a, 0x2a, 0x3b, 0xcd, 0x42, 0xc4, 0x8b, 0xa1, 0xf2, 0x6e, 0xfe, 0xbe, 0xa0, 0xc5,
	0x90, 0x01, 0x2a, 0x91, 0x86, 0x03, 0x20, 0xfb, 0xe4, 0x4f, 0x41, 0xb8, 0xf2, 0x9a, 0xbe, 0xeb,
	0xa5, 0xdc, 0x56, 0xb8, 0xdb, 0x5f, 0x8e, 0x86, 0x8a, 0x8c, 0x4a, 0x30, 0xb3, 0x1d, 0x97, 0xca,
	0xc3, 0x2f, 0xc1, 0x26, 0x16, 0x27, 0xfd, 0xa4, 0xd7, 0x79, 0xee, 0xf5, 0xde, 0x68, 0xa8, 0x6c,
	0xee, 0xe7, 0xcd, 0xb3, 0x1d, 0x16, 0x89, 0xc2, 0x06, 0x58, 0xea, 0xf3, 0x4b, 0x01, 0x95, 0x17,
	0xb8, 0xfe, 0xf6, 0x68, 0xa8, 0x2c, 0x8d, 0xef, 0x09, 0xa1, 0xe6, 0xe2, 0x51, 0x9b, 0x1f, 0x35,
	0x23, 0x14, 0xfc, 0x04, 0xac, 0x9c, 0xbb, 0x94, 0x3d, 0x22, 0xec, 0x2b, 0xd7, 0xbf, 0xe0, 0xd5,
	0xa7, 0xaa, 0x6f, 0x8a, 0x19, 0x5c, 0x79, 0x30, 0x31, 0xa1, 0x24, 0x0e, 0xfe, 0x16, 0x2c, 0x9f,
	0x8b, 0x83, 0x65, 0x54, 0x7a, 0x6e, 0x4e, 0x49, 0xb4, 0xd4, 0x21, 0x54, 0xaf, 0x09, 0xf9, 0xe5,
	0xa8, 0x99, 0xa2, 0x89, 0x1a, 0xfc, 0x09, 0x58, 0xe2, 0x3f, 0xc7, 0x4d, 0xb9, 0xca, 0x7b, 0x73,
	0x55, 0xc0, 0x97, 0x1e, 0x8c, 0x9b, 0x51, 0x64, 0x8f, 0xa0, 0xc7, 0xad, 0x03, 0x79, 0x39, 0x0f,
	0x3d, 0x6e, 0x1d, 0xa0, 0xc8, 0x0e, 0x9f, 0x82, 0x25, 0x4a, 0x1e, 0x5a, 0x4e, 0x70, 0x29, 0x03,
	0xbe, 0xe4, 0x6e, 0x4d, 0xe9, 0x6e, 0xfb, 0x90, 0x23, 0x33, 0x47, 0xfa, 0x89, 0xba, 0xb0, 0xa3,
	0x48, 0x12, 0x9a, 0x60, 0xd9, 0x0f, 0x9c, 0x7d, 0xfa, 0x84, 0x12, 0x5f, 0x5e, 0xc9, 0x9d, 0x27,
	0xb2, 0xfa, 0x28, 0xc2, 0x66, 0x3d, 0xc4, 0x91, 0x89, 0x11, 0x68, 0x22, 0x0c, 0x4d, 0x00, 0xf8,
	0x0f, 0xbf, 0x39, 0xc8, 0x3b, 0x33, 0x4f, 0x9a, 0x28, 0x06, 0x67, 0xfd, 0xac, 0x87, 0xcb, 0x73,
	0x62, 0x46, 0x09, 0x5d, 0xf8, 0x57, 0x09, 0x40, 0x1a, 0x78, 0x9e, 0x4d, 0x7a, 0xc4, 0x61, 0xd8,
	0xe6, 0xad, 0x54, 0x5e, 0xe5, 0xee, 0x7e, 0x31, 0x2d, 0x6a, 0x39, 0x52, 0xd6, 0x6d, 0xbc, 0x37,
	0xe7, 0xa1, 0xa8, 0xc0, 0x67, 0x38, 0x69, 0x67, 0x62, 0xb4, 0x6b, 0x33, 0x27, 0xad, 0xf8, 0x1e,
	0x36, 0x99, 0x34, 0x61, 0x47, 0x91, 0x24, 0xfc, 0x02, 0xec, 0x44, 0xb7, 0x54, 0xe4, 0xba, 0xec,
	0xc8, 0xb2, 0x09, 0x1d, 0x50, 0x46, 0x7a, 0xf2, 0x3a, 0x4f, 0xa6, 0xba, 0x60, 0xee, 0xa0, 0x42,
	0x14, 0x2a, 0x61, 0xc3, 0x1e, 0x50, 0xa2, 0x22, 0x14, 0xae, 0xd0, 0xb8, 0x0a, 0x1e, 0x52, 0x03,
	0xdb, 0xe3, 0xd3, 0xd7, 0x55, 0xee, 0xe0, 0xfd, 0xd1, 0x50, 0x51, 0x9a, 0xd3, 0xa1, 0x68, 0x96,
	0x16, 0xfc, 0x0d, 0x90, 0x71, 0x99, 0x9f, 0x0d, 0xee, 0xe7, 0x47, 0x61, 0x65, 0x2b, 0x75, 0x50,
	0xca, 0x86, 0x1e, 0xd8, 0xc0, 0xe9, 0xf7, 0x02, 0x2a, 0xd7, 0xf8, 0x5a, 0xff, 0x60, 0xca, 0x3c,
	0x64, 0x9e, 0x18, 0x74, 0x59, 0x84, 0x71, 0x23, 0x63, 0xa0, 0x28, 0xa7, 0x0e, 0x2f, 0x01, 0xc4,
	0xd9, 0xe7, 0x0d, 0x2a, 0xc3, 0x99, 0x1b, 0x59, 0xee, 0x4d, 0x64, 0x92, 0x6a, 0x39, 0x13, 0x45,
	0x05, 0x3e, 0x20, 0x03, 0x35, 0x9c, 0x79, 0x8e, 0xa1, 0xf2, 0x35, 0xee, 0xf8, 0xa7, 0xb3, 0x1d,
	0xc7, 0x1c, 0xfd, 0xba, 0xf0, 0x5b, 0xcb, 0x5a, 0x28, 0xca, 0x3b, 0x80, 0x0f, 0xc1, 0x96, 0x68,
	0x7c, 0xe2, 0x50, 0x7c, 0x46, 0xda, 0x03, 0x6a, 0x30, 0x9b, 0xca, 0x9b, 0xbc, 0x76, 0xcb, 0xa3,
	0xa1, 0xb2, 0xb5, 0x5f, 0x60, 0x47, 0x85, 0x2c, 0xf8, 0x19, 0xd8, 0x38, 0x73, 0xfd, 0x8e, 0x65,
	0x9a, 0xc4, 0x89, 0x94, 0xb6, 0xb8, 0xd2, 0x56, 0x18, 0xff, 0xa3, 0x8c, 0x0d, 0xe5, 0xd0, 0x90,
	0x82, 0x6d, 0xa1, 0xdc, 0xf2, 0x5d, 0xe3, 0xc4, 0x0d, 0x1c, 0x16, 0x6e, 0x17, 0x54, 0xde, 0x8e,
	0xb7, 0xc8, 0xed, 0xfd, 0x22, 0xc0, 0x8b, 0xa1, 0x72, 0xa3, 0x60, 0xbb, 0x4a, 0x81, 0x50, 0xb1,
	0x36, 0xb4, 0xc1, 0xaa, 0x78, 0x60, 0x3b, 0xb0, 0x31, 0xa5, 0xb2, 0xcc, 0x97, 0xfa, 0xdd, 0xe9,
	0x85, 0x2d, 0x86, 0x67, 0xd7, 0x3b, 0xbf, 0xf9, 0x25, 0x01, 0x28, 0xa5, 0xae, 0xfe, 0x5d, 0x02,
	0xd7, 0x4b, 0x0b, 0x23, 0xbc, 0x9b, 0x7a, 0xb5, 0x51, 0x33, 0xaf, 0x36, 0x30, 0x4f, 0x7c, 0x03,
	0x8f, 0x36, 0x5f, 0x4b, 0x40, 0x2e, 0xdb, 0x21, 0xe0, 0x27, 0xa9, 0x0e, 0xbe, 0x97, 0xe9, 0x60,
	0x2d, 0xc7, 0x7b, 0x03, 0xfd, 0xfb, 0x56, 0x02, 0xef, 0x4c, 0x99, 0x81, 0xb8, 0x20, 0x11, 0x33,
	0x89, 0x7a, 0x84, 0xc3, 0xa5, 0x2c, 0xf1, 0x3c, 0x9a, 0x14, 0xa4, 0x02, 0x0c, 0x2a, 0x65, 0xc3,
	0x27, 0xe0, 0x9a, 0xa8, 0x86, 0x59, 0x1b, 0x3f, 0xb9, 0x2f, 0xeb, 0xef, 0x8c, 0x86, 0xca, 0xb5,
	0x66, 0x31, 0x04, 0x95, 0x71, 0xd5, 0x7f, 0x4a, 0x60, 0xa7, 0x78, 0xcb, 0x87, 0xb7, 0x53, 0xe1,
	0x56, 0x32, 0xe1, 0xbe, 0x9a, 0x61, 0x89, 0x60, 0xff, 0x01, 0xac, 0x8b, 0x83, 0x41, 0xfa, 0x11,
	0x32, 0x15, 0xf4, 0x70, 0x89, 0x84, 0x67, 0x7a, 0x21, 0x11, 0xa5, 0x2f, 0xbf, 0xef, 0xa7, 0xdb,
	0x50, 0x46, 0x4d, 0xfd, 0x97, 0x04, 0xde, 0x9b, 0xb9, 0xd9, 0x42, 0x3d, 0xd5, 0x75, 0x2d, 0xd3,
	0xf5, 0x7a, 0xb9, 0xc0, 0x9b, 0x79, 0x8b, 0xd4, 0x3f, 0x7a, 0xf6, 0xbc, 0x3e, 0xf7, 0xdd, 0xf3,
	0xfa, 0xdc, 0xf7, 0xcf, 0xeb, 0x73, 0x7f, 0x1e, 0xd5, 0xa5, 0x67, 0xa3, 0xba, 0xf4, 0xdd, 0xa8,
	0x2e, 0x7d, 0x3f, 0xaa, 0x4b, 0xff, 0x1b, 0xd5, 0xa5, 0xbf, 0xfd, 0xbf, 0x3e, 0xf7, 0xbb, 0x25,
	0x21, 0xf7, 0x43, 0x00, 0x00, 0x00, 0xff, 0xff, 0xde, 0x4e, 0x7c, 0x8c, 0xa6, 0x18, 0x00, 0x00,
}

func (m *AllowedCSIDriver) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllowedCSIDriver) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllowedCSIDriver) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AllowedFlexVolume) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllowedFlexVolume) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllowedFlexVolume) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Driver)
	copy(dAtA[i:], m.Driver)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Driver)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AllowedHostPath) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllowedHostPath) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllowedHostPath) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i--
	if m.ReadOnly {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x10
	i -= len(m.PathPrefix)
	copy(dAtA[i:], m.PathPrefix)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.PathPrefix)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Eviction) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Eviction) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Eviction) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DeleteOptions != nil {
		{
			size, err := m.DeleteOptions.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *FSGroupStrategyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FSGroupStrategyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FSGroupStrategyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	i -= len(m.Rule)
	copy(dAtA[i:], m.Rule)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Rule)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *HostPortRange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HostPortRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *HostPortRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i = encodeVarintGenerated(dAtA, i, uint64(m.Max))
	i--
	dAtA[i] = 0x10
	i = encodeVarintGenerated(dAtA, i, uint64(m.Min))
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m *IDRange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IDRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IDRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i = encodeVarintGenerated(dAtA, i, uint64(m.Max))
	i--
	dAtA[i] = 0x10
	i = encodeVarintGenerated(dAtA, i, uint64(m.Min))
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m *PodDisruptionBudget) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodDisruptionBudget) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodDisruptionBudget) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Status.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	{
		size, err := m.Spec.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *PodDisruptionBudgetList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodDisruptionBudgetList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodDisruptionBudgetList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *PodDisruptionBudgetSpec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodDisruptionBudgetSpec) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodDisruptionBudgetSpec) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MaxUnavailable != nil {
		{
			size, err := m.MaxUnavailable.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Selector != nil {
		{
			size, err := m.Selector.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.MinAvailable != nil {
		{
			size, err := m.MinAvailable.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PodDisruptionBudgetStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodDisruptionBudgetStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodDisruptionBudgetStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Conditions) > 0 {
		for iNdEx := len(m.Conditions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Conditions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	i = encodeVarintGenerated(dAtA, i, uint64(m.ExpectedPods))
	i--
	dAtA[i] = 0x30
	i = encodeVarintGenerated(dAtA, i, uint64(m.DesiredHealthy))
	i--
	dAtA[i] = 0x28
	i = encodeVarintGenerated(dAtA, i, uint64(m.CurrentHealthy))
	i--
	dAtA[i] = 0x20
	i = encodeVarintGenerated(dAtA, i, uint64(m.DisruptionsAllowed))
	i--
	dAtA[i] = 0x18
	if len(m.DisruptedPods) > 0 {
		keysForDisruptedPods := make([]string, 0, len(m.DisruptedPods))
		for k := range m.DisruptedPods {
			keysForDisruptedPods = append(keysForDisruptedPods, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForDisruptedPods)
		for iNdEx := len(keysForDisruptedPods) - 1; iNdEx >= 0; iNdEx-- {
			v := m.DisruptedPods[string(keysForDisruptedPods[iNdEx])]
			baseI := i
			{
				size, err := (&v).MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
			i -= len(keysForDisruptedPods[iNdEx])
			copy(dAtA[i:], keysForDisruptedPods[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForDisruptedPods[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	i = encodeVarintGenerated(dAtA, i, uint64(m.ObservedGeneration))
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m *PodSecurityPolicy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodSecurityPolicy) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodSecurityPolicy) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Spec.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *PodSecurityPolicyList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodSecurityPolicyList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodSecurityPolicyList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *PodSecurityPolicySpec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PodSecurityPolicySpec) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PodSecurityPolicySpec) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RuntimeClass != nil {
		{
			size, err := m.RuntimeClass.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	if len(m.AllowedCSIDrivers) > 0 {
		for iNdEx := len(m.AllowedCSIDrivers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllowedCSIDrivers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xba
		}
	}
	if m.RunAsGroup != nil {
		{
			size, err := m.RunAsGroup.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	if len(m.AllowedProcMountTypes) > 0 {
		for iNdEx := len(m.AllowedProcMountTypes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.AllowedProcMountTypes[iNdEx])
			copy(dAtA[i:], m.AllowedProcMountTypes[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.AllowedProcMountTypes[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xaa
		}
	}
	if len(m.ForbiddenSysctls) > 0 {
		for iNdEx := len(m.ForbiddenSysctls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ForbiddenSysctls[iNdEx])
			copy(dAtA[i:], m.ForbiddenSysctls[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.ForbiddenSysctls[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xa2
		}
	}
	if len(m.AllowedUnsafeSysctls) > 0 {
		for iNdEx := len(m.AllowedUnsafeSysctls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.AllowedUnsafeSysctls[iNdEx])
			copy(dAtA[i:], m.AllowedUnsafeSysctls[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.AllowedUnsafeSysctls[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0x9a
		}
	}
	if len(m.AllowedFlexVolumes) > 0 {
		for iNdEx := len(m.AllowedFlexVolumes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllowedFlexVolumes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0x92
		}
	}
	if len(m.AllowedHostPaths) > 0 {
		for iNdEx := len(m.AllowedHostPaths) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllowedHostPaths[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0x8a
		}
	}
	if m.AllowPrivilegeEscalation != nil {
		i--
		if *m.AllowPrivilegeEscalation {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.DefaultAllowPrivilegeEscalation != nil {
		i--
		if *m.DefaultAllowPrivilegeEscalation {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x78
	}
	i--
	if m.ReadOnlyRootFilesystem {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x70
	{
		size, err := m.FSGroup.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x6a
	{
		size, err := m.SupplementalGroups.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x62
	{
		size, err := m.RunAsUser.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x5a
	{
		size, err := m.SELinux.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x52
	i--
	if m.HostIPC {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x48
	i--
	if m.HostPID {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x40
	if len(m.HostPorts) > 0 {
		for iNdEx := len(m.HostPorts) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.HostPorts[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	i--
	if m.HostNetwork {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x30
	if len(m.Volumes) > 0 {
		for iNdEx := len(m.Volumes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Volumes[iNdEx])
			copy(dAtA[i:], m.Volumes[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Volumes[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.AllowedCapabilities) > 0 {
		for iNdEx := len(m.AllowedCapabilities) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.AllowedCapabilities[iNdEx])
			copy(dAtA[i:], m.AllowedCapabilities[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.AllowedCapabilities[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.RequiredDropCapabilities) > 0 {
		for iNdEx := len(m.RequiredDropCapabilities) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.RequiredDropCapabilities[iNdEx])
			copy(dAtA[i:], m.RequiredDropCapabilities[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.RequiredDropCapabilities[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.DefaultAddCapabilities) > 0 {
		for iNdEx := len(m.DefaultAddCapabilities) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DefaultAddCapabilities[iNdEx])
			copy(dAtA[i:], m.DefaultAddCapabilities[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.DefaultAddCapabilities[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	i--
	if m.Privileged {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m *RunAsGroupStrategyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RunAsGroupStrategyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RunAsGroupStrategyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	i -= len(m.Rule)
	copy(dAtA[i:], m.Rule)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Rule)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *RunAsUserStrategyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RunAsUserStrategyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RunAsUserStrategyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	i -= len(m.Rule)
	copy(dAtA[i:], m.Rule)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Rule)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *RuntimeClassStrategyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RuntimeClassStrategyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RuntimeClassStrategyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DefaultRuntimeClassName != nil {
		i -= len(*m.DefaultRuntimeClassName)
		copy(dAtA[i:], *m.DefaultRuntimeClassName)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.DefaultRuntimeClassName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.AllowedRuntimeClassNames) > 0 {
		for iNdEx := len(m.AllowedRuntimeClassNames) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.AllowedRuntimeClassNames[iNdEx])
			copy(dAtA[i:], m.AllowedRuntimeClassNames[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.AllowedRuntimeClassNames[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SELinuxStrategyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SELinuxStrategyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SELinuxStrategyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SELinuxOptions != nil {
		{
			size, err := m.SELinuxOptions.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	i -= len(m.Rule)
	copy(dAtA[i:], m.Rule)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Rule)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *SupplementalGroupsStrategyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SupplementalGroupsStrategyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SupplementalGroupsStrategyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	i -= len(m.Rule)
	copy(dAtA[i:], m.Rule)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Rule)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func encodeVarintGenerated(dAtA []byte, offset int, v uint64) int {
	offset -= sovGenerated(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *AllowedCSIDriver) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *AllowedFlexVolume) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Driver)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *AllowedHostPath) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PathPrefix)
	n += 1 + l + sovGenerated(uint64(l))
	n += 2
	return n
}

func (m *Eviction) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if m.DeleteOptions != nil {
		l = m.DeleteOptions.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *FSGroupStrategyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rule)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *HostPortRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovGenerated(uint64(m.Min))
	n += 1 + sovGenerated(uint64(m.Max))
	return n
}

func (m *IDRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovGenerated(uint64(m.Min))
	n += 1 + sovGenerated(uint64(m.Max))
	return n
}

func (m *PodDisruptionBudget) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Spec.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Status.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *PodDisruptionBudgetList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *PodDisruptionBudgetSpec) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MinAvailable != nil {
		l = m.MinAvailable.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Selector != nil {
		l = m.Selector.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.MaxUnavailable != nil {
		l = m.MaxUnavailable.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *PodDisruptionBudgetStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovGenerated(uint64(m.ObservedGeneration))
	if len(m.DisruptedPods) > 0 {
		for k, v := range m.DisruptedPods {
			_ = k
			_ = v
			l = v.Size()
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + l + sovGenerated(uint64(l))
			n += mapEntrySize + 1 + sovGenerated(uint64(mapEntrySize))
		}
	}
	n += 1 + sovGenerated(uint64(m.DisruptionsAllowed))
	n += 1 + sovGenerated(uint64(m.CurrentHealthy))
	n += 1 + sovGenerated(uint64(m.DesiredHealthy))
	n += 1 + sovGenerated(uint64(m.ExpectedPods))
	if len(m.Conditions) > 0 {
		for _, e := range m.Conditions {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *PodSecurityPolicy) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Spec.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *PodSecurityPolicyList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *PodSecurityPolicySpec) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 2
	if len(m.DefaultAddCapabilities) > 0 {
		for _, s := range m.DefaultAddCapabilities {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.RequiredDropCapabilities) > 0 {
		for _, s := range m.RequiredDropCapabilities {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.AllowedCapabilities) > 0 {
		for _, s := range m.AllowedCapabilities {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.Volumes) > 0 {
		for _, s := range m.Volumes {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	n += 2
	if len(m.HostPorts) > 0 {
		for _, e := range m.HostPorts {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	n += 2
	n += 2
	l = m.SELinux.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.RunAsUser.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.SupplementalGroups.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.FSGroup.Size()
	n += 1 + l + sovGenerated(uint64(l))
	n += 2
	if m.DefaultAllowPrivilegeEscalation != nil {
		n += 2
	}
	if m.AllowPrivilegeEscalation != nil {
		n += 3
	}
	if len(m.AllowedHostPaths) > 0 {
		for _, e := range m.AllowedHostPaths {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.AllowedFlexVolumes) > 0 {
		for _, e := range m.AllowedFlexVolumes {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.AllowedUnsafeSysctls) > 0 {
		for _, s := range m.AllowedUnsafeSysctls {
			l = len(s)
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.ForbiddenSysctls) > 0 {
		for _, s := range m.ForbiddenSysctls {
			l = len(s)
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.AllowedProcMountTypes) > 0 {
		for _, s := range m.AllowedProcMountTypes {
			l = len(s)
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if m.RunAsGroup != nil {
		l = m.RunAsGroup.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	if len(m.AllowedCSIDrivers) > 0 {
		for _, e := range m.AllowedCSIDrivers {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if m.RuntimeClass != nil {
		l = m.RuntimeClass.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *RunAsGroupStrategyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rule)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *RunAsUserStrategyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rule)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *RuntimeClassStrategyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.AllowedRuntimeClassNames) > 0 {
		for _, s := range m.AllowedRuntimeClassNames {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.DefaultRuntimeClassName != nil {
		l = len(*m.DefaultRuntimeClassName)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *SELinuxStrategyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rule)
	n += 1 + l + sovGenerated(uint64(l))
	if m.SELinuxOptions != nil {
		l = m.SELinuxOptions.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *SupplementalGroupsStrategyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rule)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func sovGenerated(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGenerated(x uint64) (n int) {
	return sovGenerated(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *AllowedCSIDriver) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AllowedCSIDriver{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AllowedFlexVolume) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AllowedFlexVolume{`,
		`Driver:` + fmt.Sprintf("%v", this.Driver) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AllowedHostPath) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AllowedHostPath{`,
		`PathPrefix:` + fmt.Sprintf("%v", this.PathPrefix) + `,`,
		`ReadOnly:` + fmt.Sprintf("%v", this.ReadOnly) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Eviction) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Eviction{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`DeleteOptions:` + strings.Replace(fmt.Sprintf("%v", this.DeleteOptions), "DeleteOptions", "v1.DeleteOptions", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *FSGroupStrategyOptions) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRanges := "[]IDRange{"
	for _, f := range this.Ranges {
		repeatedStringForRanges += strings.Replace(strings.Replace(f.String(), "IDRange", "IDRange", 1), `&`, ``, 1) + ","
	}
	repeatedStringForRanges += "}"
	s := strings.Join([]string{`&FSGroupStrategyOptions{`,
		`Rule:` + fmt.Sprintf("%v", this.Rule) + `,`,
		`Ranges:` + repeatedStringForRanges + `,`,
		`}`,
	}, "")
	return s
}
func (this *HostPortRange) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&HostPortRange{`,
		`Min:` + fmt.Sprintf("%v", this.Min) + `,`,
		`Max:` + fmt.Sprintf("%v", this.Max) + `,`,
		`}`,
	}, "")
	return s
}
func (this *IDRange) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IDRange{`,
		`Min:` + fmt.Sprintf("%v", this.Min) + `,`,
		`Max:` + fmt.Sprintf("%v", this.Max) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodDisruptionBudget) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PodDisruptionBudget{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Spec:` + strings.Replace(strings.Replace(this.Spec.String(), "PodDisruptionBudgetSpec", "PodDisruptionBudgetSpec", 1), `&`, ``, 1) + `,`,
		`Status:` + strings.Replace(strings.Replace(this.Status.String(), "PodDisruptionBudgetStatus", "PodDisruptionBudgetStatus", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodDisruptionBudgetList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]PodDisruptionBudget{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "PodDisruptionBudget", "PodDisruptionBudget", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&PodDisruptionBudgetList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodDisruptionBudgetSpec) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PodDisruptionBudgetSpec{`,
		`MinAvailable:` + strings.Replace(fmt.Sprintf("%v", this.MinAvailable), "IntOrString", "intstr.IntOrString", 1) + `,`,
		`Selector:` + strings.Replace(fmt.Sprintf("%v", this.Selector), "LabelSelector", "v1.LabelSelector", 1) + `,`,
		`MaxUnavailable:` + strings.Replace(fmt.Sprintf("%v", this.MaxUnavailable), "IntOrString", "intstr.IntOrString", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodDisruptionBudgetStatus) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForConditions := "[]Condition{"
	for _, f := range this.Conditions {
		repeatedStringForConditions += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForConditions += "}"
	keysForDisruptedPods := make([]string, 0, len(this.DisruptedPods))
	for k := range this.DisruptedPods {
		keysForDisruptedPods = append(keysForDisruptedPods, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForDisruptedPods)
	mapStringForDisruptedPods := "map[string]v1.Time{"
	for _, k := range keysForDisruptedPods {
		mapStringForDisruptedPods += fmt.Sprintf("%v: %v,", k, this.DisruptedPods[k])
	}
	mapStringForDisruptedPods += "}"
	s := strings.Join([]string{`&PodDisruptionBudgetStatus{`,
		`ObservedGeneration:` + fmt.Sprintf("%v", this.ObservedGeneration) + `,`,
		`DisruptedPods:` + mapStringForDisruptedPods + `,`,
		`DisruptionsAllowed:` + fmt.Sprintf("%v", this.DisruptionsAllowed) + `,`,
		`CurrentHealthy:` + fmt.Sprintf("%v", this.CurrentHealthy) + `,`,
		`DesiredHealthy:` + fmt.Sprintf("%v", this.DesiredHealthy) + `,`,
		`ExpectedPods:` + fmt.Sprintf("%v", this.ExpectedPods) + `,`,
		`Conditions:` + repeatedStringForConditions + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodSecurityPolicy) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PodSecurityPolicy{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Spec:` + strings.Replace(strings.Replace(this.Spec.String(), "PodSecurityPolicySpec", "PodSecurityPolicySpec", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodSecurityPolicyList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]PodSecurityPolicy{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "PodSecurityPolicy", "PodSecurityPolicy", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&PodSecurityPolicyList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *PodSecurityPolicySpec) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForHostPorts := "[]HostPortRange{"
	for _, f := range this.HostPorts {
		repeatedStringForHostPorts += strings.Replace(strings.Replace(f.String(), "HostPortRange", "HostPortRange", 1), `&`, ``, 1) + ","
	}
	repeatedStringForHostPorts += "}"
	repeatedStringForAllowedHostPaths := "[]AllowedHostPath{"
	for _, f := range this.AllowedHostPaths {
		repeatedStringForAllowedHostPaths += strings.Replace(strings.Replace(f.String(), "AllowedHostPath", "AllowedHostPath", 1), `&`, ``, 1) + ","
	}
	repeatedStringForAllowedHostPaths += "}"
	repeatedStringForAllowedFlexVolumes := "[]AllowedFlexVolume{"
	for _, f := range this.AllowedFlexVolumes {
		repeatedStringForAllowedFlexVolumes += strings.Replace(strings.Replace(f.String(), "AllowedFlexVolume", "AllowedFlexVolume", 1), `&`, ``, 1) + ","
	}
	repeatedStringForAllowedFlexVolumes += "}"
	repeatedStringForAllowedCSIDrivers := "[]AllowedCSIDriver{"
	for _, f := range this.AllowedCSIDrivers {
		repeatedStringForAllowedCSIDrivers += strings.Replace(strings.Replace(f.String(), "AllowedCSIDriver", "AllowedCSIDriver", 1), `&`, ``, 1) + ","
	}
	repeatedStringForAllowedCSIDrivers += "}"
	s := strings.Join([]string{`&PodSecurityPolicySpec{`,
		`Privileged:` + fmt.Sprintf("%v", this.Privileged) + `,`,
		`DefaultAddCapabilities:` + fmt.Sprintf("%v", this.DefaultAddCapabilities) + `,`,
		`RequiredDropCapabilities:` + fmt.Sprintf("%v", this.RequiredDropCapabilities) + `,`,
		`AllowedCapabilities:` + fmt.Sprintf("%v", this.AllowedCapabilities) + `,`,
		`Volumes:` + fmt.Sprintf("%v", this.Volumes) + `,`,
		`HostNetwork:` + fmt.Sprintf("%v", this.HostNetwork) + `,`,
		`HostPorts:` + repeatedStringForHostPorts + `,`,
		`HostPID:` + fmt.Sprintf("%v", this.HostPID) + `,`,
		`HostIPC:` + fmt.Sprintf("%v", this.HostIPC) + `,`,
		`SELinux:` + strings.Replace(strings.Replace(this.SELinux.String(), "SELinuxStrategyOptions", "SELinuxStrategyOptions", 1), `&`, ``, 1) + `,`,
		`RunAsUser:` + strings.Replace(strings.Replace(this.RunAsUser.String(), "RunAsUserStrategyOptions", "RunAsUserStrategyOptions", 1), `&`, ``, 1) + `,`,
		`SupplementalGroups:` + strings.Replace(strings.Replace(this.SupplementalGroups.String(), "SupplementalGroupsStrategyOptions", "SupplementalGroupsStrategyOptions", 1), `&`, ``, 1) + `,`,
		`FSGroup:` + strings.Replace(strings.Replace(this.FSGroup.String(), "FSGroupStrategyOptions", "FSGroupStrategyOptions", 1), `&`, ``, 1) + `,`,
		`ReadOnlyRootFilesystem:` + fmt.Sprintf("%v", this.ReadOnlyRootFilesystem) + `,`,
		`DefaultAllowPrivilegeEscalation:` + valueToStringGenerated(this.DefaultAllowPrivilegeEscalation) + `,`,
		`AllowPrivilegeEscalation:` + valueToStringGenerated(this.AllowPrivilegeEscalation) + `,`,
		`AllowedHostPaths:` + repeatedStringForAllowedHostPaths + `,`,
		`AllowedFlexVolumes:` + repeatedStringForAllowedFlexVolumes + `,`,
		`AllowedUnsafeSysctls:` + fmt.Sprintf("%v", this.AllowedUnsafeSysctls) + `,`,
		`ForbiddenSysctls:` + fmt.Sprintf("%v", this.ForbiddenSysctls) + `,`,
		`AllowedProcMountTypes:` + fmt.Sprintf("%v", this.AllowedProcMountTypes) + `,`,
		`RunAsGroup:` + strings.Replace(this.RunAsGroup.String(), "RunAsGroupStrategyOptions", "RunAsGroupStrategyOptions", 1) + `,`,
		`AllowedCSIDrivers:` + repeatedStringForAllowedCSIDrivers + `,`,
		`RuntimeClass:` + strings.Replace(this.RuntimeClass.String(), "RuntimeClassStrategyOptions", "RuntimeClassStrategyOptions", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *RunAsGroupStrategyOptions) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRanges := "[]IDRange{"
	for _, f := range this.Ranges {
		repeatedStringForRanges += strings.Replace(strings.Replace(f.String(), "IDRange", "IDRange", 1), `&`, ``, 1) + ","
	}
	repeatedStringForRanges += "}"
	s := strings.Join([]string{`&RunAsGroupStrategyOptions{`,
		`Rule:` + fmt.Sprintf("%v", this.Rule) + `,`,
		`Ranges:` + repeatedStringForRanges + `,`,
		`}`,
	}, "")
	return s
}
func (this *RunAsUserStrategyOptions) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRanges := "[]IDRange{"
	for _, f := range this.Ranges {
		repeatedStringForRanges += strings.Replace(strings.Replace(f.String(), "IDRange", "IDRange", 1), `&`, ``, 1) + ","
	}
	repeatedStringForRanges += "}"
	s := strings.Join([]string{`&RunAsUserStrategyOptions{`,
		`Rule:` + fmt.Sprintf("%v", this.Rule) + `,`,
		`Ranges:` + repeatedStringForRanges + `,`,
		`}`,
	}, "")
	return s
}
func (this *RuntimeClassStrategyOptions) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&RuntimeClassStrategyOptions{`,
		`AllowedRuntimeClassNames:` + fmt.Sprintf("%v", this.AllowedRuntimeClassNames) + `,`,
		`DefaultRuntimeClassName:` + valueToStringGenerated(this.DefaultRuntimeClassName) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SELinuxStrategyOptions) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SELinuxStrategyOptions{`,
		`Rule:` + fmt.Sprintf("%v", this.Rule) + `,`,
		`SELinuxOptions:` + strings.Replace(fmt.Sprintf("%v", this.SELinuxOptions), "SELinuxOptions", "v11.SELinuxOptions", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SupplementalGroupsStrategyOptions) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRanges := "[]IDRange{"
	for _, f := range this.Ranges {
		repeatedStringForRanges += strings.Replace(strings.Replace(f.String(), "IDRange", "IDRange", 1), `&`, ``, 1) + ","
	}
	repeatedStringForRanges += "}"
	s := strings.Join([]string{`&SupplementalGroupsStrategyOptions{`,
		`Rule:` + fmt.Sprintf("%v", this.Rule) + `,`,
		`Ranges:` + repeatedStringForRanges + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGenerated(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *AllowedCSIDriver) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllowedCSIDriver: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllowedCSIDriver: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllowedFlexVolume) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllowedFlexVolume: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllowedFlexVolume: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Driver", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Driver = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllowedHostPath) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllowedHostPath: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllowedHostPath: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PathPrefix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PathPrefix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadOnly", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReadOnly = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Eviction) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Eviction: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Eviction: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeleteOptions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DeleteOptions == nil {
				m.DeleteOptions = &v1.DeleteOptions{}
			}
			if err := m.DeleteOptions.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FSGroupStrategyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FSGroupStrategyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FSGroupStrategyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rule = FSGroupStrategyType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, IDRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *HostPortRange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: HostPortRange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: HostPortRange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Min", wireType)
			}
			m.Min = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Min |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Max", wireType)
			}
			m.Max = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Max |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IDRange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IDRange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IDRange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Min", wireType)
			}
			m.Min = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Min |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Max", wireType)
			}
			m.Max = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Max |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodDisruptionBudget) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodDisruptionBudget: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodDisruptionBudget: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Spec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Status.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodDisruptionBudgetList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodDisruptionBudgetList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodDisruptionBudgetList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, PodDisruptionBudget{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodDisruptionBudgetSpec) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodDisruptionBudgetSpec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodDisruptionBudgetSpec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinAvailable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MinAvailable == nil {
				m.MinAvailable = &intstr.IntOrString{}
			}
			if err := m.MinAvailable.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Selector", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Selector == nil {
				m.Selector = &v1.LabelSelector{}
			}
			if err := m.Selector.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxUnavailable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MaxUnavailable == nil {
				m.MaxUnavailable = &intstr.IntOrString{}
			}
			if err := m.MaxUnavailable.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodDisruptionBudgetStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodDisruptionBudgetStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodDisruptionBudgetStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObservedGeneration", wireType)
			}
			m.ObservedGeneration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObservedGeneration |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DisruptedPods", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DisruptedPods == nil {
				m.DisruptedPods = make(map[string]v1.Time)
			}
			var mapkey string
			mapvalue := &v1.Time{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthGenerated
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &v1.Time{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.DisruptedPods[mapkey] = *mapvalue
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DisruptionsAllowed", wireType)
			}
			m.DisruptionsAllowed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisruptionsAllowed |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentHealthy", wireType)
			}
			m.CurrentHealthy = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentHealthy |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DesiredHealthy", wireType)
			}
			m.DesiredHealthy = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DesiredHealthy |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExpectedPods", wireType)
			}
			m.ExpectedPods = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpectedPods |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Conditions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Conditions = append(m.Conditions, v1.Condition{})
			if err := m.Conditions[len(m.Conditions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodSecurityPolicy) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodSecurityPolicy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodSecurityPolicy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Spec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodSecurityPolicyList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodSecurityPolicyList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodSecurityPolicyList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, PodSecurityPolicy{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PodSecurityPolicySpec) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PodSecurityPolicySpec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PodSecurityPolicySpec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Privileged", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Privileged = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultAddCapabilities", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DefaultAddCapabilities = append(m.DefaultAddCapabilities, k8s_io_api_core_v1.Capability(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequiredDropCapabilities", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequiredDropCapabilities = append(m.RequiredDropCapabilities, k8s_io_api_core_v1.Capability(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedCapabilities", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedCapabilities = append(m.AllowedCapabilities, k8s_io_api_core_v1.Capability(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Volumes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Volumes = append(m.Volumes, FSType(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HostNetwork", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HostNetwork = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HostPorts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HostPorts = append(m.HostPorts, HostPortRange{})
			if err := m.HostPorts[len(m.HostPorts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HostPID", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HostPID = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HostIPC", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HostIPC = bool(v != 0)
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SELinux", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.SELinux.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RunAsUser", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.RunAsUser.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupplementalGroups", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.SupplementalGroups.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FSGroup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.FSGroup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadOnlyRootFilesystem", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReadOnlyRootFilesystem = bool(v != 0)
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultAllowPrivilegeEscalation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.DefaultAllowPrivilegeEscalation = &b
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowPrivilegeEscalation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.AllowPrivilegeEscalation = &b
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedHostPaths", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedHostPaths = append(m.AllowedHostPaths, AllowedHostPath{})
			if err := m.AllowedHostPaths[len(m.AllowedHostPaths)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedFlexVolumes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedFlexVolumes = append(m.AllowedFlexVolumes, AllowedFlexVolume{})
			if err := m.AllowedFlexVolumes[len(m.AllowedFlexVolumes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedUnsafeSysctls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedUnsafeSysctls = append(m.AllowedUnsafeSysctls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ForbiddenSysctls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ForbiddenSysctls = append(m.ForbiddenSysctls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedProcMountTypes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedProcMountTypes = append(m.AllowedProcMountTypes, k8s_io_api_core_v1.ProcMountType(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RunAsGroup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RunAsGroup == nil {
				m.RunAsGroup = &RunAsGroupStrategyOptions{}
			}
			if err := m.RunAsGroup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedCSIDrivers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedCSIDrivers = append(m.AllowedCSIDrivers, AllowedCSIDriver{})
			if err := m.AllowedCSIDrivers[len(m.AllowedCSIDrivers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RuntimeClass", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RuntimeClass == nil {
				m.RuntimeClass = &RuntimeClassStrategyOptions{}
			}
			if err := m.RuntimeClass.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RunAsGroupStrategyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RunAsGroupStrategyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RunAsGroupStrategyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rule = RunAsGroupStrategy(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, IDRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RunAsUserStrategyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RunAsUserStrategyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RunAsUserStrategyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rule = RunAsUserStrategy(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, IDRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RuntimeClassStrategyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RuntimeClassStrategyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RuntimeClassStrategyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllowedRuntimeClassNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllowedRuntimeClassNames = append(m.AllowedRuntimeClassNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultRuntimeClassName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.DefaultRuntimeClassName = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SELinuxStrategyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SELinuxStrategyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SELinuxStrategyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rule = SELinuxStrategy(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SELinuxOptions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SELinuxOptions == nil {
				m.SELinuxOptions = &v11.SELinuxOptions{}
			}
			if err := m.SELinuxOptions.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SupplementalGroupsStrategyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SupplementalGroupsStrategyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SupplementalGroupsStrategyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rule = SupplementalGroupsStrategyType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, IDRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGenerated(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGenerated
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupGenerated
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthGenerated
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthGenerated        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGenerated          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupGenerated = fmt.Errorf("proto: unexpected end of group")
)
