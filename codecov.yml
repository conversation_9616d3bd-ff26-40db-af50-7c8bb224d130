codecov:
  require_ci_to_pass: yes

coverage:
  precision: 2
  round: down
  range: "70...100"
  status:
    project:
      default:
        informational: true
    patch:
      default:
        informational: true

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: no

ignore:
  - "**/*.pb.go"
  - "**/*.y.go"
  - "**/*.md"
