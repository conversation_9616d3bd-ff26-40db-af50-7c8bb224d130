[{"type": "bool", "parser": "strconv.ParseBool(s)"}, {"type": "string", "parser": "s, error(nil)", "format": "string(*f.v)", "plural": "Strings"}, {"type": "uint", "parser": "strconv.ParseUint(s, 0, 64)", "plural": "Uints"}, {"type": "uint8", "parser": "strconv.ParseUint(s, 0, 8)"}, {"type": "uint16", "parser": "strconv.<PERSON><PERSON><PERSON><PERSON>(s, 0, 16)"}, {"type": "uint32", "parser": "strconv.<PERSON><PERSON><PERSON><PERSON>(s, 0, 32)"}, {"type": "uint64", "parser": "strconv.ParseUint(s, 0, 64)"}, {"type": "int", "parser": "strconv.<PERSON><PERSON><PERSON>(s, 64)", "plural": "Ints"}, {"type": "int8", "parser": "strconv.ParseInt(s, 0, 8)"}, {"type": "int16", "parser": "strconv.ParseInt(s, 0, 16)"}, {"type": "int32", "parser": "strconv.ParseInt(s, 0, 32)"}, {"type": "int64", "parser": "strconv.ParseInt(s, 0, 64)"}, {"type": "float64", "parser": "strconv.<PERSON><PERSON><PERSON>(s, 64)"}, {"type": "float32", "parser": "strconv.<PERSON><PERSON><PERSON>(s, 32)"}, {"name": "Duration", "type": "time.Duration", "no_value_parser": true}, {"name": "IP", "type": "net.IP", "no_value_parser": true}, {"name": "TCPAddr", "Type": "*net.TCPAddr", "plural": "TCPList", "no_value_parser": true}, {"name": "ExistingFile", "Type": "string", "plural": "ExistingFiles", "no_value_parser": true}, {"name": "ExistingDir", "Type": "string", "plural": "ExistingDirs", "no_value_parser": true}, {"name": "ExistingFileOrDir", "Type": "string", "plural": "ExistingFilesOrDirs", "no_value_parser": true}, {"name": "Regexp", "Type": "*regexp.Regexp", "parser": "regexp.<PERSON><PERSON>(s)"}, {"name": "ResolvedIP", "Type": "net.IP", "parser": "resolveHost(s)", "help": "Resolve a hostname or IP to an IP."}, {"name": "HexBytes", "Type": "[]byte", "parser": "hex.DecodeString(s)", "help": "Bytes as a hex string."}]