{{var "v"}} := *{{ .Varname }}
{{var "l"}} := z.DecReadMapStart()
if {{var "l"}} == codecSelferDecContainerLenNil{{xs}} {
	*{{ .Varname }} = nil
} else {
if {{var "v"}} == nil {
	{{var "rl"}} := z.DecInfer<PERSON>en({{var "l"}}, z.<PERSON>asi<PERSON>andle().MaxInitLen, {{ .Size }})
	{{var "v"}} = make(map[{{ .KTyp }}]{{ .Typ }}, {{var "rl"}})
	*{{ .Varname }} = {{var "v"}}
}
var {{var "mk"}} {{ .KTyp }}
var {{var "mv"}} {{ .Typ }}
var {{var "mg"}}, {{var "mdn"}} {{if decElemKindPtr}}, {{var "ms"}}, {{var "mok"}}{{end}} bool
if z.<PERSON>().MapValueReset {
	{{if decElemKindPtr}}{{var "mg"}} = true
	{{else if decElemKindIntf}}if !z.DecBasic<PERSON>andle().InterfaceReset { {{var "mg"}} = true }
	{{else if not decElemKindImmutable}}{{var "mg"}} = true
	{{end}} }
if {{var "l"}} != 0 {
	{{var "hl"}} := {{var "l"}} > 0 
	for {{var "j"}} := 0; ({{var "hl"}} && {{var "j"}} < {{var "l"}}) || !({{var "hl"}} || z.DecCheckBreak()); {{var "j"}}++ {
	z.DecReadMapElemKey()
	{{ $x := printf "%vmk%v" .TempVar .Rand }}{{ decLineVarK $x -}}
	{{ if eq .KTyp "interface{}" }}{{/* // special case if a byte array. */ -}}
    if {{var "bv"}}, {{var "bok"}} := {{var "mk"}}.([]byte); {{var "bok"}} {
		{{var "mk"}} = string({{var "bv"}})
	}
    {{ end -}}
    {{if decElemKindPtr -}}
	{{var "ms"}} = true
    {{end -}}
	if {{var "mg"}} {
		{{if decElemKindPtr -}}
        {{var "mv"}}, {{var "mok"}} = {{var "v"}}[{{var "mk"}}] 
		if {{var "mok"}} {
			{{var "ms"}} = false
		}
        {{else -}}
        {{var "mv"}} = {{var "v"}}[{{var "mk"}}]
        {{end -}}
	} {{if not decElemKindImmutable}}else { {{var "mv"}} = {{decElemZero}} }{{end}}
	z.DecReadMapElemValue()
	{{var "mdn"}} = false
	{{ $x := printf "%vmv%v" .TempVar .Rand }}{{ $y := printf "%vmdn%v" .TempVar .Rand }}{{ decLineVar $x $y -}}
	if {{var "mdn"}} {
		if z.DecBasicHandle().DeleteOnNilMapValue { delete({{var "v"}}, {{var "mk"}}) } else { {{var "v"}}[{{var "mk"}}] = {{decElemZero}} }
	} else if {{if decElemKindPtr}} {{var "ms"}} && {{end}} {{var "v"}} != nil {
		{{var "v"}}[{{var "mk"}}] = {{var "mv"}}
	}
}
} // else len==0: TODO: Should we clear map entries?
z.DecReadMapEnd()
}
