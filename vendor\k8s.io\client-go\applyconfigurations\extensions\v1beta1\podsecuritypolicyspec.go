/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1beta1

import (
	v1 "k8s.io/api/core/v1"
	v1beta1 "k8s.io/api/extensions/v1beta1"
)

// PodSecurityPolicySpecApplyConfiguration represents an declarative configuration of the PodSecurityPolicySpec type for use
// with apply.
type PodSecurityPolicySpecApplyConfiguration struct {
	Privileged                      *bool                                                `json:"privileged,omitempty"`
	DefaultAddCapabilities          []v1.Capability                                      `json:"defaultAddCapabilities,omitempty"`
	RequiredDropCapabilities        []v1.Capability                                      `json:"requiredDropCapabilities,omitempty"`
	AllowedCapabilities             []v1.Capability                                      `json:"allowedCapabilities,omitempty"`
	Volumes                         []v1beta1.FSType                                     `json:"volumes,omitempty"`
	HostNetwork                     *bool                                                `json:"hostNetwork,omitempty"`
	HostPorts                       []HostPortRangeApplyConfiguration                    `json:"hostPorts,omitempty"`
	HostPID                         *bool                                                `json:"hostPID,omitempty"`
	HostIPC                         *bool                                                `json:"hostIPC,omitempty"`
	SELinux                         *SELinuxStrategyOptionsApplyConfiguration            `json:"seLinux,omitempty"`
	RunAsUser                       *RunAsUserStrategyOptionsApplyConfiguration          `json:"runAsUser,omitempty"`
	RunAsGroup                      *RunAsGroupStrategyOptionsApplyConfiguration         `json:"runAsGroup,omitempty"`
	SupplementalGroups              *SupplementalGroupsStrategyOptionsApplyConfiguration `json:"supplementalGroups,omitempty"`
	FSGroup                         *FSGroupStrategyOptionsApplyConfiguration            `json:"fsGroup,omitempty"`
	ReadOnlyRootFilesystem          *bool                                                `json:"readOnlyRootFilesystem,omitempty"`
	DefaultAllowPrivilegeEscalation *bool                                                `json:"defaultAllowPrivilegeEscalation,omitempty"`
	AllowPrivilegeEscalation        *bool                                                `json:"allowPrivilegeEscalation,omitempty"`
	AllowedHostPaths                []AllowedHostPathApplyConfiguration                  `json:"allowedHostPaths,omitempty"`
	AllowedFlexVolumes              []AllowedFlexVolumeApplyConfiguration                `json:"allowedFlexVolumes,omitempty"`
	AllowedCSIDrivers               []AllowedCSIDriverApplyConfiguration                 `json:"allowedCSIDrivers,omitempty"`
	AllowedUnsafeSysctls            []string                                             `json:"allowedUnsafeSysctls,omitempty"`
	ForbiddenSysctls                []string                                             `json:"forbiddenSysctls,omitempty"`
	AllowedProcMountTypes           []v1.ProcMountType                                   `json:"allowedProcMountTypes,omitempty"`
	RuntimeClass                    *RuntimeClassStrategyOptionsApplyConfiguration       `json:"runtimeClass,omitempty"`
}

// PodSecurityPolicySpecApplyConfiguration constructs an declarative configuration of the PodSecurityPolicySpec type for use with
// apply.
func PodSecurityPolicySpec() *PodSecurityPolicySpecApplyConfiguration {
	return &PodSecurityPolicySpecApplyConfiguration{}
}

// WithPrivileged sets the Privileged field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Privileged field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithPrivileged(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.Privileged = &value
	return b
}

// WithDefaultAddCapabilities adds the given value to the DefaultAddCapabilities field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the DefaultAddCapabilities field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithDefaultAddCapabilities(values ...v1.Capability) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.DefaultAddCapabilities = append(b.DefaultAddCapabilities, values[i])
	}
	return b
}

// WithRequiredDropCapabilities adds the given value to the RequiredDropCapabilities field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the RequiredDropCapabilities field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithRequiredDropCapabilities(values ...v1.Capability) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.RequiredDropCapabilities = append(b.RequiredDropCapabilities, values[i])
	}
	return b
}

// WithAllowedCapabilities adds the given value to the AllowedCapabilities field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedCapabilities field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowedCapabilities(values ...v1.Capability) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.AllowedCapabilities = append(b.AllowedCapabilities, values[i])
	}
	return b
}

// WithVolumes adds the given value to the Volumes field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Volumes field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithVolumes(values ...v1beta1.FSType) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.Volumes = append(b.Volumes, values[i])
	}
	return b
}

// WithHostNetwork sets the HostNetwork field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the HostNetwork field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithHostNetwork(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.HostNetwork = &value
	return b
}

// WithHostPorts adds the given value to the HostPorts field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the HostPorts field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithHostPorts(values ...*HostPortRangeApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithHostPorts")
		}
		b.HostPorts = append(b.HostPorts, *values[i])
	}
	return b
}

// WithHostPID sets the HostPID field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the HostPID field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithHostPID(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.HostPID = &value
	return b
}

// WithHostIPC sets the HostIPC field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the HostIPC field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithHostIPC(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.HostIPC = &value
	return b
}

// WithSELinux sets the SELinux field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SELinux field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithSELinux(value *SELinuxStrategyOptionsApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	b.SELinux = value
	return b
}

// WithRunAsUser sets the RunAsUser field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RunAsUser field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithRunAsUser(value *RunAsUserStrategyOptionsApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	b.RunAsUser = value
	return b
}

// WithRunAsGroup sets the RunAsGroup field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RunAsGroup field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithRunAsGroup(value *RunAsGroupStrategyOptionsApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	b.RunAsGroup = value
	return b
}

// WithSupplementalGroups sets the SupplementalGroups field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SupplementalGroups field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithSupplementalGroups(value *SupplementalGroupsStrategyOptionsApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	b.SupplementalGroups = value
	return b
}

// WithFSGroup sets the FSGroup field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FSGroup field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithFSGroup(value *FSGroupStrategyOptionsApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	b.FSGroup = value
	return b
}

// WithReadOnlyRootFilesystem sets the ReadOnlyRootFilesystem field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ReadOnlyRootFilesystem field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithReadOnlyRootFilesystem(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.ReadOnlyRootFilesystem = &value
	return b
}

// WithDefaultAllowPrivilegeEscalation sets the DefaultAllowPrivilegeEscalation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DefaultAllowPrivilegeEscalation field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithDefaultAllowPrivilegeEscalation(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.DefaultAllowPrivilegeEscalation = &value
	return b
}

// WithAllowPrivilegeEscalation sets the AllowPrivilegeEscalation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AllowPrivilegeEscalation field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowPrivilegeEscalation(value bool) *PodSecurityPolicySpecApplyConfiguration {
	b.AllowPrivilegeEscalation = &value
	return b
}

// WithAllowedHostPaths adds the given value to the AllowedHostPaths field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedHostPaths field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowedHostPaths(values ...*AllowedHostPathApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAllowedHostPaths")
		}
		b.AllowedHostPaths = append(b.AllowedHostPaths, *values[i])
	}
	return b
}

// WithAllowedFlexVolumes adds the given value to the AllowedFlexVolumes field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedFlexVolumes field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowedFlexVolumes(values ...*AllowedFlexVolumeApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAllowedFlexVolumes")
		}
		b.AllowedFlexVolumes = append(b.AllowedFlexVolumes, *values[i])
	}
	return b
}

// WithAllowedCSIDrivers adds the given value to the AllowedCSIDrivers field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedCSIDrivers field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowedCSIDrivers(values ...*AllowedCSIDriverApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAllowedCSIDrivers")
		}
		b.AllowedCSIDrivers = append(b.AllowedCSIDrivers, *values[i])
	}
	return b
}

// WithAllowedUnsafeSysctls adds the given value to the AllowedUnsafeSysctls field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedUnsafeSysctls field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowedUnsafeSysctls(values ...string) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.AllowedUnsafeSysctls = append(b.AllowedUnsafeSysctls, values[i])
	}
	return b
}

// WithForbiddenSysctls adds the given value to the ForbiddenSysctls field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the ForbiddenSysctls field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithForbiddenSysctls(values ...string) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.ForbiddenSysctls = append(b.ForbiddenSysctls, values[i])
	}
	return b
}

// WithAllowedProcMountTypes adds the given value to the AllowedProcMountTypes field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedProcMountTypes field.
func (b *PodSecurityPolicySpecApplyConfiguration) WithAllowedProcMountTypes(values ...v1.ProcMountType) *PodSecurityPolicySpecApplyConfiguration {
	for i := range values {
		b.AllowedProcMountTypes = append(b.AllowedProcMountTypes, values[i])
	}
	return b
}

// WithRuntimeClass sets the RuntimeClass field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RuntimeClass field is set to the value of the last call.
func (b *PodSecurityPolicySpecApplyConfiguration) WithRuntimeClass(value *RuntimeClassStrategyOptionsApplyConfiguration) *PodSecurityPolicySpecApplyConfiguration {
	b.RuntimeClass = value
	return b
}
