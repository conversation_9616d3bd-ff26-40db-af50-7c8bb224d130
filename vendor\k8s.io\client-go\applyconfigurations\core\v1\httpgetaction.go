/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	v1 "k8s.io/api/core/v1"
	intstr "k8s.io/apimachinery/pkg/util/intstr"
)

// HTTPGetActionApplyConfiguration represents an declarative configuration of the HTTPGetAction type for use
// with apply.
type HTTPGetActionApplyConfiguration struct {
	Path        *string                        `json:"path,omitempty"`
	Port        *intstr.IntOrString            `json:"port,omitempty"`
	Host        *string                        `json:"host,omitempty"`
	Scheme      *v1.URIScheme                  `json:"scheme,omitempty"`
	HTTPHeaders []HTTPHeaderApplyConfiguration `json:"httpHeaders,omitempty"`
}

// HTTPGetActionApplyConfiguration constructs an declarative configuration of the HTTPGetAction type for use with
// apply.
func HTTPGetAction() *HTTPGetActionApplyConfiguration {
	return &HTTPGetActionApplyConfiguration{}
}

// WithPath sets the Path field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Path field is set to the value of the last call.
func (b *HTTPGetActionApplyConfiguration) WithPath(value string) *HTTPGetActionApplyConfiguration {
	b.Path = &value
	return b
}

// WithPort sets the Port field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Port field is set to the value of the last call.
func (b *HTTPGetActionApplyConfiguration) WithPort(value intstr.IntOrString) *HTTPGetActionApplyConfiguration {
	b.Port = &value
	return b
}

// WithHost sets the Host field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Host field is set to the value of the last call.
func (b *HTTPGetActionApplyConfiguration) WithHost(value string) *HTTPGetActionApplyConfiguration {
	b.Host = &value
	return b
}

// WithScheme sets the Scheme field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Scheme field is set to the value of the last call.
func (b *HTTPGetActionApplyConfiguration) WithScheme(value v1.URIScheme) *HTTPGetActionApplyConfiguration {
	b.Scheme = &value
	return b
}

// WithHTTPHeaders adds the given value to the HTTPHeaders field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the HTTPHeaders field.
func (b *HTTPGetActionApplyConfiguration) WithHTTPHeaders(values ...*HTTPHeaderApplyConfiguration) *HTTPGetActionApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithHTTPHeaders")
		}
		b.HTTPHeaders = append(b.HTTPHeaders, *values[i])
	}
	return b
}
