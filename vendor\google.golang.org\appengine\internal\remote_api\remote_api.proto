syntax = "proto2";
option go_package = "remote_api";

package remote_api;

message Request {
  required string service_name = 2;
  required string method = 3;
  required bytes request = 4;
  optional string request_id = 5;
}

message ApplicationError {
  required int32 code = 1;
  required string detail = 2;
}

message RpcError {
  enum ErrorCode {
    UNKNOWN = 0;
    CALL_NOT_FOUND = 1;
    PARSE_ERROR = 2;
    SECURITY_VIOLATION = 3;
    OVER_QUOTA = 4;
    REQUEST_TOO_LARGE = 5;
    CAPABILITY_DISABLED = 6;
    FEATURE_DISABLED = 7;
    BAD_REQUEST = 8;
    RESPONSE_TOO_LARGE = 9;
    CANCELLED = 10;
    REPLAY_ERROR = 11;
    DEADLINE_EXCEEDED = 12;
  }
  required int32 code = 1;
  optional string detail = 2;
}

message Response {
  optional bytes response = 1;
  optional bytes exception = 2;
  optional ApplicationError application_error = 3;
  optional bytes java_exception = 4;
  optional RpcError rpc_error = 5;
}
