(import 'config.libsonnet') +
(import 'dashboards/loki-retention.libsonnet') +
(import 'dashboards/loki-chunks.libsonnet') +
(import 'dashboards/loki-logs.libsonnet') +
(import 'dashboards/loki-operational.libsonnet') +
(import 'dashboards/loki-reads.libsonnet') +
(import 'dashboards/loki-writes.libsonnet') +
(import 'dashboards/loki-writes-resources.libsonnet') +
(import 'dashboards/loki-reads-resources.libsonnet') +
(import 'dashboards/loki-deletion.libsonnet') +
(import 'dashboards/recording-rules.libsonnet')
