// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/logqlmodel/stats/stats.proto

package stats

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// Result contains LogQL query statistics.
type Result struct {
	Summary  Summary  `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary"`
	Querier  Querier  `protobuf:"bytes,2,opt,name=querier,proto3" json:"querier"`
	Ingester Ingester `protobuf:"bytes,3,opt,name=ingester,proto3" json:"ingester"`
}

func (m *Result) Reset()      { *m = Result{} }
func (*Result) ProtoMessage() {}
func (*Result) Descriptor() ([]byte, []int) {
	return fileDescriptor_6cdfe5d2aea33ebb, []int{0}
}
func (m *Result) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Result) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Result.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Result) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Result.Merge(m, src)
}
func (m *Result) XXX_Size() int {
	return m.Size()
}
func (m *Result) XXX_DiscardUnknown() {
	xxx_messageInfo_Result.DiscardUnknown(m)
}

var xxx_messageInfo_Result proto.InternalMessageInfo

func (m *Result) GetSummary() Summary {
	if m != nil {
		return m.Summary
	}
	return Summary{}
}

func (m *Result) GetQuerier() Querier {
	if m != nil {
		return m.Querier
	}
	return Querier{}
}

func (m *Result) GetIngester() Ingester {
	if m != nil {
		return m.Ingester
	}
	return Ingester{}
}

// Summary is the summary of a query statistics.
type Summary struct {
	// Total bytes processed per second.
	BytesProcessedPerSecond int64 `protobuf:"varint,1,opt,name=bytesProcessedPerSecond,proto3" json:"bytesProcessedPerSecond"`
	// Total lines processed per second.
	LinesProcessedPerSecond int64 `protobuf:"varint,2,opt,name=linesProcessedPerSecond,proto3" json:"linesProcessedPerSecond"`
	// Total bytes processed.
	TotalBytesProcessed int64 `protobuf:"varint,3,opt,name=totalBytesProcessed,proto3" json:"totalBytesProcessed"`
	// Total lines processed.
	TotalLinesProcessed int64 `protobuf:"varint,4,opt,name=totalLinesProcessed,proto3" json:"totalLinesProcessed"`
	// Execution time in seconds.
	// In addition to internal calculations this is also returned by the HTTP API.
	// Grafana expects time values to be returned in seconds as float.
	ExecTime float64 `protobuf:"fixed64,5,opt,name=execTime,proto3" json:"execTime"`
	// Queue time in seconds.
	// In addition to internal calculations this is also returned by the HTTP API.
	// Grafana expects time values to be returned in seconds as float.
	QueueTime float64 `protobuf:"fixed64,6,opt,name=queueTime,proto3" json:"queueTime"`
	// Total of subqueries created to fulfill this query.
	Subqueries int64 `protobuf:"varint,7,opt,name=subqueries,proto3" json:"subqueries"`
	// Total number of result entries returned
	TotalEntriesReturned int64 `protobuf:"varint,8,opt,name=totalEntriesReturned,proto3" json:"totalEntriesReturned"`
}

func (m *Summary) Reset()      { *m = Summary{} }
func (*Summary) ProtoMessage() {}
func (*Summary) Descriptor() ([]byte, []int) {
	return fileDescriptor_6cdfe5d2aea33ebb, []int{1}
}
func (m *Summary) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Summary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Summary.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Summary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Summary.Merge(m, src)
}
func (m *Summary) XXX_Size() int {
	return m.Size()
}
func (m *Summary) XXX_DiscardUnknown() {
	xxx_messageInfo_Summary.DiscardUnknown(m)
}

var xxx_messageInfo_Summary proto.InternalMessageInfo

func (m *Summary) GetBytesProcessedPerSecond() int64 {
	if m != nil {
		return m.BytesProcessedPerSecond
	}
	return 0
}

func (m *Summary) GetLinesProcessedPerSecond() int64 {
	if m != nil {
		return m.LinesProcessedPerSecond
	}
	return 0
}

func (m *Summary) GetTotalBytesProcessed() int64 {
	if m != nil {
		return m.TotalBytesProcessed
	}
	return 0
}

func (m *Summary) GetTotalLinesProcessed() int64 {
	if m != nil {
		return m.TotalLinesProcessed
	}
	return 0
}

func (m *Summary) GetExecTime() float64 {
	if m != nil {
		return m.ExecTime
	}
	return 0
}

func (m *Summary) GetQueueTime() float64 {
	if m != nil {
		return m.QueueTime
	}
	return 0
}

func (m *Summary) GetSubqueries() int64 {
	if m != nil {
		return m.Subqueries
	}
	return 0
}

func (m *Summary) GetTotalEntriesReturned() int64 {
	if m != nil {
		return m.TotalEntriesReturned
	}
	return 0
}

type Querier struct {
	Store Store `protobuf:"bytes,1,opt,name=store,proto3" json:"store"`
}

func (m *Querier) Reset()      { *m = Querier{} }
func (*Querier) ProtoMessage() {}
func (*Querier) Descriptor() ([]byte, []int) {
	return fileDescriptor_6cdfe5d2aea33ebb, []int{2}
}
func (m *Querier) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Querier) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Querier.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Querier) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Querier.Merge(m, src)
}
func (m *Querier) XXX_Size() int {
	return m.Size()
}
func (m *Querier) XXX_DiscardUnknown() {
	xxx_messageInfo_Querier.DiscardUnknown(m)
}

var xxx_messageInfo_Querier proto.InternalMessageInfo

func (m *Querier) GetStore() Store {
	if m != nil {
		return m.Store
	}
	return Store{}
}

type Ingester struct {
	// Total ingester reached for this query.
	TotalReached int32 `protobuf:"varint,1,opt,name=totalReached,proto3" json:"totalReached"`
	// Total of chunks matched by the query from ingesters
	TotalChunksMatched int64 `protobuf:"varint,2,opt,name=totalChunksMatched,proto3" json:"totalChunksMatched"`
	// Total of batches sent from ingesters.
	TotalBatches int64 `protobuf:"varint,3,opt,name=totalBatches,proto3" json:"totalBatches"`
	// Total lines sent by ingesters.
	TotalLinesSent int64 `protobuf:"varint,4,opt,name=totalLinesSent,proto3" json:"totalLinesSent"`
	Store          Store `protobuf:"bytes,5,opt,name=store,proto3" json:"store"`
}

func (m *Ingester) Reset()      { *m = Ingester{} }
func (*Ingester) ProtoMessage() {}
func (*Ingester) Descriptor() ([]byte, []int) {
	return fileDescriptor_6cdfe5d2aea33ebb, []int{3}
}
func (m *Ingester) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Ingester) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Ingester.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Ingester) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ingester.Merge(m, src)
}
func (m *Ingester) XXX_Size() int {
	return m.Size()
}
func (m *Ingester) XXX_DiscardUnknown() {
	xxx_messageInfo_Ingester.DiscardUnknown(m)
}

var xxx_messageInfo_Ingester proto.InternalMessageInfo

func (m *Ingester) GetTotalReached() int32 {
	if m != nil {
		return m.TotalReached
	}
	return 0
}

func (m *Ingester) GetTotalChunksMatched() int64 {
	if m != nil {
		return m.TotalChunksMatched
	}
	return 0
}

func (m *Ingester) GetTotalBatches() int64 {
	if m != nil {
		return m.TotalBatches
	}
	return 0
}

func (m *Ingester) GetTotalLinesSent() int64 {
	if m != nil {
		return m.TotalLinesSent
	}
	return 0
}

func (m *Ingester) GetStore() Store {
	if m != nil {
		return m.Store
	}
	return Store{}
}

type Store struct {
	// The total of chunk reference fetched from index.
	TotalChunksRef int64 `protobuf:"varint,1,opt,name=totalChunksRef,proto3" json:"totalChunksRef"`
	// Total number of chunks fetched.
	TotalChunksDownloaded int64 `protobuf:"varint,2,opt,name=totalChunksDownloaded,proto3" json:"totalChunksDownloaded"`
	// Time spent fetching chunks in nanoseconds.
	ChunksDownloadTime int64 `protobuf:"varint,3,opt,name=chunksDownloadTime,proto3" json:"chunksDownloadTime"`
	Chunk              Chunk `protobuf:"bytes,4,opt,name=chunk,proto3" json:"chunk"`
}

func (m *Store) Reset()      { *m = Store{} }
func (*Store) ProtoMessage() {}
func (*Store) Descriptor() ([]byte, []int) {
	return fileDescriptor_6cdfe5d2aea33ebb, []int{4}
}
func (m *Store) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Store) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Store.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Store) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Store.Merge(m, src)
}
func (m *Store) XXX_Size() int {
	return m.Size()
}
func (m *Store) XXX_DiscardUnknown() {
	xxx_messageInfo_Store.DiscardUnknown(m)
}

var xxx_messageInfo_Store proto.InternalMessageInfo

func (m *Store) GetTotalChunksRef() int64 {
	if m != nil {
		return m.TotalChunksRef
	}
	return 0
}

func (m *Store) GetTotalChunksDownloaded() int64 {
	if m != nil {
		return m.TotalChunksDownloaded
	}
	return 0
}

func (m *Store) GetChunksDownloadTime() int64 {
	if m != nil {
		return m.ChunksDownloadTime
	}
	return 0
}

func (m *Store) GetChunk() Chunk {
	if m != nil {
		return m.Chunk
	}
	return Chunk{}
}

type Chunk struct {
	// Total bytes processed but was already in memory. (found in the headchunk)
	HeadChunkBytes int64 `protobuf:"varint,4,opt,name=headChunkBytes,proto3" json:"headChunkBytes"`
	// Total lines processed but was already in memory. (found in the headchunk)
	HeadChunkLines int64 `protobuf:"varint,5,opt,name=headChunkLines,proto3" json:"headChunkLines"`
	// Total bytes decompressed and processed from chunks.
	DecompressedBytes int64 `protobuf:"varint,6,opt,name=decompressedBytes,proto3" json:"decompressedBytes"`
	// Total lines decompressed and processed from chunks.
	DecompressedLines int64 `protobuf:"varint,7,opt,name=decompressedLines,proto3" json:"decompressedLines"`
	// Total bytes of compressed chunks (blocks) processed.
	CompressedBytes int64 `protobuf:"varint,8,opt,name=compressedBytes,proto3" json:"compressedBytes"`
	// Total duplicates found while processing.
	TotalDuplicates int64 `protobuf:"varint,9,opt,name=totalDuplicates,proto3" json:"totalDuplicates"`
}

func (m *Chunk) Reset()      { *m = Chunk{} }
func (*Chunk) ProtoMessage() {}
func (*Chunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_6cdfe5d2aea33ebb, []int{5}
}
func (m *Chunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Chunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Chunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Chunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Chunk.Merge(m, src)
}
func (m *Chunk) XXX_Size() int {
	return m.Size()
}
func (m *Chunk) XXX_DiscardUnknown() {
	xxx_messageInfo_Chunk.DiscardUnknown(m)
}

var xxx_messageInfo_Chunk proto.InternalMessageInfo

func (m *Chunk) GetHeadChunkBytes() int64 {
	if m != nil {
		return m.HeadChunkBytes
	}
	return 0
}

func (m *Chunk) GetHeadChunkLines() int64 {
	if m != nil {
		return m.HeadChunkLines
	}
	return 0
}

func (m *Chunk) GetDecompressedBytes() int64 {
	if m != nil {
		return m.DecompressedBytes
	}
	return 0
}

func (m *Chunk) GetDecompressedLines() int64 {
	if m != nil {
		return m.DecompressedLines
	}
	return 0
}

func (m *Chunk) GetCompressedBytes() int64 {
	if m != nil {
		return m.CompressedBytes
	}
	return 0
}

func (m *Chunk) GetTotalDuplicates() int64 {
	if m != nil {
		return m.TotalDuplicates
	}
	return 0
}

func init() {
	proto.RegisterType((*Result)(nil), "stats.Result")
	proto.RegisterType((*Summary)(nil), "stats.Summary")
	proto.RegisterType((*Querier)(nil), "stats.Querier")
	proto.RegisterType((*Ingester)(nil), "stats.Ingester")
	proto.RegisterType((*Store)(nil), "stats.Store")
	proto.RegisterType((*Chunk)(nil), "stats.Chunk")
}

func init() { proto.RegisterFile("pkg/logqlmodel/stats/stats.proto", fileDescriptor_6cdfe5d2aea33ebb) }

var fileDescriptor_6cdfe5d2aea33ebb = []byte{
	// 759 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x55, 0x4d, 0x6f, 0xd3, 0x4c,
	0x10, 0x8e, 0x93, 0xe6, 0xa3, 0xfb, 0xf6, 0xeb, 0xdd, 0x52, 0x6a, 0x40, 0xb2, 0xab, 0x9c, 0x2a,
	0x01, 0x89, 0xf8, 0xb8, 0x80, 0xe8, 0xc5, 0x2d, 0x48, 0x95, 0x8a, 0x28, 0x53, 0xb8, 0x70, 0x73,
	0x9c, 0x6d, 0x12, 0xd5, 0xf1, 0xa6, 0xf6, 0x5a, 0xd0, 0x1b, 0x37, 0x8e, 0xf0, 0x1b, 0x38, 0x71,
	0xe1, 0x27, 0x70, 0xef, 0xb1, 0xc7, 0x9e, 0x2c, 0x9a, 0x5e, 0x90, 0x4f, 0xfd, 0x09, 0xc8, 0xb3,
	0x8e, 0x1d, 0x3b, 0x8e, 0xc4, 0x25, 0x99, 0x79, 0x9e, 0x79, 0x66, 0xd6, 0x33, 0xb3, 0x5a, 0xb2,
	0x35, 0x3a, 0xe9, 0xb5, 0x6d, 0xde, 0x3b, 0xb5, 0x87, 0xbc, 0xcb, 0xec, 0xb6, 0x27, 0x4c, 0xe1,
	0xc9, 0xdf, 0xd6, 0xc8, 0xe5, 0x82, 0xd3, 0x2a, 0x3a, 0x77, 0x1f, 0xf6, 0x06, 0xa2, 0xef, 0x77,
	0x5a, 0x16, 0x1f, 0xb6, 0x7b, 0xbc, 0xc7, 0xdb, 0xc8, 0x76, 0xfc, 0x63, 0xf4, 0xd0, 0x41, 0x4b,
	0xaa, 0x9a, 0xbf, 0x14, 0x52, 0x03, 0xe6, 0xf9, 0xb6, 0xa0, 0xcf, 0x48, 0xdd, 0xf3, 0x87, 0x43,
	0xd3, 0x3d, 0x53, 0x95, 0x2d, 0x65, 0xfb, 0xbf, 0xc7, 0x2b, 0x2d, 0x99, 0xff, 0x48, 0xa2, 0xc6,
	0xea, 0x79, 0xa0, 0x97, 0xc2, 0x40, 0x9f, 0x84, 0xc1, 0xc4, 0x88, 0xa4, 0xa7, 0x3e, 0x73, 0x07,
	0xcc, 0x55, 0xcb, 0x19, 0xe9, 0x5b, 0x89, 0xa6, 0xd2, 0x38, 0x0c, 0x26, 0x06, 0xdd, 0x21, 0x8d,
	0x81, 0xd3, 0x63, 0x9e, 0x60, 0xae, 0x5a, 0x41, 0xed, 0x6a, 0xac, 0xdd, 0x8f, 0x61, 0x63, 0x2d,
	0x16, 0x27, 0x81, 0x90, 0x58, 0xcd, 0xef, 0x0b, 0xa4, 0x1e, 0x9f, 0x8f, 0xbe, 0x27, 0x9b, 0x9d,
	0x33, 0xc1, 0xbc, 0x43, 0x97, 0x5b, 0xcc, 0xf3, 0x58, 0xf7, 0x90, 0xb9, 0x47, 0xcc, 0xe2, 0x4e,
	0x17, 0x3f, 0xa8, 0x62, 0xdc, 0x0b, 0x03, 0x7d, 0x5e, 0x08, 0xcc, 0x23, 0xa2, 0xb4, 0xf6, 0xc0,
	0x29, 0x4c, 0x5b, 0x4e, 0xd3, 0xce, 0x09, 0x81, 0x79, 0x04, 0xdd, 0x27, 0xeb, 0x82, 0x0b, 0xd3,
	0x36, 0x32, 0x65, 0xb1, 0x07, 0x15, 0x63, 0x33, 0x0c, 0xf4, 0x22, 0x1a, 0x8a, 0xc0, 0x24, 0xd5,
	0x41, 0xa6, 0x94, 0xba, 0x90, 0x4b, 0x95, 0xa5, 0xa1, 0x08, 0xa4, 0xdb, 0xa4, 0xc1, 0x3e, 0x31,
	0xeb, 0xdd, 0x60, 0xc8, 0xd4, 0xea, 0x96, 0xb2, 0xad, 0x18, 0x4b, 0x51, 0xe7, 0x27, 0x18, 0x24,
	0x16, 0xbd, 0x4f, 0x16, 0x4f, 0x7d, 0xe6, 0x33, 0x0c, 0xad, 0x61, 0xe8, 0x72, 0x18, 0xe8, 0x29,
	0x08, 0xa9, 0x49, 0x5b, 0x84, 0x78, 0x7e, 0x47, 0xce, 0xdc, 0x53, 0xeb, 0x78, 0xb0, 0x95, 0x30,
	0xd0, 0xa7, 0x50, 0x98, 0xb2, 0xe9, 0x01, 0xb9, 0x85, 0xa7, 0x7b, 0xe9, 0x08, 0xe4, 0x98, 0xf0,
	0x5d, 0x87, 0x75, 0xd5, 0x06, 0x2a, 0xd5, 0x30, 0xd0, 0x0b, 0x79, 0x28, 0x44, 0x9b, 0x2f, 0x48,
	0x3d, 0x5e, 0x44, 0xfa, 0x88, 0x54, 0x3d, 0xc1, 0x5d, 0x16, 0xaf, 0xf8, 0xd2, 0x64, 0xc5, 0x23,
	0xcc, 0x58, 0x8e, 0x17, 0x4d, 0x86, 0x80, 0xfc, 0x6b, 0xfe, 0x2c, 0x93, 0xc6, 0x64, 0x17, 0xe9,
	0x53, 0xb2, 0x84, 0x25, 0x80, 0x99, 0x56, 0x9f, 0xc9, 0xc5, 0xaa, 0x1a, 0x6b, 0x61, 0xa0, 0x67,
	0x70, 0xc8, 0x78, 0xf4, 0x15, 0xa1, 0xe8, 0xef, 0xf6, 0x7d, 0xe7, 0xc4, 0x7b, 0x6d, 0x0a, 0xd4,
	0xca, 0xed, 0xb9, 0x1d, 0x06, 0x7a, 0x01, 0x0b, 0x05, 0x58, 0x52, 0xdd, 0x40, 0xdf, 0x8b, 0x97,
	0x25, 0xad, 0x1e, 0xe3, 0x90, 0xf1, 0xe8, 0x73, 0xb2, 0x92, 0x8e, 0xfa, 0x88, 0x39, 0x22, 0xde,
	0x0c, 0x1a, 0x06, 0x7a, 0x8e, 0x81, 0x9c, 0x9f, 0xf6, 0xab, 0xfa, 0xcf, 0xfd, 0xfa, 0x5a, 0x26,
	0x55, 0xe4, 0x93, 0xc2, 0xf2, 0x23, 0x80, 0x1d, 0xc7, 0xf7, 0x30, 0x2d, 0x9c, 0x30, 0x90, 0xf3,
	0xe9, 0x1b, 0xb2, 0x31, 0x85, 0xec, 0xf1, 0x8f, 0x8e, 0xcd, 0xcd, 0x6e, 0xd2, 0xb5, 0x3b, 0x61,
	0xa0, 0x17, 0x07, 0x40, 0x31, 0x1c, 0xcd, 0xc0, 0xca, 0x60, 0xb8, 0xb8, 0x95, 0x74, 0x06, 0xb3,
	0x2c, 0x14, 0x60, 0x51, 0x47, 0x10, 0xc5, 0x26, 0xa6, 0x1d, 0xc1, 0x7a, 0x69, 0x47, 0x30, 0x04,
	0xe4, 0x5f, 0xf3, 0x4b, 0x85, 0x54, 0x91, 0x8f, 0x3a, 0xd2, 0x67, 0x66, 0x57, 0x06, 0x47, 0x97,
	0x78, 0x7a, 0x14, 0x59, 0x06, 0x72, 0x7e, 0x46, 0x8b, 0x03, 0xc2, 0x99, 0xe4, 0xb5, 0xc8, 0x40,
	0xce, 0xa7, 0xbb, 0xe4, 0xff, 0x2e, 0xb3, 0xf8, 0x70, 0xe4, 0xe2, 0x35, 0x97, 0xa5, 0x6b, 0x28,
	0xdf, 0x08, 0x03, 0x7d, 0x96, 0x84, 0x59, 0x28, 0x9f, 0x44, 0x9e, 0xa1, 0x5e, 0x9c, 0x44, 0x1e,
	0x63, 0x16, 0xa2, 0x3b, 0x64, 0x35, 0x7f, 0x0e, 0x79, 0xa9, 0xd7, 0xc3, 0x40, 0xcf, 0x53, 0x90,
	0x07, 0x22, 0x39, 0x8e, 0x77, 0xcf, 0x1f, 0xd9, 0x03, 0xcb, 0x8c, 0xe4, 0x8b, 0xa9, 0x3c, 0x47,
	0x41, 0x1e, 0x30, 0x3a, 0x17, 0x57, 0x5a, 0xe9, 0xf2, 0x4a, 0x2b, 0xdd, 0x5c, 0x69, 0xca, 0xe7,
	0xb1, 0xa6, 0xfc, 0x18, 0x6b, 0xca, 0xf9, 0x58, 0x53, 0x2e, 0xc6, 0x9a, 0xf2, 0x7b, 0xac, 0x29,
	0x7f, 0xc6, 0x5a, 0xe9, 0x66, 0xac, 0x29, 0xdf, 0xae, 0xb5, 0xd2, 0xc5, 0xb5, 0x56, 0xba, 0xbc,
	0xd6, 0x4a, 0x1f, 0x1e, 0x4c, 0xbf, 0xa9, 0xae, 0x79, 0x6c, 0x3a, 0x66, 0xdb, 0xe6, 0x27, 0x83,
	0x76, 0xd1, 0xa3, 0xdc, 0xa9, 0xe1, 0xcb, 0xfa, 0xe4, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x9a,
	0x84, 0xb3, 0x79, 0xb3, 0x07, 0x00, 0x00,
}

func (this *Result) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Result)
	if !ok {
		that2, ok := that.(Result)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Summary.Equal(&that1.Summary) {
		return false
	}
	if !this.Querier.Equal(&that1.Querier) {
		return false
	}
	if !this.Ingester.Equal(&that1.Ingester) {
		return false
	}
	return true
}
func (this *Summary) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Summary)
	if !ok {
		that2, ok := that.(Summary)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BytesProcessedPerSecond != that1.BytesProcessedPerSecond {
		return false
	}
	if this.LinesProcessedPerSecond != that1.LinesProcessedPerSecond {
		return false
	}
	if this.TotalBytesProcessed != that1.TotalBytesProcessed {
		return false
	}
	if this.TotalLinesProcessed != that1.TotalLinesProcessed {
		return false
	}
	if this.ExecTime != that1.ExecTime {
		return false
	}
	if this.QueueTime != that1.QueueTime {
		return false
	}
	if this.Subqueries != that1.Subqueries {
		return false
	}
	if this.TotalEntriesReturned != that1.TotalEntriesReturned {
		return false
	}
	return true
}
func (this *Querier) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Querier)
	if !ok {
		that2, ok := that.(Querier)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Store.Equal(&that1.Store) {
		return false
	}
	return true
}
func (this *Ingester) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Ingester)
	if !ok {
		that2, ok := that.(Ingester)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TotalReached != that1.TotalReached {
		return false
	}
	if this.TotalChunksMatched != that1.TotalChunksMatched {
		return false
	}
	if this.TotalBatches != that1.TotalBatches {
		return false
	}
	if this.TotalLinesSent != that1.TotalLinesSent {
		return false
	}
	if !this.Store.Equal(&that1.Store) {
		return false
	}
	return true
}
func (this *Store) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Store)
	if !ok {
		that2, ok := that.(Store)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TotalChunksRef != that1.TotalChunksRef {
		return false
	}
	if this.TotalChunksDownloaded != that1.TotalChunksDownloaded {
		return false
	}
	if this.ChunksDownloadTime != that1.ChunksDownloadTime {
		return false
	}
	if !this.Chunk.Equal(&that1.Chunk) {
		return false
	}
	return true
}
func (this *Chunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Chunk)
	if !ok {
		that2, ok := that.(Chunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.HeadChunkBytes != that1.HeadChunkBytes {
		return false
	}
	if this.HeadChunkLines != that1.HeadChunkLines {
		return false
	}
	if this.DecompressedBytes != that1.DecompressedBytes {
		return false
	}
	if this.DecompressedLines != that1.DecompressedLines {
		return false
	}
	if this.CompressedBytes != that1.CompressedBytes {
		return false
	}
	if this.TotalDuplicates != that1.TotalDuplicates {
		return false
	}
	return true
}
func (this *Result) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&stats.Result{")
	s = append(s, "Summary: "+strings.Replace(this.Summary.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "Querier: "+strings.Replace(this.Querier.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "Ingester: "+strings.Replace(this.Ingester.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Summary) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&stats.Summary{")
	s = append(s, "BytesProcessedPerSecond: "+fmt.Sprintf("%#v", this.BytesProcessedPerSecond)+",\n")
	s = append(s, "LinesProcessedPerSecond: "+fmt.Sprintf("%#v", this.LinesProcessedPerSecond)+",\n")
	s = append(s, "TotalBytesProcessed: "+fmt.Sprintf("%#v", this.TotalBytesProcessed)+",\n")
	s = append(s, "TotalLinesProcessed: "+fmt.Sprintf("%#v", this.TotalLinesProcessed)+",\n")
	s = append(s, "ExecTime: "+fmt.Sprintf("%#v", this.ExecTime)+",\n")
	s = append(s, "QueueTime: "+fmt.Sprintf("%#v", this.QueueTime)+",\n")
	s = append(s, "Subqueries: "+fmt.Sprintf("%#v", this.Subqueries)+",\n")
	s = append(s, "TotalEntriesReturned: "+fmt.Sprintf("%#v", this.TotalEntriesReturned)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Querier) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&stats.Querier{")
	s = append(s, "Store: "+strings.Replace(this.Store.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Ingester) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&stats.Ingester{")
	s = append(s, "TotalReached: "+fmt.Sprintf("%#v", this.TotalReached)+",\n")
	s = append(s, "TotalChunksMatched: "+fmt.Sprintf("%#v", this.TotalChunksMatched)+",\n")
	s = append(s, "TotalBatches: "+fmt.Sprintf("%#v", this.TotalBatches)+",\n")
	s = append(s, "TotalLinesSent: "+fmt.Sprintf("%#v", this.TotalLinesSent)+",\n")
	s = append(s, "Store: "+strings.Replace(this.Store.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Store) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&stats.Store{")
	s = append(s, "TotalChunksRef: "+fmt.Sprintf("%#v", this.TotalChunksRef)+",\n")
	s = append(s, "TotalChunksDownloaded: "+fmt.Sprintf("%#v", this.TotalChunksDownloaded)+",\n")
	s = append(s, "ChunksDownloadTime: "+fmt.Sprintf("%#v", this.ChunksDownloadTime)+",\n")
	s = append(s, "Chunk: "+strings.Replace(this.Chunk.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Chunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 10)
	s = append(s, "&stats.Chunk{")
	s = append(s, "HeadChunkBytes: "+fmt.Sprintf("%#v", this.HeadChunkBytes)+",\n")
	s = append(s, "HeadChunkLines: "+fmt.Sprintf("%#v", this.HeadChunkLines)+",\n")
	s = append(s, "DecompressedBytes: "+fmt.Sprintf("%#v", this.DecompressedBytes)+",\n")
	s = append(s, "DecompressedLines: "+fmt.Sprintf("%#v", this.DecompressedLines)+",\n")
	s = append(s, "CompressedBytes: "+fmt.Sprintf("%#v", this.CompressedBytes)+",\n")
	s = append(s, "TotalDuplicates: "+fmt.Sprintf("%#v", this.TotalDuplicates)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringStats(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *Result) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Result) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Result) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Ingester.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStats(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	{
		size, err := m.Querier.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStats(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.Summary.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStats(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Summary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Summary) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Summary) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TotalEntriesReturned != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalEntriesReturned))
		i--
		dAtA[i] = 0x40
	}
	if m.Subqueries != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.Subqueries))
		i--
		dAtA[i] = 0x38
	}
	if m.QueueTime != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.QueueTime))))
		i--
		dAtA[i] = 0x31
	}
	if m.ExecTime != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.ExecTime))))
		i--
		dAtA[i] = 0x29
	}
	if m.TotalLinesProcessed != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalLinesProcessed))
		i--
		dAtA[i] = 0x20
	}
	if m.TotalBytesProcessed != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalBytesProcessed))
		i--
		dAtA[i] = 0x18
	}
	if m.LinesProcessedPerSecond != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.LinesProcessedPerSecond))
		i--
		dAtA[i] = 0x10
	}
	if m.BytesProcessedPerSecond != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.BytesProcessedPerSecond))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Querier) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Querier) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Querier) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Store.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStats(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Ingester) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Ingester) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Ingester) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Store.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStats(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x2a
	if m.TotalLinesSent != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalLinesSent))
		i--
		dAtA[i] = 0x20
	}
	if m.TotalBatches != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalBatches))
		i--
		dAtA[i] = 0x18
	}
	if m.TotalChunksMatched != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalChunksMatched))
		i--
		dAtA[i] = 0x10
	}
	if m.TotalReached != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalReached))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Store) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Store) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Store) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Chunk.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStats(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x22
	if m.ChunksDownloadTime != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.ChunksDownloadTime))
		i--
		dAtA[i] = 0x18
	}
	if m.TotalChunksDownloaded != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalChunksDownloaded))
		i--
		dAtA[i] = 0x10
	}
	if m.TotalChunksRef != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalChunksRef))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Chunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Chunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Chunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TotalDuplicates != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.TotalDuplicates))
		i--
		dAtA[i] = 0x48
	}
	if m.CompressedBytes != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.CompressedBytes))
		i--
		dAtA[i] = 0x40
	}
	if m.DecompressedLines != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.DecompressedLines))
		i--
		dAtA[i] = 0x38
	}
	if m.DecompressedBytes != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.DecompressedBytes))
		i--
		dAtA[i] = 0x30
	}
	if m.HeadChunkLines != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.HeadChunkLines))
		i--
		dAtA[i] = 0x28
	}
	if m.HeadChunkBytes != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.HeadChunkBytes))
		i--
		dAtA[i] = 0x20
	}
	return len(dAtA) - i, nil
}

func encodeVarintStats(dAtA []byte, offset int, v uint64) int {
	offset -= sovStats(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Result) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Summary.Size()
	n += 1 + l + sovStats(uint64(l))
	l = m.Querier.Size()
	n += 1 + l + sovStats(uint64(l))
	l = m.Ingester.Size()
	n += 1 + l + sovStats(uint64(l))
	return n
}

func (m *Summary) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BytesProcessedPerSecond != 0 {
		n += 1 + sovStats(uint64(m.BytesProcessedPerSecond))
	}
	if m.LinesProcessedPerSecond != 0 {
		n += 1 + sovStats(uint64(m.LinesProcessedPerSecond))
	}
	if m.TotalBytesProcessed != 0 {
		n += 1 + sovStats(uint64(m.TotalBytesProcessed))
	}
	if m.TotalLinesProcessed != 0 {
		n += 1 + sovStats(uint64(m.TotalLinesProcessed))
	}
	if m.ExecTime != 0 {
		n += 9
	}
	if m.QueueTime != 0 {
		n += 9
	}
	if m.Subqueries != 0 {
		n += 1 + sovStats(uint64(m.Subqueries))
	}
	if m.TotalEntriesReturned != 0 {
		n += 1 + sovStats(uint64(m.TotalEntriesReturned))
	}
	return n
}

func (m *Querier) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Store.Size()
	n += 1 + l + sovStats(uint64(l))
	return n
}

func (m *Ingester) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TotalReached != 0 {
		n += 1 + sovStats(uint64(m.TotalReached))
	}
	if m.TotalChunksMatched != 0 {
		n += 1 + sovStats(uint64(m.TotalChunksMatched))
	}
	if m.TotalBatches != 0 {
		n += 1 + sovStats(uint64(m.TotalBatches))
	}
	if m.TotalLinesSent != 0 {
		n += 1 + sovStats(uint64(m.TotalLinesSent))
	}
	l = m.Store.Size()
	n += 1 + l + sovStats(uint64(l))
	return n
}

func (m *Store) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TotalChunksRef != 0 {
		n += 1 + sovStats(uint64(m.TotalChunksRef))
	}
	if m.TotalChunksDownloaded != 0 {
		n += 1 + sovStats(uint64(m.TotalChunksDownloaded))
	}
	if m.ChunksDownloadTime != 0 {
		n += 1 + sovStats(uint64(m.ChunksDownloadTime))
	}
	l = m.Chunk.Size()
	n += 1 + l + sovStats(uint64(l))
	return n
}

func (m *Chunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HeadChunkBytes != 0 {
		n += 1 + sovStats(uint64(m.HeadChunkBytes))
	}
	if m.HeadChunkLines != 0 {
		n += 1 + sovStats(uint64(m.HeadChunkLines))
	}
	if m.DecompressedBytes != 0 {
		n += 1 + sovStats(uint64(m.DecompressedBytes))
	}
	if m.DecompressedLines != 0 {
		n += 1 + sovStats(uint64(m.DecompressedLines))
	}
	if m.CompressedBytes != 0 {
		n += 1 + sovStats(uint64(m.CompressedBytes))
	}
	if m.TotalDuplicates != 0 {
		n += 1 + sovStats(uint64(m.TotalDuplicates))
	}
	return n
}

func sovStats(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozStats(x uint64) (n int) {
	return sovStats(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *Result) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Result{`,
		`Summary:` + strings.Replace(strings.Replace(this.Summary.String(), "Summary", "Summary", 1), `&`, ``, 1) + `,`,
		`Querier:` + strings.Replace(strings.Replace(this.Querier.String(), "Querier", "Querier", 1), `&`, ``, 1) + `,`,
		`Ingester:` + strings.Replace(strings.Replace(this.Ingester.String(), "Ingester", "Ingester", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Summary) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Summary{`,
		`BytesProcessedPerSecond:` + fmt.Sprintf("%v", this.BytesProcessedPerSecond) + `,`,
		`LinesProcessedPerSecond:` + fmt.Sprintf("%v", this.LinesProcessedPerSecond) + `,`,
		`TotalBytesProcessed:` + fmt.Sprintf("%v", this.TotalBytesProcessed) + `,`,
		`TotalLinesProcessed:` + fmt.Sprintf("%v", this.TotalLinesProcessed) + `,`,
		`ExecTime:` + fmt.Sprintf("%v", this.ExecTime) + `,`,
		`QueueTime:` + fmt.Sprintf("%v", this.QueueTime) + `,`,
		`Subqueries:` + fmt.Sprintf("%v", this.Subqueries) + `,`,
		`TotalEntriesReturned:` + fmt.Sprintf("%v", this.TotalEntriesReturned) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Querier) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Querier{`,
		`Store:` + strings.Replace(strings.Replace(this.Store.String(), "Store", "Store", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Ingester) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Ingester{`,
		`TotalReached:` + fmt.Sprintf("%v", this.TotalReached) + `,`,
		`TotalChunksMatched:` + fmt.Sprintf("%v", this.TotalChunksMatched) + `,`,
		`TotalBatches:` + fmt.Sprintf("%v", this.TotalBatches) + `,`,
		`TotalLinesSent:` + fmt.Sprintf("%v", this.TotalLinesSent) + `,`,
		`Store:` + strings.Replace(strings.Replace(this.Store.String(), "Store", "Store", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Store) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Store{`,
		`TotalChunksRef:` + fmt.Sprintf("%v", this.TotalChunksRef) + `,`,
		`TotalChunksDownloaded:` + fmt.Sprintf("%v", this.TotalChunksDownloaded) + `,`,
		`ChunksDownloadTime:` + fmt.Sprintf("%v", this.ChunksDownloadTime) + `,`,
		`Chunk:` + strings.Replace(strings.Replace(this.Chunk.String(), "Chunk", "Chunk", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Chunk) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Chunk{`,
		`HeadChunkBytes:` + fmt.Sprintf("%v", this.HeadChunkBytes) + `,`,
		`HeadChunkLines:` + fmt.Sprintf("%v", this.HeadChunkLines) + `,`,
		`DecompressedBytes:` + fmt.Sprintf("%v", this.DecompressedBytes) + `,`,
		`DecompressedLines:` + fmt.Sprintf("%v", this.DecompressedLines) + `,`,
		`CompressedBytes:` + fmt.Sprintf("%v", this.CompressedBytes) + `,`,
		`TotalDuplicates:` + fmt.Sprintf("%v", this.TotalDuplicates) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringStats(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *Result) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Result: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Result: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Summary.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Querier", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Querier.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ingester", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Ingester.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Summary) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Summary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Summary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesProcessedPerSecond", wireType)
			}
			m.BytesProcessedPerSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BytesProcessedPerSecond |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LinesProcessedPerSecond", wireType)
			}
			m.LinesProcessedPerSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LinesProcessedPerSecond |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalBytesProcessed", wireType)
			}
			m.TotalBytesProcessed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalBytesProcessed |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalLinesProcessed", wireType)
			}
			m.TotalLinesProcessed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalLinesProcessed |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecTime", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.ExecTime = float64(math.Float64frombits(v))
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueueTime", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.QueueTime = float64(math.Float64frombits(v))
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Subqueries", wireType)
			}
			m.Subqueries = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Subqueries |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalEntriesReturned", wireType)
			}
			m.TotalEntriesReturned = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalEntriesReturned |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Querier) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Querier: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Querier: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Store", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Store.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Ingester) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Ingester: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Ingester: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalReached", wireType)
			}
			m.TotalReached = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalReached |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalChunksMatched", wireType)
			}
			m.TotalChunksMatched = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalChunksMatched |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalBatches", wireType)
			}
			m.TotalBatches = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalBatches |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalLinesSent", wireType)
			}
			m.TotalLinesSent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalLinesSent |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Store", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Store.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Store) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Store: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Store: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalChunksRef", wireType)
			}
			m.TotalChunksRef = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalChunksRef |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalChunksDownloaded", wireType)
			}
			m.TotalChunksDownloaded = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalChunksDownloaded |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChunksDownloadTime", wireType)
			}
			m.ChunksDownloadTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChunksDownloadTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunk", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Chunk.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Chunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Chunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Chunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeadChunkBytes", wireType)
			}
			m.HeadChunkBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadChunkBytes |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeadChunkLines", wireType)
			}
			m.HeadChunkLines = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadChunkLines |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DecompressedBytes", wireType)
			}
			m.DecompressedBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DecompressedBytes |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DecompressedLines", wireType)
			}
			m.DecompressedLines = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DecompressedLines |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompressedBytes", wireType)
			}
			m.CompressedBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CompressedBytes |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalDuplicates", wireType)
			}
			m.TotalDuplicates = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalDuplicates |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipStats(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowStats
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStats
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStats
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthStats
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthStats
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowStats
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipStats(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthStats
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthStats = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowStats   = fmt.Errorf("proto: integer overflow")
)
