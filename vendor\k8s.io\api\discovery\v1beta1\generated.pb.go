/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: k8s.io/kubernetes/vendor/k8s.io/api/discovery/v1beta1/generated.proto

package v1beta1

import (
	fmt "fmt"

	io "io"

	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"
	k8s_io_api_core_v1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"

	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func (m *Endpoint) Reset()      { *m = Endpoint{} }
func (*Endpoint) ProtoMessage() {}
func (*Endpoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{0}
}
func (m *Endpoint) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Endpoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *Endpoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Endpoint.Merge(m, src)
}
func (m *Endpoint) XXX_Size() int {
	return m.Size()
}
func (m *Endpoint) XXX_DiscardUnknown() {
	xxx_messageInfo_Endpoint.DiscardUnknown(m)
}

var xxx_messageInfo_Endpoint proto.InternalMessageInfo

func (m *EndpointConditions) Reset()      { *m = EndpointConditions{} }
func (*EndpointConditions) ProtoMessage() {}
func (*EndpointConditions) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{1}
}
func (m *EndpointConditions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EndpointConditions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *EndpointConditions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointConditions.Merge(m, src)
}
func (m *EndpointConditions) XXX_Size() int {
	return m.Size()
}
func (m *EndpointConditions) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointConditions.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointConditions proto.InternalMessageInfo

func (m *EndpointHints) Reset()      { *m = EndpointHints{} }
func (*EndpointHints) ProtoMessage() {}
func (*EndpointHints) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{2}
}
func (m *EndpointHints) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EndpointHints) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *EndpointHints) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointHints.Merge(m, src)
}
func (m *EndpointHints) XXX_Size() int {
	return m.Size()
}
func (m *EndpointHints) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointHints.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointHints proto.InternalMessageInfo

func (m *EndpointPort) Reset()      { *m = EndpointPort{} }
func (*EndpointPort) ProtoMessage() {}
func (*EndpointPort) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{3}
}
func (m *EndpointPort) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EndpointPort) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *EndpointPort) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointPort.Merge(m, src)
}
func (m *EndpointPort) XXX_Size() int {
	return m.Size()
}
func (m *EndpointPort) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointPort.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointPort proto.InternalMessageInfo

func (m *EndpointSlice) Reset()      { *m = EndpointSlice{} }
func (*EndpointSlice) ProtoMessage() {}
func (*EndpointSlice) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{4}
}
func (m *EndpointSlice) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EndpointSlice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *EndpointSlice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointSlice.Merge(m, src)
}
func (m *EndpointSlice) XXX_Size() int {
	return m.Size()
}
func (m *EndpointSlice) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointSlice.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointSlice proto.InternalMessageInfo

func (m *EndpointSliceList) Reset()      { *m = EndpointSliceList{} }
func (*EndpointSliceList) ProtoMessage() {}
func (*EndpointSliceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{5}
}
func (m *EndpointSliceList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EndpointSliceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *EndpointSliceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointSliceList.Merge(m, src)
}
func (m *EndpointSliceList) XXX_Size() int {
	return m.Size()
}
func (m *EndpointSliceList) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointSliceList.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointSliceList proto.InternalMessageInfo

func (m *ForZone) Reset()      { *m = ForZone{} }
func (*ForZone) ProtoMessage() {}
func (*ForZone) Descriptor() ([]byte, []int) {
	return fileDescriptor_ece80bbc872d519b, []int{6}
}
func (m *ForZone) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ForZone) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ForZone) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForZone.Merge(m, src)
}
func (m *ForZone) XXX_Size() int {
	return m.Size()
}
func (m *ForZone) XXX_DiscardUnknown() {
	xxx_messageInfo_ForZone.DiscardUnknown(m)
}

var xxx_messageInfo_ForZone proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Endpoint)(nil), "k8s.io.api.discovery.v1beta1.Endpoint")
	proto.RegisterMapType((map[string]string)(nil), "k8s.io.api.discovery.v1beta1.Endpoint.TopologyEntry")
	proto.RegisterType((*EndpointConditions)(nil), "k8s.io.api.discovery.v1beta1.EndpointConditions")
	proto.RegisterType((*EndpointHints)(nil), "k8s.io.api.discovery.v1beta1.EndpointHints")
	proto.RegisterType((*EndpointPort)(nil), "k8s.io.api.discovery.v1beta1.EndpointPort")
	proto.RegisterType((*EndpointSlice)(nil), "k8s.io.api.discovery.v1beta1.EndpointSlice")
	proto.RegisterType((*EndpointSliceList)(nil), "k8s.io.api.discovery.v1beta1.EndpointSliceList")
	proto.RegisterType((*ForZone)(nil), "k8s.io.api.discovery.v1beta1.ForZone")
}

func init() {
	proto.RegisterFile("k8s.io/kubernetes/vendor/k8s.io/api/discovery/v1beta1/generated.proto", fileDescriptor_ece80bbc872d519b)
}

var fileDescriptor_ece80bbc872d519b = []byte{
	// 870 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0x41, 0x8f, 0xe3, 0x34,
	0x14, 0x6e, 0xa6, 0x53, 0x9a, 0xb8, 0x33, 0x62, 0xd7, 0xe2, 0x50, 0x0d, 0xab, 0xa4, 0x0a, 0x5a,
	0x54, 0x31, 0xda, 0x84, 0x19, 0xad, 0xd0, 0x0a, 0x4e, 0x13, 0x18, 0x58, 0xa4, 0x65, 0x77, 0xe4,
	0x19, 0x09, 0x69, 0xc5, 0x01, 0x37, 0xf1, 0xa4, 0xa1, 0x53, 0x3b, 0xb2, 0xdd, 0x4a, 0xbd, 0xf1,
	0x0f, 0xe0, 0xb7, 0xf0, 0x17, 0x90, 0xd0, 0x1c, 0xf7, 0xb8, 0xa7, 0x88, 0x09, 0xff, 0x62, 0x4f,
	0xc8, 0x8e, 0x93, 0xb4, 0x14, 0x86, 0xde, 0xec, 0xcf, 0xef, 0xfb, 0xde, 0x7b, 0xdf, 0xb3, 0x0d,
	0xce, 0x67, 0xcf, 0x44, 0x90, 0xb1, 0x70, 0xb6, 0x98, 0x10, 0x4e, 0x89, 0x24, 0x22, 0x5c, 0x12,
	0x9a, 0x30, 0x1e, 0x9a, 0x03, 0x9c, 0x67, 0x61, 0x92, 0x89, 0x98, 0x2d, 0x09, 0x5f, 0x85, 0xcb,
	0x93, 0x09, 0x91, 0xf8, 0x24, 0x4c, 0x09, 0x25, 0x1c, 0x4b, 0x92, 0x04, 0x39, 0x67, 0x92, 0xc1,
	0x47, 0x55, 0x74, 0x80, 0xf3, 0x2c, 0x68, 0xa2, 0x03, 0x13, 0x7d, 0xf4, 0x24, 0xcd, 0xe4, 0x74,
	0x31, 0x09, 0x62, 0x36, 0x0f, 0x53, 0x96, 0xb2, 0x50, 0x93, 0x26, 0x8b, 0x6b, 0xbd, 0xd3, 0x1b,
	0xbd, 0xaa, 0xc4, 0x8e, 0xfc, 0xb5, 0xd4, 0x31, 0xe3, 0x24, 0x5c, 0x6e, 0x25, 0x3c, 0x7a, 0xda,
	0xc6, 0xcc, 0x71, 0x3c, 0xcd, 0xa8, 0xaa, 0x2e, 0x9f, 0xa5, 0x0a, 0x10, 0xe1, 0x9c, 0x48, 0xfc,
	0x6f, 0xac, 0xf0, 0xbf, 0x58, 0x7c, 0x41, 0x65, 0x36, 0x27, 0x5b, 0x84, 0xcf, 0xfe, 0x8f, 0x20,
	0xe2, 0x29, 0x99, 0xe3, 0x7f, 0xf2, 0xfc, 0xdf, 0xf6, 0x81, 0x7d, 0x4e, 0x93, 0x9c, 0x65, 0x54,
	0xc2, 0x63, 0xe0, 0xe0, 0x24, 0xe1, 0x44, 0x08, 0x22, 0x86, 0xd6, 0xa8, 0x3b, 0x76, 0xa2, 0xc3,
	0xb2, 0xf0, 0x9c, 0xb3, 0x1a, 0x44, 0xed, 0x39, 0x4c, 0x00, 0x88, 0x19, 0x4d, 0x32, 0x99, 0x31,
	0x2a, 0x86, 0x7b, 0x23, 0x6b, 0x3c, 0x38, 0xfd, 0x34, 0xb8, 0xcf, 0xde, 0xa0, 0x4e, 0xf4, 0x65,
	0xc3, 0x8b, 0xe0, 0x6d, 0xe1, 0x75, 0xca, 0xc2, 0x03, 0x2d, 0x86, 0xd6, 0x74, 0xe1, 0x18, 0xd8,
	0x53, 0x26, 0x24, 0xc5, 0x73, 0x32, 0xec, 0x8e, 0xac, 0xb1, 0x13, 0x1d, 0x94, 0x85, 0x67, 0x3f,
	0x37, 0x18, 0x6a, 0x4e, 0xe1, 0x05, 0x70, 0x24, 0xe6, 0x29, 0x91, 0x88, 0x5c, 0x0f, 0xf7, 0x75,
	0x39, 0x1f, 0xad, 0x97, 0xa3, 0x06, 0x14, 0x2c, 0x4f, 0x82, 0x57, 0x93, 0x9f, 0x48, 0xac, 0x82,
	0x08, 0x27, 0x34, 0x26, 0x55, 0x87, 0x57, 0x35, 0x13, 0xb5, 0x22, 0x70, 0x02, 0x6c, 0xc9, 0x72,
	0x76, 0xc3, 0xd2, 0xd5, 0xb0, 0x37, 0xea, 0x8e, 0x07, 0xa7, 0x4f, 0x77, 0xeb, 0x2f, 0xb8, 0x32,
	0xb4, 0x73, 0x2a, 0xf9, 0x2a, 0x7a, 0x60, 0x7a, 0xb4, 0x6b, 0x18, 0x35, 0xba, 0xaa, 0x3f, 0xca,
	0x12, 0xf2, 0x52, 0xf5, 0xf7, 0x5e, 0xdb, 0xdf, 0x4b, 0x83, 0xa1, 0xe6, 0x14, 0xbe, 0x00, 0xbd,
	0x69, 0x46, 0xa5, 0x18, 0xf6, 0x75, 0x6f, 0xc7, 0xbb, 0x95, 0xf2, 0x5c, 0x51, 0x22, 0xa7, 0x2c,
	0xbc, 0x9e, 0x5e, 0xa2, 0x4a, 0xe4, 0xe8, 0x0b, 0x70, 0xb8, 0x51, 0x24, 0x7c, 0x00, 0xba, 0x33,
	0xb2, 0x1a, 0x5a, 0xaa, 0x06, 0xa4, 0x96, 0xf0, 0x03, 0xd0, 0x5b, 0xe2, 0x9b, 0x05, 0xd1, 0xb3,
	0x75, 0x50, 0xb5, 0xf9, 0x7c, 0xef, 0x99, 0xe5, 0xff, 0x62, 0x01, 0xb8, 0x3d, 0x4b, 0xe8, 0x81,
	0x1e, 0x27, 0x38, 0xa9, 0x44, 0xec, 0x2a, 0x29, 0x52, 0x00, 0xaa, 0x70, 0xf8, 0x18, 0xf4, 0x05,
	0xe1, 0xcb, 0x8c, 0xa6, 0x5a, 0xd3, 0x8e, 0x06, 0x65, 0xe1, 0xf5, 0x2f, 0x2b, 0x08, 0xd5, 0x67,
	0xf0, 0x04, 0x0c, 0x24, 0xe1, 0xf3, 0x8c, 0x62, 0xa9, 0x42, 0xbb, 0x3a, 0xf4, 0xfd, 0xb2, 0xf0,
	0x06, 0x57, 0x2d, 0x8c, 0xd6, 0x63, 0xfc, 0x04, 0x1c, 0x6e, 0x74, 0x0c, 0x2f, 0x81, 0x7d, 0xcd,
	0xf8, 0x6b, 0x46, 0xcd, 0x4d, 0x1e, 0x9c, 0x3e, 0xbe, 0xdf, 0xb0, 0xaf, 0xab, 0xe8, 0x76, 0x58,
	0x06, 0x10, 0xa8, 0x11, 0xf2, 0xff, 0xb0, 0xc0, 0x41, 0x9d, 0xe6, 0x82, 0x71, 0x09, 0x1f, 0x81,
	0x7d, 0x7d, 0x33, 0xb5, 0x6b, 0x91, 0x5d, 0x16, 0xde, 0xbe, 0x9e, 0x9a, 0x46, 0xe1, 0x37, 0xc0,
	0xd6, 0x8f, 0x2c, 0x66, 0x37, 0x95, 0x87, 0xd1, 0xb1, 0x12, 0xbe, 0x30, 0xd8, 0xbb, 0xc2, 0xfb,
	0x70, 0xfb, 0x03, 0x09, 0xea, 0x63, 0xd4, 0x90, 0x55, 0x9a, 0x9c, 0x71, 0xa9, 0x9d, 0xe8, 0x55,
	0x69, 0x54, 0x7a, 0xa4, 0x51, 0x65, 0x17, 0xce, 0xf3, 0x9a, 0xa6, 0xaf, 0xbe, 0x53, 0xd9, 0x75,
	0xd6, 0xc2, 0x68, 0x3d, 0xc6, 0xbf, 0xdb, 0x6b, 0xfd, 0xba, 0xbc, 0xc9, 0x62, 0x02, 0x7f, 0x04,
	0xb6, 0xfa, 0x8b, 0x12, 0x2c, 0xb1, 0xee, 0x66, 0xf3, 0x2d, 0x37, 0x5f, 0x4a, 0x90, 0xcf, 0x52,
	0x05, 0x88, 0x40, 0x45, 0xb7, 0xcf, 0xe9, 0x3b, 0x22, 0x71, 0xfb, 0x96, 0x5b, 0x0c, 0x35, 0xaa,
	0xf0, 0x2b, 0x30, 0x30, 0x9f, 0xc7, 0xd5, 0x2a, 0x27, 0xa6, 0x4c, 0xdf, 0x50, 0x06, 0x67, 0xed,
	0xd1, 0xbb, 0xcd, 0x2d, 0x5a, 0xa7, 0xc1, 0xef, 0x81, 0x43, 0x4c, 0xe1, 0xea, 0xd3, 0x51, 0x83,
	0xfd, 0x78, 0xb7, 0x97, 0x10, 0x3d, 0x34, 0xb9, 0x9c, 0x1a, 0x11, 0xa8, 0xd5, 0x82, 0xaf, 0x40,
	0x4f, 0xb9, 0x29, 0x86, 0x5d, 0x2d, 0xfa, 0xc9, 0x6e, 0xa2, 0x6a, 0x0c, 0xd1, 0xa1, 0x11, 0xee,
	0xa9, 0x9d, 0x40, 0x95, 0x8e, 0xff, 0xbb, 0x05, 0x1e, 0x6e, 0x78, 0xfc, 0x22, 0x13, 0x12, 0xfe,
	0xb0, 0xe5, 0x73, 0xb0, 0x9b, 0xcf, 0x8a, 0xad, 0x5d, 0x6e, 0x2e, 0x68, 0x8d, 0xac, 0x79, 0x7c,
	0x01, 0x7a, 0x99, 0x24, 0xf3, 0xda, 0x99, 0x1d, 0xff, 0x08, 0x5d, 0x5d, 0xdb, 0xc5, 0xb7, 0x4a,
	0x01, 0x55, 0x42, 0xfe, 0x31, 0xe8, 0x9b, 0x87, 0x00, 0x47, 0x1b, 0x97, 0xfd, 0xc0, 0x84, 0xaf,
	0x5d, 0xf8, 0xe8, 0xc9, 0xed, 0x9d, 0xdb, 0x79, 0x73, 0xe7, 0x76, 0xde, 0xde, 0xb9, 0x9d, 0x9f,
	0x4b, 0xd7, 0xba, 0x2d, 0x5d, 0xeb, 0x4d, 0xe9, 0x5a, 0x6f, 0x4b, 0xd7, 0xfa, 0xb3, 0x74, 0xad,
	0x5f, 0xff, 0x72, 0x3b, 0xaf, 0xfb, 0x26, 0xff, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xf7, 0x0d,
	0x6f, 0x98, 0xd3, 0x07, 0x00, 0x00,
}

func (m *Endpoint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Endpoint) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Endpoint) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Hints != nil {
		{
			size, err := m.Hints.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.NodeName != nil {
		i -= len(*m.NodeName)
		copy(dAtA[i:], *m.NodeName)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.NodeName)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Topology) > 0 {
		keysForTopology := make([]string, 0, len(m.Topology))
		for k := range m.Topology {
			keysForTopology = append(keysForTopology, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForTopology)
		for iNdEx := len(keysForTopology) - 1; iNdEx >= 0; iNdEx-- {
			v := m.Topology[string(keysForTopology[iNdEx])]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintGenerated(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(keysForTopology[iNdEx])
			copy(dAtA[i:], keysForTopology[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForTopology[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.TargetRef != nil {
		{
			size, err := m.TargetRef.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Hostname != nil {
		i -= len(*m.Hostname)
		copy(dAtA[i:], *m.Hostname)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Hostname)))
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Conditions.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Addresses) > 0 {
		for iNdEx := len(m.Addresses) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Addresses[iNdEx])
			copy(dAtA[i:], m.Addresses[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Addresses[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *EndpointConditions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EndpointConditions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EndpointConditions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Terminating != nil {
		i--
		if *m.Terminating {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.Serving != nil {
		i--
		if *m.Serving {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Ready != nil {
		i--
		if *m.Ready {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *EndpointHints) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EndpointHints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EndpointHints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ForZones) > 0 {
		for iNdEx := len(m.ForZones) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ForZones[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *EndpointPort) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EndpointPort) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EndpointPort) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AppProtocol != nil {
		i -= len(*m.AppProtocol)
		copy(dAtA[i:], *m.AppProtocol)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.AppProtocol)))
		i--
		dAtA[i] = 0x22
	}
	if m.Port != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.Port))
		i--
		dAtA[i] = 0x18
	}
	if m.Protocol != nil {
		i -= len(*m.Protocol)
		copy(dAtA[i:], *m.Protocol)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Protocol)))
		i--
		dAtA[i] = 0x12
	}
	if m.Name != nil {
		i -= len(*m.Name)
		copy(dAtA[i:], *m.Name)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EndpointSlice) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EndpointSlice) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EndpointSlice) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.AddressType)
	copy(dAtA[i:], m.AddressType)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.AddressType)))
	i--
	dAtA[i] = 0x22
	if len(m.Ports) > 0 {
		for iNdEx := len(m.Ports) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ports[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Endpoints) > 0 {
		for iNdEx := len(m.Endpoints) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Endpoints[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *EndpointSliceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EndpointSliceList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EndpointSliceList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ForZone) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ForZone) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ForZone) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func encodeVarintGenerated(dAtA []byte, offset int, v uint64) int {
	offset -= sovGenerated(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Endpoint) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Addresses) > 0 {
		for _, s := range m.Addresses {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	l = m.Conditions.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if m.Hostname != nil {
		l = len(*m.Hostname)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.TargetRef != nil {
		l = m.TargetRef.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if len(m.Topology) > 0 {
		for k, v := range m.Topology {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + len(v) + sovGenerated(uint64(len(v)))
			n += mapEntrySize + 1 + sovGenerated(uint64(mapEntrySize))
		}
	}
	if m.NodeName != nil {
		l = len(*m.NodeName)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Hints != nil {
		l = m.Hints.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *EndpointConditions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ready != nil {
		n += 2
	}
	if m.Serving != nil {
		n += 2
	}
	if m.Terminating != nil {
		n += 2
	}
	return n
}

func (m *EndpointHints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ForZones) > 0 {
		for _, e := range m.ForZones {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *EndpointPort) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Name != nil {
		l = len(*m.Name)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Protocol != nil {
		l = len(*m.Protocol)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Port != nil {
		n += 1 + sovGenerated(uint64(*m.Port))
	}
	if m.AppProtocol != nil {
		l = len(*m.AppProtocol)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *EndpointSlice) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Endpoints) > 0 {
		for _, e := range m.Endpoints {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.Ports) > 0 {
		for _, e := range m.Ports {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	l = len(m.AddressType)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *EndpointSliceList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *ForZone) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func sovGenerated(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGenerated(x uint64) (n int) {
	return sovGenerated(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *Endpoint) String() string {
	if this == nil {
		return "nil"
	}
	keysForTopology := make([]string, 0, len(this.Topology))
	for k := range this.Topology {
		keysForTopology = append(keysForTopology, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForTopology)
	mapStringForTopology := "map[string]string{"
	for _, k := range keysForTopology {
		mapStringForTopology += fmt.Sprintf("%v: %v,", k, this.Topology[k])
	}
	mapStringForTopology += "}"
	s := strings.Join([]string{`&Endpoint{`,
		`Addresses:` + fmt.Sprintf("%v", this.Addresses) + `,`,
		`Conditions:` + strings.Replace(strings.Replace(this.Conditions.String(), "EndpointConditions", "EndpointConditions", 1), `&`, ``, 1) + `,`,
		`Hostname:` + valueToStringGenerated(this.Hostname) + `,`,
		`TargetRef:` + strings.Replace(fmt.Sprintf("%v", this.TargetRef), "ObjectReference", "v1.ObjectReference", 1) + `,`,
		`Topology:` + mapStringForTopology + `,`,
		`NodeName:` + valueToStringGenerated(this.NodeName) + `,`,
		`Hints:` + strings.Replace(this.Hints.String(), "EndpointHints", "EndpointHints", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *EndpointConditions) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&EndpointConditions{`,
		`Ready:` + valueToStringGenerated(this.Ready) + `,`,
		`Serving:` + valueToStringGenerated(this.Serving) + `,`,
		`Terminating:` + valueToStringGenerated(this.Terminating) + `,`,
		`}`,
	}, "")
	return s
}
func (this *EndpointHints) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForForZones := "[]ForZone{"
	for _, f := range this.ForZones {
		repeatedStringForForZones += strings.Replace(strings.Replace(f.String(), "ForZone", "ForZone", 1), `&`, ``, 1) + ","
	}
	repeatedStringForForZones += "}"
	s := strings.Join([]string{`&EndpointHints{`,
		`ForZones:` + repeatedStringForForZones + `,`,
		`}`,
	}, "")
	return s
}
func (this *EndpointPort) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&EndpointPort{`,
		`Name:` + valueToStringGenerated(this.Name) + `,`,
		`Protocol:` + valueToStringGenerated(this.Protocol) + `,`,
		`Port:` + valueToStringGenerated(this.Port) + `,`,
		`AppProtocol:` + valueToStringGenerated(this.AppProtocol) + `,`,
		`}`,
	}, "")
	return s
}
func (this *EndpointSlice) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForEndpoints := "[]Endpoint{"
	for _, f := range this.Endpoints {
		repeatedStringForEndpoints += strings.Replace(strings.Replace(f.String(), "Endpoint", "Endpoint", 1), `&`, ``, 1) + ","
	}
	repeatedStringForEndpoints += "}"
	repeatedStringForPorts := "[]EndpointPort{"
	for _, f := range this.Ports {
		repeatedStringForPorts += strings.Replace(strings.Replace(f.String(), "EndpointPort", "EndpointPort", 1), `&`, ``, 1) + ","
	}
	repeatedStringForPorts += "}"
	s := strings.Join([]string{`&EndpointSlice{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v11.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Endpoints:` + repeatedStringForEndpoints + `,`,
		`Ports:` + repeatedStringForPorts + `,`,
		`AddressType:` + fmt.Sprintf("%v", this.AddressType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *EndpointSliceList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]EndpointSlice{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "EndpointSlice", "EndpointSlice", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&EndpointSliceList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v11.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *ForZone) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ForZone{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGenerated(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *Endpoint) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Endpoint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Endpoint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Addresses", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Addresses = append(m.Addresses, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Conditions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Conditions.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hostname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Hostname = &s
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetRef", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TargetRef == nil {
				m.TargetRef = &v1.ObjectReference{}
			}
			if err := m.TargetRef.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Topology", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Topology == nil {
				m.Topology = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Topology[mapkey] = mapvalue
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.NodeName = &s
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Hints == nil {
				m.Hints = &EndpointHints{}
			}
			if err := m.Hints.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EndpointConditions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EndpointConditions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EndpointConditions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ready", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Ready = &b
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Serving", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Serving = &b
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Terminating", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Terminating = &b
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EndpointHints) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EndpointHints: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EndpointHints: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ForZones", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ForZones = append(m.ForZones, ForZone{})
			if err := m.ForZones[len(m.ForZones)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EndpointPort) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EndpointPort: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EndpointPort: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Name = &s
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Protocol", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := k8s_io_api_core_v1.Protocol(dAtA[iNdEx:postIndex])
			m.Protocol = &s
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Port = &v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppProtocol", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.AppProtocol = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EndpointSlice) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EndpointSlice: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EndpointSlice: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Endpoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Endpoints = append(m.Endpoints, Endpoint{})
			if err := m.Endpoints[len(m.Endpoints)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ports", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ports = append(m.Ports, EndpointPort{})
			if err := m.Ports[len(m.Ports)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AddressType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AddressType = AddressType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EndpointSliceList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EndpointSliceList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EndpointSliceList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, EndpointSlice{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ForZone) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ForZone: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ForZone: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGenerated(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGenerated
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupGenerated
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthGenerated
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthGenerated        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGenerated          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupGenerated = fmt.Errorf("proto: unexpected end of group")
)
