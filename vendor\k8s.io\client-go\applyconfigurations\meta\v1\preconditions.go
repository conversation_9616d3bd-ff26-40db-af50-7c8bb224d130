/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	types "k8s.io/apimachinery/pkg/types"
)

// PreconditionsApplyConfiguration represents an declarative configuration of the Preconditions type for use
// with apply.
type PreconditionsApplyConfiguration struct {
	UID             *types.UID `json:"uid,omitempty"`
	ResourceVersion *string    `json:"resourceVersion,omitempty"`
}

// PreconditionsApplyConfiguration constructs an declarative configuration of the Preconditions type for use with
// apply.
func Preconditions() *PreconditionsApplyConfiguration {
	return &PreconditionsApplyConfiguration{}
}

// With<PERSON><PERSON> sets the UID field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UID field is set to the value of the last call.
func (b *PreconditionsApplyConfiguration) WithUID(value types.UID) *PreconditionsApplyConfiguration {
	b.UID = &value
	return b
}

// WithResourceVersion sets the ResourceVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResourceVersion field is set to the value of the last call.
func (b *PreconditionsApplyConfiguration) WithResourceVersion(value string) *PreconditionsApplyConfiguration {
	b.ResourceVersion = &value
	return b
}
