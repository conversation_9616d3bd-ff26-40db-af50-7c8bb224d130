// Copyright 2013 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// GENERATED BY make_perl_groups.pl; DO NOT EDIT.
// make_perl_groups.pl >perl_groups.go

package syntax

var code1 = []rune{ /* \d */
	0x30, 0x39,
}

var code2 = []rune{ /* \s */
	0x9, 0xa,
	0xc, 0xd,
	0x20, 0x20,
}

var code3 = []rune{ /* \w */
	0x30, 0x39,
	0x41, 0x5a,
	0x5f, 0x5f,
	0x61, 0x7a,
}

var perlGroup = map[string]charGroup{
	`\d`: {+1, code1},
	`\D`: {-1, code1},
	`\s`: {+1, code2},
	`\S`: {-1, code2},
	`\w`: {+1, code3},
	`\W`: {-1, code3},
}
var code4 = []rune{ /* [:alnum:] */
	0x30, 0x39,
	0x41, 0x5a,
	0x61, 0x7a,
}

var code5 = []rune{ /* [:alpha:] */
	0x41, 0x5a,
	0x61, 0x7a,
}

var code6 = []rune{ /* [:ascii:] */
	0x0, 0x7f,
}

var code7 = []rune{ /* [:blank:] */
	0x9, 0x9,
	0x20, 0x20,
}

var code8 = []rune{ /* [:cntrl:] */
	0x0, 0x1f,
	0x7f, 0x7f,
}

var code9 = []rune{ /* [:digit:] */
	0x30, 0x39,
}

var code10 = []rune{ /* [:graph:] */
	0x21, 0x7e,
}

var code11 = []rune{ /* [:lower:] */
	0x61, 0x7a,
}

var code12 = []rune{ /* [:print:] */
	0x20, 0x7e,
}

var code13 = []rune{ /* [:punct:] */
	0x21, 0x2f,
	0x3a, 0x40,
	0x5b, 0x60,
	0x7b, 0x7e,
}

var code14 = []rune{ /* [:space:] */
	0x9, 0xd,
	0x20, 0x20,
}

var code15 = []rune{ /* [:upper:] */
	0x41, 0x5a,
}

var code16 = []rune{ /* [:word:] */
	0x30, 0x39,
	0x41, 0x5a,
	0x5f, 0x5f,
	0x61, 0x7a,
}

var code17 = []rune{ /* [:xdigit:] */
	0x30, 0x39,
	0x41, 0x46,
	0x61, 0x66,
}

var posixGroup = map[string]charGroup{
	`[:alnum:]`:   {+1, code4},
	`[:^alnum:]`:  {-1, code4},
	`[:alpha:]`:   {+1, code5},
	`[:^alpha:]`:  {-1, code5},
	`[:ascii:]`:   {+1, code6},
	`[:^ascii:]`:  {-1, code6},
	`[:blank:]`:   {+1, code7},
	`[:^blank:]`:  {-1, code7},
	`[:cntrl:]`:   {+1, code8},
	`[:^cntrl:]`:  {-1, code8},
	`[:digit:]`:   {+1, code9},
	`[:^digit:]`:  {-1, code9},
	`[:graph:]`:   {+1, code10},
	`[:^graph:]`:  {-1, code10},
	`[:lower:]`:   {+1, code11},
	`[:^lower:]`:  {-1, code11},
	`[:print:]`:   {+1, code12},
	`[:^print:]`:  {-1, code12},
	`[:punct:]`:   {+1, code13},
	`[:^punct:]`:  {-1, code13},
	`[:space:]`:   {+1, code14},
	`[:^space:]`:  {-1, code14},
	`[:upper:]`:   {+1, code15},
	`[:^upper:]`:  {-1, code15},
	`[:word:]`:    {+1, code16},
	`[:^word:]`:   {-1, code16},
	`[:xdigit:]`:  {+1, code17},
	`[:^xdigit:]`: {-1, code17},
}
