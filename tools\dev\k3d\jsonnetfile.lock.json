{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/cortex-jsonnet.git", "subdir": "cortex"}}, "version": "acbe9614c51e2020930901d1b9f3ef33c527409c", "sum": "maEEiq7pOZVlyQgwFdjujsDP3f4q5wGLna8X89s0DQg="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "consul"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "Po3c1Ic96ngrJCtOazic/7OsLkoILOKZWXWyZWl+od8="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "enterprise-metrics"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "zBjtNmnhfGlOHX0yOQIkaN8qX0LymfUkbj5yEG9EakQ="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "etcd-operator"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "fQC1HY2iSTGfd4UfUE0nomf6NcRT+6xaVTDgwwRCrQc="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "grafana"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "7rANfqY8ERvoABHbwoGsdGpUeHxxYCSVOcM4Eky4QtQ="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "jaeger-agent-mixin"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "DsdBoqgx5kE3zc6fMYnfiGjW2+9Mx2OXFieWm1oFHgY="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "ksonnet-util"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "JDsc/bUs5Yv1RkGKcm0hMteqCKZqemxA3qP6eiEATr8="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "loki-simple-scalable"}}, "version": "0dfdeb96f42f2050ba923b92dfdbf090d26569f4", "sum": "788HNReqyKDNH7ARqwepctP7bqBAfawarD4jgK+2ugQ="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "memcached"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "dTOeEux3t9bYSqP2L/uCuLo/wUDpCKH4w+4OD9fePUk="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "tanka-util"}}, "version": "5a128df878434da37969b811e99bb9cd0a3779e3", "sum": "AGgjH6IJe/1qwNtxFIiG8V1uyOJZascEydQsNrfPQ4c="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/docsonnet.git", "subdir": "doc-util"}}, "version": "fc3f9bca2dff836b0e924a993bdf11bc51af78d4", "sum": "JUBWG9ybm0TlY3uCWrNoQS00BcfPlCvuK9jPFU0NIj8="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/k8s-libsonnet.git", "subdir": "1.20"}}, "version": "f8efa81cf15257bd151b97e31599e20b2ba5311b", "sum": "GSPYEqrqDbHKCZ/uFqx3MBmofDhE9Bh+2wP5k8Z39RY="}], "legacyImports": false}