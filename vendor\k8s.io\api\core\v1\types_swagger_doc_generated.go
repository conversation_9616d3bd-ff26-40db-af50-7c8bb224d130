/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

// This file contains a collection of methods that can be used from go-restful to
// generate Swagger API documentation for its models. Please read this PR for more
// information on the implementation: https://github.com/emicklei/go-restful/pull/215
//
// TODOs are ignored from the parser (e.g. TODO(andronat):... || TODO:...) if and only if
// they are on one line! For multiple line or blocks that you want to ignore use ---.
// Any context after a --- is ignored.
//
// Those methods can be generated by using hack/update-generated-swagger-docs.sh

// AUTO-GENERATED FUNCTIONS START HERE. DO NOT EDIT.
var map_AWSElasticBlockStoreVolumeSource = map[string]string{
	"":          "Represents a Persistent Disk resource in AWS.\n\nAn AWS EBS disk must exist before mounting to a container. The disk must also be in the same AWS zone as the kubelet. An AWS EBS disk can only be mounted as read/write once. AWS EBS volumes support ownership management and SELinux relabeling.",
	"volumeID":  "Unique ID of the persistent disk resource in AWS (Amazon EBS volume). More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore",
	"fsType":    "Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore",
	"partition": "The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \"1\". Similarly, the volume partition for /dev/sda is \"0\" (or you can leave the property empty).",
	"readOnly":  "Specify \"true\" to force and set the ReadOnly property in VolumeMounts to \"true\". If omitted, the default is \"false\". More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore",
}

func (AWSElasticBlockStoreVolumeSource) SwaggerDoc() map[string]string {
	return map_AWSElasticBlockStoreVolumeSource
}

var map_Affinity = map[string]string{
	"":                "Affinity is a group of affinity scheduling rules.",
	"nodeAffinity":    "Describes node affinity scheduling rules for the pod.",
	"podAffinity":     "Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).",
	"podAntiAffinity": "Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).",
}

func (Affinity) SwaggerDoc() map[string]string {
	return map_Affinity
}

var map_AttachedVolume = map[string]string{
	"":           "AttachedVolume describes a volume attached to a node",
	"name":       "Name of the attached volume",
	"devicePath": "DevicePath represents the device path where the volume should be available",
}

func (AttachedVolume) SwaggerDoc() map[string]string {
	return map_AttachedVolume
}

var map_AvoidPods = map[string]string{
	"":                "AvoidPods describes pods that should avoid this node. This is the value for a Node annotation with key scheduler.alpha.kubernetes.io/preferAvoidPods and will eventually become a field of NodeStatus.",
	"preferAvoidPods": "Bounded-sized list of signatures of pods that should avoid this node, sorted in timestamp order from oldest to newest. Size of the slice is unspecified.",
}

func (AvoidPods) SwaggerDoc() map[string]string {
	return map_AvoidPods
}

var map_AzureDiskVolumeSource = map[string]string{
	"":            "AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.",
	"diskName":    "The Name of the data disk in the blob storage",
	"diskURI":     "The URI the data disk in the blob storage",
	"cachingMode": "Host Caching mode: None, Read Only, Read Write.",
	"fsType":      "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.",
	"readOnly":    "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"kind":        "Expected values Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared",
}

func (AzureDiskVolumeSource) SwaggerDoc() map[string]string {
	return map_AzureDiskVolumeSource
}

var map_AzureFilePersistentVolumeSource = map[string]string{
	"":                "AzureFile represents an Azure File Service mount on the host and bind mount to the pod.",
	"secretName":      "the name of secret that contains Azure Storage Account Name and Key",
	"shareName":       "Share Name",
	"readOnly":        "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"secretNamespace": "the namespace of the secret that contains Azure Storage Account Name and Key default is the same as the Pod",
}

func (AzureFilePersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_AzureFilePersistentVolumeSource
}

var map_AzureFileVolumeSource = map[string]string{
	"":           "AzureFile represents an Azure File Service mount on the host and bind mount to the pod.",
	"secretName": "the name of secret that contains Azure Storage Account Name and Key",
	"shareName":  "Share Name",
	"readOnly":   "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
}

func (AzureFileVolumeSource) SwaggerDoc() map[string]string {
	return map_AzureFileVolumeSource
}

var map_Binding = map[string]string{
	"":         "Binding ties one object to another; for example, a pod is bound to a node by a scheduler. Deprecated in 1.7, please use the bindings subresource of pods instead.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"target":   "The target object that you want to bind to the standard object.",
}

func (Binding) SwaggerDoc() map[string]string {
	return map_Binding
}

var map_CSIPersistentVolumeSource = map[string]string{
	"":                           "Represents storage that is managed by an external CSI volume driver (Beta feature)",
	"driver":                     "Driver is the name of the driver to use for this volume. Required.",
	"volumeHandle":               "VolumeHandle is the unique volume name returned by the CSI volume plugin’s CreateVolume to refer to the volume on all subsequent calls. Required.",
	"readOnly":                   "Optional: The value to pass to ControllerPublishVolumeRequest. Defaults to false (read/write).",
	"fsType":                     "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\".",
	"volumeAttributes":           "Attributes of the volume to publish.",
	"controllerPublishSecretRef": "ControllerPublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI ControllerPublishVolume and ControllerUnpublishVolume calls. This field is optional, and may be empty if no secret is required. If the secret object contains more than one secret, all secrets are passed.",
	"nodeStageSecretRef":         "NodeStageSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodeStageVolume and NodeStageVolume and NodeUnstageVolume calls. This field is optional, and may be empty if no secret is required. If the secret object contains more than one secret, all secrets are passed.",
	"nodePublishSecretRef":       "NodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and may be empty if no secret is required. If the secret object contains more than one secret, all secrets are passed.",
	"controllerExpandSecretRef":  "ControllerExpandSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI ControllerExpandVolume call. This is an alpha field and requires enabling ExpandCSIVolumes feature gate. This field is optional, and may be empty if no secret is required. If the secret object contains more than one secret, all secrets are passed.",
}

func (CSIPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_CSIPersistentVolumeSource
}

var map_CSIVolumeSource = map[string]string{
	"":                     "Represents a source location of a volume to mount, managed by an external CSI driver",
	"driver":               "Driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster.",
	"readOnly":             "Specifies a read-only configuration for the volume. Defaults to false (read/write).",
	"fsType":               "Filesystem type to mount. Ex. \"ext4\", \"xfs\", \"ntfs\". If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply.",
	"volumeAttributes":     "VolumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver's documentation for supported values.",
	"nodePublishSecretRef": "NodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and  may be empty if no secret is required. If the secret object contains more than one secret, all secret references are passed.",
}

func (CSIVolumeSource) SwaggerDoc() map[string]string {
	return map_CSIVolumeSource
}

var map_Capabilities = map[string]string{
	"":     "Adds and removes POSIX capabilities from running containers.",
	"add":  "Added capabilities",
	"drop": "Removed capabilities",
}

func (Capabilities) SwaggerDoc() map[string]string {
	return map_Capabilities
}

var map_CephFSPersistentVolumeSource = map[string]string{
	"":           "Represents a Ceph Filesystem mount that lasts the lifetime of a pod Cephfs volumes do not support ownership management or SELinux relabeling.",
	"monitors":   "Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"path":       "Optional: Used as the mounted root, rather than the full Ceph tree, default is /",
	"user":       "Optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"secretFile": "Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"secretRef":  "Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"readOnly":   "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
}

func (CephFSPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_CephFSPersistentVolumeSource
}

var map_CephFSVolumeSource = map[string]string{
	"":           "Represents a Ceph Filesystem mount that lasts the lifetime of a pod Cephfs volumes do not support ownership management or SELinux relabeling.",
	"monitors":   "Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"path":       "Optional: Used as the mounted root, rather than the full Ceph tree, default is /",
	"user":       "Optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"secretFile": "Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"secretRef":  "Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
	"readOnly":   "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it",
}

func (CephFSVolumeSource) SwaggerDoc() map[string]string {
	return map_CephFSVolumeSource
}

var map_CinderPersistentVolumeSource = map[string]string{
	"":          "Represents a cinder volume resource in Openstack. A Cinder volume must exist before mounting to a container. The volume must also be in the same region as the kubelet. Cinder volumes support ownership management and SELinux relabeling.",
	"volumeID":  "volume id used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"fsType":    "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"readOnly":  "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"secretRef": "Optional: points to a secret object containing parameters used to connect to OpenStack.",
}

func (CinderPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_CinderPersistentVolumeSource
}

var map_CinderVolumeSource = map[string]string{
	"":          "Represents a cinder volume resource in Openstack. A Cinder volume must exist before mounting to a container. The volume must also be in the same region as the kubelet. Cinder volumes support ownership management and SELinux relabeling.",
	"volumeID":  "volume id used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"fsType":    "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"readOnly":  "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"secretRef": "Optional: points to a secret object containing parameters used to connect to OpenStack.",
}

func (CinderVolumeSource) SwaggerDoc() map[string]string {
	return map_CinderVolumeSource
}

var map_ClientIPConfig = map[string]string{
	"":               "ClientIPConfig represents the configurations of Client IP based session affinity.",
	"timeoutSeconds": "timeoutSeconds specifies the seconds of ClientIP type session sticky time. The value must be >0 && <=86400(for 1 day) if ServiceAffinity == \"ClientIP\". Default value is 10800(for 3 hours).",
}

func (ClientIPConfig) SwaggerDoc() map[string]string {
	return map_ClientIPConfig
}

var map_ComponentCondition = map[string]string{
	"":        "Information about the condition of a component.",
	"type":    "Type of condition for a component. Valid value: \"Healthy\"",
	"status":  "Status of the condition for a component. Valid values for \"Healthy\": \"True\", \"False\", or \"Unknown\".",
	"message": "Message about the condition for a component. For example, information about a health check.",
	"error":   "Condition error code for a component. For example, a health check error code.",
}

func (ComponentCondition) SwaggerDoc() map[string]string {
	return map_ComponentCondition
}

var map_ComponentStatus = map[string]string{
	"":           "ComponentStatus (and ComponentStatusList) holds the cluster validation info. Deprecated: This API is deprecated in v1.19+",
	"metadata":   "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"conditions": "List of component conditions observed",
}

func (ComponentStatus) SwaggerDoc() map[string]string {
	return map_ComponentStatus
}

var map_ComponentStatusList = map[string]string{
	"":         "Status of all the conditions for the component as a list of ComponentStatus objects. Deprecated: This API is deprecated in v1.19+",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of ComponentStatus objects.",
}

func (ComponentStatusList) SwaggerDoc() map[string]string {
	return map_ComponentStatusList
}

var map_ConfigMap = map[string]string{
	"":           "ConfigMap holds configuration data for pods to consume.",
	"metadata":   "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"immutable":  "Immutable, if set to true, ensures that data stored in the ConfigMap cannot be updated (only object metadata can be modified). If not set to true, the field can be modified at any time. Defaulted to nil.",
	"data":       "Data contains the configuration data. Each key must consist of alphanumeric characters, '-', '_' or '.'. Values with non-UTF-8 byte sequences must use the BinaryData field. The keys stored in Data must not overlap with the keys in the BinaryData field, this is enforced during validation process.",
	"binaryData": "BinaryData contains the binary data. Each key must consist of alphanumeric characters, '-', '_' or '.'. BinaryData can contain byte sequences that are not in the UTF-8 range. The keys stored in BinaryData must not overlap with the ones in the Data field, this is enforced during validation process. Using this field will require 1.10+ apiserver and kubelet.",
}

func (ConfigMap) SwaggerDoc() map[string]string {
	return map_ConfigMap
}

var map_ConfigMapEnvSource = map[string]string{
	"":         "ConfigMapEnvSource selects a ConfigMap to populate the environment variables with.\n\nThe contents of the target ConfigMap's Data field will represent the key-value pairs as environment variables.",
	"optional": "Specify whether the ConfigMap must be defined",
}

func (ConfigMapEnvSource) SwaggerDoc() map[string]string {
	return map_ConfigMapEnvSource
}

var map_ConfigMapKeySelector = map[string]string{
	"":         "Selects a key from a ConfigMap.",
	"key":      "The key to select.",
	"optional": "Specify whether the ConfigMap or its key must be defined",
}

func (ConfigMapKeySelector) SwaggerDoc() map[string]string {
	return map_ConfigMapKeySelector
}

var map_ConfigMapList = map[string]string{
	"":         "ConfigMapList is a resource containing a list of ConfigMap objects.",
	"metadata": "More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"items":    "Items is the list of ConfigMaps.",
}

func (ConfigMapList) SwaggerDoc() map[string]string {
	return map_ConfigMapList
}

var map_ConfigMapNodeConfigSource = map[string]string{
	"":                 "ConfigMapNodeConfigSource contains the information to reference a ConfigMap as a config source for the Node. This API is deprecated since 1.22: https://git.k8s.io/enhancements/keps/sig-node/281-dynamic-kubelet-configuration",
	"namespace":        "Namespace is the metadata.namespace of the referenced ConfigMap. This field is required in all cases.",
	"name":             "Name is the metadata.name of the referenced ConfigMap. This field is required in all cases.",
	"uid":              "UID is the metadata.UID of the referenced ConfigMap. This field is forbidden in Node.Spec, and required in Node.Status.",
	"resourceVersion":  "ResourceVersion is the metadata.ResourceVersion of the referenced ConfigMap. This field is forbidden in Node.Spec, and required in Node.Status.",
	"kubeletConfigKey": "KubeletConfigKey declares which key of the referenced ConfigMap corresponds to the KubeletConfiguration structure This field is required in all cases.",
}

func (ConfigMapNodeConfigSource) SwaggerDoc() map[string]string {
	return map_ConfigMapNodeConfigSource
}

var map_ConfigMapProjection = map[string]string{
	"":         "Adapts a ConfigMap into a projected volume.\n\nThe contents of the target ConfigMap's Data field will be presented in a projected volume as files using the keys in the Data field as the file names, unless the items element is populated with specific mappings of keys to paths. Note that this is identical to a configmap volume source without the default mode.",
	"items":    "If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.",
	"optional": "Specify whether the ConfigMap or its keys must be defined",
}

func (ConfigMapProjection) SwaggerDoc() map[string]string {
	return map_ConfigMapProjection
}

var map_ConfigMapVolumeSource = map[string]string{
	"":            "Adapts a ConfigMap into a volume.\n\nThe contents of the target ConfigMap's Data field will be presented in a volume as files using the keys in the Data field as the file names, unless the items element is populated with specific mappings of keys to paths. ConfigMap volumes support ownership management and SELinux relabeling.",
	"items":       "If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.",
	"defaultMode": "Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.",
	"optional":    "Specify whether the ConfigMap or its keys must be defined",
}

func (ConfigMapVolumeSource) SwaggerDoc() map[string]string {
	return map_ConfigMapVolumeSource
}

var map_Container = map[string]string{
	"":                         "A single application container that you want to run within a pod.",
	"name":                     "Name of the container specified as a DNS_LABEL. Each container in a pod must have a unique name (DNS_LABEL). Cannot be updated.",
	"image":                    "Docker image name. More info: https://kubernetes.io/docs/concepts/containers/images This field is optional to allow higher level config management to default or override container images in workload controllers like Deployments and StatefulSets.",
	"command":                  "Entrypoint array. Not executed within a shell. The docker image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \"$$(VAR_NAME)\" will produce the string literal \"$(VAR_NAME)\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell",
	"args":                     "Arguments to the entrypoint. The docker image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \"$$(VAR_NAME)\" will produce the string literal \"$(VAR_NAME)\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell",
	"workingDir":               "Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.",
	"ports":                    "List of ports to expose from the container. Exposing a port here gives the system additional information about the network connections a container uses, but is primarily informational. Not specifying a port here DOES NOT prevent that port from being exposed. Any port which is listening on the default \"0.0.0.0\" address inside a container will be accessible from the network. Cannot be updated.",
	"envFrom":                  "List of sources to populate environment variables in the container. The keys defined within a source must be a C_IDENTIFIER. All invalid keys will be reported as an event when the container is starting. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.",
	"env":                      "List of environment variables to set in the container. Cannot be updated.",
	"resources":                "Compute Resources required by this container. Cannot be updated. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/",
	"volumeMounts":             "Pod volumes to mount into the container's filesystem. Cannot be updated.",
	"volumeDevices":            "volumeDevices is the list of block devices to be used by the container.",
	"livenessProbe":            "Periodic probe of container liveness. Container will be restarted if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes",
	"readinessProbe":           "Periodic probe of container service readiness. Container will be removed from service endpoints if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes",
	"startupProbe":             "StartupProbe indicates that the Pod has successfully initialized. If specified, no other probes are executed until this completes successfully. If this probe fails, the Pod will be restarted, just as if the livenessProbe failed. This can be used to provide different probe parameters at the beginning of a Pod's lifecycle, when it might take a long time to load data or warm a cache, than during steady-state operation. This cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes",
	"lifecycle":                "Actions that the management system should take in response to container lifecycle events. Cannot be updated.",
	"terminationMessagePath":   "Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.",
	"terminationMessagePolicy": "Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.",
	"imagePullPolicy":          "Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images",
	"securityContext":          "SecurityContext defines the security options the container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext. More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/",
	"stdin":                    "Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.",
	"stdinOnce":                "Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false",
	"tty":                      "Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.",
}

func (Container) SwaggerDoc() map[string]string {
	return map_Container
}

var map_ContainerImage = map[string]string{
	"":          "Describe a container image",
	"names":     "Names by which this image is known. e.g. [\"k8s.gcr.io/hyperkube:v1.0.7\", \"dockerhub.io/google_containers/hyperkube:v1.0.7\"]",
	"sizeBytes": "The size of the image in bytes.",
}

func (ContainerImage) SwaggerDoc() map[string]string {
	return map_ContainerImage
}

var map_ContainerPort = map[string]string{
	"":              "ContainerPort represents a network port in a single container.",
	"name":          "If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.",
	"hostPort":      "Number of port to expose on the host. If specified, this must be a valid port number, 0 < x < 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.",
	"containerPort": "Number of port to expose on the pod's IP address. This must be a valid port number, 0 < x < 65536.",
	"protocol":      "Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \"TCP\".",
	"hostIP":        "What host IP to bind the external port to.",
}

func (ContainerPort) SwaggerDoc() map[string]string {
	return map_ContainerPort
}

var map_ContainerState = map[string]string{
	"":           "ContainerState holds a possible state of container. Only one of its members may be specified. If none of them is specified, the default one is ContainerStateWaiting.",
	"waiting":    "Details about a waiting container",
	"running":    "Details about a running container",
	"terminated": "Details about a terminated container",
}

func (ContainerState) SwaggerDoc() map[string]string {
	return map_ContainerState
}

var map_ContainerStateRunning = map[string]string{
	"":          "ContainerStateRunning is a running state of a container.",
	"startedAt": "Time at which the container was last (re-)started",
}

func (ContainerStateRunning) SwaggerDoc() map[string]string {
	return map_ContainerStateRunning
}

var map_ContainerStateTerminated = map[string]string{
	"":            "ContainerStateTerminated is a terminated state of a container.",
	"exitCode":    "Exit status from the last termination of the container",
	"signal":      "Signal from the last termination of the container",
	"reason":      "(brief) reason from the last termination of the container",
	"message":     "Message regarding the last termination of the container",
	"startedAt":   "Time at which previous execution of the container started",
	"finishedAt":  "Time at which the container last terminated",
	"containerID": "Container's ID in the format 'docker://<container_id>'",
}

func (ContainerStateTerminated) SwaggerDoc() map[string]string {
	return map_ContainerStateTerminated
}

var map_ContainerStateWaiting = map[string]string{
	"":        "ContainerStateWaiting is a waiting state of a container.",
	"reason":  "(brief) reason the container is not yet running.",
	"message": "Message regarding why the container is not yet running.",
}

func (ContainerStateWaiting) SwaggerDoc() map[string]string {
	return map_ContainerStateWaiting
}

var map_ContainerStatus = map[string]string{
	"":             "ContainerStatus contains details for the current status of this container.",
	"name":         "This must be a DNS_LABEL. Each container in a pod must have a unique name. Cannot be updated.",
	"state":        "Details about the container's current condition.",
	"lastState":    "Details about the container's last termination condition.",
	"ready":        "Specifies whether the container has passed its readiness probe.",
	"restartCount": "The number of times the container has been restarted.",
	"image":        "The image the container is running. More info: https://kubernetes.io/docs/concepts/containers/images.",
	"imageID":      "ImageID of the container's image.",
	"containerID":  "Container's ID in the format 'docker://<container_id>'.",
	"started":      "Specifies whether the container has passed its startup probe. Initialized as false, becomes true after startupProbe is considered successful. Resets to false when the container is restarted, or if kubelet loses state temporarily. Is always true when no startupProbe is defined.",
}

func (ContainerStatus) SwaggerDoc() map[string]string {
	return map_ContainerStatus
}

var map_DaemonEndpoint = map[string]string{
	"":     "DaemonEndpoint contains information about a single Daemon endpoint.",
	"Port": "Port number of the given endpoint.",
}

func (DaemonEndpoint) SwaggerDoc() map[string]string {
	return map_DaemonEndpoint
}

var map_DownwardAPIProjection = map[string]string{
	"":      "Represents downward API info for projecting into a projected volume. Note that this is identical to a downwardAPI volume source without the default mode.",
	"items": "Items is a list of DownwardAPIVolume file",
}

func (DownwardAPIProjection) SwaggerDoc() map[string]string {
	return map_DownwardAPIProjection
}

var map_DownwardAPIVolumeFile = map[string]string{
	"":                 "DownwardAPIVolumeFile represents information to create the file containing the pod field",
	"path":             "Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'",
	"fieldRef":         "Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.",
	"resourceFieldRef": "Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.",
	"mode":             "Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.",
}

func (DownwardAPIVolumeFile) SwaggerDoc() map[string]string {
	return map_DownwardAPIVolumeFile
}

var map_DownwardAPIVolumeSource = map[string]string{
	"":            "DownwardAPIVolumeSource represents a volume containing downward API info. Downward API volumes support ownership management and SELinux relabeling.",
	"items":       "Items is a list of downward API volume file",
	"defaultMode": "Optional: mode bits to use on created files by default. Must be a Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.",
}

func (DownwardAPIVolumeSource) SwaggerDoc() map[string]string {
	return map_DownwardAPIVolumeSource
}

var map_EmptyDirVolumeSource = map[string]string{
	"":          "Represents an empty directory for a pod. Empty directory volumes support ownership management and SELinux relabeling.",
	"medium":    "What type of storage medium should back this directory. The default is \"\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir",
	"sizeLimit": "Total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: http://kubernetes.io/docs/user-guide/volumes#emptydir",
}

func (EmptyDirVolumeSource) SwaggerDoc() map[string]string {
	return map_EmptyDirVolumeSource
}

var map_EndpointAddress = map[string]string{
	"":          "EndpointAddress is a tuple that describes single IP address.",
	"ip":        "The IP of this endpoint. May not be loopback (*********/8), link-local (***********/16), or link-local multicast ((*********/24). IPv6 is also accepted but not fully supported on all platforms. Also, certain kubernetes components, like kube-proxy, are not IPv6 ready.",
	"hostname":  "The Hostname of this endpoint",
	"nodeName":  "Optional: Node hosting this endpoint. This can be used to determine endpoints local to a node.",
	"targetRef": "Reference to object providing the endpoint.",
}

func (EndpointAddress) SwaggerDoc() map[string]string {
	return map_EndpointAddress
}

var map_EndpointPort = map[string]string{
	"":            "EndpointPort is a tuple that describes a single port.",
	"name":        "The name of this port.  This must match the 'name' field in the corresponding ServicePort. Must be a DNS_LABEL. Optional only if one port is defined.",
	"port":        "The port number of the endpoint.",
	"protocol":    "The IP protocol for this port. Must be UDP, TCP, or SCTP. Default is TCP.",
	"appProtocol": "The application protocol for this port. This field follows standard Kubernetes label syntax. Un-prefixed names are reserved for IANA standard service names (as per RFC-6335 and http://www.iana.org/assignments/service-names). Non-standard protocols should use prefixed names such as mycompany.com/my-custom-protocol.",
}

func (EndpointPort) SwaggerDoc() map[string]string {
	return map_EndpointPort
}

var map_EndpointSubset = map[string]string{
	"":                  "EndpointSubset is a group of addresses with a common set of ports. The expanded set of endpoints is the Cartesian product of Addresses x Ports. For example, given:\n  {\n    Addresses: [{\"ip\": \"*********\"}, {\"ip\": \"*********\"}],\n    Ports:     [{\"name\": \"a\", \"port\": 8675}, {\"name\": \"b\", \"port\": 309}]\n  }\nThe resulting set of endpoints can be viewed as:\n    a: [ *********:8675, *********:8675 ],\n    b: [ *********:309, *********:309 ]",
	"addresses":         "IP addresses which offer the related ports that are marked as ready. These endpoints should be considered safe for load balancers and clients to utilize.",
	"notReadyAddresses": "IP addresses which offer the related ports but are not currently marked as ready because they have not yet finished starting, have recently failed a readiness check, or have recently failed a liveness check.",
	"ports":             "Port numbers available on the related IP addresses.",
}

func (EndpointSubset) SwaggerDoc() map[string]string {
	return map_EndpointSubset
}

var map_Endpoints = map[string]string{
	"":         "Endpoints is a collection of endpoints that implement the actual service. Example:\n  Name: \"mysvc\",\n  Subsets: [\n    {\n      Addresses: [{\"ip\": \"*********\"}, {\"ip\": \"*********\"}],\n      Ports: [{\"name\": \"a\", \"port\": 8675}, {\"name\": \"b\", \"port\": 309}]\n    },\n    {\n      Addresses: [{\"ip\": \"*********\"}],\n      Ports: [{\"name\": \"a\", \"port\": 93}, {\"name\": \"b\", \"port\": 76}]\n    },\n ]",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"subsets":  "The set of all endpoints is the union of all subsets. Addresses are placed into subsets according to the IPs they share. A single address with multiple ports, some of which are ready and some of which are not (because they come from different containers) will result in the address being displayed in different subsets for the different ports. No address will appear in both Addresses and NotReadyAddresses in the same subset. Sets of addresses and ports that comprise a service.",
}

func (Endpoints) SwaggerDoc() map[string]string {
	return map_Endpoints
}

var map_EndpointsList = map[string]string{
	"":         "EndpointsList is a list of endpoints.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of endpoints.",
}

func (EndpointsList) SwaggerDoc() map[string]string {
	return map_EndpointsList
}

var map_EnvFromSource = map[string]string{
	"":             "EnvFromSource represents the source of a set of ConfigMaps",
	"prefix":       "An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.",
	"configMapRef": "The ConfigMap to select from",
	"secretRef":    "The Secret to select from",
}

func (EnvFromSource) SwaggerDoc() map[string]string {
	return map_EnvFromSource
}

var map_EnvVar = map[string]string{
	"":          "EnvVar represents an environment variable present in a Container.",
	"name":      "Name of the environment variable. Must be a C_IDENTIFIER.",
	"value":     "Variable references $(VAR_NAME) are expanded using the previously defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \"$$(VAR_NAME)\" will produce the string literal \"$(VAR_NAME)\". Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \"\".",
	"valueFrom": "Source for the environment variable's value. Cannot be used if value is not empty.",
}

func (EnvVar) SwaggerDoc() map[string]string {
	return map_EnvVar
}

var map_EnvVarSource = map[string]string{
	"":                 "EnvVarSource represents a source for the value of an EnvVar.",
	"fieldRef":         "Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.",
	"resourceFieldRef": "Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.",
	"configMapKeyRef":  "Selects a key of a ConfigMap.",
	"secretKeyRef":     "Selects a key of a secret in the pod's namespace",
}

func (EnvVarSource) SwaggerDoc() map[string]string {
	return map_EnvVarSource
}

var map_EphemeralContainer = map[string]string{
	"":                    "An EphemeralContainer is a temporary container that you may add to an existing Pod for user-initiated activities such as debugging. Ephemeral containers have no resource or scheduling guarantees, and they will not be restarted when they exit or when a Pod is removed or restarted. The kubelet may evict a Pod if an ephemeral container causes the Pod to exceed its resource allocation.\n\nTo add an ephemeral container, use the ephemeralcontainers subresource of an existing Pod. Ephemeral containers may not be removed or restarted.\n\nThis is a beta feature available on clusters that haven't disabled the EphemeralContainers feature gate.",
	"targetContainerName": "If set, the name of the container from PodSpec that this ephemeral container targets. The ephemeral container will be run in the namespaces (IPC, PID, etc) of this container. If not set then the ephemeral container uses the namespaces configured in the Pod spec.\n\nThe container runtime must implement support for this feature. If the runtime does not support namespace targeting then the result of setting this field is undefined.",
}

func (EphemeralContainer) SwaggerDoc() map[string]string {
	return map_EphemeralContainer
}

var map_EphemeralContainerCommon = map[string]string{
	"":                         "EphemeralContainerCommon is a copy of all fields in Container to be inlined in EphemeralContainer. This separate type allows easy conversion from EphemeralContainer to Container and allows separate documentation for the fields of EphemeralContainer. When a new field is added to Container it must be added here as well.",
	"name":                     "Name of the ephemeral container specified as a DNS_LABEL. This name must be unique among all containers, init containers and ephemeral containers.",
	"image":                    "Docker image name. More info: https://kubernetes.io/docs/concepts/containers/images",
	"command":                  "Entrypoint array. Not executed within a shell. The docker image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \"$$(VAR_NAME)\" will produce the string literal \"$(VAR_NAME)\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell",
	"args":                     "Arguments to the entrypoint. The docker image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \"$$(VAR_NAME)\" will produce the string literal \"$(VAR_NAME)\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell",
	"workingDir":               "Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.",
	"ports":                    "Ports are not allowed for ephemeral containers.",
	"envFrom":                  "List of sources to populate environment variables in the container. The keys defined within a source must be a C_IDENTIFIER. All invalid keys will be reported as an event when the container is starting. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.",
	"env":                      "List of environment variables to set in the container. Cannot be updated.",
	"resources":                "Resources are not allowed for ephemeral containers. Ephemeral containers use spare resources already allocated to the pod.",
	"volumeMounts":             "Pod volumes to mount into the container's filesystem. Subpath mounts are not allowed for ephemeral containers. Cannot be updated.",
	"volumeDevices":            "volumeDevices is the list of block devices to be used by the container.",
	"livenessProbe":            "Probes are not allowed for ephemeral containers.",
	"readinessProbe":           "Probes are not allowed for ephemeral containers.",
	"startupProbe":             "Probes are not allowed for ephemeral containers.",
	"lifecycle":                "Lifecycle is not allowed for ephemeral containers.",
	"terminationMessagePath":   "Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.",
	"terminationMessagePolicy": "Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.",
	"imagePullPolicy":          "Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images",
	"securityContext":          "Optional: SecurityContext defines the security options the ephemeral container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.",
	"stdin":                    "Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.",
	"stdinOnce":                "Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false",
	"tty":                      "Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.",
}

func (EphemeralContainerCommon) SwaggerDoc() map[string]string {
	return map_EphemeralContainerCommon
}

var map_EphemeralVolumeSource = map[string]string{
	"":                    "Represents an ephemeral volume that is handled by a normal storage driver.",
	"volumeClaimTemplate": "Will be used to create a stand-alone PVC to provision the volume. The pod in which this EphemeralVolumeSource is embedded will be the owner of the PVC, i.e. the PVC will be deleted together with the pod.  The name of the PVC will be `<pod name>-<volume name>` where `<volume name>` is the name from the `PodSpec.Volumes` array entry. Pod validation will reject the pod if the concatenated name is not valid for a PVC (for example, too long).\n\nAn existing PVC with that name that is not owned by the pod will *not* be used for the pod to avoid using an unrelated volume by mistake. Starting the pod is then blocked until the unrelated PVC is removed. If such a pre-created PVC is meant to be used by the pod, the PVC has to updated with an owner reference to the pod once the pod exists. Normally this should not be necessary, but it may be useful when manually reconstructing a broken cluster.\n\nThis field is read-only and no changes will be made by Kubernetes to the PVC after it has been created.\n\nRequired, must not be nil.",
}

func (EphemeralVolumeSource) SwaggerDoc() map[string]string {
	return map_EphemeralVolumeSource
}

var map_Event = map[string]string{
	"":                   "Event is a report of an event somewhere in the cluster.  Events have a limited retention time and triggers and messages may evolve with time.  Event consumers should not rely on the timing of an event with a given Reason reflecting a consistent underlying trigger, or the continued existence of events with that Reason.  Events should be treated as informative, best-effort, supplemental data.",
	"metadata":           "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"involvedObject":     "The object that this event is about.",
	"reason":             "This should be a short, machine understandable string that gives the reason for the transition into the object's current status.",
	"message":            "A human-readable description of the status of this operation.",
	"source":             "The component reporting this event. Should be a short machine understandable string.",
	"firstTimestamp":     "The time at which the event was first recorded. (Time of server receipt is in TypeMeta.)",
	"lastTimestamp":      "The time at which the most recent occurrence of this event was recorded.",
	"count":              "The number of times this event has occurred.",
	"type":               "Type of this event (Normal, Warning), new types could be added in the future",
	"eventTime":          "Time when this Event was first observed.",
	"series":             "Data about the Event series this event represents or nil if it's a singleton Event.",
	"action":             "What action was taken/failed regarding to the Regarding object.",
	"related":            "Optional secondary object for more complex actions.",
	"reportingComponent": "Name of the controller that emitted this Event, e.g. `kubernetes.io/kubelet`.",
	"reportingInstance":  "ID of the controller instance, e.g. `kubelet-xyzf`.",
}

func (Event) SwaggerDoc() map[string]string {
	return map_Event
}

var map_EventList = map[string]string{
	"":         "EventList is a list of events.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of events",
}

func (EventList) SwaggerDoc() map[string]string {
	return map_EventList
}

var map_EventSeries = map[string]string{
	"":                 "EventSeries contain information on series of events, i.e. thing that was/is happening continuously for some time.",
	"count":            "Number of occurrences in this series up to the last heartbeat time",
	"lastObservedTime": "Time of the last occurrence observed",
}

func (EventSeries) SwaggerDoc() map[string]string {
	return map_EventSeries
}

var map_EventSource = map[string]string{
	"":          "EventSource contains information for an event.",
	"component": "Component from which the event is generated.",
	"host":      "Node name on which the event is generated.",
}

func (EventSource) SwaggerDoc() map[string]string {
	return map_EventSource
}

var map_ExecAction = map[string]string{
	"":        "ExecAction describes a \"run in container\" action.",
	"command": "Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.",
}

func (ExecAction) SwaggerDoc() map[string]string {
	return map_ExecAction
}

var map_FCVolumeSource = map[string]string{
	"":           "Represents a Fibre Channel volume. Fibre Channel volumes can only be mounted as read/write once. Fibre Channel volumes support ownership management and SELinux relabeling.",
	"targetWWNs": "Optional: FC target worldwide names (WWNs)",
	"lun":        "Optional: FC target lun number",
	"fsType":     "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.",
	"readOnly":   "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"wwids":      "Optional: FC volume world wide identifiers (wwids) Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.",
}

func (FCVolumeSource) SwaggerDoc() map[string]string {
	return map_FCVolumeSource
}

var map_FlexPersistentVolumeSource = map[string]string{
	"":          "FlexPersistentVolumeSource represents a generic persistent volume resource that is provisioned/attached using an exec based plugin.",
	"driver":    "Driver is the name of the driver to use for this volume.",
	"fsType":    "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". The default filesystem depends on FlexVolume script.",
	"secretRef": "Optional: SecretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.",
	"readOnly":  "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"options":   "Optional: Extra command options if any.",
}

func (FlexPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_FlexPersistentVolumeSource
}

var map_FlexVolumeSource = map[string]string{
	"":          "FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.",
	"driver":    "Driver is the name of the driver to use for this volume.",
	"fsType":    "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". The default filesystem depends on FlexVolume script.",
	"secretRef": "Optional: SecretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.",
	"readOnly":  "Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"options":   "Optional: Extra command options if any.",
}

func (FlexVolumeSource) SwaggerDoc() map[string]string {
	return map_FlexVolumeSource
}

var map_FlockerVolumeSource = map[string]string{
	"":            "Represents a Flocker volume mounted by the Flocker agent. One and only one of datasetName and datasetUUID should be set. Flocker volumes do not support ownership management or SELinux relabeling.",
	"datasetName": "Name of the dataset stored as metadata -> name on the dataset for Flocker should be considered as deprecated",
	"datasetUUID": "UUID of the dataset. This is unique identifier of a Flocker dataset",
}

func (FlockerVolumeSource) SwaggerDoc() map[string]string {
	return map_FlockerVolumeSource
}

var map_GCEPersistentDiskVolumeSource = map[string]string{
	"":          "Represents a Persistent Disk resource in Google Compute Engine.\n\nA GCE PD must exist before mounting to a container. The disk must also be in the same GCE project and zone as the kubelet. A GCE PD can only be mounted as read/write once or read-only many times. GCE PDs support ownership management and SELinux relabeling.",
	"pdName":    "Unique name of the PD resource in GCE. Used to identify the disk in GCE. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk",
	"fsType":    "Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk",
	"partition": "The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \"1\". Similarly, the volume partition for /dev/sda is \"0\" (or you can leave the property empty). More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk",
	"readOnly":  "ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk",
}

func (GCEPersistentDiskVolumeSource) SwaggerDoc() map[string]string {
	return map_GCEPersistentDiskVolumeSource
}

var map_GRPCAction = map[string]string{
	"port":    "Port number of the gRPC service. Number must be in the range 1 to 65535.",
	"service": "Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\n\nIf this is not specified, the default behavior is defined by gRPC.",
}

func (GRPCAction) SwaggerDoc() map[string]string {
	return map_GRPCAction
}

var map_GitRepoVolumeSource = map[string]string{
	"":           "Represents a volume that is populated with the contents of a git repository. Git repo volumes do not support ownership management. Git repo volumes support SELinux relabeling.\n\nDEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.",
	"repository": "Repository URL",
	"revision":   "Commit hash for the specified revision.",
	"directory":  "Target directory name. Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the git repository.  Otherwise, if specified, the volume will contain the git repository in the subdirectory with the given name.",
}

func (GitRepoVolumeSource) SwaggerDoc() map[string]string {
	return map_GitRepoVolumeSource
}

var map_GlusterfsPersistentVolumeSource = map[string]string{
	"":                   "Represents a Glusterfs mount that lasts the lifetime of a pod. Glusterfs volumes do not support ownership management or SELinux relabeling.",
	"endpoints":          "EndpointsName is the endpoint name that details Glusterfs topology. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
	"path":               "Path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
	"readOnly":           "ReadOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
	"endpointsNamespace": "EndpointsNamespace is the namespace that contains Glusterfs endpoint. If this field is empty, the EndpointNamespace defaults to the same namespace as the bound PVC. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
}

func (GlusterfsPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_GlusterfsPersistentVolumeSource
}

var map_GlusterfsVolumeSource = map[string]string{
	"":          "Represents a Glusterfs mount that lasts the lifetime of a pod. Glusterfs volumes do not support ownership management or SELinux relabeling.",
	"endpoints": "EndpointsName is the endpoint name that details Glusterfs topology. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
	"path":      "Path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
	"readOnly":  "ReadOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod",
}

func (GlusterfsVolumeSource) SwaggerDoc() map[string]string {
	return map_GlusterfsVolumeSource
}

var map_HTTPGetAction = map[string]string{
	"":            "HTTPGetAction describes an action based on HTTP Get requests.",
	"path":        "Path to access on the HTTP server.",
	"port":        "Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.",
	"host":        "Host name to connect to, defaults to the pod IP. You probably want to set \"Host\" in httpHeaders instead.",
	"scheme":      "Scheme to use for connecting to the host. Defaults to HTTP.",
	"httpHeaders": "Custom headers to set in the request. HTTP allows repeated headers.",
}

func (HTTPGetAction) SwaggerDoc() map[string]string {
	return map_HTTPGetAction
}

var map_HTTPHeader = map[string]string{
	"":      "HTTPHeader describes a custom header to be used in HTTP probes",
	"name":  "The header field name",
	"value": "The header field value",
}

func (HTTPHeader) SwaggerDoc() map[string]string {
	return map_HTTPHeader
}

var map_HostAlias = map[string]string{
	"":          "HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the pod's hosts file.",
	"ip":        "IP address of the host file entry.",
	"hostnames": "Hostnames for the above IP address.",
}

func (HostAlias) SwaggerDoc() map[string]string {
	return map_HostAlias
}

var map_HostPathVolumeSource = map[string]string{
	"":     "Represents a host path mapped into a pod. Host path volumes do not support ownership management or SELinux relabeling.",
	"path": "Path of the directory on the host. If the path is a symlink, it will follow the link to the real path. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath",
	"type": "Type for HostPath Volume Defaults to \"\" More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath",
}

func (HostPathVolumeSource) SwaggerDoc() map[string]string {
	return map_HostPathVolumeSource
}

var map_ISCSIPersistentVolumeSource = map[string]string{
	"":                  "ISCSIPersistentVolumeSource represents an ISCSI disk. ISCSI volumes can only be mounted as read/write once. ISCSI volumes support ownership management and SELinux relabeling.",
	"targetPortal":      "iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).",
	"iqn":               "Target iSCSI Qualified Name.",
	"lun":               "iSCSI Target Lun number.",
	"iscsiInterface":    "iSCSI Interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).",
	"fsType":            "Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi",
	"readOnly":          "ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.",
	"portals":           "iSCSI Target Portal List. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).",
	"chapAuthDiscovery": "whether support iSCSI Discovery CHAP authentication",
	"chapAuthSession":   "whether support iSCSI Session CHAP authentication",
	"secretRef":         "CHAP Secret for iSCSI target and initiator authentication",
	"initiatorName":     "Custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface <target portal>:<volume name> will be created for the connection.",
}

func (ISCSIPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_ISCSIPersistentVolumeSource
}

var map_ISCSIVolumeSource = map[string]string{
	"":                  "Represents an ISCSI disk. ISCSI volumes can only be mounted as read/write once. ISCSI volumes support ownership management and SELinux relabeling.",
	"targetPortal":      "iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).",
	"iqn":               "Target iSCSI Qualified Name.",
	"lun":               "iSCSI Target Lun number.",
	"iscsiInterface":    "iSCSI Interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).",
	"fsType":            "Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi",
	"readOnly":          "ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.",
	"portals":           "iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).",
	"chapAuthDiscovery": "whether support iSCSI Discovery CHAP authentication",
	"chapAuthSession":   "whether support iSCSI Session CHAP authentication",
	"secretRef":         "CHAP Secret for iSCSI target and initiator authentication",
	"initiatorName":     "Custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface <target portal>:<volume name> will be created for the connection.",
}

func (ISCSIVolumeSource) SwaggerDoc() map[string]string {
	return map_ISCSIVolumeSource
}

var map_KeyToPath = map[string]string{
	"":     "Maps a string key to a path within a volume.",
	"key":  "The key to project.",
	"path": "The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.",
	"mode": "Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.",
}

func (KeyToPath) SwaggerDoc() map[string]string {
	return map_KeyToPath
}

var map_Lifecycle = map[string]string{
	"":          "Lifecycle describes actions that the management system should take in response to container lifecycle events. For the PostStart and PreStop lifecycle handlers, management of the container blocks until the action is complete, unless the container process fails, in which case the handler is aborted.",
	"postStart": "PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks",
	"preStop":   "PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks",
}

func (Lifecycle) SwaggerDoc() map[string]string {
	return map_Lifecycle
}

var map_LifecycleHandler = map[string]string{
	"":          "LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.",
	"exec":      "Exec specifies the action to take.",
	"httpGet":   "HTTPGet specifies the http request to perform.",
	"tcpSocket": "Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for the backward compatibility. There are no validation of this field and lifecycle hooks will fail in runtime when tcp handler is specified.",
}

func (LifecycleHandler) SwaggerDoc() map[string]string {
	return map_LifecycleHandler
}

var map_LimitRange = map[string]string{
	"":         "LimitRange sets resource usage limits for each kind of resource in a Namespace.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the limits enforced. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (LimitRange) SwaggerDoc() map[string]string {
	return map_LimitRange
}

var map_LimitRangeItem = map[string]string{
	"":                     "LimitRangeItem defines a min/max usage limit for any resource that matches on kind.",
	"type":                 "Type of resource that this limit applies to.",
	"max":                  "Max usage constraints on this kind by resource name.",
	"min":                  "Min usage constraints on this kind by resource name.",
	"default":              "Default resource requirement limit value by resource name if resource limit is omitted.",
	"defaultRequest":       "DefaultRequest is the default resource requirement request value by resource name if resource request is omitted.",
	"maxLimitRequestRatio": "MaxLimitRequestRatio if specified, the named resource must have a request and limit that are both non-zero where limit divided by request is less than or equal to the enumerated value; this represents the max burst for the named resource.",
}

func (LimitRangeItem) SwaggerDoc() map[string]string {
	return map_LimitRangeItem
}

var map_LimitRangeList = map[string]string{
	"":         "LimitRangeList is a list of LimitRange items.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "Items is a list of LimitRange objects. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/",
}

func (LimitRangeList) SwaggerDoc() map[string]string {
	return map_LimitRangeList
}

var map_LimitRangeSpec = map[string]string{
	"":       "LimitRangeSpec defines a min/max usage limit for resources that match on kind.",
	"limits": "Limits is the list of LimitRangeItem objects that are enforced.",
}

func (LimitRangeSpec) SwaggerDoc() map[string]string {
	return map_LimitRangeSpec
}

var map_LoadBalancerIngress = map[string]string{
	"":         "LoadBalancerIngress represents the status of a load-balancer ingress point: traffic intended for the service should be sent to an ingress point.",
	"ip":       "IP is set for load-balancer ingress points that are IP based (typically GCE or OpenStack load-balancers)",
	"hostname": "Hostname is set for load-balancer ingress points that are DNS based (typically AWS load-balancers)",
	"ports":    "Ports is a list of records of service ports If used, every port defined in the service should have an entry in it",
}

func (LoadBalancerIngress) SwaggerDoc() map[string]string {
	return map_LoadBalancerIngress
}

var map_LoadBalancerStatus = map[string]string{
	"":        "LoadBalancerStatus represents the status of a load-balancer.",
	"ingress": "Ingress is a list containing ingress points for the load-balancer. Traffic intended for the service should be sent to these ingress points.",
}

func (LoadBalancerStatus) SwaggerDoc() map[string]string {
	return map_LoadBalancerStatus
}

var map_LocalObjectReference = map[string]string{
	"":     "LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.",
	"name": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names",
}

func (LocalObjectReference) SwaggerDoc() map[string]string {
	return map_LocalObjectReference
}

var map_LocalVolumeSource = map[string]string{
	"":       "Local represents directly-attached storage with node affinity (Beta feature)",
	"path":   "The full path to the volume on the node. It can be either a directory or block device (disk, partition, ...).",
	"fsType": "Filesystem type to mount. It applies only when the Path is a block device. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". The default value is to auto-select a filesystem if unspecified.",
}

func (LocalVolumeSource) SwaggerDoc() map[string]string {
	return map_LocalVolumeSource
}

var map_NFSVolumeSource = map[string]string{
	"":         "Represents an NFS mount that lasts the lifetime of a pod. NFS volumes do not support ownership management or SELinux relabeling.",
	"server":   "Server is the hostname or IP address of the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs",
	"path":     "Path that is exported by the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs",
	"readOnly": "ReadOnly here will force the NFS export to be mounted with read-only permissions. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs",
}

func (NFSVolumeSource) SwaggerDoc() map[string]string {
	return map_NFSVolumeSource
}

var map_Namespace = map[string]string{
	"":         "Namespace provides a scope for Names. Use of multiple namespaces is optional.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the behavior of the Namespace. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
	"status":   "Status describes the current status of a Namespace. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (Namespace) SwaggerDoc() map[string]string {
	return map_Namespace
}

var map_NamespaceCondition = map[string]string{
	"":       "NamespaceCondition contains details about state of namespace.",
	"type":   "Type of namespace controller condition.",
	"status": "Status of the condition, one of True, False, Unknown.",
}

func (NamespaceCondition) SwaggerDoc() map[string]string {
	return map_NamespaceCondition
}

var map_NamespaceList = map[string]string{
	"":         "NamespaceList is a list of Namespaces.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "Items is the list of Namespace objects in the list. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/",
}

func (NamespaceList) SwaggerDoc() map[string]string {
	return map_NamespaceList
}

var map_NamespaceSpec = map[string]string{
	"":           "NamespaceSpec describes the attributes on a Namespace.",
	"finalizers": "Finalizers is an opaque list of values that must be empty to permanently remove object from storage. More info: https://kubernetes.io/docs/tasks/administer-cluster/namespaces/",
}

func (NamespaceSpec) SwaggerDoc() map[string]string {
	return map_NamespaceSpec
}

var map_NamespaceStatus = map[string]string{
	"":           "NamespaceStatus is information about the current status of a Namespace.",
	"phase":      "Phase is the current lifecycle phase of the namespace. More info: https://kubernetes.io/docs/tasks/administer-cluster/namespaces/",
	"conditions": "Represents the latest available observations of a namespace's current state.",
}

func (NamespaceStatus) SwaggerDoc() map[string]string {
	return map_NamespaceStatus
}

var map_Node = map[string]string{
	"":         "Node is a worker node in Kubernetes. Each node will have a unique identifier in the cache (i.e. in etcd).",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the behavior of a node. https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
	"status":   "Most recently observed status of the node. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (Node) SwaggerDoc() map[string]string {
	return map_Node
}

var map_NodeAddress = map[string]string{
	"":        "NodeAddress contains information for the node's address.",
	"type":    "Node address type, one of Hostname, ExternalIP or InternalIP.",
	"address": "The node address.",
}

func (NodeAddress) SwaggerDoc() map[string]string {
	return map_NodeAddress
}

var map_NodeAffinity = map[string]string{
	"": "Node affinity is a group of node affinity scheduling rules.",
	"requiredDuringSchedulingIgnoredDuringExecution":  "If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to an update), the system may or may not try to eventually evict the pod from its node.",
	"preferredDuringSchedulingIgnoredDuringExecution": "The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node matches the corresponding matchExpressions; the node(s) with the highest sum are the most preferred.",
}

func (NodeAffinity) SwaggerDoc() map[string]string {
	return map_NodeAffinity
}

var map_NodeCondition = map[string]string{
	"":                   "NodeCondition contains condition information for a node.",
	"type":               "Type of node condition.",
	"status":             "Status of the condition, one of True, False, Unknown.",
	"lastHeartbeatTime":  "Last time we got an update on a given condition.",
	"lastTransitionTime": "Last time the condition transit from one status to another.",
	"reason":             "(brief) reason for the condition's last transition.",
	"message":            "Human readable message indicating details about last transition.",
}

func (NodeCondition) SwaggerDoc() map[string]string {
	return map_NodeCondition
}

var map_NodeConfigSource = map[string]string{
	"":          "NodeConfigSource specifies a source of node configuration. Exactly one subfield (excluding metadata) must be non-nil. This API is deprecated since 1.22",
	"configMap": "ConfigMap is a reference to a Node's ConfigMap",
}

func (NodeConfigSource) SwaggerDoc() map[string]string {
	return map_NodeConfigSource
}

var map_NodeConfigStatus = map[string]string{
	"":              "NodeConfigStatus describes the status of the config assigned by Node.Spec.ConfigSource.",
	"assigned":      "Assigned reports the checkpointed config the node will try to use. When Node.Spec.ConfigSource is updated, the node checkpoints the associated config payload to local disk, along with a record indicating intended config. The node refers to this record to choose its config checkpoint, and reports this record in Assigned. Assigned only updates in the status after the record has been checkpointed to disk. When the Kubelet is restarted, it tries to make the Assigned config the Active config by loading and validating the checkpointed payload identified by Assigned.",
	"active":        "Active reports the checkpointed config the node is actively using. Active will represent either the current version of the Assigned config, or the current LastKnownGood config, depending on whether attempting to use the Assigned config results in an error.",
	"lastKnownGood": "LastKnownGood reports the checkpointed config the node will fall back to when it encounters an error attempting to use the Assigned config. The Assigned config becomes the LastKnownGood config when the node determines that the Assigned config is stable and correct. This is currently implemented as a 10-minute soak period starting when the local record of Assigned config is updated. If the Assigned config is Active at the end of this period, it becomes the LastKnownGood. Note that if Spec.ConfigSource is reset to nil (use local defaults), the LastKnownGood is also immediately reset to nil, because the local default config is always assumed good. You should not make assumptions about the node's method of determining config stability and correctness, as this may change or become configurable in the future.",
	"error":         "Error describes any problems reconciling the Spec.ConfigSource to the Active config. Errors may occur, for example, attempting to checkpoint Spec.ConfigSource to the local Assigned record, attempting to checkpoint the payload associated with Spec.ConfigSource, attempting to load or validate the Assigned config, etc. Errors may occur at different points while syncing config. Earlier errors (e.g. download or checkpointing errors) will not result in a rollback to LastKnownGood, and may resolve across Kubelet retries. Later errors (e.g. loading or validating a checkpointed config) will result in a rollback to LastKnownGood. In the latter case, it is usually possible to resolve the error by fixing the config assigned in Spec.ConfigSource. You can find additional information for debugging by searching the error message in the Kubelet log. Error is a human-readable description of the error state; machines can check whether or not Error is empty, but should not rely on the stability of the Error text across Kubelet versions.",
}

func (NodeConfigStatus) SwaggerDoc() map[string]string {
	return map_NodeConfigStatus
}

var map_NodeDaemonEndpoints = map[string]string{
	"":                "NodeDaemonEndpoints lists ports opened by daemons running on the Node.",
	"kubeletEndpoint": "Endpoint on which Kubelet is listening.",
}

func (NodeDaemonEndpoints) SwaggerDoc() map[string]string {
	return map_NodeDaemonEndpoints
}

var map_NodeList = map[string]string{
	"":         "NodeList is the whole list of all Nodes which have been registered with master.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of nodes",
}

func (NodeList) SwaggerDoc() map[string]string {
	return map_NodeList
}

var map_NodeProxyOptions = map[string]string{
	"":     "NodeProxyOptions is the query options to a Node's proxy call.",
	"path": "Path is the URL path to use for the current proxy request to node.",
}

func (NodeProxyOptions) SwaggerDoc() map[string]string {
	return map_NodeProxyOptions
}

var map_NodeResources = map[string]string{
	"":         "NodeResources is an object for conveying resource information about a node. see https://kubernetes.io/docs/concepts/architecture/nodes/#capacity for more details.",
	"Capacity": "Capacity represents the available resources of a node",
}

func (NodeResources) SwaggerDoc() map[string]string {
	return map_NodeResources
}

var map_NodeSelector = map[string]string{
	"":                  "A node selector represents the union of the results of one or more label queries over a set of nodes; that is, it represents the OR of the selectors represented by the node selector terms.",
	"nodeSelectorTerms": "Required. A list of node selector terms. The terms are ORed.",
}

func (NodeSelector) SwaggerDoc() map[string]string {
	return map_NodeSelector
}

var map_NodeSelectorRequirement = map[string]string{
	"":         "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.",
	"key":      "The label key that the selector applies to.",
	"operator": "Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.",
	"values":   "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.",
}

func (NodeSelectorRequirement) SwaggerDoc() map[string]string {
	return map_NodeSelectorRequirement
}

var map_NodeSelectorTerm = map[string]string{
	"":                 "A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.",
	"matchExpressions": "A list of node selector requirements by node's labels.",
	"matchFields":      "A list of node selector requirements by node's fields.",
}

func (NodeSelectorTerm) SwaggerDoc() map[string]string {
	return map_NodeSelectorTerm
}

var map_NodeSpec = map[string]string{
	"":              "NodeSpec describes the attributes that a node is created with.",
	"podCIDR":       "PodCIDR represents the pod IP range assigned to the node.",
	"podCIDRs":      "podCIDRs represents the IP ranges assigned to the node for usage by Pods on that node. If this field is specified, the 0th entry must match the podCIDR field. It may contain at most 1 value for each of IPv4 and IPv6.",
	"providerID":    "ID of the node assigned by the cloud provider in the format: <ProviderName>://<ProviderSpecificNodeID>",
	"unschedulable": "Unschedulable controls node schedulability of new pods. By default, node is schedulable. More info: https://kubernetes.io/docs/concepts/nodes/node/#manual-node-administration",
	"taints":        "If specified, the node's taints.",
	"configSource":  "Deprecated. If specified, the source of the node's configuration. The DynamicKubeletConfig feature gate must be enabled for the Kubelet to use this field. This field is deprecated as of 1.22: https://git.k8s.io/enhancements/keps/sig-node/281-dynamic-kubelet-configuration",
	"externalID":    "Deprecated. Not all kubelets will set this field. Remove field after 1.13. see: https://issues.k8s.io/61966",
}

func (NodeSpec) SwaggerDoc() map[string]string {
	return map_NodeSpec
}

var map_NodeStatus = map[string]string{
	"":                "NodeStatus is information about the current status of a node.",
	"capacity":        "Capacity represents the total resources of a node. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#capacity",
	"allocatable":     "Allocatable represents the resources of a node that are available for scheduling. Defaults to Capacity.",
	"phase":           "NodePhase is the recently observed lifecycle phase of the node. More info: https://kubernetes.io/docs/concepts/nodes/node/#phase The field is never populated, and now is deprecated.",
	"conditions":      "Conditions is an array of current observed node conditions. More info: https://kubernetes.io/docs/concepts/nodes/node/#condition",
	"addresses":       "List of addresses reachable to the node. Queried from cloud provider, if available. More info: https://kubernetes.io/docs/concepts/nodes/node/#addresses Note: This field is declared as mergeable, but the merge key is not sufficiently unique, which can cause data corruption when it is merged. Callers should instead use a full-replacement patch. See http://pr.k8s.io/79391 for an example.",
	"daemonEndpoints": "Endpoints of daemons running on the Node.",
	"nodeInfo":        "Set of ids/uuids to uniquely identify the node. More info: https://kubernetes.io/docs/concepts/nodes/node/#info",
	"images":          "List of container images on this node",
	"volumesInUse":    "List of attachable volumes in use (mounted) by the node.",
	"volumesAttached": "List of volumes that are attached to the node.",
	"config":          "Status of the config assigned to the node via the dynamic Kubelet config feature.",
}

func (NodeStatus) SwaggerDoc() map[string]string {
	return map_NodeStatus
}

var map_NodeSystemInfo = map[string]string{
	"":                        "NodeSystemInfo is a set of ids/uuids to uniquely identify the node.",
	"machineID":               "MachineID reported by the node. For unique machine identification in the cluster this field is preferred. Learn more from man(5) machine-id: http://man7.org/linux/man-pages/man5/machine-id.5.html",
	"systemUUID":              "SystemUUID reported by the node. For unique machine identification MachineID is preferred. This field is specific to Red Hat hosts https://access.redhat.com/documentation/en-us/red_hat_subscription_management/1/html/rhsm/uuid",
	"bootID":                  "Boot ID reported by the node.",
	"kernelVersion":           "Kernel Version reported by the node from 'uname -r' (e.g. 3.16.0-0.bpo.4-amd64).",
	"osImage":                 "OS Image reported by the node from /etc/os-release (e.g. Debian GNU/Linux 7 (wheezy)).",
	"containerRuntimeVersion": "ContainerRuntime Version reported by the node through runtime remote API (e.g. docker://1.5.0).",
	"kubeletVersion":          "Kubelet Version reported by the node.",
	"kubeProxyVersion":        "KubeProxy Version reported by the node.",
	"operatingSystem":         "The Operating System reported by the node",
	"architecture":            "The Architecture reported by the node",
}

func (NodeSystemInfo) SwaggerDoc() map[string]string {
	return map_NodeSystemInfo
}

var map_ObjectFieldSelector = map[string]string{
	"":           "ObjectFieldSelector selects an APIVersioned field of an object.",
	"apiVersion": "Version of the schema the FieldPath is written in terms of, defaults to \"v1\".",
	"fieldPath":  "Path of the field to select in the specified API version.",
}

func (ObjectFieldSelector) SwaggerDoc() map[string]string {
	return map_ObjectFieldSelector
}

var map_ObjectReference = map[string]string{
	"":                "ObjectReference contains enough information to let you inspect or modify the referred object.",
	"kind":            "Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"namespace":       "Namespace of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/",
	"name":            "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names",
	"uid":             "UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids",
	"apiVersion":      "API version of the referent.",
	"resourceVersion": "Specific resourceVersion to which this reference is made, if any. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency",
	"fieldPath":       "If referring to a piece of an object instead of an entire object, this string should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2]. For example, if the object reference is to a container within a pod, this would take on a value like: \"spec.containers{name}\" (where \"name\" refers to the name of the container that triggered the event) or if no container name is specified \"spec.containers[2]\" (container with index 2 in this pod). This syntax is chosen only to have some well-defined way of referencing a part of an object.",
}

func (ObjectReference) SwaggerDoc() map[string]string {
	return map_ObjectReference
}

var map_PersistentVolume = map[string]string{
	"":         "PersistentVolume (PV) is a storage resource provisioned by an administrator. It is analogous to a node. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines a specification of a persistent volume owned by the cluster. Provisioned by an administrator. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistent-volumes",
	"status":   "Status represents the current information/status for the persistent volume. Populated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistent-volumes",
}

func (PersistentVolume) SwaggerDoc() map[string]string {
	return map_PersistentVolume
}

var map_PersistentVolumeClaim = map[string]string{
	"":         "PersistentVolumeClaim is a user's request for and claim to a persistent volume",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the desired characteristics of a volume requested by a pod author. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims",
	"status":   "Status represents the current information/status of a persistent volume claim. Read-only. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims",
}

func (PersistentVolumeClaim) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaim
}

var map_PersistentVolumeClaimCondition = map[string]string{
	"":                   "PersistentVolumeClaimCondition contails details about state of pvc",
	"lastProbeTime":      "Last time we probed the condition.",
	"lastTransitionTime": "Last time the condition transitioned from one status to another.",
	"reason":             "Unique, this should be a short, machine understandable string that gives the reason for condition's last transition. If it reports \"ResizeStarted\" that means the underlying persistent volume is being resized.",
	"message":            "Human-readable message indicating details about last transition.",
}

func (PersistentVolumeClaimCondition) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaimCondition
}

var map_PersistentVolumeClaimList = map[string]string{
	"":         "PersistentVolumeClaimList is a list of PersistentVolumeClaim items.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "A list of persistent volume claims. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims",
}

func (PersistentVolumeClaimList) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaimList
}

var map_PersistentVolumeClaimSpec = map[string]string{
	"":                 "PersistentVolumeClaimSpec describes the common attributes of storage devices and allows a Source for provider-specific attributes",
	"accessModes":      "AccessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1",
	"selector":         "A label query over volumes to consider for binding.",
	"resources":        "Resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources",
	"volumeName":       "VolumeName is the binding reference to the PersistentVolume backing this claim.",
	"storageClassName": "Name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1",
	"volumeMode":       "volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.",
	"dataSource":       "This field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. If the AnyVolumeDataSource feature gate is enabled, this field will always have the same contents as the DataSourceRef field.",
	"dataSourceRef":    "Specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any local object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the DataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, both fields (DataSource and DataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. There are two important differences between DataSource and DataSourceRef: * While DataSource only allows two specific types of objects, DataSourceRef\n  allows any non-core object, as well as PersistentVolumeClaim objects.\n* While DataSource ignores disallowed values (dropping them), DataSourceRef\n  preserves all values, and generates an error if a disallowed value is\n  specified.\n(Alpha) Using this field requires the AnyVolumeDataSource feature gate to be enabled.",
}

func (PersistentVolumeClaimSpec) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaimSpec
}

var map_PersistentVolumeClaimStatus = map[string]string{
	"":                   "PersistentVolumeClaimStatus is the current status of a persistent volume claim.",
	"phase":              "Phase represents the current phase of PersistentVolumeClaim.",
	"accessModes":        "AccessModes contains the actual access modes the volume backing the PVC has. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1",
	"capacity":           "Represents the actual resources of the underlying volume.",
	"conditions":         "Current Condition of persistent volume claim. If underlying persistent volume is being resized then the Condition will be set to 'ResizeStarted'.",
	"allocatedResources": "The storage resource within AllocatedResources tracks the capacity allocated to a PVC. It may be larger than the actual capacity when a volume expansion operation is requested. For storage quota, the larger value from allocatedResources and PVC.spec.resources is used. If allocatedResources is not set, PVC.spec.resources alone is used for quota calculation. If a volume expansion capacity request is lowered, allocatedResources is only lowered if there are no expansion operations in progress and if the actual volume capacity is equal or lower than the requested capacity. This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.",
	"resizeStatus":       "ResizeStatus stores status of resize operation. ResizeStatus is not set by default but when expansion is complete resizeStatus is set to empty string by resize controller or kubelet. This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.",
}

func (PersistentVolumeClaimStatus) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaimStatus
}

var map_PersistentVolumeClaimTemplate = map[string]string{
	"":         "PersistentVolumeClaimTemplate is used to produce PersistentVolumeClaim objects as part of an EphemeralVolumeSource.",
	"metadata": "May contain labels and annotations that will be copied into the PVC when creating it. No other fields are allowed and will be rejected during validation.",
	"spec":     "The specification for the PersistentVolumeClaim. The entire content is copied unchanged into the PVC that gets created from this template. The same fields as in a PersistentVolumeClaim are also valid here.",
}

func (PersistentVolumeClaimTemplate) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaimTemplate
}

var map_PersistentVolumeClaimVolumeSource = map[string]string{
	"":          "PersistentVolumeClaimVolumeSource references the user's PVC in the same namespace. This volume finds the bound PV and mounts that volume for the pod. A PersistentVolumeClaimVolumeSource is, essentially, a wrapper around another type of volume that is owned by someone else (the system).",
	"claimName": "ClaimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims",
	"readOnly":  "Will force the ReadOnly setting in VolumeMounts. Default false.",
}

func (PersistentVolumeClaimVolumeSource) SwaggerDoc() map[string]string {
	return map_PersistentVolumeClaimVolumeSource
}

var map_PersistentVolumeList = map[string]string{
	"":         "PersistentVolumeList is a list of PersistentVolume items.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of persistent volumes. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes",
}

func (PersistentVolumeList) SwaggerDoc() map[string]string {
	return map_PersistentVolumeList
}

var map_PersistentVolumeSource = map[string]string{
	"":                     "PersistentVolumeSource is similar to VolumeSource but meant for the administrator who creates PVs. Exactly one of its members must be set.",
	"gcePersistentDisk":    "GCEPersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Provisioned by an admin. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk",
	"awsElasticBlockStore": "AWSElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore",
	"hostPath":             "HostPath represents a directory on the host. Provisioned by a developer or tester. This is useful for single-node development and testing only! On-host storage is not supported in any way and WILL NOT WORK in a multi-node cluster. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath",
	"glusterfs":            "Glusterfs represents a Glusterfs volume that is attached to a host and exposed to the pod. Provisioned by an admin. More info: https://examples.k8s.io/volumes/glusterfs/README.md",
	"nfs":                  "NFS represents an NFS mount on the host. Provisioned by an admin. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs",
	"rbd":                  "RBD represents a Rados Block Device mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/rbd/README.md",
	"iscsi":                "ISCSI represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Provisioned by an admin.",
	"cinder":               "Cinder represents a cinder volume attached and mounted on kubelets host machine. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"cephfs":               "CephFS represents a Ceph FS mount on the host that shares a pod's lifetime",
	"fc":                   "FC represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.",
	"flocker":              "Flocker represents a Flocker volume attached to a kubelet's host machine and exposed to the pod for its usage. This depends on the Flocker control service being running",
	"flexVolume":           "FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.",
	"azureFile":            "AzureFile represents an Azure File Service mount on the host and bind mount to the pod.",
	"vsphereVolume":        "VsphereVolume represents a vSphere volume attached and mounted on kubelets host machine",
	"quobyte":              "Quobyte represents a Quobyte mount on the host that shares a pod's lifetime",
	"azureDisk":            "AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.",
	"photonPersistentDisk": "PhotonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine",
	"portworxVolume":       "PortworxVolume represents a portworx volume attached and mounted on kubelets host machine",
	"scaleIO":              "ScaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.",
	"local":                "Local represents directly-attached storage with node affinity",
	"storageos":            "StorageOS represents a StorageOS volume that is attached to the kubelet's host machine and mounted into the pod More info: https://examples.k8s.io/volumes/storageos/README.md",
	"csi":                  "CSI represents storage that is handled by an external CSI driver (Beta feature).",
}

func (PersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_PersistentVolumeSource
}

var map_PersistentVolumeSpec = map[string]string{
	"":                              "PersistentVolumeSpec is the specification of a persistent volume.",
	"capacity":                      "A description of the persistent volume's resources and capacity. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#capacity",
	"accessModes":                   "AccessModes contains all ways the volume can be mounted. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes",
	"claimRef":                      "ClaimRef is part of a bi-directional binding between PersistentVolume and PersistentVolumeClaim. Expected to be non-nil when bound. claim.VolumeName is the authoritative bind between PV and PVC. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#binding",
	"persistentVolumeReclaimPolicy": "What happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
	"storageClassName":              "Name of StorageClass to which this persistent volume belongs. Empty value means that this volume does not belong to any StorageClass.",
	"mountOptions":                  "A list of mount options, e.g. [\"ro\", \"soft\"]. Not validated - mount will simply fail if one is invalid. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#mount-options",
	"volumeMode":                    "volumeMode defines if a volume is intended to be used with a formatted filesystem or to remain in raw block state. Value of Filesystem is implied when not included in spec.",
	"nodeAffinity":                  "NodeAffinity defines constraints that limit what nodes this volume can be accessed from. This field influences the scheduling of pods that use this volume.",
}

func (PersistentVolumeSpec) SwaggerDoc() map[string]string {
	return map_PersistentVolumeSpec
}

var map_PersistentVolumeStatus = map[string]string{
	"":        "PersistentVolumeStatus is the current status of a persistent volume.",
	"phase":   "Phase indicates if a volume is available, bound to a claim, or released by a claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#phase",
	"message": "A human-readable message indicating details about why the volume is in this state.",
	"reason":  "Reason is a brief CamelCase string that describes any failure and is meant for machine parsing and tidy display in the CLI.",
}

func (PersistentVolumeStatus) SwaggerDoc() map[string]string {
	return map_PersistentVolumeStatus
}

var map_PhotonPersistentDiskVolumeSource = map[string]string{
	"":       "Represents a Photon Controller persistent disk resource.",
	"pdID":   "ID that identifies Photon Controller persistent disk",
	"fsType": "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.",
}

func (PhotonPersistentDiskVolumeSource) SwaggerDoc() map[string]string {
	return map_PhotonPersistentDiskVolumeSource
}

var map_Pod = map[string]string{
	"":         "Pod is a collection of containers that can run on a host. This resource is created by clients and scheduled onto hosts.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Specification of the desired behavior of the pod. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
	"status":   "Most recently observed status of the pod. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (Pod) SwaggerDoc() map[string]string {
	return map_Pod
}

var map_PodAffinity = map[string]string{
	"": "Pod affinity is a group of inter pod affinity scheduling rules.",
	"requiredDuringSchedulingIgnoredDuringExecution":  "If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.",
	"preferredDuringSchedulingIgnoredDuringExecution": "The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.",
}

func (PodAffinity) SwaggerDoc() map[string]string {
	return map_PodAffinity
}

var map_PodAffinityTerm = map[string]string{
	"":                  "Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key <topologyKey> matches that of any node on which a pod of the set of pods is running",
	"labelSelector":     "A label query over a set of resources, in this case pods.",
	"namespaces":        "namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \"this pod's namespace\"",
	"topologyKey":       "This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.",
	"namespaceSelector": "A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \"this pod's namespace\". An empty selector ({}) matches all namespaces. This field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.",
}

func (PodAffinityTerm) SwaggerDoc() map[string]string {
	return map_PodAffinityTerm
}

var map_PodAntiAffinity = map[string]string{
	"": "Pod anti affinity is a group of inter pod anti affinity scheduling rules.",
	"requiredDuringSchedulingIgnoredDuringExecution":  "If the anti-affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the anti-affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.",
	"preferredDuringSchedulingIgnoredDuringExecution": "The scheduler will prefer to schedule pods to nodes that satisfy the anti-affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling anti-affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.",
}

func (PodAntiAffinity) SwaggerDoc() map[string]string {
	return map_PodAntiAffinity
}

var map_PodAttachOptions = map[string]string{
	"":          "PodAttachOptions is the query options to a Pod's remote attach call.",
	"stdin":     "Stdin if true, redirects the standard input stream of the pod for this call. Defaults to false.",
	"stdout":    "Stdout if true indicates that stdout is to be redirected for the attach call. Defaults to true.",
	"stderr":    "Stderr if true indicates that stderr is to be redirected for the attach call. Defaults to true.",
	"tty":       "TTY if true indicates that a tty will be allocated for the attach call. This is passed through the container runtime so the tty is allocated on the worker node by the container runtime. Defaults to false.",
	"container": "The container in which to execute the command. Defaults to only container if there is only one container in the pod.",
}

func (PodAttachOptions) SwaggerDoc() map[string]string {
	return map_PodAttachOptions
}

var map_PodCondition = map[string]string{
	"":                   "PodCondition contains details for the current condition of this pod.",
	"type":               "Type is the type of the condition. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions",
	"status":             "Status is the status of the condition. Can be True, False, Unknown. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions",
	"lastProbeTime":      "Last time we probed the condition.",
	"lastTransitionTime": "Last time the condition transitioned from one status to another.",
	"reason":             "Unique, one-word, CamelCase reason for the condition's last transition.",
	"message":            "Human-readable message indicating details about last transition.",
}

func (PodCondition) SwaggerDoc() map[string]string {
	return map_PodCondition
}

var map_PodDNSConfig = map[string]string{
	"":            "PodDNSConfig defines the DNS parameters of a pod in addition to those generated from DNSPolicy.",
	"nameservers": "A list of DNS name server IP addresses. This will be appended to the base nameservers generated from DNSPolicy. Duplicated nameservers will be removed.",
	"searches":    "A list of DNS search domains for host-name lookup. This will be appended to the base search paths generated from DNSPolicy. Duplicated search paths will be removed.",
	"options":     "A list of DNS resolver options. This will be merged with the base options generated from DNSPolicy. Duplicated entries will be removed. Resolution options given in Options will override those that appear in the base DNSPolicy.",
}

func (PodDNSConfig) SwaggerDoc() map[string]string {
	return map_PodDNSConfig
}

var map_PodDNSConfigOption = map[string]string{
	"":     "PodDNSConfigOption defines DNS resolver options of a pod.",
	"name": "Required.",
}

func (PodDNSConfigOption) SwaggerDoc() map[string]string {
	return map_PodDNSConfigOption
}

var map_PodExecOptions = map[string]string{
	"":          "PodExecOptions is the query options to a Pod's remote exec call.",
	"stdin":     "Redirect the standard input stream of the pod for this call. Defaults to false.",
	"stdout":    "Redirect the standard output stream of the pod for this call.",
	"stderr":    "Redirect the standard error stream of the pod for this call.",
	"tty":       "TTY if true indicates that a tty will be allocated for the exec call. Defaults to false.",
	"container": "Container in which to execute the command. Defaults to only container if there is only one container in the pod.",
	"command":   "Command is the remote command to execute. argv array. Not executed within a shell.",
}

func (PodExecOptions) SwaggerDoc() map[string]string {
	return map_PodExecOptions
}

var map_PodIP = map[string]string{
	"":   "IP address information for entries in the (plural) PodIPs field. Each entry includes:\n   IP: An IP address allocated to the pod. Routable at least within the cluster.",
	"ip": "ip is an IP address (IPv4 or IPv6) assigned to the pod",
}

func (PodIP) SwaggerDoc() map[string]string {
	return map_PodIP
}

var map_PodList = map[string]string{
	"":         "PodList is a list of Pods.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of pods. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md",
}

func (PodList) SwaggerDoc() map[string]string {
	return map_PodList
}

var map_PodLogOptions = map[string]string{
	"":                             "PodLogOptions is the query options for a Pod's logs REST call.",
	"container":                    "The container for which to stream logs. Defaults to only container if there is one container in the pod.",
	"follow":                       "Follow the log stream of the pod. Defaults to false.",
	"previous":                     "Return previous terminated container logs. Defaults to false.",
	"sinceSeconds":                 "A relative time in seconds before the current time from which to show logs. If this value precedes the time a pod was started, only logs since the pod start will be returned. If this value is in the future, no logs will be returned. Only one of sinceSeconds or sinceTime may be specified.",
	"sinceTime":                    "An RFC3339 timestamp from which to show logs. If this value precedes the time a pod was started, only logs since the pod start will be returned. If this value is in the future, no logs will be returned. Only one of sinceSeconds or sinceTime may be specified.",
	"timestamps":                   "If true, add an RFC3339 or RFC3339Nano timestamp at the beginning of every line of log output. Defaults to false.",
	"tailLines":                    "If set, the number of lines from the end of the logs to show. If not specified, logs are shown from the creation of the container or sinceSeconds or sinceTime",
	"limitBytes":                   "If set, the number of bytes to read from the server before terminating the log output. This may not display a complete final line of logging, and may return slightly more or slightly less than the specified limit.",
	"insecureSkipTLSVerifyBackend": "insecureSkipTLSVerifyBackend indicates that the apiserver should not confirm the validity of the serving certificate of the backend it is connecting to.  This will make the HTTPS connection between the apiserver and the backend insecure. This means the apiserver cannot verify the log data it is receiving came from the real kubelet.  If the kubelet is configured to verify the apiserver's TLS credentials, it does not mean the connection to the real kubelet is vulnerable to a man in the middle attack (e.g. an attacker could not intercept the actual log data coming from the real kubelet).",
}

func (PodLogOptions) SwaggerDoc() map[string]string {
	return map_PodLogOptions
}

var map_PodOS = map[string]string{
	"":     "PodOS defines the OS parameters of a pod.",
	"name": "Name is the name of the operating system. The currently supported values are linux and windows. Additional value may be defined in future and can be one of: https://github.com/opencontainers/runtime-spec/blob/master/config.md#platform-specific-configuration Clients should expect to handle additional values and treat unrecognized values in this field as os: null",
}

func (PodOS) SwaggerDoc() map[string]string {
	return map_PodOS
}

var map_PodPortForwardOptions = map[string]string{
	"":      "PodPortForwardOptions is the query options to a Pod's port forward call when using WebSockets. The `port` query parameter must specify the port or ports (comma separated) to forward over. Port forwarding over SPDY does not use these options. It requires the port to be passed in the `port` header as part of request.",
	"ports": "List of ports to forward Required when using WebSockets",
}

func (PodPortForwardOptions) SwaggerDoc() map[string]string {
	return map_PodPortForwardOptions
}

var map_PodProxyOptions = map[string]string{
	"":     "PodProxyOptions is the query options to a Pod's proxy call.",
	"path": "Path is the URL path to use for the current proxy request to pod.",
}

func (PodProxyOptions) SwaggerDoc() map[string]string {
	return map_PodProxyOptions
}

var map_PodReadinessGate = map[string]string{
	"":              "PodReadinessGate contains the reference to a pod condition",
	"conditionType": "ConditionType refers to a condition in the pod's condition list with matching type.",
}

func (PodReadinessGate) SwaggerDoc() map[string]string {
	return map_PodReadinessGate
}

var map_PodSecurityContext = map[string]string{
	"":                    "PodSecurityContext holds pod-level security attributes and common container settings. Some fields are also present in container.securityContext.  Field values of container.securityContext take precedence over field values of PodSecurityContext.",
	"seLinuxOptions":      "The SELinux context to be applied to all containers. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.",
	"windowsOptions":      "The Windows specific settings applied to all containers. If unspecified, the options within a container's SecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.",
	"runAsUser":           "The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.",
	"runAsGroup":          "The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.",
	"runAsNonRoot":        "Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.",
	"supplementalGroups":  "A list of groups applied to the first process run in each container, in addition to the container's primary GID.  If unspecified, no groups will be added to any container. Note that this field cannot be set when spec.os.name is windows.",
	"fsGroup":             "A special supplemental group that applies to all containers in a pod. Some volume types allow the Kubelet to change the ownership of that volume to be owned by the pod:\n\n1. The owning GID will be the FSGroup 2. The setgid bit is set (new files created in the volume will be owned by FSGroup) 3. The permission bits are OR'd with rw-rw ",
	"sysctls":             "Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported sysctls (by the container runtime) might fail to launch. Note that this field cannot be set when spec.os.name is windows.",
	"fsGroupChangePolicy": "fsGroupChangePolicy defines behavior of changing ownership and permission of the volume before being exposed inside Pod. This field will only apply to volume types which support fsGroup based ownership(and permissions). It will have no effect on ephemeral volume types such as: secret, configmaps and emptydir. Valid values are \"OnRootMismatch\" and \"Always\". If not specified, \"Always\" is used. Note that this field cannot be set when spec.os.name is windows.",
	"seccompProfile":      "The seccomp options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows.",
}

func (PodSecurityContext) SwaggerDoc() map[string]string {
	return map_PodSecurityContext
}

var map_PodSignature = map[string]string{
	"":              "Describes the class of pods that should avoid this node. Exactly one field should be set.",
	"podController": "Reference to controller whose pods should avoid this node.",
}

func (PodSignature) SwaggerDoc() map[string]string {
	return map_PodSignature
}

var map_PodSpec = map[string]string{
	"":                              "PodSpec is a description of a pod.",
	"volumes":                       "List of volumes that can be mounted by containers belonging to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes",
	"initContainers":                "List of initialization containers belonging to the pod. Init containers are executed in order prior to containers being started. If any init container fails, the pod is considered to have failed and is handled according to its restartPolicy. The name for an init container or normal container must be unique among all containers. Init containers may not have Lifecycle actions, Readiness probes, Liveness probes, or Startup probes. The resourceRequirements of an init container are taken into account during scheduling by finding the highest request/limit for each resource type, and then using the max of of that value or the sum of the normal containers. Limits are applied to init containers in a similar fashion. Init containers cannot currently be added or removed. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/",
	"containers":                    "List of containers belonging to the pod. Containers cannot currently be added or removed. There must be at least one container in a Pod. Cannot be updated.",
	"ephemeralContainers":           "List of ephemeral containers run in this pod. Ephemeral containers may be run in an existing pod to perform user-initiated actions such as debugging. This list cannot be specified when creating a pod, and it cannot be modified by updating the pod spec. In order to add an ephemeral container to an existing pod, use the pod's ephemeralcontainers subresource. This field is beta-level and available on clusters that haven't disabled the EphemeralContainers feature gate.",
	"restartPolicy":                 "Restart policy for all containers within the pod. One of Always, OnFailure, Never. Default to Always. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#restart-policy",
	"terminationGracePeriodSeconds": "Optional duration in seconds the pod needs to terminate gracefully. May be decreased in delete request. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). If this value is nil, the default grace period will be used instead. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. Defaults to 30 seconds.",
	"activeDeadlineSeconds":         "Optional duration in seconds the pod may be active on the node relative to StartTime before the system will actively try to mark it failed and kill associated containers. Value must be a positive integer.",
	"dnsPolicy":                     "Set DNS policy for the pod. Defaults to \"ClusterFirst\". Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'. DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy. To have DNS options set along with hostNetwork, you have to specify DNS policy explicitly to 'ClusterFirstWithHostNet'.",
	"nodeSelector":                  "NodeSelector is a selector which must be true for the pod to fit on a node. Selector which must match a node's labels for the pod to be scheduled on that node. More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/",
	"serviceAccountName":            "ServiceAccountName is the name of the ServiceAccount to use to run this pod. More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/",
	"serviceAccount":                "DeprecatedServiceAccount is a depreciated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
	"automountServiceAccountToken":  "AutomountServiceAccountToken indicates whether a service account token should be automatically mounted.",
	"nodeName":                      "NodeName is a request to schedule this pod onto a specific node. If it is non-empty, the scheduler simply schedules this pod onto that node, assuming that it fits resource requirements.",
	"hostNetwork":                   "Host networking requested for this pod. Use the host's network namespace. If this option is set, the ports that will be used must be specified. Default to false.",
	"hostPID":                       "Use the host's pid namespace. Optional: Default to false.",
	"hostIPC":                       "Use the host's ipc namespace. Optional: Default to false.",
	"shareProcessNamespace":         "Share a single process namespace between all of the containers in a pod. When this is set containers will be able to view and signal processes from other containers in the same pod, and the first process in each container will not be assigned PID 1. HostPID and ShareProcessNamespace cannot both be set. Optional: Default to false.",
	"securityContext":               "SecurityContext holds pod-level security attributes and common container settings. Optional: Defaults to empty.  See type description for default values of each field.",
	"imagePullSecrets":              "ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images used by this PodSpec. If specified, these secrets will be passed to individual puller implementations for them to use. For example, in the case of docker, only DockerConfig type secrets are honored. More info: https://kubernetes.io/docs/concepts/containers/images#specifying-imagepullsecrets-on-a-pod",
	"hostname":                      "Specifies the hostname of the Pod If not specified, the pod's hostname will be set to a system-defined value.",
	"subdomain":                     "If specified, the fully qualified Pod hostname will be \"<hostname>.<subdomain>.<pod namespace>.svc.<cluster domain>\". If not specified, the pod will not have a domainname at all.",
	"affinity":                      "If specified, the pod's scheduling constraints",
	"schedulerName":                 "If specified, the pod will be dispatched by specified scheduler. If not specified, the pod will be dispatched by default scheduler.",
	"tolerations":                   "If specified, the pod's tolerations.",
	"hostAliases":                   "HostAliases is an optional list of hosts and IPs that will be injected into the pod's hosts file if specified. This is only valid for non-hostNetwork pods.",
	"priorityClassName":             "If specified, indicates the pod's priority. \"system-node-critical\" and \"system-cluster-critical\" are two special keywords which indicate the highest priorities with the former being the highest priority. Any other name must be defined by creating a PriorityClass object with that name. If not specified, the pod priority will be default or zero if there is no default.",
	"priority":                      "The priority value. Various system components use this field to find the priority of the pod. When Priority Admission Controller is enabled, it prevents users from setting this field. The admission controller populates this field from PriorityClassName. The higher the value, the higher the priority.",
	"dnsConfig":                     "Specifies the DNS parameters of a pod. Parameters specified here will be merged to the generated DNS configuration based on DNSPolicy.",
	"readinessGates":                "If specified, all readiness gates will be evaluated for pod readiness. A pod is ready when all its containers are ready AND all conditions specified in the readiness gates have status equal to \"True\" More info: https://git.k8s.io/enhancements/keps/sig-network/580-pod-readiness-gates",
	"runtimeClassName":              "RuntimeClassName refers to a RuntimeClass object in the node.k8s.io group, which should be used to run this pod.  If no RuntimeClass resource matches the named class, the pod will not be run. If unset or empty, the \"legacy\" RuntimeClass will be used, which is an implicit class with an empty definition that uses the default runtime handler. More info: https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class This is a beta feature as of Kubernetes v1.14.",
	"enableServiceLinks":            "EnableServiceLinks indicates whether information about services should be injected into pod's environment variables, matching the syntax of Docker links. Optional: Defaults to true.",
	"preemptionPolicy":              "PreemptionPolicy is the Policy for preempting pods with lower priority. One of Never, PreemptLowerPriority. Defaults to PreemptLowerPriority if unset. This field is beta-level, gated by the NonPreemptingPriority feature-gate.",
	"overhead":                      "Overhead represents the resource overhead associated with running a pod for a given RuntimeClass. This field will be autopopulated at admission time by the RuntimeClass admission controller. If the RuntimeClass admission controller is enabled, overhead must not be set in Pod create requests. The RuntimeClass admission controller will reject Pod create requests which have the overhead already set. If RuntimeClass is configured and selected in the PodSpec, Overhead will be set to the value defined in the corresponding RuntimeClass, otherwise it will remain unset and treated as zero. More info: https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md This field is beta-level as of Kubernetes v1.18, and is only honored by servers that enable the PodOverhead feature.",
	"topologySpreadConstraints":     "TopologySpreadConstraints describes how a group of pods ought to spread across topology domains. Scheduler will schedule pods in a way which abides by the constraints. All topologySpreadConstraints are ANDed.",
	"setHostnameAsFQDN":             "If true the pod's hostname will be configured as the pod's FQDN, rather than the leaf name (the default). In Linux containers, this means setting the FQDN in the hostname field of the kernel (the nodename field of struct utsname). In Windows containers, this means setting the registry value of hostname for the registry key HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters to FQDN. If a pod does not have FQDN, this has no effect. Default to false.",
	"os":                            "Specifies the OS of the containers in the pod. Some pod and container fields are restricted if this is set.\n\nIf the OS field is set to linux, the following fields must be unset: -securityContext.windowsOptions\n\nIf the OS field is set to windows, following fields must be unset: - spec.hostPID - spec.hostIPC - spec.securityContext.seLinuxOptions - spec.securityContext.seccompProfile - spec.securityContext.fsGroup - spec.securityContext.fsGroupChangePolicy - spec.securityContext.sysctls - spec.shareProcessNamespace - spec.securityContext.runAsUser - spec.securityContext.runAsGroup - spec.securityContext.supplementalGroups - spec.containers[*].securityContext.seLinuxOptions - spec.containers[*].securityContext.seccompProfile - spec.containers[*].securityContext.capabilities - spec.containers[*].securityContext.readOnlyRootFilesystem - spec.containers[*].securityContext.privileged - spec.containers[*].securityContext.allowPrivilegeEscalation - spec.containers[*].securityContext.procMount - spec.containers[*].securityContext.runAsUser - spec.containers[*].securityContext.runAsGroup This is an alpha field and requires the IdentifyPodOS feature",
}

func (PodSpec) SwaggerDoc() map[string]string {
	return map_PodSpec
}

var map_PodStatus = map[string]string{
	"":                           "PodStatus represents information about the status of a pod. Status may trail the actual state of a system, especially if the node that hosts the pod cannot contact the control plane.",
	"phase":                      "The phase of a Pod is a simple, high-level summary of where the Pod is in its lifecycle. The conditions array, the reason and message fields, and the individual container status arrays contain more detail about the pod's status. There are five possible phase values:\n\nPending: The pod has been accepted by the Kubernetes system, but one or more of the container images has not been created. This includes time before being scheduled as well as time spent downloading images over the network, which could take a while. Running: The pod has been bound to a node, and all of the containers have been created. At least one container is still running, or is in the process of starting or restarting. Succeeded: All containers in the pod have terminated in success, and will not be restarted. Failed: All containers in the pod have terminated, and at least one container has terminated in failure. The container either exited with non-zero status or was terminated by the system. Unknown: For some reason the state of the pod could not be obtained, typically due to an error in communicating with the host of the pod.\n\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-phase",
	"conditions":                 "Current service state of pod. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions",
	"message":                    "A human readable message indicating details about why the pod is in this condition.",
	"reason":                     "A brief CamelCase message indicating details about why the pod is in this state. e.g. 'Evicted'",
	"nominatedNodeName":          "nominatedNodeName is set only when this pod preempts other pods on the node, but it cannot be scheduled right away as preemption victims receive their graceful termination periods. This field does not guarantee that the pod will be scheduled on this node. Scheduler may decide to place the pod elsewhere if other nodes become available sooner. Scheduler may also decide to give the resources on this node to a higher priority pod that is created after preemption. As a result, this field may be different than PodSpec.nodeName when the pod is scheduled.",
	"hostIP":                     "IP address of the host to which the pod is assigned. Empty if not yet scheduled.",
	"podIP":                      "IP address allocated to the pod. Routable at least within the cluster. Empty if not yet allocated.",
	"podIPs":                     "podIPs holds the IP addresses allocated to the pod. If this field is specified, the 0th entry must match the podIP field. Pods may be allocated at most 1 value for each of IPv4 and IPv6. This list is empty if no IPs have been allocated yet.",
	"startTime":                  "RFC 3339 date and time at which the object was acknowledged by the Kubelet. This is before the Kubelet pulled the container image(s) for the pod.",
	"initContainerStatuses":      "The list has one entry per init container in the manifest. The most recent successful init container will have ready = true, the most recently started container will have startTime set. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-and-container-status",
	"containerStatuses":          "The list has one entry per container in the manifest. Each entry is currently the output of `docker inspect`. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-and-container-status",
	"qosClass":                   "The Quality of Service (QOS) classification assigned to the pod based on resource requirements See PodQOSClass type for available QOS classes More info: https://git.k8s.io/community/contributors/design-proposals/node/resource-qos.md",
	"ephemeralContainerStatuses": "Status for any ephemeral containers that have run in this pod. This field is beta-level and available on clusters that haven't disabled the EphemeralContainers feature gate.",
}

func (PodStatus) SwaggerDoc() map[string]string {
	return map_PodStatus
}

var map_PodStatusResult = map[string]string{
	"":         "PodStatusResult is a wrapper for PodStatus returned by kubelet that can be encode/decoded",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"status":   "Most recently observed status of the pod. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (PodStatusResult) SwaggerDoc() map[string]string {
	return map_PodStatusResult
}

var map_PodTemplate = map[string]string{
	"":         "PodTemplate describes a template for creating copies of a predefined pod.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"template": "Template defines the pods that will be created from this pod template. https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (PodTemplate) SwaggerDoc() map[string]string {
	return map_PodTemplate
}

var map_PodTemplateList = map[string]string{
	"":         "PodTemplateList is a list of PodTemplates.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of pod templates",
}

func (PodTemplateList) SwaggerDoc() map[string]string {
	return map_PodTemplateList
}

var map_PodTemplateSpec = map[string]string{
	"":         "PodTemplateSpec describes the data a pod should have when created from a template",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Specification of the desired behavior of the pod. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (PodTemplateSpec) SwaggerDoc() map[string]string {
	return map_PodTemplateSpec
}

var map_PortStatus = map[string]string{
	"port":     "Port is the port number of the service port of which status is recorded here",
	"protocol": "Protocol is the protocol of the service port of which status is recorded here The supported values are: \"TCP\", \"UDP\", \"SCTP\"",
	"error":    "Error is to record the problem with the service port The format of the error shall comply with the following rules: - built-in error values shall be specified in this file and those shall use\n  CamelCase names\n- cloud provider specific error values must have names that comply with the\n  format foo.example.com/CamelCase.",
}

func (PortStatus) SwaggerDoc() map[string]string {
	return map_PortStatus
}

var map_PortworxVolumeSource = map[string]string{
	"":         "PortworxVolumeSource represents a Portworx volume resource.",
	"volumeID": "VolumeID uniquely identifies a Portworx volume",
	"fsType":   "FSType represents the filesystem type to mount Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\". Implicitly inferred to be \"ext4\" if unspecified.",
	"readOnly": "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
}

func (PortworxVolumeSource) SwaggerDoc() map[string]string {
	return map_PortworxVolumeSource
}

var map_Preconditions = map[string]string{
	"":    "Preconditions must be fulfilled before an operation (update, delete, etc.) is carried out.",
	"uid": "Specifies the target UID.",
}

func (Preconditions) SwaggerDoc() map[string]string {
	return map_Preconditions
}

var map_PreferAvoidPodsEntry = map[string]string{
	"":             "Describes a class of pods that should avoid this node.",
	"podSignature": "The class of pods.",
	"evictionTime": "Time at which this entry was added to the list.",
	"reason":       "(brief) reason why this entry was added to the list.",
	"message":      "Human readable message indicating why this entry was added to the list.",
}

func (PreferAvoidPodsEntry) SwaggerDoc() map[string]string {
	return map_PreferAvoidPodsEntry
}

var map_PreferredSchedulingTerm = map[string]string{
	"":           "An empty preferred scheduling term matches all objects with implicit weight 0 (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).",
	"weight":     "Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.",
	"preference": "A node selector term, associated with the corresponding weight.",
}

func (PreferredSchedulingTerm) SwaggerDoc() map[string]string {
	return map_PreferredSchedulingTerm
}

var map_Probe = map[string]string{
	"":                              "Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.",
	"initialDelaySeconds":           "Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes",
	"timeoutSeconds":                "Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes",
	"periodSeconds":                 "How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.",
	"successThreshold":              "Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.",
	"failureThreshold":              "Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.",
	"terminationGracePeriodSeconds": "Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.",
}

func (Probe) SwaggerDoc() map[string]string {
	return map_Probe
}

var map_ProbeHandler = map[string]string{
	"":          "ProbeHandler defines a specific action that should be taken in a probe. One and only one of the fields must be specified.",
	"exec":      "Exec specifies the action to take.",
	"httpGet":   "HTTPGet specifies the http request to perform.",
	"tcpSocket": "TCPSocket specifies an action involving a TCP port.",
	"grpc":      "GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.",
}

func (ProbeHandler) SwaggerDoc() map[string]string {
	return map_ProbeHandler
}

var map_ProjectedVolumeSource = map[string]string{
	"":            "Represents a projected volume source",
	"sources":     "list of volume projections",
	"defaultMode": "Mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.",
}

func (ProjectedVolumeSource) SwaggerDoc() map[string]string {
	return map_ProjectedVolumeSource
}

var map_QuobyteVolumeSource = map[string]string{
	"":         "Represents a Quobyte mount that lasts the lifetime of a pod. Quobyte volumes do not support ownership management or SELinux relabeling.",
	"registry": "Registry represents a single or multiple Quobyte Registry services specified as a string as host:port pair (multiple entries are separated with commas) which acts as the central registry for volumes",
	"volume":   "Volume is a string that references an already created Quobyte volume by name.",
	"readOnly": "ReadOnly here will force the Quobyte volume to be mounted with read-only permissions. Defaults to false.",
	"user":     "User to map volume access to Defaults to serivceaccount user",
	"group":    "Group to map volume access to Default is no group",
	"tenant":   "Tenant owning the given Quobyte volume in the Backend Used with dynamically provisioned Quobyte volumes, value is set by the plugin",
}

func (QuobyteVolumeSource) SwaggerDoc() map[string]string {
	return map_QuobyteVolumeSource
}

var map_RBDPersistentVolumeSource = map[string]string{
	"":          "Represents a Rados Block Device mount that lasts the lifetime of a pod. RBD volumes support ownership management and SELinux relabeling.",
	"monitors":  "A collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"image":     "The rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"fsType":    "Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd",
	"pool":      "The rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"user":      "The rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"keyring":   "Keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"secretRef": "SecretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"readOnly":  "ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
}

func (RBDPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_RBDPersistentVolumeSource
}

var map_RBDVolumeSource = map[string]string{
	"":          "Represents a Rados Block Device mount that lasts the lifetime of a pod. RBD volumes support ownership management and SELinux relabeling.",
	"monitors":  "A collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"image":     "The rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"fsType":    "Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd",
	"pool":      "The rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"user":      "The rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"keyring":   "Keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"secretRef": "SecretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
	"readOnly":  "ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it",
}

func (RBDVolumeSource) SwaggerDoc() map[string]string {
	return map_RBDVolumeSource
}

var map_RangeAllocation = map[string]string{
	"":         "RangeAllocation is not a public type.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"range":    "Range is string that identifies the range represented by 'data'.",
	"data":     "Data is a bit array containing all allocated addresses in the previous segment.",
}

func (RangeAllocation) SwaggerDoc() map[string]string {
	return map_RangeAllocation
}

var map_ReplicationController = map[string]string{
	"":         "ReplicationController represents the configuration of a replication controller.",
	"metadata": "If the Labels of a ReplicationController are empty, they are defaulted to be the same as the Pod(s) that the replication controller manages. Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the specification of the desired behavior of the replication controller. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
	"status":   "Status is the most recently observed status of the replication controller. This data may be out of date by some window of time. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (ReplicationController) SwaggerDoc() map[string]string {
	return map_ReplicationController
}

var map_ReplicationControllerCondition = map[string]string{
	"":                   "ReplicationControllerCondition describes the state of a replication controller at a certain point.",
	"type":               "Type of replication controller condition.",
	"status":             "Status of the condition, one of True, False, Unknown.",
	"lastTransitionTime": "The last time the condition transitioned from one status to another.",
	"reason":             "The reason for the condition's last transition.",
	"message":            "A human readable message indicating details about the transition.",
}

func (ReplicationControllerCondition) SwaggerDoc() map[string]string {
	return map_ReplicationControllerCondition
}

var map_ReplicationControllerList = map[string]string{
	"":         "ReplicationControllerList is a collection of replication controllers.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of replication controllers. More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller",
}

func (ReplicationControllerList) SwaggerDoc() map[string]string {
	return map_ReplicationControllerList
}

var map_ReplicationControllerSpec = map[string]string{
	"":                "ReplicationControllerSpec is the specification of a replication controller.",
	"replicas":        "Replicas is the number of desired replicas. This is a pointer to distinguish between explicit zero and unspecified. Defaults to 1. More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#what-is-a-replicationcontroller",
	"minReadySeconds": "Minimum number of seconds for which a newly created pod should be ready without any of its container crashing, for it to be considered available. Defaults to 0 (pod will be considered available as soon as it is ready)",
	"selector":        "Selector is a label query over pods that should match the Replicas count. If Selector is empty, it is defaulted to the labels present on the Pod template. Label keys and values that must match in order to be controlled by this replication controller, if empty defaulted to labels on Pod template. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors",
	"template":        "Template is the object that describes the pod that will be created if insufficient replicas are detected. This takes precedence over a TemplateRef. More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#pod-template",
}

func (ReplicationControllerSpec) SwaggerDoc() map[string]string {
	return map_ReplicationControllerSpec
}

var map_ReplicationControllerStatus = map[string]string{
	"":                     "ReplicationControllerStatus represents the current status of a replication controller.",
	"replicas":             "Replicas is the most recently oberved number of replicas. More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#what-is-a-replicationcontroller",
	"fullyLabeledReplicas": "The number of pods that have labels matching the labels of the pod template of the replication controller.",
	"readyReplicas":        "The number of ready replicas for this replication controller.",
	"availableReplicas":    "The number of available replicas (ready for at least minReadySeconds) for this replication controller.",
	"observedGeneration":   "ObservedGeneration reflects the generation of the most recently observed replication controller.",
	"conditions":           "Represents the latest available observations of a replication controller's current state.",
}

func (ReplicationControllerStatus) SwaggerDoc() map[string]string {
	return map_ReplicationControllerStatus
}

var map_ResourceFieldSelector = map[string]string{
	"":              "ResourceFieldSelector represents container resources (cpu, memory) and their output format",
	"containerName": "Container name: required for volumes, optional for env vars",
	"resource":      "Required: resource to select",
	"divisor":       "Specifies the output format of the exposed resources, defaults to \"1\"",
}

func (ResourceFieldSelector) SwaggerDoc() map[string]string {
	return map_ResourceFieldSelector
}

var map_ResourceQuota = map[string]string{
	"":         "ResourceQuota sets aggregate quota restrictions enforced per namespace",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the desired quota. https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
	"status":   "Status defines the actual enforced quota and its current usage. https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (ResourceQuota) SwaggerDoc() map[string]string {
	return map_ResourceQuota
}

var map_ResourceQuotaList = map[string]string{
	"":         "ResourceQuotaList is a list of ResourceQuota items.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "Items is a list of ResourceQuota objects. More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/",
}

func (ResourceQuotaList) SwaggerDoc() map[string]string {
	return map_ResourceQuotaList
}

var map_ResourceQuotaSpec = map[string]string{
	"":              "ResourceQuotaSpec defines the desired hard limits to enforce for Quota.",
	"hard":          "hard is the set of desired hard limits for each named resource. More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/",
	"scopes":        "A collection of filters that must match each object tracked by a quota. If not specified, the quota matches all objects.",
	"scopeSelector": "scopeSelector is also a collection of filters like scopes that must match each object tracked by a quota but expressed using ScopeSelectorOperator in combination with possible values. For a resource to match, both scopes AND scopeSelector (if specified in spec), must be matched.",
}

func (ResourceQuotaSpec) SwaggerDoc() map[string]string {
	return map_ResourceQuotaSpec
}

var map_ResourceQuotaStatus = map[string]string{
	"":     "ResourceQuotaStatus defines the enforced hard limits and observed use.",
	"hard": "Hard is the set of enforced hard limits for each named resource. More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/",
	"used": "Used is the current observed total usage of the resource in the namespace.",
}

func (ResourceQuotaStatus) SwaggerDoc() map[string]string {
	return map_ResourceQuotaStatus
}

var map_ResourceRequirements = map[string]string{
	"":         "ResourceRequirements describes the compute resource requirements.",
	"limits":   "Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/",
	"requests": "Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/",
}

func (ResourceRequirements) SwaggerDoc() map[string]string {
	return map_ResourceRequirements
}

var map_SELinuxOptions = map[string]string{
	"":      "SELinuxOptions are the labels to be applied to the container",
	"user":  "User is a SELinux user label that applies to the container.",
	"role":  "Role is a SELinux role label that applies to the container.",
	"type":  "Type is a SELinux type label that applies to the container.",
	"level": "Level is SELinux level label that applies to the container.",
}

func (SELinuxOptions) SwaggerDoc() map[string]string {
	return map_SELinuxOptions
}

var map_ScaleIOPersistentVolumeSource = map[string]string{
	"":                 "ScaleIOPersistentVolumeSource represents a persistent ScaleIO volume",
	"gateway":          "The host address of the ScaleIO API Gateway.",
	"system":           "The name of the storage system as configured in ScaleIO.",
	"secretRef":        "SecretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.",
	"sslEnabled":       "Flag to enable/disable SSL communication with Gateway, default false",
	"protectionDomain": "The name of the ScaleIO Protection Domain for the configured storage.",
	"storagePool":      "The ScaleIO Storage Pool associated with the protection domain.",
	"storageMode":      "Indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.",
	"volumeName":       "The name of a volume already created in the ScaleIO system that is associated with this volume source.",
	"fsType":           "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Default is \"xfs\"",
	"readOnly":         "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
}

func (ScaleIOPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_ScaleIOPersistentVolumeSource
}

var map_ScaleIOVolumeSource = map[string]string{
	"":                 "ScaleIOVolumeSource represents a persistent ScaleIO volume",
	"gateway":          "The host address of the ScaleIO API Gateway.",
	"system":           "The name of the storage system as configured in ScaleIO.",
	"secretRef":        "SecretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.",
	"sslEnabled":       "Flag to enable/disable SSL communication with Gateway, default false",
	"protectionDomain": "The name of the ScaleIO Protection Domain for the configured storage.",
	"storagePool":      "The ScaleIO Storage Pool associated with the protection domain.",
	"storageMode":      "Indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.",
	"volumeName":       "The name of a volume already created in the ScaleIO system that is associated with this volume source.",
	"fsType":           "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Default is \"xfs\".",
	"readOnly":         "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
}

func (ScaleIOVolumeSource) SwaggerDoc() map[string]string {
	return map_ScaleIOVolumeSource
}

var map_ScopeSelector = map[string]string{
	"":                 "A scope selector represents the AND of the selectors represented by the scoped-resource selector requirements.",
	"matchExpressions": "A list of scope selector requirements by scope of the resources.",
}

func (ScopeSelector) SwaggerDoc() map[string]string {
	return map_ScopeSelector
}

var map_ScopedResourceSelectorRequirement = map[string]string{
	"":          "A scoped-resource selector requirement is a selector that contains values, a scope name, and an operator that relates the scope name and values.",
	"scopeName": "The name of the scope that the selector applies to.",
	"operator":  "Represents a scope's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist.",
	"values":    "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.",
}

func (ScopedResourceSelectorRequirement) SwaggerDoc() map[string]string {
	return map_ScopedResourceSelectorRequirement
}

var map_SeccompProfile = map[string]string{
	"":                 "SeccompProfile defines a pod/container's seccomp profile settings. Only one profile source may be set.",
	"type":             "type indicates which kind of seccomp profile will be applied. Valid options are:\n\nLocalhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.",
	"localhostProfile": "localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must only be set if type is \"Localhost\".",
}

func (SeccompProfile) SwaggerDoc() map[string]string {
	return map_SeccompProfile
}

var map_Secret = map[string]string{
	"":           "Secret holds secret data of a certain type. The total bytes of the values in the Data field must be less than MaxSecretSize bytes.",
	"metadata":   "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"immutable":  "Immutable, if set to true, ensures that data stored in the Secret cannot be updated (only object metadata can be modified). If not set to true, the field can be modified at any time. Defaulted to nil.",
	"data":       "Data contains the secret data. Each key must consist of alphanumeric characters, '-', '_' or '.'. The serialized form of the secret data is a base64 encoded string, representing the arbitrary (possibly non-string) data value here. Described in https://tools.ietf.org/html/rfc4648#section-4",
	"stringData": "stringData allows specifying non-binary secret data in string form. It is provided as a write-only input field for convenience. All keys and values are merged into the data field on write, overwriting any existing values. The stringData field is never output when reading from the API.",
	"type":       "Used to facilitate programmatic handling of secret data. More info: https://kubernetes.io/docs/concepts/configuration/secret/#secret-types",
}

func (Secret) SwaggerDoc() map[string]string {
	return map_Secret
}

var map_SecretEnvSource = map[string]string{
	"":         "SecretEnvSource selects a Secret to populate the environment variables with.\n\nThe contents of the target Secret's Data field will represent the key-value pairs as environment variables.",
	"optional": "Specify whether the Secret must be defined",
}

func (SecretEnvSource) SwaggerDoc() map[string]string {
	return map_SecretEnvSource
}

var map_SecretKeySelector = map[string]string{
	"":         "SecretKeySelector selects a key of a Secret.",
	"key":      "The key of the secret to select from.  Must be a valid secret key.",
	"optional": "Specify whether the Secret or its key must be defined",
}

func (SecretKeySelector) SwaggerDoc() map[string]string {
	return map_SecretKeySelector
}

var map_SecretList = map[string]string{
	"":         "SecretList is a list of Secret.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "Items is a list of secret objects. More info: https://kubernetes.io/docs/concepts/configuration/secret",
}

func (SecretList) SwaggerDoc() map[string]string {
	return map_SecretList
}

var map_SecretProjection = map[string]string{
	"":         "Adapts a secret into a projected volume.\n\nThe contents of the target Secret's Data field will be presented in a projected volume as files using the keys in the Data field as the file names. Note that this is identical to a secret volume source without the default mode.",
	"items":    "If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.",
	"optional": "Specify whether the Secret or its key must be defined",
}

func (SecretProjection) SwaggerDoc() map[string]string {
	return map_SecretProjection
}

var map_SecretReference = map[string]string{
	"":          "SecretReference represents a Secret Reference. It has enough information to retrieve secret in any namespace",
	"name":      "Name is unique within a namespace to reference a secret resource.",
	"namespace": "Namespace defines the space within which the secret name must be unique.",
}

func (SecretReference) SwaggerDoc() map[string]string {
	return map_SecretReference
}

var map_SecretVolumeSource = map[string]string{
	"":            "Adapts a Secret into a volume.\n\nThe contents of the target Secret's Data field will be presented in a volume as files using the keys in the Data field as the file names. Secret volumes support ownership management and SELinux relabeling.",
	"secretName":  "Name of the secret in the pod's namespace to use. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret",
	"items":       "If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.",
	"defaultMode": "Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.",
	"optional":    "Specify whether the Secret or its keys must be defined",
}

func (SecretVolumeSource) SwaggerDoc() map[string]string {
	return map_SecretVolumeSource
}

var map_SecurityContext = map[string]string{
	"":                         "SecurityContext holds security configuration that will be applied to a container. Some fields are present in both SecurityContext and PodSecurityContext.  When both are set, the values in SecurityContext take precedence.",
	"capabilities":             "The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows.",
	"privileged":               "Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.",
	"seLinuxOptions":           "The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.",
	"windowsOptions":           "The Windows specific settings applied to all containers. If unspecified, the options from the PodSecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.",
	"runAsUser":                "The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.",
	"runAsGroup":               "The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.",
	"runAsNonRoot":             "Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.",
	"readOnlyRootFilesystem":   "Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.",
	"allowPrivilegeEscalation": "AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.",
	"procMount":                "procMount denotes the type of proc mount to use for the containers. The default is DefaultProcMount which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.",
	"seccompProfile":           "The seccomp options to use by this container. If seccomp options are provided at both the pod & container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows.",
}

func (SecurityContext) SwaggerDoc() map[string]string {
	return map_SecurityContext
}

var map_SerializedReference = map[string]string{
	"":          "SerializedReference is a reference to serialized object.",
	"reference": "The reference to an object in the system.",
}

func (SerializedReference) SwaggerDoc() map[string]string {
	return map_SerializedReference
}

var map_Service = map[string]string{
	"":         "Service is a named abstraction of software service (for example, mysql) consisting of local port (for example 3306) that the proxy listens on, and the selector that determines which pods will answer requests sent through the proxy.",
	"metadata": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"spec":     "Spec defines the behavior of a service. https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
	"status":   "Most recently observed status of the service. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
}

func (Service) SwaggerDoc() map[string]string {
	return map_Service
}

var map_ServiceAccount = map[string]string{
	"":                             "ServiceAccount binds together: * a name, understood by users, and perhaps by peripheral systems, for an identity * a principal that can be authenticated and authorized * a set of secrets",
	"metadata":                     "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
	"secrets":                      "Secrets is the list of secrets allowed to be used by pods running using this ServiceAccount. More info: https://kubernetes.io/docs/concepts/configuration/secret",
	"imagePullSecrets":             "ImagePullSecrets is a list of references to secrets in the same namespace to use for pulling any images in pods that reference this ServiceAccount. ImagePullSecrets are distinct from Secrets because Secrets can be mounted in the pod, but ImagePullSecrets are only accessed by the kubelet. More info: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod",
	"automountServiceAccountToken": "AutomountServiceAccountToken indicates whether pods running as this service account should have an API token automatically mounted. Can be overridden at the pod level.",
}

func (ServiceAccount) SwaggerDoc() map[string]string {
	return map_ServiceAccount
}

var map_ServiceAccountList = map[string]string{
	"":         "ServiceAccountList is a list of ServiceAccount objects",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of ServiceAccounts. More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/",
}

func (ServiceAccountList) SwaggerDoc() map[string]string {
	return map_ServiceAccountList
}

var map_ServiceAccountTokenProjection = map[string]string{
	"":                  "ServiceAccountTokenProjection represents a projected service account token volume. This projection can be used to insert a service account token into the pods runtime filesystem for use against APIs (Kubernetes API Server or otherwise).",
	"audience":          "Audience is the intended audience of the token. A recipient of a token must identify itself with an identifier specified in the audience of the token, and otherwise should reject the token. The audience defaults to the identifier of the apiserver.",
	"expirationSeconds": "ExpirationSeconds is the requested duration of validity of the service account token. As the token approaches expiration, the kubelet volume plugin will proactively rotate the service account token. The kubelet will start trying to rotate the token if the token is older than 80 percent of its time to live or if the token is older than 24 hours.Defaults to 1 hour and must be at least 10 minutes.",
	"path":              "Path is the path relative to the mount point of the file to project the token into.",
}

func (ServiceAccountTokenProjection) SwaggerDoc() map[string]string {
	return map_ServiceAccountTokenProjection
}

var map_ServiceList = map[string]string{
	"":         "ServiceList holds a list of services.",
	"metadata": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
	"items":    "List of services",
}

func (ServiceList) SwaggerDoc() map[string]string {
	return map_ServiceList
}

var map_ServicePort = map[string]string{
	"":            "ServicePort contains information on service's port.",
	"name":        "The name of this port within the service. This must be a DNS_LABEL. All ports within a ServiceSpec must have unique names. When considering the endpoints for a Service, this must match the 'name' field in the EndpointPort. Optional if only one ServicePort is defined on this service.",
	"protocol":    "The IP protocol for this port. Supports \"TCP\", \"UDP\", and \"SCTP\". Default is TCP.",
	"appProtocol": "The application protocol for this port. This field follows standard Kubernetes label syntax. Un-prefixed names are reserved for IANA standard service names (as per RFC-6335 and http://www.iana.org/assignments/service-names). Non-standard protocols should use prefixed names such as mycompany.com/my-custom-protocol.",
	"port":        "The port that will be exposed by this service.",
	"targetPort":  "Number or name of the port to access on the pods targeted by the service. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME. If this is a string, it will be looked up as a named port in the target Pod's container ports. If this is not specified, the value of the 'port' field is used (an identity map). This field is ignored for services with clusterIP=None, and should be omitted or set equal to the 'port' field. More info: https://kubernetes.io/docs/concepts/services-networking/service/#defining-a-service",
	"nodePort":    "The port on each node on which this service is exposed when type is NodePort or LoadBalancer.  Usually assigned by the system. If a value is specified, in-range, and not in use it will be used, otherwise the operation will fail.  If not specified, a port will be allocated if this Service requires one.  If this field is specified when creating a Service which does not need it, creation will fail. This field will be wiped when updating a Service to no longer need it (e.g. changing type from NodePort to ClusterIP). More info: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport",
}

func (ServicePort) SwaggerDoc() map[string]string {
	return map_ServicePort
}

var map_ServiceProxyOptions = map[string]string{
	"":     "ServiceProxyOptions is the query options to a Service's proxy call.",
	"path": "Path is the part of URLs that include service endpoints, suffixes, and parameters to use for the current proxy request to service. For example, the whole request URL is http://localhost/api/v1/namespaces/kube-system/services/elasticsearch-logging/_search?q=user:kimchy. Path is _search?q=user:kimchy.",
}

func (ServiceProxyOptions) SwaggerDoc() map[string]string {
	return map_ServiceProxyOptions
}

var map_ServiceSpec = map[string]string{
	"":                              "ServiceSpec describes the attributes that a user creates on a service.",
	"ports":                         "The list of ports that are exposed by this service. More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies",
	"selector":                      "Route service traffic to pods with label keys and values matching this selector. If empty or not present, the service is assumed to have an external process managing its endpoints, which Kubernetes will not modify. Only applies to types ClusterIP, NodePort, and LoadBalancer. Ignored if type is ExternalName. More info: https://kubernetes.io/docs/concepts/services-networking/service/",
	"clusterIP":                     "clusterIP is the IP address of the service and is usually assigned randomly. If an address is specified manually, is in-range (as per system configuration), and is not in use, it will be allocated to the service; otherwise creation of the service will fail. This field may not be changed through updates unless the type field is also being changed to ExternalName (which requires this field to be blank) or the type field is being changed from ExternalName (in which case this field may optionally be specified, as describe above).  Valid values are \"None\", empty string (\"\"), or a valid IP address. Setting this to \"None\" makes a \"headless service\" (no virtual IP), which is useful when direct endpoint connections are preferred and proxying is not required.  Only applies to types ClusterIP, NodePort, and LoadBalancer. If this field is specified when creating a Service of type ExternalName, creation will fail. This field will be wiped when updating a Service to type ExternalName. More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies",
	"clusterIPs":                    "ClusterIPs is a list of IP addresses assigned to this service, and are usually assigned randomly.  If an address is specified manually, is in-range (as per system configuration), and is not in use, it will be allocated to the service; otherwise creation of the service will fail. This field may not be changed through updates unless the type field is also being changed to ExternalName (which requires this field to be empty) or the type field is being changed from ExternalName (in which case this field may optionally be specified, as describe above).  Valid values are \"None\", empty string (\"\"), or a valid IP address.  Setting this to \"None\" makes a \"headless service\" (no virtual IP), which is useful when direct endpoint connections are preferred and proxying is not required.  Only applies to types ClusterIP, NodePort, and LoadBalancer. If this field is specified when creating a Service of type ExternalName, creation will fail. This field will be wiped when updating a Service to type ExternalName.  If this field is not specified, it will be initialized from the clusterIP field.  If this field is specified, clients must ensure that clusterIPs[0] and clusterIP have the same value.\n\nThis field may hold a maximum of two entries (dual-stack IPs, in either order). These IPs must correspond to the values of the ipFamilies field. Both clusterIPs and ipFamilies are governed by the ipFamilyPolicy field. More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies",
	"type":                          "type determines how the Service is exposed. Defaults to ClusterIP. Valid options are ExternalName, ClusterIP, NodePort, and LoadBalancer. \"ClusterIP\" allocates a cluster-internal IP address for load-balancing to endpoints. Endpoints are determined by the selector or if that is not specified, by manual construction of an Endpoints object or EndpointSlice objects. If clusterIP is \"None\", no virtual IP is allocated and the endpoints are published as a set of endpoints rather than a virtual IP. \"NodePort\" builds on ClusterIP and allocates a port on every node which routes to the same endpoints as the clusterIP. \"LoadBalancer\" builds on NodePort and creates an external load-balancer (if supported in the current cloud) which routes to the same endpoints as the clusterIP. \"ExternalName\" aliases this service to the specified externalName. Several other fields do not apply to ExternalName services. More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types",
	"externalIPs":                   "externalIPs is a list of IP addresses for which nodes in the cluster will also accept traffic for this service.  These IPs are not managed by Kubernetes.  The user is responsible for ensuring that traffic arrives at a node with this IP.  A common example is external load-balancers that are not part of the Kubernetes system.",
	"sessionAffinity":               "Supports \"ClientIP\" and \"None\". Used to maintain session affinity. Enable client IP based session affinity. Must be ClientIP or None. Defaults to None. More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies",
	"loadBalancerIP":                "Only applies to Service Type: LoadBalancer LoadBalancer will get created with the IP specified in this field. This feature depends on whether the underlying cloud-provider supports specifying the loadBalancerIP when a load balancer is created. This field will be ignored if the cloud-provider does not support the feature.",
	"loadBalancerSourceRanges":      "If specified and supported by the platform, this will restrict traffic through the cloud-provider load-balancer will be restricted to the specified client IPs. This field will be ignored if the cloud-provider does not support the feature.\" More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/",
	"externalName":                  "externalName is the external reference that discovery mechanisms will return as an alias for this service (e.g. a DNS CNAME record). No proxying will be involved.  Must be a lowercase RFC-1123 hostname (https://tools.ietf.org/html/rfc1123) and requires `type` to be \"ExternalName\".",
	"externalTrafficPolicy":         "externalTrafficPolicy denotes if this Service desires to route external traffic to node-local or cluster-wide endpoints. \"Local\" preserves the client source IP and avoids a second hop for LoadBalancer and Nodeport type services, but risks potentially imbalanced traffic spreading. \"Cluster\" obscures the client source IP and may cause a second hop to another node, but should have good overall load-spreading.",
	"healthCheckNodePort":           "healthCheckNodePort specifies the healthcheck nodePort for the service. This only applies when type is set to LoadBalancer and externalTrafficPolicy is set to Local. If a value is specified, is in-range, and is not in use, it will be used.  If not specified, a value will be automatically allocated.  External systems (e.g. load-balancers) can use this port to determine if a given node holds endpoints for this service or not.  If this field is specified when creating a Service which does not need it, creation will fail. This field will be wiped when updating a Service to no longer need it (e.g. changing type).",
	"publishNotReadyAddresses":      "publishNotReadyAddresses indicates that any agent which deals with endpoints for this Service should disregard any indications of ready/not-ready. The primary use case for setting this field is for a StatefulSet's Headless Service to propagate SRV DNS records for its Pods for the purpose of peer discovery. The Kubernetes controllers that generate Endpoints and EndpointSlice resources for Services interpret this to mean that all endpoints are considered \"ready\" even if the Pods themselves are not. Agents which consume only Kubernetes generated endpoints through the Endpoints or EndpointSlice resources can safely assume this behavior.",
	"sessionAffinityConfig":         "sessionAffinityConfig contains the configurations of session affinity.",
	"ipFamilies":                    "IPFamilies is a list of IP families (e.g. IPv4, IPv6) assigned to this service. This field is usually assigned automatically based on cluster configuration and the ipFamilyPolicy field. If this field is specified manually, the requested family is available in the cluster, and ipFamilyPolicy allows it, it will be used; otherwise creation of the service will fail. This field is conditionally mutable: it allows for adding or removing a secondary IP family, but it does not allow changing the primary IP family of the Service. Valid values are \"IPv4\" and \"IPv6\".  This field only applies to Services of types ClusterIP, NodePort, and LoadBalancer, and does apply to \"headless\" services. This field will be wiped when updating a Service to type ExternalName.\n\nThis field may hold a maximum of two entries (dual-stack families, in either order).  These families must correspond to the values of the clusterIPs field, if specified. Both clusterIPs and ipFamilies are governed by the ipFamilyPolicy field.",
	"ipFamilyPolicy":                "IPFamilyPolicy represents the dual-stack-ness requested or required by this Service. If there is no value provided, then this field will be set to SingleStack. Services can be \"SingleStack\" (a single IP family), \"PreferDualStack\" (two IP families on dual-stack configured clusters or a single IP family on single-stack clusters), or \"RequireDualStack\" (two IP families on dual-stack configured clusters, otherwise fail). The ipFamilies and clusterIPs fields depend on the value of this field. This field will be wiped when updating a service to type ExternalName.",
	"allocateLoadBalancerNodePorts": "allocateLoadBalancerNodePorts defines if NodePorts will be automatically allocated for services with type LoadBalancer.  Default is \"true\". It may be set to \"false\" if the cluster load-balancer does not rely on NodePorts.  If the caller requests specific NodePorts (by specifying a value), those requests will be respected, regardless of this field. This field may only be set for services with type LoadBalancer and will be cleared if the type is changed to any other type. This field is beta-level and is only honored by servers that enable the ServiceLBNodePortControl feature.",
	"loadBalancerClass":             "loadBalancerClass is the class of the load balancer implementation this Service belongs to. If specified, the value of this field must be a label-style identifier, with an optional prefix, e.g. \"internal-vip\" or \"example.com/internal-vip\". Unprefixed names are reserved for end-users. This field can only be set when the Service type is 'LoadBalancer'. If not set, the default load balancer implementation is used, today this is typically done through the cloud provider integration, but should apply for any default implementation. If set, it is assumed that a load balancer implementation is watching for Services with a matching class. Any default load balancer implementation (e.g. cloud providers) should ignore Services that set this field. This field can only be set when creating or updating a Service to type 'LoadBalancer'. Once set, it can not be changed. This field will be wiped when a service is updated to a non 'LoadBalancer' type.",
	"internalTrafficPolicy":         "InternalTrafficPolicy specifies if the cluster internal traffic should be routed to all endpoints or node-local endpoints only. \"Cluster\" routes internal traffic to a Service to all endpoints. \"Local\" routes traffic to node-local endpoints only, traffic is dropped if no node-local endpoints are ready. The default value is \"Cluster\".",
}

func (ServiceSpec) SwaggerDoc() map[string]string {
	return map_ServiceSpec
}

var map_ServiceStatus = map[string]string{
	"":             "ServiceStatus represents the current status of a service.",
	"loadBalancer": "LoadBalancer contains the current status of the load-balancer, if one is present.",
	"conditions":   "Current service state",
}

func (ServiceStatus) SwaggerDoc() map[string]string {
	return map_ServiceStatus
}

var map_SessionAffinityConfig = map[string]string{
	"":         "SessionAffinityConfig represents the configurations of session affinity.",
	"clientIP": "clientIP contains the configurations of Client IP based session affinity.",
}

func (SessionAffinityConfig) SwaggerDoc() map[string]string {
	return map_SessionAffinityConfig
}

var map_StorageOSPersistentVolumeSource = map[string]string{
	"":                "Represents a StorageOS persistent volume resource.",
	"volumeName":      "VolumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.",
	"volumeNamespace": "VolumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \"default\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.",
	"fsType":          "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.",
	"readOnly":        "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"secretRef":       "SecretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.",
}

func (StorageOSPersistentVolumeSource) SwaggerDoc() map[string]string {
	return map_StorageOSPersistentVolumeSource
}

var map_StorageOSVolumeSource = map[string]string{
	"":                "Represents a StorageOS persistent volume resource.",
	"volumeName":      "VolumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.",
	"volumeNamespace": "VolumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \"default\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.",
	"fsType":          "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.",
	"readOnly":        "Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.",
	"secretRef":       "SecretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.",
}

func (StorageOSVolumeSource) SwaggerDoc() map[string]string {
	return map_StorageOSVolumeSource
}

var map_Sysctl = map[string]string{
	"":      "Sysctl defines a kernel parameter to be set",
	"name":  "Name of a property to set",
	"value": "Value of a property to set",
}

func (Sysctl) SwaggerDoc() map[string]string {
	return map_Sysctl
}

var map_TCPSocketAction = map[string]string{
	"":     "TCPSocketAction describes an action based on opening a socket",
	"port": "Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.",
	"host": "Optional: Host name to connect to, defaults to the pod IP.",
}

func (TCPSocketAction) SwaggerDoc() map[string]string {
	return map_TCPSocketAction
}

var map_Taint = map[string]string{
	"":          "The node this Taint is attached to has the \"effect\" on any pod that does not tolerate the Taint.",
	"key":       "Required. The taint key to be applied to a node.",
	"value":     "The taint value corresponding to the taint key.",
	"effect":    "Required. The effect of the taint on pods that do not tolerate the taint. Valid effects are NoSchedule, PreferNoSchedule and NoExecute.",
	"timeAdded": "TimeAdded represents the time at which the taint was added. It is only written for NoExecute taints.",
}

func (Taint) SwaggerDoc() map[string]string {
	return map_Taint
}

var map_Toleration = map[string]string{
	"":                  "The pod this Toleration is attached to tolerates any taint that matches the triple <key,value,effect> using the matching operator <operator>.",
	"key":               "Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys.",
	"operator":          "Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a pod can tolerate all taints of a particular category.",
	"value":             "Value is the taint value the toleration matches to. If the operator is Exists, the value should be empty, otherwise just a regular string.",
	"effect":            "Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.",
	"tolerationSeconds": "TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system.",
}

func (Toleration) SwaggerDoc() map[string]string {
	return map_Toleration
}

var map_TopologySelectorLabelRequirement = map[string]string{
	"":       "A topology selector requirement is a selector that matches given label. This is an alpha feature and may change in the future.",
	"key":    "The label key that the selector applies to.",
	"values": "An array of string values. One value must match the label to be selected. Each entry in Values is ORed.",
}

func (TopologySelectorLabelRequirement) SwaggerDoc() map[string]string {
	return map_TopologySelectorLabelRequirement
}

var map_TopologySelectorTerm = map[string]string{
	"":                      "A topology selector term represents the result of label queries. A null or empty topology selector term matches no objects. The requirements of them are ANDed. It provides a subset of functionality as NodeSelectorTerm. This is an alpha feature and may change in the future.",
	"matchLabelExpressions": "A list of topology selector requirements by labels.",
}

func (TopologySelectorTerm) SwaggerDoc() map[string]string {
	return map_TopologySelectorTerm
}

var map_TopologySpreadConstraint = map[string]string{
	"":                  "TopologySpreadConstraint specifies how to spread matching pods among the given topology.",
	"maxSkew":           "MaxSkew describes the degree to which pods may be unevenly distributed. When `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference between the number of matching pods in the target topology and the global minimum. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 1/1/0: ",
	"topologyKey":       "TopologyKey is the key of node labels. Nodes that have a label with this key and identical values are considered to be in the same topology. We consider each <key, value> as a \"bucket\", and try to put balanced number of pods into each bucket. It's a required field.",
	"whenUnsatisfiable": "WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy the spread constraint. - DoNotSchedule (default) tells the scheduler not to schedule it. - ScheduleAnyway tells the scheduler to schedule the pod in any location,\n  but giving higher precedence to topologies that would help reduce the\n  skew.\nA constraint is considered \"Unsatisfiable\" for an incoming pod if and only if every possible node assignment for that pod would violate \"MaxSkew\" on some topology. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 3/1/1: ",
	"labelSelector":     "LabelSelector is used to find matching pods. Pods that match this label selector are counted to determine the number of pods in their corresponding topology domain.",
}

func (TopologySpreadConstraint) SwaggerDoc() map[string]string {
	return map_TopologySpreadConstraint
}

var map_TypedLocalObjectReference = map[string]string{
	"":         "TypedLocalObjectReference contains enough information to let you locate the typed referenced object inside the same namespace.",
	"apiGroup": "APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.",
	"kind":     "Kind is the type of resource being referenced",
	"name":     "Name is the name of resource being referenced",
}

func (TypedLocalObjectReference) SwaggerDoc() map[string]string {
	return map_TypedLocalObjectReference
}

var map_Volume = map[string]string{
	"":     "Volume represents a named volume in a pod that may be accessed by any container in the pod.",
	"name": "Volume's name. Must be a DNS_LABEL and unique within the pod. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names",
}

func (Volume) SwaggerDoc() map[string]string {
	return map_Volume
}

var map_VolumeDevice = map[string]string{
	"":           "volumeDevice describes a mapping of a raw block device within a container.",
	"name":       "name must match the name of a persistentVolumeClaim in the pod",
	"devicePath": "devicePath is the path inside of the container that the device will be mapped to.",
}

func (VolumeDevice) SwaggerDoc() map[string]string {
	return map_VolumeDevice
}

var map_VolumeMount = map[string]string{
	"":                 "VolumeMount describes a mounting of a Volume within a container.",
	"name":             "This must match the Name of a Volume.",
	"readOnly":         "Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.",
	"mountPath":        "Path within the container at which the volume should be mounted.  Must not contain ':'.",
	"subPath":          "Path within the volume from which the container's volume should be mounted. Defaults to \"\" (volume's root).",
	"mountPropagation": "mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.",
	"subPathExpr":      "Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \"\" (volume's root). SubPathExpr and SubPath are mutually exclusive.",
}

func (VolumeMount) SwaggerDoc() map[string]string {
	return map_VolumeMount
}

var map_VolumeNodeAffinity = map[string]string{
	"":         "VolumeNodeAffinity defines constraints that limit what nodes this volume can be accessed from.",
	"required": "Required specifies hard node constraints that must be met.",
}

func (VolumeNodeAffinity) SwaggerDoc() map[string]string {
	return map_VolumeNodeAffinity
}

var map_VolumeProjection = map[string]string{
	"":                    "Projection that may be projected along with other supported volume types",
	"secret":              "information about the secret data to project",
	"downwardAPI":         "information about the downwardAPI data to project",
	"configMap":           "information about the configMap data to project",
	"serviceAccountToken": "information about the serviceAccountToken data to project",
}

func (VolumeProjection) SwaggerDoc() map[string]string {
	return map_VolumeProjection
}

var map_VolumeSource = map[string]string{
	"":                      "Represents the source of a volume to mount. Only one of its members may be specified.",
	"hostPath":              "HostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath",
	"emptyDir":              "EmptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir",
	"gcePersistentDisk":     "GCEPersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk",
	"awsElasticBlockStore":  "AWSElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore",
	"gitRepo":               "GitRepo represents a git repository at a particular revision. DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.",
	"secret":                "Secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret",
	"nfs":                   "NFS represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs",
	"iscsi":                 "ISCSI represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://examples.k8s.io/volumes/iscsi/README.md",
	"glusterfs":             "Glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/glusterfs/README.md",
	"persistentVolumeClaim": "PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims",
	"rbd":                   "RBD represents a Rados Block Device mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/rbd/README.md",
	"flexVolume":            "FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.",
	"cinder":                "Cinder represents a cinder volume attached and mounted on kubelets host machine. More info: https://examples.k8s.io/mysql-cinder-pd/README.md",
	"cephfs":                "CephFS represents a Ceph FS mount on the host that shares a pod's lifetime",
	"flocker":               "Flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running",
	"downwardAPI":           "DownwardAPI represents downward API about the pod that should populate this volume",
	"fc":                    "FC represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.",
	"azureFile":             "AzureFile represents an Azure File Service mount on the host and bind mount to the pod.",
	"configMap":             "ConfigMap represents a configMap that should populate this volume",
	"vsphereVolume":         "VsphereVolume represents a vSphere volume attached and mounted on kubelets host machine",
	"quobyte":               "Quobyte represents a Quobyte mount on the host that shares a pod's lifetime",
	"azureDisk":             "AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.",
	"photonPersistentDisk":  "PhotonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine",
	"projected":             "Items for all in one resources secrets, configmaps, and downward API",
	"portworxVolume":        "PortworxVolume represents a portworx volume attached and mounted on kubelets host machine",
	"scaleIO":               "ScaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.",
	"storageos":             "StorageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.",
	"csi":                   "CSI (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers (Beta feature).",
	"ephemeral":             "Ephemeral represents a volume that is handled by a cluster storage driver. The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts, and deleted when the pod is removed.\n\nUse this if: a) the volume is only needed while the pod runs, b) features of normal volumes like restoring from snapshot or capacity\n   tracking are needed,\nc) the storage driver is specified through a storage class, and d) the storage driver supports dynamic volume provisioning through\n   a PersistentVolumeClaim (see EphemeralVolumeSource for more\n   information on the connection between this volume type\n   and PersistentVolumeClaim).\n\nUse PersistentVolumeClaim or one of the vendor-specific APIs for volumes that persist for longer than the lifecycle of an individual pod.\n\nUse CSI for light-weight local ephemeral volumes if the CSI driver is meant to be used that way - see the documentation of the driver for more information.\n\nA pod can use both types of ephemeral volumes and persistent volumes at the same time.",
}

func (VolumeSource) SwaggerDoc() map[string]string {
	return map_VolumeSource
}

var map_VsphereVirtualDiskVolumeSource = map[string]string{
	"":                  "Represents a vSphere volume resource.",
	"volumePath":        "Path that identifies vSphere volume vmdk",
	"fsType":            "Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.",
	"storagePolicyName": "Storage Policy Based Management (SPBM) profile name.",
	"storagePolicyID":   "Storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.",
}

func (VsphereVirtualDiskVolumeSource) SwaggerDoc() map[string]string {
	return map_VsphereVirtualDiskVolumeSource
}

var map_WeightedPodAffinityTerm = map[string]string{
	"":                "The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)",
	"weight":          "weight associated with matching the corresponding podAffinityTerm, in the range 1-100.",
	"podAffinityTerm": "Required. A pod affinity term, associated with the corresponding weight.",
}

func (WeightedPodAffinityTerm) SwaggerDoc() map[string]string {
	return map_WeightedPodAffinityTerm
}

var map_WindowsSecurityContextOptions = map[string]string{
	"":                       "WindowsSecurityContextOptions contain Windows-specific options and credentials.",
	"gmsaCredentialSpecName": "GMSACredentialSpecName is the name of the GMSA credential spec to use.",
	"gmsaCredentialSpec":     "GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.",
	"runAsUserName":          "The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.",
	"hostProcess":            "HostProcess determines if a container should be run as a 'Host Process' container. This field is alpha-level and will only be honored by components that enable the WindowsHostProcessContainers feature flag. Setting this field without the feature flag will result in errors when validating the Pod. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).  In addition, if HostProcess is true then HostNetwork must also be set to true.",
}

func (WindowsSecurityContextOptions) SwaggerDoc() map[string]string {
	return map_WindowsSecurityContextOptions
}

// AUTO-GENERATED FUNCTIONS END HERE
