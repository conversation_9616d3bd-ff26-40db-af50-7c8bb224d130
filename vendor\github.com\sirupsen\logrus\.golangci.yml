run:
  # do not run on test files yet
  tests: false

# all available settings of specific linters
linters-settings:
  errcheck:
    # report about not checking of errors in type assetions: `a := b.(MyStruct)`;
    # default is false: such cases aren't reported by default.
    check-type-assertions: false

    # report about assignment of errors to blank identifier: `num, _ := strconv.Atoi(numStr)`;
    # default is false: such cases aren't reported by default.
    check-blank: false

  lll:
    line-length: 100
    tab-width: 4

  prealloc:
    simple: false
    range-loops: false
    for-loops: false

  whitespace:
    multi-if: false   # Enforces newlines (or comments) after every multi-line if statement
    multi-func: false # Enforces newlines (or comments) after every multi-line function signature

linters:
  enable:
    - megacheck
    - govet
  disable:
    - maligned
    - prealloc
  disable-all: false
  presets:
    - bugs
    - unused
  fast: false
