// Code generated by "go run msg_generate.go"; DO NOT EDIT.

package dns

// pack*() functions

func (rr *A) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDataA(rr.A, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *AAAA) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDataAAAA(rr.AAAA, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *AFSDB) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Subtype, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Hostname, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *ANY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	return off, nil
}

func (rr *APL) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDataApl(rr.Prefixes, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *AVC) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringTxt(rr.Txt, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CAA) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Flag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Tag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringOctet(rr.Value, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CDNSKEY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Protocol, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.PublicKey, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CDS) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.DigestType, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Digest, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CERT) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Type, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.Certificate, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CNAME) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Target, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CSYNC) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint32(rr.Serial, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDataNsec(rr.TypeBitMap, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DHCID) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringBase64(rr.Digest, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DLV) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.DigestType, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Digest, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DNAME) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Target, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DNSKEY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Protocol, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.PublicKey, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DS) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.DigestType, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Digest, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *EID) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringHex(rr.Endpoint, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *EUI48) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint48(rr.Address, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *EUI64) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint64(rr.Address, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *GID) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint32(rr.Gid, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *GPOS) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packString(rr.Longitude, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Latitude, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Altitude, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *HINFO) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packString(rr.Cpu, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Os, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *HIP) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.HitLength, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.PublicKeyAlgorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.PublicKeyLength, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Hit, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.PublicKey, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDataDomainNames(rr.RendezvousServers, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *HTTPS) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Priority, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Target, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packDataSVCB(rr.Value, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *KEY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Protocol, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.PublicKey, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *KX) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Exchanger, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *L32) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDataA(rr.Locator32, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *L64) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint64(rr.Locator64, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *LOC) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Version, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Size, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.HorizPre, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.VertPre, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Latitude, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Longitude, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Altitude, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *LP) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Fqdn, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MB) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Mb, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MD) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Md, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MF) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Mf, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MG) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Mg, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MINFO) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Rmail, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Email, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MR) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Mr, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MX) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Mx, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NAPTR) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Order, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Service, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packString(rr.Regexp, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Replacement, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NID) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint64(rr.NodeID, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NIMLOC) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringHex(rr.Locator, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NINFO) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringTxt(rr.ZSData, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NS) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Ns, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSAPPTR) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Ptr, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSEC) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.NextDomain, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packDataNsec(rr.TypeBitMap, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSEC3) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Hash, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Iterations, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.SaltLength, msg, off)
	if err != nil {
		return off, err
	}
	// Only pack salt if value is not "-", i.e. empty
	if rr.Salt != "-" {
		off, err = packStringHex(rr.Salt, msg, off)
		if err != nil {
			return off, err
		}
	}
	off, err = packUint8(rr.HashLength, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase32(rr.NextDomain, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDataNsec(rr.TypeBitMap, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSEC3PARAM) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Hash, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Iterations, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.SaltLength, msg, off)
	if err != nil {
		return off, err
	}
	// Only pack salt if value is not "-", i.e. empty
	if rr.Salt != "-" {
		off, err = packStringHex(rr.Salt, msg, off)
		if err != nil {
			return off, err
		}
	}
	return off, nil
}

func (rr *NULL) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringAny(rr.Data, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *OPENPGPKEY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringBase64(rr.PublicKey, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *OPT) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDataOpt(rr.Option, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *PTR) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Ptr, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *PX) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Map822, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Mapx400, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RFC3597) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringHex(rr.Rdata, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RKEY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Flags, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Protocol, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.PublicKey, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RP) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Mbox, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Txt, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RRSIG) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.TypeCovered, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Labels, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.OrigTtl, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Expiration, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Inception, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.SignerName, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.Signature, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RT) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Preference, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Host, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SIG) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.TypeCovered, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Labels, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.OrigTtl, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Expiration, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Inception, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.SignerName, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packStringBase64(rr.Signature, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SMIMEA) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Usage, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Selector, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.MatchingType, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Certificate, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SOA) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Ns, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Mbox, msg, off, compression, compress)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Serial, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Refresh, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Retry, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Expire, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Minttl, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SPF) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringTxt(rr.Txt, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SRV) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Priority, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Weight, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Port, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Target, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SSHFP) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Type, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.FingerPrint, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SVCB) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Priority, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.Target, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packDataSVCB(rr.Value, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TA) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.KeyTag, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Algorithm, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.DigestType, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Digest, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TALINK) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.PreviousName, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packDomainName(rr.NextName, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TKEY) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Algorithm, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Inception, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint32(rr.Expiration, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Mode, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Error, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.KeySize, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Key, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.OtherLen, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.OtherData, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TLSA) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint8(rr.Usage, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Selector, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.MatchingType, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Certificate, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TSIG) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packDomainName(rr.Algorithm, msg, off, compression, false)
	if err != nil {
		return off, err
	}
	off, err = packUint48(rr.TimeSigned, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Fudge, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.MACSize, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.MAC, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.OrigId, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Error, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.OtherLen, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.OtherData, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TXT) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packStringTxt(rr.Txt, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *UID) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint32(rr.Uid, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *UINFO) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packString(rr.Uinfo, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *URI) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint16(rr.Priority, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint16(rr.Weight, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringOctet(rr.Target, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *X25) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packString(rr.PSDNAddress, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *ZONEMD) pack(msg []byte, off int, compression compressionMap, compress bool) (off1 int, err error) {
	off, err = packUint32(rr.Serial, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Scheme, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packUint8(rr.Hash, msg, off)
	if err != nil {
		return off, err
	}
	off, err = packStringHex(rr.Digest, msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

// unpack*() functions

func (rr *A) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.A, off, err = unpackDataA(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *AAAA) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.AAAA, off, err = unpackDataAAAA(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *AFSDB) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Subtype, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Hostname, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *ANY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	return off, nil
}

func (rr *APL) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Prefixes, off, err = unpackDataApl(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *AVC) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Txt, off, err = unpackStringTxt(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CAA) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Flag, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Tag, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Value, off, err = unpackStringOctet(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CDNSKEY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Flags, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Protocol, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.PublicKey, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CDS) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.DigestType, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Digest, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CERT) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Type, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Certificate, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CNAME) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Target, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *CSYNC) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Serial, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Flags, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.TypeBitMap, off, err = unpackDataNsec(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DHCID) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Digest, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DLV) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.DigestType, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Digest, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DNAME) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Target, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DNSKEY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Flags, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Protocol, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.PublicKey, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *DS) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.DigestType, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Digest, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *EID) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Endpoint, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *EUI48) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Address, off, err = unpackUint48(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *EUI64) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Address, off, err = unpackUint64(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *GID) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Gid, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *GPOS) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Longitude, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Latitude, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Altitude, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *HINFO) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Cpu, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Os, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *HIP) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.HitLength, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.PublicKeyAlgorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.PublicKeyLength, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Hit, off, err = unpackStringHex(msg, off, off+int(rr.HitLength))
	if err != nil {
		return off, err
	}
	rr.PublicKey, off, err = unpackStringBase64(msg, off, off+int(rr.PublicKeyLength))
	if err != nil {
		return off, err
	}
	rr.RendezvousServers, off, err = unpackDataDomainNames(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *HTTPS) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Priority, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Target, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Value, off, err = unpackDataSVCB(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *KEY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Flags, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Protocol, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.PublicKey, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *KX) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Exchanger, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *L32) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Locator32, off, err = unpackDataA(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *L64) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Locator64, off, err = unpackUint64(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *LOC) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Version, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Size, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.HorizPre, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.VertPre, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Latitude, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Longitude, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Altitude, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *LP) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Fqdn, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MB) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Mb, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MD) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Md, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MF) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Mf, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MG) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Mg, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MINFO) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Rmail, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Email, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MR) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Mr, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *MX) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Mx, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NAPTR) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Order, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Flags, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Service, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Regexp, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Replacement, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NID) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.NodeID, off, err = unpackUint64(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NIMLOC) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Locator, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NINFO) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.ZSData, off, err = unpackStringTxt(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NS) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Ns, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSAPPTR) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Ptr, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSEC) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.NextDomain, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.TypeBitMap, off, err = unpackDataNsec(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSEC3) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Hash, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Flags, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Iterations, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.SaltLength, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Salt, off, err = unpackStringHex(msg, off, off+int(rr.SaltLength))
	if err != nil {
		return off, err
	}
	rr.HashLength, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.NextDomain, off, err = unpackStringBase32(msg, off, off+int(rr.HashLength))
	if err != nil {
		return off, err
	}
	rr.TypeBitMap, off, err = unpackDataNsec(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NSEC3PARAM) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Hash, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Flags, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Iterations, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.SaltLength, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Salt, off, err = unpackStringHex(msg, off, off+int(rr.SaltLength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *NULL) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Data, off, err = unpackStringAny(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *OPENPGPKEY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.PublicKey, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *OPT) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Option, off, err = unpackDataOpt(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *PTR) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Ptr, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *PX) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Map822, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Mapx400, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RFC3597) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Rdata, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RKEY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Flags, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Protocol, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.PublicKey, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RP) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Mbox, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Txt, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RRSIG) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.TypeCovered, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Labels, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.OrigTtl, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Expiration, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Inception, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.SignerName, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Signature, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *RT) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Preference, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Host, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SIG) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.TypeCovered, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Labels, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.OrigTtl, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Expiration, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Inception, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.SignerName, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Signature, off, err = unpackStringBase64(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SMIMEA) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Usage, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Selector, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.MatchingType, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Certificate, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SOA) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Ns, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Mbox, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Serial, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Refresh, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Retry, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Expire, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Minttl, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SPF) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Txt, off, err = unpackStringTxt(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SRV) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Priority, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Weight, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Port, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Target, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SSHFP) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Type, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.FingerPrint, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *SVCB) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Priority, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Target, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Value, off, err = unpackDataSVCB(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TA) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.KeyTag, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Algorithm, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.DigestType, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Digest, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TALINK) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.PreviousName, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.NextName, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TKEY) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Algorithm, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Inception, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Expiration, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Mode, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Error, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.KeySize, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Key, off, err = unpackStringHex(msg, off, off+int(rr.KeySize))
	if err != nil {
		return off, err
	}
	rr.OtherLen, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.OtherData, off, err = unpackStringHex(msg, off, off+int(rr.OtherLen))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TLSA) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Usage, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Selector, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.MatchingType, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Certificate, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TSIG) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Algorithm, off, err = UnpackDomainName(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.TimeSigned, off, err = unpackUint48(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Fudge, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.MACSize, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.MAC, off, err = unpackStringHex(msg, off, off+int(rr.MACSize))
	if err != nil {
		return off, err
	}
	rr.OrigId, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Error, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.OtherLen, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.OtherData, off, err = unpackStringHex(msg, off, off+int(rr.OtherLen))
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *TXT) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Txt, off, err = unpackStringTxt(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *UID) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Uid, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *UINFO) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Uinfo, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *URI) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Priority, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Weight, off, err = unpackUint16(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Target, off, err = unpackStringOctet(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *X25) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.PSDNAddress, off, err = unpackString(msg, off)
	if err != nil {
		return off, err
	}
	return off, nil
}

func (rr *ZONEMD) unpack(msg []byte, off int) (off1 int, err error) {
	rdStart := off
	_ = rdStart

	rr.Serial, off, err = unpackUint32(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Scheme, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Hash, off, err = unpackUint8(msg, off)
	if err != nil {
		return off, err
	}
	if off == len(msg) {
		return off, nil
	}
	rr.Digest, off, err = unpackStringHex(msg, off, rdStart+int(rr.Hdr.Rdlength))
	if err != nil {
		return off, err
	}
	return off, nil
}
