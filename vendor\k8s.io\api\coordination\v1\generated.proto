/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.coordination.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// Lease defines a lease concept.
message Lease {
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the Lease.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional LeaseSpec spec = 2;
}

// LeaseList is a list of Lease objects.
message LeaseList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of schema objects.
  repeated Lease items = 2;
}

// LeaseSpec is a specification of a Lease.
message LeaseSpec {
  // holderIdentity contains the identity of the holder of a current lease.
  // +optional
  optional string holderIdentity = 1;

  // leaseDurationSeconds is a duration that candidates for a lease need
  // to wait to force acquire it. This is measure against time of last
  // observed RenewTime.
  // +optional
  optional int32 leaseDurationSeconds = 2;

  // acquireTime is a time when the current lease was acquired.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.MicroTime acquireTime = 3;

  // renewTime is a time when the current holder of a lease has last
  // updated the lease.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.MicroTime renewTime = 4;

  // leaseTransitions is the number of transitions of a lease between
  // holders.
  // +optional
  optional int32 leaseTransitions = 5;
}

