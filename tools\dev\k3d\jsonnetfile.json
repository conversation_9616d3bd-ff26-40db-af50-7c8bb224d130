{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "ksonnet-util"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "tanka-util"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/k8s-libsonnet.git", "subdir": "1.20"}}, "version": "main"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "grafana"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "enterprise-metrics"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "loki-simple-scalable"}}, "version": "create-ssd-lib"}], "legacyImports": true}