AWSTemplateFormatVersion: '2010-09-09'
Description: >
  lambda-promtail:

  propagate Cloudwatch Logs to Loki/Promtail via Loki Write API.

Parameters:
  WriteAddress:
    Description: 'Address to write to in the form of: http<s>://<location><:port>/loki/api/v1/push'
    Type: String
    Default: 'http://localhost:8080/loki/api/v1/push'
  ReservedConcurrency:
    Description: The maximum of concurrent executions you want to reserve for the function.
    Type: Number
    Default: 2
  Username:
    Description: The basic auth username, necessary if writing directly to Grafana Cloud Loki.
    Type: String
    Default: ""
  Password:
    Description: The basic auth password, necessary if writing directly to Grafana Cloud Loki.
    Type: String
    Default: ""
    NoEcho: true
  LambdaPromtailImage:
    Description: The ECR image URI to pull and use for lambda-promtail.
    Type: String
    Default: ""
  KeepStream:
    Description: Determines whether to keep the CloudWatch Log Stream value as a Loki label when writing logs from lambda-promtail.
    Type: String
    Default: "false"
  ExtraLabels:
    Description: Comma separated list of extra labels, in the format 'name1,value1,name2,value2,...,nameN,valueN' to add to entries forwarded by lambda-promtail.
    Type: String
    Default: ""
  TenantID:
    Description: Tenant ID to be added when writing logs from lambda-promtail.
    Type: String
    Default: ""

Resources:
  LambdaPromtailRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
        - Effect: Allow
          Principal:
            Service:
            - lambda.amazonaws.com
          Action:
          - sts:AssumeRole
      Description: "Lambda Promtail Role"
      Policies:
      - PolicyName: logs
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
          - Effect: Allow
            Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
            Resource: arn:aws:logs:*:*:*
      RoleName: iam_for_lambda
  LambdaPromtailFunction:
    Type: AWS::Lambda::Function
    Properties:
      Code:
        ImageUri: !Ref LambdaPromtailImage
      MemorySize: 128
      PackageType: Image
      Timeout: 60
      Role: !GetAtt LambdaPromtailRole.Arn
      ReservedConcurrentExecutions: !Ref ReservedConcurrency
      # # https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lambda-function-vpcconfig.html
      # VpcConfig:
      Environment: # More info about Env Vars: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#environment-object
        Variables:
          WRITE_ADDRESS: !Ref WriteAddress
          USERNAME: !Ref Username
          PASSWORD: !Ref Password
          KEEP_STREAM: !Ref KeepStream
          EXTRA_LABELS: !Ref ExtraLabels
          TENANT_ID: !Ref TenantID
  LambdaPromtailVersion:
    Type: AWS::Lambda::Version
    Properties:
      FunctionName: !Ref LambdaPromtailFunction
  LambdaPromtailPermissions:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt LambdaPromtailFunction.Arn
      Principal: !Sub
        - logs.${Region}.amazonaws.com
        - { Region: !Ref "AWS::Region" }
  LambdaPromtailEventInvokeConfig:
    Type: AWS::Lambda::EventInvokeConfig
    Properties:
      FunctionName: !Ref LambdaPromtailFunction
      MaximumRetryAttempts: 2
      Qualifier: !GetAtt LambdaPromtailVersion.Version
  # Copy this block and modify as required to create Subscription Filters for
  # additional CloudWatch Log Groups.
  MainLambdaPromtailSubscriptionFilter:
    Type: AWS::Logs::SubscriptionFilter
    Properties:
      DestinationArn: !GetAtt LambdaPromtailFunction.Arn
      FilterPattern: ""
      LogGroupName: "/aws/lambda/some-lamda-log-group"

Outputs:
  LambdaPromtailFunction:
    Description: "Lambda Promtail Function ARN"
    Value: !GetAtt LambdaPromtailFunction.Arn
