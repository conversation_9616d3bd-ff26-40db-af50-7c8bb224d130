{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "consul"}}, "version": "00795013f5975f518a0a3de99253f9d5590271c8", "sum": "Po3c1Ic96ngrJCtOazic/7OsLkoILOKZWXWyZWl+od8="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "jaeger-agent-mixin"}}, "version": "00795013f5975f518a0a3de99253f9d5590271c8", "sum": "DsdBoqgx5kE3zc6fMYnfiGjW2+9Mx2OXFieWm1oFHgY="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "ksonnet-util"}}, "version": "00795013f5975f518a0a3de99253f9d5590271c8", "sum": "OxgtIWL4hjvG0xkMwUzZ7Yjs52zUhLhaVQpwHCbqf8A="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "memcached"}}, "version": "00795013f5975f518a0a3de99253f9d5590271c8", "sum": "dTOeEux3t9bYSqP2L/uCuLo/wUDpCKH4w+4OD9fePUk="}, {"source": {"git": {"remote": "https://github.com/grafana/loki.git", "subdir": "production/ksonnet/loki"}}, "version": "9ea59f2062016d91398387ee2231e2e840af6a06", "sum": "i27fS9sssvYd9Ywyq6uoB52EUWTaOPxo9DczCBVBuaM="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/k8s-libsonnet.git", "subdir": "1.18"}}, "version": "91008dbd2ea5734288467d6dcafef7c285c3f7e6", "sum": "x881PM+6ARMsa9OSJcxO6L+4GOBy91clZipjeYbbzpw="}], "legacyImports": false}