// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package ec2 provides the client and types for making API
// requests to Amazon Elastic Compute Cloud.
//
// Amazon Elastic Compute Cloud (Amazon EC2) provides secure and resizable computing
// capacity in the Amazon Web Services Cloud. Using Amazon EC2 eliminates the
// need to invest in hardware up front, so you can develop and deploy applications
// faster. Amazon Virtual Private Cloud (Amazon VPC) enables you to provision
// a logically isolated section of the Amazon Web Services Cloud where you can
// launch Amazon Web Services resources in a virtual network that you've defined.
// Amazon Elastic Block Store (Amazon EBS) provides block level storage volumes
// for use with EC2 instances. EBS volumes are highly available and reliable
// storage volumes that can be attached to any running instance and used like
// a hard drive.
//
// To learn more, see the following resources:
//
//    * Amazon EC2: AmazonEC2 product page (http://aws.amazon.com/ec2), Amazon
//    EC2 documentation (http://aws.amazon.com/documentation/ec2)
//
//    * Amazon EBS: Amazon EBS product page (http://aws.amazon.com/ebs), Amazon
//    EBS documentation (http://aws.amazon.com/documentation/ebs)
//
//    * Amazon VPC: Amazon VPC product page (http://aws.amazon.com/vpc), Amazon
//    VPC documentation (http://aws.amazon.com/documentation/vpc)
//
//    * Amazon Web Services VPN: Amazon Web Services VPN product page (http://aws.amazon.com/vpn),
//    Amazon Web Services VPN documentation (http://aws.amazon.com/documentation/vpn)
//
// See https://docs.aws.amazon.com/goto/WebAPI/ec2-2016-11-15 for more information on this service.
//
// See ec2 package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/ec2/
//
// Using the Client
//
// To contact Amazon Elastic Compute Cloud with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the Amazon Elastic Compute Cloud client EC2 for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/ec2/#New
package ec2
