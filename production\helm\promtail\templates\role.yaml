{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "promtail.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "promtail.name" . }}
    chart: {{ template "promtail.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
{{- if .Values.rbac.pspEnabled }}
rules:
- apiGroups:      ['extensions']
  resources:      ['podsecuritypolicies']
  verbs:          ['use']
  resourceNames:  [{{ template "promtail.fullname" . }}]
{{- end }}
{{- end }}
