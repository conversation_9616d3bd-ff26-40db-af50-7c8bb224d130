version: '3.7'
services:
  zookeeper-1:
    image: 'confluentinc/cp-zookeeper:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      ZOOKEEPER_SERVER_ID: '1'
      ZOOKEEPER_SERVERS: 'zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888'
      ZOOKEEPER_CLIENT_PORT: '2181'
      ZOOKEEPER_PEER_PORT: '2888'
      ZOOKEEPER_LEADER_PORT: '3888'
      ZOOKEEPER_INIT_LIMIT: '10'
      ZOOKEEPER_SYNC_LIMIT: '5'
      ZOOKEEPER_MAX_CLIENT_CONNS: '0'
  zookeeper-2:
    image: 'confluentinc/cp-zookeeper:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      ZOOKEEPER_SERVER_ID: '2'
      ZOOKEEPER_SERVERS: 'zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888'
      ZOOKEEPER_CLIENT_PORT: '2181'
      ZOOKEEPER_PEER_PORT: '2888'
      ZOOKEEPER_LEADER_PORT: '3888'
      ZOOKEEPER_INIT_LIMIT: '10'
      ZOOKEEPER_SYNC_LIMIT: '5'
      ZOOKEEPER_MAX_CLIENT_CONNS: '0'
  zookeeper-3:
    image: 'confluentinc/cp-zookeeper:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      ZOOKEEPER_SERVER_ID: '3'
      ZOOKEEPER_SERVERS: 'zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888'
      ZOOKEEPER_CLIENT_PORT: '2181'
      ZOOKEEPER_PEER_PORT: '2888'
      ZOOKEEPER_LEADER_PORT: '3888'
      ZOOKEEPER_INIT_LIMIT: '10'
      ZOOKEEPER_SYNC_LIMIT: '5'
      ZOOKEEPER_MAX_CLIENT_CONNS: '0'
  kafka-1:
    image: 'confluentinc/cp-kafka:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181'
      KAFKA_LISTENERS: 'LISTENER_INTERNAL://:9091,LISTENER_LOCAL://:29091'
      KAFKA_ADVERTISED_LISTENERS: 'LISTENER_INTERNAL://kafka-1:9091,LISTENER_LOCAL://localhost:29091'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'LISTENER_INTERNAL'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'LISTENER_INTERNAL:PLAINTEXT,LISTENER_LOCAL:PLAINTEXT'
      KAFKA_DEFAULT_REPLICATION_FACTOR: '2'
      KAFKA_BROKER_ID: '1'
      KAFKA_BROKER_RACK: '1'
      KAFKA_ZOOKEEPER_SESSION_TIMEOUT_MS: '3000'
      KAFKA_ZOOKEEPER_CONNECTION_TIMEOUT_MS: '3000'
      KAFKA_REPLICA_SELECTOR_CLASS: 'org.apache.kafka.common.replica.RackAwareReplicaSelector'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
  kafka-2:
    image: 'confluentinc/cp-kafka:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181'
      KAFKA_LISTENERS: 'LISTENER_INTERNAL://:9091,LISTENER_LOCAL://:29092'
      KAFKA_ADVERTISED_LISTENERS: 'LISTENER_INTERNAL://kafka-2:9091,LISTENER_LOCAL://localhost:29092'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'LISTENER_INTERNAL'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'LISTENER_INTERNAL:PLAINTEXT,LISTENER_LOCAL:PLAINTEXT'
      KAFKA_DEFAULT_REPLICATION_FACTOR: '2'
      KAFKA_BROKER_ID: '2'
      KAFKA_BROKER_RACK: '2'
      KAFKA_ZOOKEEPER_SESSION_TIMEOUT_MS: '3000'
      KAFKA_ZOOKEEPER_CONNECTION_TIMEOUT_MS: '3000'
      KAFKA_REPLICA_SELECTOR_CLASS: 'org.apache.kafka.common.replica.RackAwareReplicaSelector'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
  kafka-3:
    image: 'confluentinc/cp-kafka:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181'
      KAFKA_LISTENERS: 'LISTENER_INTERNAL://:9091,LISTENER_LOCAL://:29093'
      KAFKA_ADVERTISED_LISTENERS: 'LISTENER_INTERNAL://kafka-3:9091,LISTENER_LOCAL://localhost:29093'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'LISTENER_INTERNAL'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'LISTENER_INTERNAL:PLAINTEXT,LISTENER_LOCAL:PLAINTEXT'
      KAFKA_DEFAULT_REPLICATION_FACTOR: '2'
      KAFKA_BROKER_ID: '3'
      KAFKA_BROKER_RACK: '3'
      KAFKA_ZOOKEEPER_SESSION_TIMEOUT_MS: '3000'
      KAFKA_ZOOKEEPER_CONNECTION_TIMEOUT_MS: '3000'
      KAFKA_REPLICA_SELECTOR_CLASS: 'org.apache.kafka.common.replica.RackAwareReplicaSelector'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
  kafka-4:
    image: 'confluentinc/cp-kafka:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181'
      KAFKA_LISTENERS: 'LISTENER_INTERNAL://:9091,LISTENER_LOCAL://:29094'
      KAFKA_ADVERTISED_LISTENERS: 'LISTENER_INTERNAL://kafka-4:9091,LISTENER_LOCAL://localhost:29094'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'LISTENER_INTERNAL'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'LISTENER_INTERNAL:PLAINTEXT,LISTENER_LOCAL:PLAINTEXT'
      KAFKA_DEFAULT_REPLICATION_FACTOR: '2'
      KAFKA_BROKER_ID: '4'
      KAFKA_BROKER_RACK: '4'
      KAFKA_ZOOKEEPER_SESSION_TIMEOUT_MS: '3000'
      KAFKA_ZOOKEEPER_CONNECTION_TIMEOUT_MS: '3000'
      KAFKA_REPLICA_SELECTOR_CLASS: 'org.apache.kafka.common.replica.RackAwareReplicaSelector'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
  kafka-5:
    image: 'confluentinc/cp-kafka:${CONFLUENT_PLATFORM_VERSION:-6.2.0}'
    restart: always
    environment:
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181'
      KAFKA_LISTENERS: 'LISTENER_INTERNAL://:9091,LISTENER_LOCAL://:29095'
      KAFKA_ADVERTISED_LISTENERS: 'LISTENER_INTERNAL://kafka-5:9091,LISTENER_LOCAL://localhost:29095'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'LISTENER_INTERNAL'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'LISTENER_INTERNAL:PLAINTEXT,LISTENER_LOCAL:PLAINTEXT'
      KAFKA_DEFAULT_REPLICATION_FACTOR: '2'
      KAFKA_BROKER_ID: '5'
      KAFKA_BROKER_RACK: '5'
      KAFKA_ZOOKEEPER_SESSION_TIMEOUT_MS: '3000'
      KAFKA_ZOOKEEPER_CONNECTION_TIMEOUT_MS: '3000'
      KAFKA_REPLICA_SELECTOR_CLASS: 'org.apache.kafka.common.replica.RackAwareReplicaSelector'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
  toxiproxy:
    image: 'ghcr.io/shopify/toxiproxy:2.1.5'
    ports:
      # The tests themselves actually start the proxies on these ports
      - '29091:29091'
      - '29092:29092'
      - '29093:29093'
      - '29094:29094'
      - '29095:29095'
      # This is the toxiproxy API port
      - '8474:8474'
