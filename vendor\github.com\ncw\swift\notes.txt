Notes on Go Swift
=================

Make a builder style interface like the Google Go APIs?  Advantages
are that it is easy to add named methods to the service object to do
specific things.  Slightly less efficient.  Not sure about how to
return extra stuff though - in an object?

Make a container struct so these could be methods on it?

Make noResponse check for 204?

Make storage public so it can be extended easily?

Rename to go-swift to match user agent string?

Reconnect on auth error - 401 when token expires isn't tested

Make more api compatible with python cloudfiles?

Retry operations on timeout / network errors?
- also 408 error
- GET requests only?

Make Connection thread safe - whenever it is changed take a write lock whenever it is read from a read lock

Add extra headers field to Connection (for via etc)

Make errors use an error heirachy then can catch them with a type assertion

 Error(...)
 ObjectCorrupted{ Error }

Make a Debug flag in connection for logging stuff

Object If-Match, If-None-Match, If-Modified-Since, If-Unmodified-Since etc

Object range

Object create, update with X-Delete-At or X-Delete-After

Large object support
- check uploads are less than 5GB in normal mode?

Access control CORS?

Swift client retries and backs off for all types of errors

Implement net error interface?

type Error interface {
    error
    Timeout() bool   // Is the error a timeout?
    Temporary() bool // Is the error temporary?
}
