/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.apps.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";
import "k8s.io/apimachinery/pkg/util/intstr/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1beta1";

// DEPRECATED - This group version of ControllerRevision is deprecated by apps/v1beta2/ControllerRevision. See the
// release notes for more information.
// ControllerRevision implements an immutable snapshot of state data. Clients
// are responsible for serializing and deserializing the objects that contain
// their internal state.
// Once a ControllerRevision has been successfully created, it can not be updated.
// The API Server will fail validation of all requests that attempt to mutate
// the Data field. ControllerRevisions may, however, be deleted. Note that, due to its use by both
// the DaemonSet and StatefulSet controllers for update and rollback, this object is beta. However,
// it may be subject to name and representation changes in future releases, and clients should not
// depend on its stability. It is primarily for internal use by controllers.
message ControllerRevision {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Data is the serialized representation of the state.
  optional k8s.io.apimachinery.pkg.runtime.RawExtension data = 2;

  // Revision indicates the revision of the state represented by Data.
  optional int64 revision = 3;
}

// ControllerRevisionList is a resource containing a list of ControllerRevision objects.
message ControllerRevisionList {
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of ControllerRevisions
  repeated ControllerRevision items = 2;
}

// DEPRECATED - This group version of Deployment is deprecated by apps/v1beta2/Deployment. See the release notes for
// more information.
// Deployment enables declarative updates for Pods and ReplicaSets.
message Deployment {
  // Standard object metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the Deployment.
  // +optional
  optional DeploymentSpec spec = 2;

  // Most recently observed status of the Deployment.
  // +optional
  optional DeploymentStatus status = 3;
}

// DeploymentCondition describes the state of a deployment at a certain point.
message DeploymentCondition {
  // Type of deployment condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // The last time this condition was updated.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastUpdateTime = 6;

  // Last time the condition transitioned from one status to another.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 7;

  // The reason for the condition's last transition.
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  optional string message = 5;
}

// DeploymentList is a list of Deployments.
message DeploymentList {
  // Standard list metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of Deployments.
  repeated Deployment items = 2;
}

// DEPRECATED.
// DeploymentRollback stores the information required to rollback a deployment.
message DeploymentRollback {
  // Required: This must match the Name of a deployment.
  optional string name = 1;

  // The annotations to be updated to a deployment
  // +optional
  map<string, string> updatedAnnotations = 2;

  // The config of this deployment rollback.
  optional RollbackConfig rollbackTo = 3;
}

// DeploymentSpec is the specification of the desired behavior of the Deployment.
message DeploymentSpec {
  // Number of desired pods. This is a pointer to distinguish between explicit
  // zero and not specified. Defaults to 1.
  // +optional
  optional int32 replicas = 1;

  // Label selector for pods. Existing ReplicaSets whose pods are
  // selected by this will be the ones affected by this deployment.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // Template describes the pods that will be created.
  optional k8s.io.api.core.v1.PodTemplateSpec template = 3;

  // The deployment strategy to use to replace existing pods with new ones.
  // +optional
  // +patchStrategy=retainKeys
  optional DeploymentStrategy strategy = 4;

  // Minimum number of seconds for which a newly created pod should be ready
  // without any of its container crashing, for it to be considered available.
  // Defaults to 0 (pod will be considered available as soon as it is ready)
  // +optional
  optional int32 minReadySeconds = 5;

  // The number of old ReplicaSets to retain to allow rollback.
  // This is a pointer to distinguish between explicit zero and not specified.
  // Defaults to 2.
  // +optional
  optional int32 revisionHistoryLimit = 6;

  // Indicates that the deployment is paused.
  // +optional
  optional bool paused = 7;

  // DEPRECATED.
  // The config this deployment is rolling back to. Will be cleared after rollback is done.
  // +optional
  optional RollbackConfig rollbackTo = 8;

  // The maximum time in seconds for a deployment to make progress before it
  // is considered to be failed. The deployment controller will continue to
  // process failed deployments and a condition with a ProgressDeadlineExceeded
  // reason will be surfaced in the deployment status. Note that progress will
  // not be estimated during the time a deployment is paused. Defaults to 600s.
  // +optional
  optional int32 progressDeadlineSeconds = 9;
}

// DeploymentStatus is the most recently observed status of the Deployment.
message DeploymentStatus {
  // The generation observed by the deployment controller.
  // +optional
  optional int64 observedGeneration = 1;

  // Total number of non-terminated pods targeted by this deployment (their labels match the selector).
  // +optional
  optional int32 replicas = 2;

  // Total number of non-terminated pods targeted by this deployment that have the desired template spec.
  // +optional
  optional int32 updatedReplicas = 3;

  // readyReplicas is the number of pods targeted by this Deployment controller with a Ready Condition.
  // +optional
  optional int32 readyReplicas = 7;

  // Total number of available pods (ready for at least minReadySeconds) targeted by this deployment.
  // +optional
  optional int32 availableReplicas = 4;

  // Total number of unavailable pods targeted by this deployment. This is the total number of
  // pods that are still required for the deployment to have 100% available capacity. They may
  // either be pods that are running but not yet available or pods that still have not been created.
  // +optional
  optional int32 unavailableReplicas = 5;

  // Represents the latest available observations of a deployment's current state.
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated DeploymentCondition conditions = 6;

  // Count of hash collisions for the Deployment. The Deployment controller uses this
  // field as a collision avoidance mechanism when it needs to create the name for the
  // newest ReplicaSet.
  // +optional
  optional int32 collisionCount = 8;
}

// DeploymentStrategy describes how to replace existing pods with new ones.
message DeploymentStrategy {
  // Type of deployment. Can be "Recreate" or "RollingUpdate". Default is RollingUpdate.
  // +optional
  optional string type = 1;

  // Rolling update config params. Present only if DeploymentStrategyType =
  // RollingUpdate.
  // ---
  // TODO: Update this to follow our convention for oneOf, whatever we decide it
  // to be.
  // +optional
  optional RollingUpdateDeployment rollingUpdate = 2;
}

// DEPRECATED.
message RollbackConfig {
  // The revision to rollback to. If set to 0, rollback to the last revision.
  // +optional
  optional int64 revision = 1;
}

// Spec to control the desired behavior of rolling update.
message RollingUpdateDeployment {
  // The maximum number of pods that can be unavailable during the update.
  // Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
  // Absolute number is calculated from percentage by rounding down.
  // This can not be 0 if MaxSurge is 0.
  // Defaults to 25%.
  // Example: when this is set to 30%, the old ReplicaSet can be scaled down to 70% of desired pods
  // immediately when the rolling update starts. Once new pods are ready, old ReplicaSet
  // can be scaled down further, followed by scaling up the new ReplicaSet, ensuring
  // that the total number of pods available at all times during the update is at
  // least 70% of desired pods.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnavailable = 1;

  // The maximum number of pods that can be scheduled above the desired number of
  // pods.
  // Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
  // This can not be 0 if MaxUnavailable is 0.
  // Absolute number is calculated from percentage by rounding up.
  // Defaults to 25%.
  // Example: when this is set to 30%, the new ReplicaSet can be scaled up immediately when
  // the rolling update starts, such that the total number of old and new pods do not exceed
  // 130% of desired pods. Once old pods have been killed,
  // new ReplicaSet can be scaled up further, ensuring that total number of pods running
  // at any time during the update is at most 130% of desired pods.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxSurge = 2;
}

// RollingUpdateStatefulSetStrategy is used to communicate parameter for RollingUpdateStatefulSetStrategyType.
message RollingUpdateStatefulSetStrategy {
  // Partition indicates the ordinal at which the StatefulSet should be
  // partitioned.
  optional int32 partition = 1;
}

// Scale represents a scaling request for a resource.
message Scale {
  // Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // defines the behavior of the scale. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status.
  // +optional
  optional ScaleSpec spec = 2;

  // current status of the scale. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status. Read-only.
  // +optional
  optional ScaleStatus status = 3;
}

// ScaleSpec describes the attributes of a scale subresource
message ScaleSpec {
  // desired number of instances for the scaled object.
  // +optional
  optional int32 replicas = 1;
}

// ScaleStatus represents the current status of a scale subresource.
message ScaleStatus {
  // actual number of observed instances of the scaled object.
  optional int32 replicas = 1;

  // label query over pods that should match the replicas count. More info: http://kubernetes.io/docs/user-guide/labels#label-selectors
  // +optional
  map<string, string> selector = 2;

  // label selector for pods that should match the replicas count. This is a serializated
  // version of both map-based and more expressive set-based selectors. This is done to
  // avoid introspection in the clients. The string will be in the same format as the
  // query-param syntax. If the target type only supports map-based selectors, both this
  // field and map-based selector field are populated.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors
  // +optional
  optional string targetSelector = 3;
}

// DEPRECATED - This group version of StatefulSet is deprecated by apps/v1beta2/StatefulSet. See the release notes for
// more information.
// StatefulSet represents a set of pods with consistent identities.
// Identities are defined as:
//  - Network: A single stable DNS and hostname.
//  - Storage: As many VolumeClaims as requested.
// The StatefulSet guarantees that a given network identity will always
// map to the same storage identity.
message StatefulSet {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of pods in this set.
  // +optional
  optional StatefulSetSpec spec = 2;

  // Status is the current status of Pods in this StatefulSet. This data
  // may be out of date by some window of time.
  // +optional
  optional StatefulSetStatus status = 3;
}

// StatefulSetCondition describes the state of a statefulset at a certain point.
message StatefulSetCondition {
  // Type of statefulset condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // The reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  // +optional
  optional string message = 5;
}

// StatefulSetList is a collection of StatefulSets.
message StatefulSetList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  repeated StatefulSet items = 2;
}

// StatefulSetPersistentVolumeClaimRetentionPolicy describes the policy used for PVCs
// created from the StatefulSet VolumeClaimTemplates.
message StatefulSetPersistentVolumeClaimRetentionPolicy {
  // WhenDeleted specifies what happens to PVCs created from StatefulSet
  // VolumeClaimTemplates when the StatefulSet is deleted. The default policy
  // of `Retain` causes PVCs to not be affected by StatefulSet deletion. The
  // `Delete` policy causes those PVCs to be deleted.
  optional string whenDeleted = 1;

  // WhenScaled specifies what happens to PVCs created from StatefulSet
  // VolumeClaimTemplates when the StatefulSet is scaled down. The default
  // policy of `Retain` causes PVCs to not be affected by a scaledown. The
  // `Delete` policy causes the associated PVCs for any excess pods above
  // the replica count to be deleted.
  optional string whenScaled = 2;
}

// A StatefulSetSpec is the specification of a StatefulSet.
message StatefulSetSpec {
  // replicas is the desired number of replicas of the given Template.
  // These are replicas in the sense that they are instantiations of the
  // same Template, but individual replicas also have a consistent identity.
  // If unspecified, defaults to 1.
  // TODO: Consider a rename of this field.
  // +optional
  optional int32 replicas = 1;

  // selector is a label query over pods that should match the replica count.
  // If empty, defaulted to labels on the pod template.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // template is the object that describes the pod that will be created if
  // insufficient replicas are detected. Each pod stamped out by the StatefulSet
  // will fulfill this Template, but have a unique identity from the rest
  // of the StatefulSet.
  optional k8s.io.api.core.v1.PodTemplateSpec template = 3;

  // volumeClaimTemplates is a list of claims that pods are allowed to reference.
  // The StatefulSet controller is responsible for mapping network identities to
  // claims in a way that maintains the identity of a pod. Every claim in
  // this list must have at least one matching (by name) volumeMount in one
  // container in the template. A claim in this list takes precedence over
  // any volumes in the template, with the same name.
  // TODO: Define the behavior if a claim already exists with the same name.
  // +optional
  repeated k8s.io.api.core.v1.PersistentVolumeClaim volumeClaimTemplates = 4;

  // serviceName is the name of the service that governs this StatefulSet.
  // This service must exist before the StatefulSet, and is responsible for
  // the network identity of the set. Pods get DNS/hostnames that follow the
  // pattern: pod-specific-string.serviceName.default.svc.cluster.local
  // where "pod-specific-string" is managed by the StatefulSet controller.
  optional string serviceName = 5;

  // podManagementPolicy controls how pods are created during initial scale up,
  // when replacing pods on nodes, or when scaling down. The default policy is
  // `OrderedReady`, where pods are created in increasing order (pod-0, then
  // pod-1, etc) and the controller will wait until each pod is ready before
  // continuing. When scaling down, the pods are removed in the opposite order.
  // The alternative policy is `Parallel` which will create pods in parallel
  // to match the desired scale without waiting, and on scale down will delete
  // all pods at once.
  // +optional
  optional string podManagementPolicy = 6;

  // updateStrategy indicates the StatefulSetUpdateStrategy that will be
  // employed to update Pods in the StatefulSet when a revision is made to
  // Template.
  optional StatefulSetUpdateStrategy updateStrategy = 7;

  // revisionHistoryLimit is the maximum number of revisions that will
  // be maintained in the StatefulSet's revision history. The revision history
  // consists of all revisions not represented by a currently applied
  // StatefulSetSpec version. The default value is 10.
  optional int32 revisionHistoryLimit = 8;

  // Minimum number of seconds for which a newly created pod should be ready
  // without any of its container crashing for it to be considered available.
  // Defaults to 0 (pod will be considered available as soon as it is ready)
  // This is an alpha field and requires enabling StatefulSetMinReadySeconds feature gate.
  // +optional
  optional int32 minReadySeconds = 9;

  // PersistentVolumeClaimRetentionPolicy describes the policy used for PVCs created from
  // the StatefulSet VolumeClaimTemplates. This requires the
  // StatefulSetAutoDeletePVC feature gate to be enabled, which is alpha.
  // +optional
  optional StatefulSetPersistentVolumeClaimRetentionPolicy persistentVolumeClaimRetentionPolicy = 10;
}

// StatefulSetStatus represents the current state of a StatefulSet.
message StatefulSetStatus {
  // observedGeneration is the most recent generation observed for this StatefulSet. It corresponds to the
  // StatefulSet's generation, which is updated on mutation by the API Server.
  // +optional
  optional int64 observedGeneration = 1;

  // replicas is the number of Pods created by the StatefulSet controller.
  optional int32 replicas = 2;

  // readyReplicas is the number of pods created by this StatefulSet controller with a Ready Condition.
  optional int32 readyReplicas = 3;

  // currentReplicas is the number of Pods created by the StatefulSet controller from the StatefulSet version
  // indicated by currentRevision.
  optional int32 currentReplicas = 4;

  // updatedReplicas is the number of Pods created by the StatefulSet controller from the StatefulSet version
  // indicated by updateRevision.
  optional int32 updatedReplicas = 5;

  // currentRevision, if not empty, indicates the version of the StatefulSet used to generate Pods in the
  // sequence [0,currentReplicas).
  optional string currentRevision = 6;

  // updateRevision, if not empty, indicates the version of the StatefulSet used to generate Pods in the sequence
  // [replicas-updatedReplicas,replicas)
  optional string updateRevision = 7;

  // collisionCount is the count of hash collisions for the StatefulSet. The StatefulSet controller
  // uses this field as a collision avoidance mechanism when it needs to create the name for the
  // newest ControllerRevision.
  // +optional
  optional int32 collisionCount = 9;

  // Represents the latest available observations of a statefulset's current state.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated StatefulSetCondition conditions = 10;

  // Total number of available pods (ready for at least minReadySeconds) targeted by this StatefulSet.
  // This is a beta field and enabled/disabled by StatefulSetMinReadySeconds feature gate.
  optional int32 availableReplicas = 11;
}

// StatefulSetUpdateStrategy indicates the strategy that the StatefulSet
// controller will use to perform updates. It includes any additional parameters
// necessary to perform the update for the indicated strategy.
message StatefulSetUpdateStrategy {
  // Type indicates the type of the StatefulSetUpdateStrategy.
  optional string type = 1;

  // RollingUpdate is used to communicate parameters when Type is RollingUpdateStatefulSetStrategyType.
  optional RollingUpdateStatefulSetStrategy rollingUpdate = 2;
}

