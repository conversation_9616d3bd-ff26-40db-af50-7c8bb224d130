// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package sts provides the client and types for making API
// requests to AWS Security Token Service.
//
// Security Token Service (STS) enables you to request temporary, limited-privilege
// credentials for Identity and Access Management (IAM) users or for users that
// you authenticate (federated users). This guide provides descriptions of the
// STS API. For more information about using this service, see Temporary Security
// Credentials (https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html).
//
// See https://docs.aws.amazon.com/goto/WebAPI/sts-2011-06-15 for more information on this service.
//
// See sts package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/sts/
//
// Using the Client
//
// To contact AWS Security Token Service with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the AWS Security Token Service client STS for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/sts/#New
package sts
