/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	resource "k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// ExternalMetricStatusApplyConfiguration represents an declarative configuration of the ExternalMetricStatus type for use
// with apply.
type ExternalMetricStatusApplyConfiguration struct {
	MetricName          *string                             `json:"metricName,omitempty"`
	MetricSelector      *v1.LabelSelectorApplyConfiguration `json:"metricSelector,omitempty"`
	CurrentValue        *resource.Quantity                  `json:"currentValue,omitempty"`
	CurrentAverageValue *resource.Quantity                  `json:"currentAverageValue,omitempty"`
}

// ExternalMetricStatusApplyConfiguration constructs an declarative configuration of the ExternalMetricStatus type for use with
// apply.
func ExternalMetricStatus() *ExternalMetricStatusApplyConfiguration {
	return &ExternalMetricStatusApplyConfiguration{}
}

// WithMetricName sets the MetricName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MetricName field is set to the value of the last call.
func (b *ExternalMetricStatusApplyConfiguration) WithMetricName(value string) *ExternalMetricStatusApplyConfiguration {
	b.MetricName = &value
	return b
}

// WithMetricSelector sets the MetricSelector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MetricSelector field is set to the value of the last call.
func (b *ExternalMetricStatusApplyConfiguration) WithMetricSelector(value *v1.LabelSelectorApplyConfiguration) *ExternalMetricStatusApplyConfiguration {
	b.MetricSelector = value
	return b
}

// WithCurrentValue sets the CurrentValue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CurrentValue field is set to the value of the last call.
func (b *ExternalMetricStatusApplyConfiguration) WithCurrentValue(value resource.Quantity) *ExternalMetricStatusApplyConfiguration {
	b.CurrentValue = &value
	return b
}

// WithCurrentAverageValue sets the CurrentAverageValue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CurrentAverageValue field is set to the value of the last call.
func (b *ExternalMetricStatusApplyConfiguration) WithCurrentAverageValue(value resource.Quantity) *ExternalMetricStatusApplyConfiguration {
	b.CurrentAverageValue = &value
	return b
}
