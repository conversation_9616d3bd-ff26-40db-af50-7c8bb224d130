{{- if .Values.grafana.sidecar.datasources.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "loki-stack.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "loki-stack.name" . }}
    chart: {{ template "loki-stack.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    grafana_datasource: "1"
data:
  loki-stack-datasource.yaml: |-
    apiVersion: 1
    datasources:
{{- if .Values.loki.enabled }}
    - name: Loki
      type: loki
      access: proxy
      url: http://{{(include "loki.serviceName" .)}}:{{ .Values.loki.service.port }}
      version: 1
{{- end }}
{{- if .Values.prometheus.enabled }}
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://{{ include "prometheus.fullname" .}}:{{ .Values.prometheus.server.service.servicePort }}{{ .Values.prometheus.server.prefixURL }}
      version: 1
{{- end }}
{{- end }}
