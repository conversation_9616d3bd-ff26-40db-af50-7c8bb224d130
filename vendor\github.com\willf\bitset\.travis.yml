language: go

sudo: false

branches:
  except:
    - release

branches:
  only:
    - master
    - travis

go:
  - "1.11.x"
  - tip

matrix:
  allow_failures:
    - go: tip

before_install:
  - if [ -n "$GH_USER" ]; then git config --global github.user ${GH_USER}; fi;
  - if [ -n "$GH_TOKEN" ]; then git config --global github.token ${GH_TOKEN}; fi;
  - go get github.com/mattn/goveralls

before_script:
  - make deps

script:
  - make qa

after_failure:
  - cat ./target/test/report.xml

after_success:
  - if [ "$TRAVIS_GO_VERSION" = "1.11.1" ]; then $HOME/gopath/bin/goveralls -covermode=count -coverprofile=target/report/coverage.out -service=travis-ci; fi;
