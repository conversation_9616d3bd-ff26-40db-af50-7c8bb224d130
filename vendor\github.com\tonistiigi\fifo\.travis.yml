language: go
go:
    - 1.11.x
    - tip

install:
  - go get -u github.com/vbatts/git-validation
  - go get -u github.com/kunalkushwaha/ltag

before_script:
  - pushd ..; git clone https://github.com/containerd/project; popd

script:
  - DCO_VERBOSITY=-q ../project/script/validate/dco
  - ../project/script/validate/fileheader ../project/
  - make deps
  - make fmt
  - make vet
  - make test

after_success:
    - bash <(curl -s https://codecov.io/bash)
