{{- if .Values.serviceMonitor.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "promtail.fullname" . }}-headless
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "promtail.name" . }}
    chart: {{ template "promtail.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.config.server.http_listen_port }}
      protocol: TCP
      name: http-metrics
      targetPort: http-metrics
  selector:
    app: {{ template "promtail.name" . }}
    release: {{ .Release.Name }}
{{- end }}
