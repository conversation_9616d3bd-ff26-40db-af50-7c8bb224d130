// Copyright 2015 go-swagger maintainers
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package runtime

import (
	"encoding/xml"
	"io"
)

// XMLConsumer creates a new XML consumer
func XMLConsumer() Consumer {
	return ConsumerFunc(func(reader io.Reader, data interface{}) error {
		dec := xml.NewDecoder(reader)
		return dec.Decode(data)
	})
}

// XMLProducer creates a new XML producer
func XMLProducer() Producer {
	return ProducerFunc(func(writer io.Writer, data interface{}) error {
		enc := xml.<PERSON><PERSON><PERSON><PERSON>(writer)
		return enc.Encode(data)
	})
}
