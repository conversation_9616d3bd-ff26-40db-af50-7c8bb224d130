sudo: false
language: go
go:
  - 1.3.x
  - 1.5.x
  - 1.6.x
  - 1.7.x
  - 1.8.x
  - 1.9.x
  - master
matrix:
  allow_failures:
    - go: master
  fast_finish: true
install:
  - # Do nothing. This is needed to prevent default install action "go get -t -v ./..." from happening here (we want it to happen inside script step).
script:
  - go get -t -v ./...
  - diff -u <(echo -n) <(gofmt -d -s .)
  - go tool vet .
  - go test -v -race ./...
