syntax = "proto3";

package stats;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package = "github.com/grafana/loki/pkg/logqlmodel/stats";
option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;

// Result contains LogQL query statistics.
message Result {
  Summary summary = 1 [
    (gogoproto.nullable) = false,
    (gogoproto.jsontag) = "summary"
  ];
  Querier querier = 2 [
    (gogoproto.nullable) = false,
    (gogoproto.jsontag) = "querier"
  ];
  Ingester ingester = 3 [
    (gogoproto.nullable) = false,
    (gogoproto.jsontag) = "ingester"
  ];
}

// Summary is the summary of a query statistics.
message Summary {
  // Total bytes processed per second.
  int64 bytesProcessedPerSecond = 1 [(gogoproto.jsontag) = "bytesProcessedPerSecond"];
  // Total lines processed per second.
  int64 linesProcessedPerSecond = 2 [(gogoproto.jsontag) = "linesProcessedPerSecond"];
  // Total bytes processed.
  int64 totalBytesProcessed = 3 [(gogoproto.jsontag) = "totalBytesProcessed"];
  // Total lines processed.
  int64 totalLinesProcessed = 4 [(gogoproto.jsontag) = "totalLinesProcessed"];
  // Execution time in seconds.
  // In addition to internal calculations this is also returned by the HTTP API.
  // Grafana expects time values to be returned in seconds as float.
  double execTime = 5 [(gogoproto.jsontag) = "execTime"];
  // Queue time in seconds.
  // In addition to internal calculations this is also returned by the HTTP API.
  // Grafana expects time values to be returned in seconds as float.
  double queueTime = 6 [(gogoproto.jsontag) = "queueTime"];
  // Total of subqueries created to fulfill this query.
  int64 subqueries = 7 [(gogoproto.jsontag) = "subqueries"];
  // Total number of result entries returned
  int64 totalEntriesReturned = 8 [(gogoproto.jsontag) = "totalEntriesReturned"];
}

message Querier {
  Store store = 1 [
    (gogoproto.nullable) = false,
    (gogoproto.jsontag) = "store"
  ];
}

message Ingester {
  // Total ingester reached for this query.
  int32 totalReached = 1 [(gogoproto.jsontag) = "totalReached"];
  // Total of chunks matched by the query from ingesters
  int64 totalChunksMatched = 2 [(gogoproto.jsontag) = "totalChunksMatched"];
  // Total of batches sent from ingesters.
  int64 totalBatches = 3 [(gogoproto.jsontag) = "totalBatches"];
  // Total lines sent by ingesters.
  int64 totalLinesSent = 4 [(gogoproto.jsontag) = "totalLinesSent"];

  Store store = 5 [
    (gogoproto.nullable) = false,
    (gogoproto.jsontag) = "store"
  ];
}

message Store {
  // The total of chunk reference fetched from index.
  int64 totalChunksRef = 1 [(gogoproto.jsontag) = "totalChunksRef"];
  // Total number of chunks fetched.
  int64 totalChunksDownloaded = 2 [(gogoproto.jsontag) = "totalChunksDownloaded"];
  // Time spent fetching chunks in nanoseconds.
  int64 chunksDownloadTime = 3 [(gogoproto.jsontag) = "chunksDownloadTime"];

  Chunk chunk = 4 [
    (gogoproto.nullable) = false,
    (gogoproto.jsontag) = "chunk"
  ];
}

message Chunk {
  // Total bytes processed but was already in memory. (found in the headchunk)
  int64 headChunkBytes = 4 [(gogoproto.jsontag) = "headChunkBytes"];
  // Total lines processed but was already in memory. (found in the headchunk)
  int64 headChunkLines = 5 [(gogoproto.jsontag) = "headChunkLines"];
  // Total bytes decompressed and processed from chunks.
  int64 decompressedBytes = 6 [(gogoproto.jsontag) = "decompressedBytes"];
  // Total lines decompressed and processed from chunks.
  int64 decompressedLines = 7 [(gogoproto.jsontag) = "decompressedLines"];
  // Total bytes of compressed chunks (blocks) processed.
  int64 compressedBytes = 8 [(gogoproto.jsontag) = "compressedBytes"];
  // Total duplicates found while processing.
  int64 totalDuplicates = 9 [(gogoproto.jsontag) = "totalDuplicates"];
}
