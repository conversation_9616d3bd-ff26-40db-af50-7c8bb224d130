/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.storage.v1alpha1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1alpha1";

// CSIStorageCapacity stores the result of one CSI GetCapacity call.
// For a given StorageClass, this describes the available capacity in a
// particular topology segment.  This can be used when considering where to
// instantiate new PersistentVolumes.
//
// For example this can express things like:
// - StorageClass "standard" has "1234 GiB" available in "topology.kubernetes.io/zone=us-east1"
// - StorageClass "localssd" has "10 GiB" available in "kubernetes.io/hostname=knode-abc123"
//
// The following three cases all imply that no capacity is available for
// a certain combination:
// - no object exists with suitable topology and storage class name
// - such an object exists, but the capacity is unset
// - such an object exists, but the capacity is zero
//
// The producer of these objects can decide which approach is more suitable.
//
// They are consumed by the kube-scheduler if the CSIStorageCapacity beta feature gate
// is enabled there and a CSI driver opts into capacity-aware scheduling with
// CSIDriver.StorageCapacity.
message CSIStorageCapacity {
  // Standard object's metadata. The name has no particular meaning. It must be
  // be a DNS subdomain (dots allowed, 253 characters). To ensure that
  // there are no conflicts with other CSI drivers on the cluster, the recommendation
  // is to use csisc-<uuid>, a generated name, or a reverse-domain name which ends
  // with the unique CSI driver name.
  //
  // Objects are namespaced.
  //
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // NodeTopology defines which nodes have access to the storage
  // for which capacity was reported. If not set, the storage is
  // not accessible from any node in the cluster. If empty, the
  // storage is accessible from all nodes. This field is
  // immutable.
  //
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector nodeTopology = 2;

  // The name of the StorageClass that the reported capacity applies to.
  // It must meet the same requirements as the name of a StorageClass
  // object (non-empty, DNS subdomain). If that object no longer exists,
  // the CSIStorageCapacity object is obsolete and should be removed by its
  // creator.
  // This field is immutable.
  optional string storageClassName = 3;

  // Capacity is the value reported by the CSI driver in its GetCapacityResponse
  // for a GetCapacityRequest with topology and parameters that match the
  // previous fields.
  //
  // The semantic is currently (CSI spec 1.2) defined as:
  // The available capacity, in bytes, of the storage that can be used
  // to provision volumes. If not set, that information is currently
  // unavailable and treated like zero capacity.
  //
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity capacity = 4;

  // MaximumVolumeSize is the value reported by the CSI driver in its GetCapacityResponse
  // for a GetCapacityRequest with topology and parameters that match the
  // previous fields.
  //
  // This is defined since CSI spec 1.4.0 as the largest size
  // that may be used in a
  // CreateVolumeRequest.capacity_range.required_bytes field to
  // create a volume with the same parameters as those in
  // GetCapacityRequest. The corresponding value in the Kubernetes
  // API is ResourceRequirements.Requests in a volume claim.
  //
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity maximumVolumeSize = 5;
}

// CSIStorageCapacityList is a collection of CSIStorageCapacity objects.
message CSIStorageCapacityList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of CSIStorageCapacity objects.
  // +listType=map
  // +listMapKey=name
  repeated CSIStorageCapacity items = 2;
}

// VolumeAttachment captures the intent to attach or detach the specified volume
// to/from the specified node.
//
// VolumeAttachment objects are non-namespaced.
message VolumeAttachment {
  // Standard object metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired attach/detach volume behavior.
  // Populated by the Kubernetes system.
  optional VolumeAttachmentSpec spec = 2;

  // Status of the VolumeAttachment request.
  // Populated by the entity completing the attach or detach
  // operation, i.e. the external-attacher.
  // +optional
  optional VolumeAttachmentStatus status = 3;
}

// VolumeAttachmentList is a collection of VolumeAttachment objects.
message VolumeAttachmentList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of VolumeAttachments
  repeated VolumeAttachment items = 2;
}

// VolumeAttachmentSource represents a volume that should be attached.
// Right now only PersistenVolumes can be attached via external attacher,
// in future we may allow also inline volumes in pods.
// Exactly one member can be set.
message VolumeAttachmentSource {
  // Name of the persistent volume to attach.
  // +optional
  optional string persistentVolumeName = 1;

  // inlineVolumeSpec contains all the information necessary to attach
  // a persistent volume defined by a pod's inline VolumeSource. This field
  // is populated only for the CSIMigration feature. It contains
  // translated fields from a pod's inline VolumeSource to a
  // PersistentVolumeSpec. This field is alpha-level and is only
  // honored by servers that enabled the CSIMigration feature.
  // +optional
  optional k8s.io.api.core.v1.PersistentVolumeSpec inlineVolumeSpec = 2;
}

// VolumeAttachmentSpec is the specification of a VolumeAttachment request.
message VolumeAttachmentSpec {
  // Attacher indicates the name of the volume driver that MUST handle this
  // request. This is the name returned by GetPluginName().
  optional string attacher = 1;

  // Source represents the volume that should be attached.
  optional VolumeAttachmentSource source = 2;

  // The node that the volume should be attached to.
  optional string nodeName = 3;
}

// VolumeAttachmentStatus is the status of a VolumeAttachment request.
message VolumeAttachmentStatus {
  // Indicates the volume is successfully attached.
  // This field must only be set by the entity completing the attach
  // operation, i.e. the external-attacher.
  optional bool attached = 1;

  // Upon successful attach, this field is populated with any
  // information returned by the attach operation that must be passed
  // into subsequent WaitForAttach or Mount calls.
  // This field must only be set by the entity completing the attach
  // operation, i.e. the external-attacher.
  // +optional
  map<string, string> attachmentMetadata = 2;

  // The last error encountered during attach operation, if any.
  // This field must only be set by the entity completing the attach
  // operation, i.e. the external-attacher.
  // +optional
  optional VolumeError attachError = 3;

  // The last error encountered during detach operation, if any.
  // This field must only be set by the entity completing the detach
  // operation, i.e. the external-attacher.
  // +optional
  optional VolumeError detachError = 4;
}

// VolumeError captures an error encountered during a volume operation.
message VolumeError {
  // Time the error was encountered.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time time = 1;

  // String detailing the error encountered during Attach or Detach operation.
  // This string maybe logged, so it should not contain sensitive
  // information.
  // +optional
  optional string message = 2;
}

