// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.13.0
// source: google/bigtable/admin/v2/instance.proto

package admin

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Possible states of an instance.
type Instance_State int32

const (
	// The state of the instance could not be determined.
	Instance_STATE_NOT_KNOWN Instance_State = 0
	// The instance has been successfully created and can serve requests
	// to its tables.
	Instance_READY Instance_State = 1
	// The instance is currently being created, and may be destroyed
	// if the creation process encounters an error.
	Instance_CREATING Instance_State = 2
)

// Enum value maps for Instance_State.
var (
	Instance_State_name = map[int32]string{
		0: "STATE_NOT_KNOWN",
		1: "READY",
		2: "CREATING",
	}
	Instance_State_value = map[string]int32{
		"STATE_NOT_KNOWN": 0,
		"READY":           1,
		"CREATING":        2,
	}
)

func (x Instance_State) Enum() *Instance_State {
	p := new(Instance_State)
	*p = x
	return p
}

func (x Instance_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Instance_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_instance_proto_enumTypes[0].Descriptor()
}

func (Instance_State) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_instance_proto_enumTypes[0]
}

func (x Instance_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Instance_State.Descriptor instead.
func (Instance_State) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{0, 0}
}

// The type of the instance.
type Instance_Type int32

const (
	// The type of the instance is unspecified. If set when creating an
	// instance, a `PRODUCTION` instance will be created. If set when updating
	// an instance, the type will be left unchanged.
	Instance_TYPE_UNSPECIFIED Instance_Type = 0
	// An instance meant for production use. `serve_nodes` must be set
	// on the cluster.
	Instance_PRODUCTION Instance_Type = 1
	// The instance is meant for development and testing purposes only; it has
	// no performance or uptime guarantees and is not covered by SLA.
	// After a development instance is created, it can be upgraded by
	// updating the instance to type `PRODUCTION`. An instance created
	// as a production instance cannot be changed to a development instance.
	// When creating a development instance, `serve_nodes` on the cluster must
	// not be set.
	Instance_DEVELOPMENT Instance_Type = 2
)

// Enum value maps for Instance_Type.
var (
	Instance_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "PRODUCTION",
		2: "DEVELOPMENT",
	}
	Instance_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"PRODUCTION":       1,
		"DEVELOPMENT":      2,
	}
)

func (x Instance_Type) Enum() *Instance_Type {
	p := new(Instance_Type)
	*p = x
	return p
}

func (x Instance_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Instance_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_instance_proto_enumTypes[1].Descriptor()
}

func (Instance_Type) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_instance_proto_enumTypes[1]
}

func (x Instance_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Instance_Type.Descriptor instead.
func (Instance_Type) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{0, 1}
}

// Possible states of a cluster.
type Cluster_State int32

const (
	// The state of the cluster could not be determined.
	Cluster_STATE_NOT_KNOWN Cluster_State = 0
	// The cluster has been successfully created and is ready to serve requests.
	Cluster_READY Cluster_State = 1
	// The cluster is currently being created, and may be destroyed
	// if the creation process encounters an error.
	// A cluster may not be able to serve requests while being created.
	Cluster_CREATING Cluster_State = 2
	// The cluster is currently being resized, and may revert to its previous
	// node count if the process encounters an error.
	// A cluster is still capable of serving requests while being resized,
	// but may exhibit performance as if its number of allocated nodes is
	// between the starting and requested states.
	Cluster_RESIZING Cluster_State = 3
	// The cluster has no backing nodes. The data (tables) still
	// exist, but no operations can be performed on the cluster.
	Cluster_DISABLED Cluster_State = 4
)

// Enum value maps for Cluster_State.
var (
	Cluster_State_name = map[int32]string{
		0: "STATE_NOT_KNOWN",
		1: "READY",
		2: "CREATING",
		3: "RESIZING",
		4: "DISABLED",
	}
	Cluster_State_value = map[string]int32{
		"STATE_NOT_KNOWN": 0,
		"READY":           1,
		"CREATING":        2,
		"RESIZING":        3,
		"DISABLED":        4,
	}
)

func (x Cluster_State) Enum() *Cluster_State {
	p := new(Cluster_State)
	*p = x
	return p
}

func (x Cluster_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Cluster_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_bigtable_admin_v2_instance_proto_enumTypes[2].Descriptor()
}

func (Cluster_State) Type() protoreflect.EnumType {
	return &file_google_bigtable_admin_v2_instance_proto_enumTypes[2]
}

func (x Cluster_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Cluster_State.Descriptor instead.
func (Cluster_State) EnumDescriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{3, 0}
}

// A collection of Bigtable [Tables][google.bigtable.admin.v2.Table] and
// the resources that serve them.
// All tables in an instance are served from all
// [Clusters][google.bigtable.admin.v2.Cluster] in the instance.
type Instance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique name of the instance. Values are of the form
	// `projects/{project}/instances/[a-z][a-z0-9\\-]+[a-z0-9]`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The descriptive name for this instance as it appears in UIs.
	// Can be changed at any time, but should be kept globally unique
	// to avoid confusion.
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// (`OutputOnly`)
	// The current state of the instance.
	State Instance_State `protobuf:"varint,3,opt,name=state,proto3,enum=google.bigtable.admin.v2.Instance_State" json:"state,omitempty"`
	// The type of the instance. Defaults to `PRODUCTION`.
	Type Instance_Type `protobuf:"varint,4,opt,name=type,proto3,enum=google.bigtable.admin.v2.Instance_Type" json:"type,omitempty"`
	// Labels are a flexible and lightweight mechanism for organizing cloud
	// resources into groups that reflect a customer's organizational needs and
	// deployment strategies. They can be used to filter resources and aggregate
	// metrics.
	//
	// * Label keys must be between 1 and 63 characters long and must conform to
	//   the regular expression: `[\p{Ll}\p{Lo}][\p{Ll}\p{Lo}\p{N}_-]{0,62}`.
	// * Label values must be between 0 and 63 characters long and must conform to
	//   the regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}`.
	// * No more than 64 labels can be associated with a given resource.
	// * Keys and values must both be under 128 bytes.
	Labels map[string]string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Output only. A server-assigned timestamp representing when this Instance was created.
	// For instances created before this field was added (August 2021), this value
	// is `seconds: 0, nanos: 1`.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *Instance) Reset() {
	*x = Instance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Instance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Instance) ProtoMessage() {}

func (x *Instance) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Instance.ProtoReflect.Descriptor instead.
func (*Instance) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{0}
}

func (x *Instance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Instance) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Instance) GetState() Instance_State {
	if x != nil {
		return x.State
	}
	return Instance_STATE_NOT_KNOWN
}

func (x *Instance) GetType() Instance_Type {
	if x != nil {
		return x.Type
	}
	return Instance_TYPE_UNSPECIFIED
}

func (x *Instance) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Instance) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// The Autoscaling targets for a Cluster. These determine the recommended nodes.
type AutoscalingTargets struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The cpu utilization that the Autoscaler should be trying to achieve.
	// This number is on a scale from 0 (no utilization) to
	// 100 (total utilization).
	CpuUtilizationPercent int32 `protobuf:"varint,2,opt,name=cpu_utilization_percent,json=cpuUtilizationPercent,proto3" json:"cpu_utilization_percent,omitempty"`
}

func (x *AutoscalingTargets) Reset() {
	*x = AutoscalingTargets{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoscalingTargets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoscalingTargets) ProtoMessage() {}

func (x *AutoscalingTargets) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoscalingTargets.ProtoReflect.Descriptor instead.
func (*AutoscalingTargets) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{1}
}

func (x *AutoscalingTargets) GetCpuUtilizationPercent() int32 {
	if x != nil {
		return x.CpuUtilizationPercent
	}
	return 0
}

// Limits for the number of nodes a Cluster can autoscale up/down to.
type AutoscalingLimits struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Minimum number of nodes to scale down to.
	MinServeNodes int32 `protobuf:"varint,1,opt,name=min_serve_nodes,json=minServeNodes,proto3" json:"min_serve_nodes,omitempty"`
	// Required. Maximum number of nodes to scale up to.
	MaxServeNodes int32 `protobuf:"varint,2,opt,name=max_serve_nodes,json=maxServeNodes,proto3" json:"max_serve_nodes,omitempty"`
}

func (x *AutoscalingLimits) Reset() {
	*x = AutoscalingLimits{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoscalingLimits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoscalingLimits) ProtoMessage() {}

func (x *AutoscalingLimits) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoscalingLimits.ProtoReflect.Descriptor instead.
func (*AutoscalingLimits) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{2}
}

func (x *AutoscalingLimits) GetMinServeNodes() int32 {
	if x != nil {
		return x.MinServeNodes
	}
	return 0
}

func (x *AutoscalingLimits) GetMaxServeNodes() int32 {
	if x != nil {
		return x.MaxServeNodes
	}
	return 0
}

// A resizable group of nodes in a particular cloud location, capable
// of serving all [Tables][google.bigtable.admin.v2.Table] in the parent
// [Instance][google.bigtable.admin.v2.Instance].
type Cluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique name of the cluster. Values are of the form
	// `projects/{project}/instances/{instance}/clusters/[a-z][-a-z0-9]*`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// (`CreationOnly`)
	// The location where this cluster's nodes and storage reside. For best
	// performance, clients should be located as close as possible to this
	// cluster. Currently only zones are supported, so values should be of the
	// form `projects/{project}/locations/{zone}`.
	Location string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	// The current state of the cluster.
	State Cluster_State `protobuf:"varint,3,opt,name=state,proto3,enum=google.bigtable.admin.v2.Cluster_State" json:"state,omitempty"`
	// The number of nodes allocated to this cluster. More nodes enable higher
	// throughput and more consistent performance.
	ServeNodes int32 `protobuf:"varint,4,opt,name=serve_nodes,json=serveNodes,proto3" json:"serve_nodes,omitempty"`
	// Types that are assignable to Config:
	//	*Cluster_ClusterConfig_
	Config isCluster_Config `protobuf_oneof:"config"`
	// (`CreationOnly`)
	// The type of storage used by this cluster to serve its
	// parent instance's tables, unless explicitly overridden.
	DefaultStorageType StorageType `protobuf:"varint,5,opt,name=default_storage_type,json=defaultStorageType,proto3,enum=google.bigtable.admin.v2.StorageType" json:"default_storage_type,omitempty"`
	// Immutable. The encryption configuration for CMEK-protected clusters.
	EncryptionConfig *Cluster_EncryptionConfig `protobuf:"bytes,6,opt,name=encryption_config,json=encryptionConfig,proto3" json:"encryption_config,omitempty"`
}

func (x *Cluster) Reset() {
	*x = Cluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster) ProtoMessage() {}

func (x *Cluster) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster.ProtoReflect.Descriptor instead.
func (*Cluster) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{3}
}

func (x *Cluster) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Cluster) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Cluster) GetState() Cluster_State {
	if x != nil {
		return x.State
	}
	return Cluster_STATE_NOT_KNOWN
}

func (x *Cluster) GetServeNodes() int32 {
	if x != nil {
		return x.ServeNodes
	}
	return 0
}

func (m *Cluster) GetConfig() isCluster_Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (x *Cluster) GetClusterConfig() *Cluster_ClusterConfig {
	if x, ok := x.GetConfig().(*Cluster_ClusterConfig_); ok {
		return x.ClusterConfig
	}
	return nil
}

func (x *Cluster) GetDefaultStorageType() StorageType {
	if x != nil {
		return x.DefaultStorageType
	}
	return StorageType_STORAGE_TYPE_UNSPECIFIED
}

func (x *Cluster) GetEncryptionConfig() *Cluster_EncryptionConfig {
	if x != nil {
		return x.EncryptionConfig
	}
	return nil
}

type isCluster_Config interface {
	isCluster_Config()
}

type Cluster_ClusterConfig_ struct {
	// Configuration for this cluster.
	ClusterConfig *Cluster_ClusterConfig `protobuf:"bytes,7,opt,name=cluster_config,json=clusterConfig,proto3,oneof"`
}

func (*Cluster_ClusterConfig_) isCluster_Config() {}

// A configuration object describing how Cloud Bigtable should treat traffic
// from a particular end user application.
type AppProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// (`OutputOnly`)
	// The unique name of the app profile. Values are of the form
	// `projects/{project}/instances/{instance}/appProfiles/[_a-zA-Z0-9][-_.a-zA-Z0-9]*`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Strongly validated etag for optimistic concurrency control. Preserve the
	// value returned from `GetAppProfile` when calling `UpdateAppProfile` to
	// fail the request if there has been a modification in the mean time. The
	// `update_mask` of the request need not include `etag` for this protection
	// to apply.
	// See [Wikipedia](https://en.wikipedia.org/wiki/HTTP_ETag) and
	// [RFC 7232](https://tools.ietf.org/html/rfc7232#section-2.3) for more
	// details.
	Etag string `protobuf:"bytes,2,opt,name=etag,proto3" json:"etag,omitempty"`
	// Optional long form description of the use case for this AppProfile.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The routing policy for all read/write requests that use this app profile.
	// A value must be explicitly set.
	//
	// Types that are assignable to RoutingPolicy:
	//	*AppProfile_MultiClusterRoutingUseAny_
	//	*AppProfile_SingleClusterRouting_
	RoutingPolicy isAppProfile_RoutingPolicy `protobuf_oneof:"routing_policy"`
}

func (x *AppProfile) Reset() {
	*x = AppProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppProfile) ProtoMessage() {}

func (x *AppProfile) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppProfile.ProtoReflect.Descriptor instead.
func (*AppProfile) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{4}
}

func (x *AppProfile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AppProfile) GetEtag() string {
	if x != nil {
		return x.Etag
	}
	return ""
}

func (x *AppProfile) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (m *AppProfile) GetRoutingPolicy() isAppProfile_RoutingPolicy {
	if m != nil {
		return m.RoutingPolicy
	}
	return nil
}

func (x *AppProfile) GetMultiClusterRoutingUseAny() *AppProfile_MultiClusterRoutingUseAny {
	if x, ok := x.GetRoutingPolicy().(*AppProfile_MultiClusterRoutingUseAny_); ok {
		return x.MultiClusterRoutingUseAny
	}
	return nil
}

func (x *AppProfile) GetSingleClusterRouting() *AppProfile_SingleClusterRouting {
	if x, ok := x.GetRoutingPolicy().(*AppProfile_SingleClusterRouting_); ok {
		return x.SingleClusterRouting
	}
	return nil
}

type isAppProfile_RoutingPolicy interface {
	isAppProfile_RoutingPolicy()
}

type AppProfile_MultiClusterRoutingUseAny_ struct {
	// Use a multi-cluster routing policy.
	MultiClusterRoutingUseAny *AppProfile_MultiClusterRoutingUseAny `protobuf:"bytes,5,opt,name=multi_cluster_routing_use_any,json=multiClusterRoutingUseAny,proto3,oneof"`
}

type AppProfile_SingleClusterRouting_ struct {
	// Use a single-cluster routing policy.
	SingleClusterRouting *AppProfile_SingleClusterRouting `protobuf:"bytes,6,opt,name=single_cluster_routing,json=singleClusterRouting,proto3,oneof"`
}

func (*AppProfile_MultiClusterRoutingUseAny_) isAppProfile_RoutingPolicy() {}

func (*AppProfile_SingleClusterRouting_) isAppProfile_RoutingPolicy() {}

// Autoscaling config for a cluster.
type Cluster_ClusterAutoscalingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Autoscaling limits for this cluster.
	AutoscalingLimits *AutoscalingLimits `protobuf:"bytes,1,opt,name=autoscaling_limits,json=autoscalingLimits,proto3" json:"autoscaling_limits,omitempty"`
	// Required. Autoscaling targets for this cluster.
	AutoscalingTargets *AutoscalingTargets `protobuf:"bytes,2,opt,name=autoscaling_targets,json=autoscalingTargets,proto3" json:"autoscaling_targets,omitempty"`
}

func (x *Cluster_ClusterAutoscalingConfig) Reset() {
	*x = Cluster_ClusterAutoscalingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster_ClusterAutoscalingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster_ClusterAutoscalingConfig) ProtoMessage() {}

func (x *Cluster_ClusterAutoscalingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster_ClusterAutoscalingConfig.ProtoReflect.Descriptor instead.
func (*Cluster_ClusterAutoscalingConfig) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Cluster_ClusterAutoscalingConfig) GetAutoscalingLimits() *AutoscalingLimits {
	if x != nil {
		return x.AutoscalingLimits
	}
	return nil
}

func (x *Cluster_ClusterAutoscalingConfig) GetAutoscalingTargets() *AutoscalingTargets {
	if x != nil {
		return x.AutoscalingTargets
	}
	return nil
}

// Configuration for a cluster.
type Cluster_ClusterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Autoscaling configuration for this cluster.
	ClusterAutoscalingConfig *Cluster_ClusterAutoscalingConfig `protobuf:"bytes,1,opt,name=cluster_autoscaling_config,json=clusterAutoscalingConfig,proto3" json:"cluster_autoscaling_config,omitempty"`
}

func (x *Cluster_ClusterConfig) Reset() {
	*x = Cluster_ClusterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster_ClusterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster_ClusterConfig) ProtoMessage() {}

func (x *Cluster_ClusterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster_ClusterConfig.ProtoReflect.Descriptor instead.
func (*Cluster_ClusterConfig) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Cluster_ClusterConfig) GetClusterAutoscalingConfig() *Cluster_ClusterAutoscalingConfig {
	if x != nil {
		return x.ClusterAutoscalingConfig
	}
	return nil
}

// Cloud Key Management Service (Cloud KMS) settings for a CMEK-protected
// cluster.
type Cluster_EncryptionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Describes the Cloud KMS encryption key that will be used to protect the
	// destination Bigtable cluster. The requirements for this key are:
	//  1) The Cloud Bigtable service account associated with the project that
	//  contains this cluster must be granted the
	//  `cloudkms.cryptoKeyEncrypterDecrypter` role on the CMEK key.
	//  2) Only regional keys can be used and the region of the CMEK key must
	//  match the region of the cluster.
	// 3) All clusters within an instance must use the same CMEK key.
	KmsKeyName string `protobuf:"bytes,1,opt,name=kms_key_name,json=kmsKeyName,proto3" json:"kms_key_name,omitempty"`
}

func (x *Cluster_EncryptionConfig) Reset() {
	*x = Cluster_EncryptionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster_EncryptionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster_EncryptionConfig) ProtoMessage() {}

func (x *Cluster_EncryptionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster_EncryptionConfig.ProtoReflect.Descriptor instead.
func (*Cluster_EncryptionConfig) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{3, 2}
}

func (x *Cluster_EncryptionConfig) GetKmsKeyName() string {
	if x != nil {
		return x.KmsKeyName
	}
	return ""
}

// Read/write requests are routed to the nearest cluster in the instance, and
// will fail over to the nearest cluster that is available in the event of
// transient errors or delays. Clusters in a region are considered
// equidistant. Choosing this option sacrifices read-your-writes consistency
// to improve availability.
type AppProfile_MultiClusterRoutingUseAny struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The set of clusters to route to. The order is ignored; clusters will be
	// tried in order of distance. If left empty, all clusters are eligible.
	ClusterIds []string `protobuf:"bytes,1,rep,name=cluster_ids,json=clusterIds,proto3" json:"cluster_ids,omitempty"`
}

func (x *AppProfile_MultiClusterRoutingUseAny) Reset() {
	*x = AppProfile_MultiClusterRoutingUseAny{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppProfile_MultiClusterRoutingUseAny) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppProfile_MultiClusterRoutingUseAny) ProtoMessage() {}

func (x *AppProfile_MultiClusterRoutingUseAny) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppProfile_MultiClusterRoutingUseAny.ProtoReflect.Descriptor instead.
func (*AppProfile_MultiClusterRoutingUseAny) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{4, 0}
}

func (x *AppProfile_MultiClusterRoutingUseAny) GetClusterIds() []string {
	if x != nil {
		return x.ClusterIds
	}
	return nil
}

// Unconditionally routes all read/write requests to a specific cluster.
// This option preserves read-your-writes consistency but does not improve
// availability.
type AppProfile_SingleClusterRouting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The cluster to which read/write requests should be routed.
	ClusterId string `protobuf:"bytes,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// Whether or not `CheckAndMutateRow` and `ReadModifyWriteRow` requests are
	// allowed by this app profile. It is unsafe to send these requests to
	// the same table/row/column in multiple clusters.
	AllowTransactionalWrites bool `protobuf:"varint,2,opt,name=allow_transactional_writes,json=allowTransactionalWrites,proto3" json:"allow_transactional_writes,omitempty"`
}

func (x *AppProfile_SingleClusterRouting) Reset() {
	*x = AppProfile_SingleClusterRouting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppProfile_SingleClusterRouting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppProfile_SingleClusterRouting) ProtoMessage() {}

func (x *AppProfile_SingleClusterRouting) ProtoReflect() protoreflect.Message {
	mi := &file_google_bigtable_admin_v2_instance_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppProfile_SingleClusterRouting.ProtoReflect.Descriptor instead.
func (*AppProfile_SingleClusterRouting) Descriptor() ([]byte, []int) {
	return file_google_bigtable_admin_v2_instance_proto_rawDescGZIP(), []int{4, 1}
}

func (x *AppProfile_SingleClusterRouting) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *AppProfile_SingleClusterRouting) GetAllowTransactionalWrites() bool {
	if x != nil {
		return x.AllowTransactionalWrites
	}
	return false
}

var File_google_bigtable_admin_v2_instance_proto protoreflect.FileDescriptor

var file_google_bigtable_admin_v2_instance_proto_rawDesc = []byte{
	0x0a, 0x27, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x04, 0x0a, 0x08, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x46, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x35, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x22, 0x3d, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x45,
	0x56, 0x45, 0x4c, 0x4f, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x3a, 0x53, 0xea, 0x41, 0x50,
	0x0a, 0x25, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x27, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x7d,
	0x22, 0x4c, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x70, 0x75, 0x5f, 0x75, 0x74,
	0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x63, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0x6d,
	0x0a, 0x11, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73,
	0x12, 0x2b, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0d,
	0x6d, 0x61, 0x78, 0x53, 0x65, 0x72, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0xf4, 0x08,
	0x0a, 0x07, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xfa, 0x41, 0x23, 0x0a, 0x21, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x58, 0x0a, 0x0e, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x57, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x53,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x64,
	0x0a, 0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x03, 0xe0,
	0x41, 0x05, 0x52, 0x10, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x1a, 0xdf, 0x01, 0x0a, 0x18, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x5f, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61,
	0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x11, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x12, 0x62, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x73,
	0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x1a, 0x89, 0x01, 0x0a, 0x0d, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x78, 0x0a, 0x1a, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2e,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x18, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x1a, 0x5c, 0x0a, 0x10, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x48, 0x0a, 0x0c, 0x6b, 0x6d, 0x73, 0x5f, 0x6b, 0x65,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xfa, 0x41,
	0x23, 0x0a, 0x21, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6b, 0x6d, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x43, 0x72, 0x79, 0x70, 0x74,
	0x6f, 0x4b, 0x65, 0x79, 0x52, 0x0a, 0x6b, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x51, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x49, 0x5a,
	0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45,
	0x44, 0x10, 0x04, 0x3a, 0x65, 0xea, 0x41, 0x62, 0x0a, 0x24, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x7d, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x42, 0x08, 0x0a, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0x84, 0x05, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x74, 0x61, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x74, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x82, 0x01,
	0x0a, 0x1d, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x73, 0x65, 0x5f, 0x61, 0x6e, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x55,
	0x73, 0x65, 0x41, 0x6e, 0x79, 0x48, 0x00, 0x52, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x41,
	0x6e, 0x79, 0x12, 0x71, 0x0a, 0x16, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x70,
	0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52,
	0x14, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x1a, 0x3c, 0x0a, 0x19, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x41,
	0x6e, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x1a, 0x73, 0x0a, 0x14, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x73, 0x3a, 0x6f, 0xea, 0x41, 0x6c, 0x0a, 0x27, 0x62,
	0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x41, 0x70, 0x70, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x41, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x7d, 0x2f,
	0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x61, 0x70, 0x70,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x7d, 0x42, 0x10, 0x0a, 0x0e, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0xd0, 0x02, 0x0a, 0x1c,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x62, 0x69, 0x67, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x32, 0x42, 0x0d, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3d, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x62, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x32, 0x3b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0xaa, 0x02, 0x1e, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x42, 0x69, 0x67, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x56, 0x32, 0xca, 0x02, 0x1e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x42, 0x69, 0x67,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x5c, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x5c, 0x56, 0x32, 0xea, 0x02,
	0x22, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a,
	0x42, 0x69, 0x67, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x3a, 0x3a, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x3a,
	0x3a, 0x56, 0x32, 0xea, 0x41, 0x78, 0x0a, 0x21, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6b, 0x6d, 0x73,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79, 0x12, 0x53, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x7d, 0x2f, 0x6b, 0x65, 0x79, 0x52, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x6b, 0x65, 0x79,
	0x5f, 0x72, 0x69, 0x6e, 0x67, 0x7d, 0x2f, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79,
	0x73, 0x2f, 0x7b, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x5f, 0x6b, 0x65, 0x79, 0x7d, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_bigtable_admin_v2_instance_proto_rawDescOnce sync.Once
	file_google_bigtable_admin_v2_instance_proto_rawDescData = file_google_bigtable_admin_v2_instance_proto_rawDesc
)

func file_google_bigtable_admin_v2_instance_proto_rawDescGZIP() []byte {
	file_google_bigtable_admin_v2_instance_proto_rawDescOnce.Do(func() {
		file_google_bigtable_admin_v2_instance_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_bigtable_admin_v2_instance_proto_rawDescData)
	})
	return file_google_bigtable_admin_v2_instance_proto_rawDescData
}

var file_google_bigtable_admin_v2_instance_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_bigtable_admin_v2_instance_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_google_bigtable_admin_v2_instance_proto_goTypes = []interface{}{
	(Instance_State)(0),                          // 0: google.bigtable.admin.v2.Instance.State
	(Instance_Type)(0),                           // 1: google.bigtable.admin.v2.Instance.Type
	(Cluster_State)(0),                           // 2: google.bigtable.admin.v2.Cluster.State
	(*Instance)(nil),                             // 3: google.bigtable.admin.v2.Instance
	(*AutoscalingTargets)(nil),                   // 4: google.bigtable.admin.v2.AutoscalingTargets
	(*AutoscalingLimits)(nil),                    // 5: google.bigtable.admin.v2.AutoscalingLimits
	(*Cluster)(nil),                              // 6: google.bigtable.admin.v2.Cluster
	(*AppProfile)(nil),                           // 7: google.bigtable.admin.v2.AppProfile
	nil,                                          // 8: google.bigtable.admin.v2.Instance.LabelsEntry
	(*Cluster_ClusterAutoscalingConfig)(nil),     // 9: google.bigtable.admin.v2.Cluster.ClusterAutoscalingConfig
	(*Cluster_ClusterConfig)(nil),                // 10: google.bigtable.admin.v2.Cluster.ClusterConfig
	(*Cluster_EncryptionConfig)(nil),             // 11: google.bigtable.admin.v2.Cluster.EncryptionConfig
	(*AppProfile_MultiClusterRoutingUseAny)(nil), // 12: google.bigtable.admin.v2.AppProfile.MultiClusterRoutingUseAny
	(*AppProfile_SingleClusterRouting)(nil),      // 13: google.bigtable.admin.v2.AppProfile.SingleClusterRouting
	(*timestamppb.Timestamp)(nil),                // 14: google.protobuf.Timestamp
	(StorageType)(0),                             // 15: google.bigtable.admin.v2.StorageType
}
var file_google_bigtable_admin_v2_instance_proto_depIdxs = []int32{
	0,  // 0: google.bigtable.admin.v2.Instance.state:type_name -> google.bigtable.admin.v2.Instance.State
	1,  // 1: google.bigtable.admin.v2.Instance.type:type_name -> google.bigtable.admin.v2.Instance.Type
	8,  // 2: google.bigtable.admin.v2.Instance.labels:type_name -> google.bigtable.admin.v2.Instance.LabelsEntry
	14, // 3: google.bigtable.admin.v2.Instance.create_time:type_name -> google.protobuf.Timestamp
	2,  // 4: google.bigtable.admin.v2.Cluster.state:type_name -> google.bigtable.admin.v2.Cluster.State
	10, // 5: google.bigtable.admin.v2.Cluster.cluster_config:type_name -> google.bigtable.admin.v2.Cluster.ClusterConfig
	15, // 6: google.bigtable.admin.v2.Cluster.default_storage_type:type_name -> google.bigtable.admin.v2.StorageType
	11, // 7: google.bigtable.admin.v2.Cluster.encryption_config:type_name -> google.bigtable.admin.v2.Cluster.EncryptionConfig
	12, // 8: google.bigtable.admin.v2.AppProfile.multi_cluster_routing_use_any:type_name -> google.bigtable.admin.v2.AppProfile.MultiClusterRoutingUseAny
	13, // 9: google.bigtable.admin.v2.AppProfile.single_cluster_routing:type_name -> google.bigtable.admin.v2.AppProfile.SingleClusterRouting
	5,  // 10: google.bigtable.admin.v2.Cluster.ClusterAutoscalingConfig.autoscaling_limits:type_name -> google.bigtable.admin.v2.AutoscalingLimits
	4,  // 11: google.bigtable.admin.v2.Cluster.ClusterAutoscalingConfig.autoscaling_targets:type_name -> google.bigtable.admin.v2.AutoscalingTargets
	9,  // 12: google.bigtable.admin.v2.Cluster.ClusterConfig.cluster_autoscaling_config:type_name -> google.bigtable.admin.v2.Cluster.ClusterAutoscalingConfig
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_google_bigtable_admin_v2_instance_proto_init() }
func file_google_bigtable_admin_v2_instance_proto_init() {
	if File_google_bigtable_admin_v2_instance_proto != nil {
		return
	}
	file_google_bigtable_admin_v2_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_bigtable_admin_v2_instance_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Instance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoscalingTargets); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoscalingLimits); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cluster_ClusterAutoscalingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cluster_ClusterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cluster_EncryptionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppProfile_MultiClusterRoutingUseAny); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_bigtable_admin_v2_instance_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppProfile_SingleClusterRouting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_bigtable_admin_v2_instance_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*Cluster_ClusterConfig_)(nil),
	}
	file_google_bigtable_admin_v2_instance_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*AppProfile_MultiClusterRoutingUseAny_)(nil),
		(*AppProfile_SingleClusterRouting_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_bigtable_admin_v2_instance_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_bigtable_admin_v2_instance_proto_goTypes,
		DependencyIndexes: file_google_bigtable_admin_v2_instance_proto_depIdxs,
		EnumInfos:         file_google_bigtable_admin_v2_instance_proto_enumTypes,
		MessageInfos:      file_google_bigtable_admin_v2_instance_proto_msgTypes,
	}.Build()
	File_google_bigtable_admin_v2_instance_proto = out.File
	file_google_bigtable_admin_v2_instance_proto_rawDesc = nil
	file_google_bigtable_admin_v2_instance_proto_goTypes = nil
	file_google_bigtable_admin_v2_instance_proto_depIdxs = nil
}
