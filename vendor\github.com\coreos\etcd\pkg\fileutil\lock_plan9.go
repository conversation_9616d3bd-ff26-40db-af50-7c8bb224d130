// Copyright 2015 The etcd Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package fileutil

import (
	"os"
	"syscall"
	"time"
)

func TryLockFile(path string, flag int, perm os.FileMode) (*LockedFile, error) {
	if err := os.Chmod(path, syscall.DMEXCL|PrivateFileMode); err != nil {
		return nil, err
	}
	f, err := os.Open(path, flag, perm)
	if err != nil {
		return nil, ErrLocked
	}
	return &LockedFile{f}, nil
}

func LockFile(path string, flag int, perm os.FileMode) (*LockedFile, error) {
	if err := os.Chmod(path, syscall.DMEXCL|PrivateFileMode); err != nil {
		return nil, err
	}
	for {
		f, err := os.OpenFile(path, flag, perm)
		if err == nil {
			return &LockedFile{f}, nil
		}
		time.Sleep(10 * time.Millisecond)
	}
}
