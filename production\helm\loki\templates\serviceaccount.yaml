{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: {{ template "loki.name" . }}
    chart: {{ template "loki.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
  annotations:
    {{- toYaml .Values.serviceAccount.annotations | nindent 4 }}
  name: {{ template "loki.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}

