version: "{build}"

platform: x64

clone_folder: c:\gopath\src\go.opencensus.io

environment:
  GOPATH: 'c:\gopath'
  GO111MODULE: 'on'
  CGO_ENABLED: '0' # See: https://github.com/appveyor/ci/issues/2613

stack: go 1.11

before_test:
  - go version
  - go env

build: false
deploy: false

test_script:
  - cd %APPVEYOR_BUILD_FOLDER%
  - go build -v .\...
  - go test -v .\... # No -race because cgo is disabled
