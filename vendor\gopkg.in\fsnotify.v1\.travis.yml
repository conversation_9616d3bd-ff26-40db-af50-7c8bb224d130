sudo: false
language: go

go:
  - 1.8.x
  - 1.9.x
  - tip

matrix:
  allow_failures:
    - go: tip
  fast_finish: true

before_script:
  - go get -u github.com/golang/lint/golint

script:
  - go test -v --race ./...

after_script:
  - test -z "$(gofmt -s -l -w . | tee /dev/stderr)"
  - test -z "$(golint ./...     | tee /dev/stderr)"
  - go vet ./...

os:
  - linux
  - osx

notifications:
  email: false
