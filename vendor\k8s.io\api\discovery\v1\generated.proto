/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.discovery.v1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// Endpoint represents a single logical "backend" implementing a service.
message Endpoint {
  // addresses of this endpoint. The contents of this field are interpreted
  // according to the corresponding EndpointSlice addressType field. Consumers
  // must handle different types of addresses in the context of their own
  // capabilities. This must contain at least one address but no more than
  // 100.
  // +listType=set
  repeated string addresses = 1;

  // conditions contains information about the current status of the endpoint.
  optional EndpointConditions conditions = 2;

  // hostname of this endpoint. This field may be used by consumers of
  // endpoints to distinguish endpoints from each other (e.g. in DNS names).
  // Multiple endpoints which use the same hostname should be considered
  // fungible (e.g. multiple A values in DNS). Must be lowercase and pass DNS
  // Label (RFC 1123) validation.
  // +optional
  optional string hostname = 3;

  // targetRef is a reference to a Kubernetes object that represents this
  // endpoint.
  // +optional
  optional k8s.io.api.core.v1.ObjectReference targetRef = 4;

  // deprecatedTopology contains topology information part of the v1beta1
  // API. This field is deprecated, and will be removed when the v1beta1
  // API is removed (no sooner than kubernetes v1.24).  While this field can
  // hold values, it is not writable through the v1 API, and any attempts to
  // write to it will be silently ignored. Topology information can be found
  // in the zone and nodeName fields instead.
  // +optional
  map<string, string> deprecatedTopology = 5;

  // nodeName represents the name of the Node hosting this endpoint. This can
  // be used to determine endpoints local to a Node. This field can be enabled
  // with the EndpointSliceNodeName feature gate.
  // +optional
  optional string nodeName = 6;

  // zone is the name of the Zone this endpoint exists in.
  // +optional
  optional string zone = 7;

  // hints contains information associated with how an endpoint should be
  // consumed.
  // +optional
  optional EndpointHints hints = 8;
}

// EndpointConditions represents the current condition of an endpoint.
message EndpointConditions {
  // ready indicates that this endpoint is prepared to receive traffic,
  // according to whatever system is managing the endpoint. A nil value
  // indicates an unknown state. In most cases consumers should interpret this
  // unknown state as ready. For compatibility reasons, ready should never be
  // "true" for terminating endpoints.
  // +optional
  optional bool ready = 1;

  // serving is identical to ready except that it is set regardless of the
  // terminating state of endpoints. This condition should be set to true for
  // a ready endpoint that is terminating. If nil, consumers should defer to
  // the ready condition. This field can be enabled with the
  // EndpointSliceTerminatingCondition feature gate.
  // +optional
  optional bool serving = 2;

  // terminating indicates that this endpoint is terminating. A nil value
  // indicates an unknown state. Consumers should interpret this unknown state
  // to mean that the endpoint is not terminating. This field can be enabled
  // with the EndpointSliceTerminatingCondition feature gate.
  // +optional
  optional bool terminating = 3;
}

// EndpointHints provides hints describing how an endpoint should be consumed.
message EndpointHints {
  // forZones indicates the zone(s) this endpoint should be consumed by to
  // enable topology aware routing.
  // +listType=atomic
  repeated ForZone forZones = 1;
}

// EndpointPort represents a Port used by an EndpointSlice
// +structType=atomic
message EndpointPort {
  // The name of this port. All ports in an EndpointSlice must have a unique
  // name. If the EndpointSlice is dervied from a Kubernetes service, this
  // corresponds to the Service.ports[].name.
  // Name must either be an empty string or pass DNS_LABEL validation:
  // * must be no more than 63 characters long.
  // * must consist of lower case alphanumeric characters or '-'.
  // * must start and end with an alphanumeric character.
  // Default is empty string.
  optional string name = 1;

  // The IP protocol for this port.
  // Must be UDP, TCP, or SCTP.
  // Default is TCP.
  optional string protocol = 2;

  // The port number of the endpoint.
  // If this is not specified, ports are not restricted and must be
  // interpreted in the context of the specific consumer.
  optional int32 port = 3;

  // The application protocol for this port.
  // This field follows standard Kubernetes label syntax.
  // Un-prefixed names are reserved for IANA standard service names (as per
  // RFC-6335 and http://www.iana.org/assignments/service-names).
  // Non-standard protocols should use prefixed names such as
  // mycompany.com/my-custom-protocol.
  // +optional
  optional string appProtocol = 4;
}

// EndpointSlice represents a subset of the endpoints that implement a service.
// For a given service there may be multiple EndpointSlice objects, selected by
// labels, which must be joined to produce the full set of endpoints.
message EndpointSlice {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // addressType specifies the type of address carried by this EndpointSlice.
  // All addresses in this slice must be the same type. This field is
  // immutable after creation. The following address types are currently
  // supported:
  // * IPv4: Represents an IPv4 Address.
  // * IPv6: Represents an IPv6 Address.
  // * FQDN: Represents a Fully Qualified Domain Name.
  optional string addressType = 4;

  // endpoints is a list of unique endpoints in this slice. Each slice may
  // include a maximum of 1000 endpoints.
  // +listType=atomic
  repeated Endpoint endpoints = 2;

  // ports specifies the list of network ports exposed by each endpoint in
  // this slice. Each port must have a unique name. When ports is empty, it
  // indicates that there are no defined ports. When a port is defined with a
  // nil port value, it indicates "all ports". Each slice may include a
  // maximum of 100 ports.
  // +optional
  // +listType=atomic
  repeated EndpointPort ports = 3;
}

// EndpointSliceList represents a list of endpoint slices
message EndpointSliceList {
  // Standard list metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of endpoint slices
  repeated EndpointSlice items = 2;
}

// ForZone provides information about which zones should consume this endpoint.
message ForZone {
  // name represents the name of the zone.
  optional string name = 1;
}

