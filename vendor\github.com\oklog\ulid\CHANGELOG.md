## 1.3.1 / 2018-10-02

* Use underlying entropy source for random increments in Monotonic (#32)

## 1.3.0 / 2018-09-29

* Monotonic entropy support (#31)

## 1.2.0 / 2018-09-09

* Add a function to convert Unix time in milliseconds back to time.Time (#30)

## 1.1.0 / 2018-08-15

* Ensure random part is always read from the entropy reader in full (#28)

## 1.0.0 / 2018-07-29

* Add ParseStrict and MustParseStrict functions (#26)
* Enforce overflow checking when parsing (#20)

## 0.3.0 / 2017-01-03

* Implement ULID.Compare method

## 0.2.0 / 2016-12-13

* Remove year 2262 Timestamp bug. (#1)
* Gracefully handle invalid encodings when parsing.

## 0.1.0 / 2016-12-06

* First ULID release
