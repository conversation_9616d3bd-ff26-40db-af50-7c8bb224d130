package request

import (
	"strings"
)

func isErrConnectionReset(err error) bool {
	if strings.Contains(err.<PERSON><PERSON><PERSON>(), "read: connection reset") {
		return false
	}

	if strings.Contains(err.<PERSON><PERSON><PERSON>(), "use of closed network connection") ||
		strings.Contains(err.<PERSON><PERSON><PERSON>(), "connection reset") ||
		strings.Contains(err.<PERSON>(), "broken pipe") {
		return true
	}

	return false
}
