## Affinity for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
affinity: {}

annotations: {}

# The update strategy to apply to the DaemonSet
##
deploymentStrategy: {}
#  rollingUpdate:
#    maxUnavailable: 1
#  type: RollingUpdate

initContainer:
  enabled: false
  fsInotifyMaxUserInstances: 128

image:
  repository: grafana/promtail
  tag: 2.0.0
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  ##
  # pullSecrets:
  #   - myRegistryKeySecretName

livenessProbe: {}

loki:
  serviceName: ""  # Defaults to "${RELEASE}-loki" if not set
  servicePort: 3100
  serviceScheme: http
  # user: user
  # password: pass

nameOverride: promtail

## Node labels for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
nodeSelector: {}

pipelineStages:
- docker: {}

## Pod Labels
podLabels: {}

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "http-metrics"

## Assign a PriorityClassName to pods if set
# priorityClassName:

rbac:
  create: true
  pspEnabled: true

podSecurityPolicy:
  privileged: false
  allowPrivilegeEscalation: false
  volumes:
    - 'secret'
    - 'configMap'
    - 'hostPath'
    - 'projected'
    - 'downwardAPI'
    - 'emptyDir'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'RunAsAny'
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
  requiredDropCapabilities:
    - ALL

readinessProbe:
  failureThreshold: 5
  httpGet:
    path: /ready
    port: http-metrics
  initialDelaySeconds: 10
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

resources: {}
#  limits:
#    cpu: 200m
#    memory: 128Mi
#  requests:
#    cpu: 100m
#    memory: 128Mi

# Custom scrape_configs to override the default ones in the configmap
scrapeConfigs: []

# Custom scrape_configs together with the default ones in the configmap
extraScrapeConfigs: []

securityContext:
  readOnlyRootFilesystem: true
  runAsGroup: 0
  runAsUser: 0

serviceAccount:
  create: true
  name:

## Tolerations for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
tolerations:
- key: node-role.kubernetes.io/master
  operator: Exists
  effect: NoSchedule

# Extra volumes to scrape logs from
volumes:
- name: docker
  hostPath:
    path: /var/lib/docker/containers
- name: pods
  hostPath:
    path: /var/log/pods

# Custom volumes together with the default ones
extraVolumes: []

volumeMounts:
- name: docker
  mountPath: /var/lib/docker/containers
  readOnly: true
- name: pods
  mountPath: /var/log/pods
  readOnly: true

# Custom volumeMounts together with the default ones
extraVolumeMounts: []

# Add extra Commandline args while starting up promtail.
# more info : https://github.com/grafana/loki/pull/1530

extraCommandlineArgs: []
# example:
# extraCommandlineArgs:
#   - -client.external-labels=hostname=$(HOSTNAME)

config:
  client:
    # Maximum wait period before sending batch
    batchwait: 1s
    # Maximum batch size to accrue before sending, unit is byte
    batchsize: 1048576

    # Maximum time to wait for server to respond to a request
    timeout: 10s

    backoff_config:
      # Initial backoff time between retries
      min_period: 500ms
      # Maximum backoff time between retries
      max_period: 5m
      # Maximum number of retries when sending batches, 0 means infinite retries
      max_retries: 10

    # The labels to add to any time series or alerts when communicating with loki
    external_labels: {}

  server:
    http_listen_port: 3101

  positions:
    filename: /run/promtail/positions.yaml
  target_config:
    # Period to resync directories being watched and files being tailed
    sync_period: 10s

serviceMonitor:
  enabled: false
  interval: ""
  additionalLabels: {}
  annotations: {}
  # scrapeTimeout: 10s

# Extra env variables to pass to the promtail container
env: []

# enable and configure if using the syslog scrape config
syslogService:
  enabled: false
  type: ClusterIP
  port: 1514
  # externalIPs: []
  ## Specify the nodePort value for the LoadBalancer and NodePort service types.
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
  ##
  # nodePort:
  ## Provide any additional annotations which may be required. This can be used to
  ## set the LoadBalancer service type to internal only.
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
  ##
  annotations: {}
  labels: {}
  ## Use loadBalancerIP to request a specific static IP,
  ## otherwise leave blank
  ##
  loadBalancerIP:
  # loadBalancerSourceRanges: []
  ## Set the externalTrafficPolicy in the Service to either Cluster or Local
  # externalTrafficPolicy: Cluster
