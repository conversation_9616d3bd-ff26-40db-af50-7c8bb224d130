linters-settings:
  govet:
    check-shadowing: true
  golint:
    min-confidence: 0
  gocyclo:
    min-complexity: 40
  maligned:
    suggest-new: true
  dupl:
    threshold: 150
  goconst:
    min-len: 2
    min-occurrences: 4

linters:
  enable-all: true
  disable:
    - maligned
    - lll
    - gochecknoglobals
    - gochecknoinits
    # scopelint is useful, but also reports false positives
    # that unfortunately can't be disabled. So we disable the
    # linter rather than changing code that works.
    # see: https://github.com/kyoh86/scopelint/issues/4
    - scopelint
    - godox
    - gocognit
    - whitespace
    - wsl
    - funlen
    - testpackage
    - wrapcheck
    - nlreturn
    - gomnd
    - goerr113
    - exhaustivestruct
    - errorlint
    - nestif
    - gofumpt
    - godot
    - gci
    - dogsled
    - paralleltest
    - tparallel
    - thelper
    - ifshort
    - forbidigo
