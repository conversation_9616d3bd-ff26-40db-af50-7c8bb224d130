/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.scheduling.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1beta1";

// DEPRECATED - This group version of PriorityClass is deprecated by scheduling.k8s.io/v1/PriorityClass.
// PriorityClass defines mapping from a priority class name to the priority
// integer value. The value can be any valid integer.
message PriorityClass {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The value of this priority class. This is the actual priority that pods
  // receive when they have the name of this class in their pod spec.
  optional int32 value = 2;

  // globalDefault specifies whether this PriorityClass should be considered as
  // the default priority for pods that do not have any priority class.
  // Only one PriorityClass can be marked as `globalDefault`. However, if more than
  // one PriorityClasses exists with their `globalDefault` field set to true,
  // the smallest value of such global default PriorityClasses will be used as the default priority.
  // +optional
  optional bool globalDefault = 3;

  // description is an arbitrary string that usually provides guidelines on
  // when this priority class should be used.
  // +optional
  optional string description = 4;

  // PreemptionPolicy is the Policy for preempting pods with lower priority.
  // One of Never, PreemptLowerPriority.
  // Defaults to PreemptLowerPriority if unset.
  // This field is beta-level, gated by the NonPreemptingPriority feature-gate.
  // +optional
  optional string preemptionPolicy = 5;
}

// PriorityClassList is a collection of priority classes.
message PriorityClassList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of PriorityClasses
  repeated PriorityClass items = 2;
}

