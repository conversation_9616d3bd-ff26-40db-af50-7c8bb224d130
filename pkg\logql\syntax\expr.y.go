// Code generated by goyacc -p expr -o pkg/logql/syntax/expr.y.go pkg/logql/syntax/expr.y. DO NOT EDIT.

package syntax

import __yyfmt__ "fmt"


import (
	"github.com/grafana/loki/pkg/logql/log"
	"github.com/prometheus/prometheus/model/labels"
	"time"
)

type exprSymType struct {
	yys                   int
	Expr                  Expr
	Filter                labels.MatchType
	Grouping              *Grouping
	Labels                []string
	LogExpr               LogSelectorExpr
	LogRangeExpr          *LogRange
	Matcher               *labels.Matcher
	Matchers              []*labels.Matcher
	RangeAggregationExpr  SampleExpr
	RangeOp               string
	ConvOp                string
	Selector              []*labels.Matcher
	VectorAggregationExpr SampleExpr
	MetricExpr            SampleExpr
	VectorOp              string
	FilterOp              string
	BinOpExpr             SampleExpr
	LabelReplaceExpr      SampleExpr
	binOp                 string
	bytes                 uint64
	str                   string
	duration              time.Duration
	LiteralExpr           *LiteralExpr
	BinOpModifier         *BinOpOptions
	BoolModifier          *BinOpOptions
	OnOrIgnoringModifier  *BinOpOptions
	LabelParser           *LabelParserExpr
	LineFilters           *LineFilterExpr
	LineFilter            *LineFilterExpr
	PipelineExpr          MultiStageExpr
	PipelineStage         StageExpr
	BytesFilter           log.LabelFilterer
	NumberFilter          log.LabelFilterer
	DurationFilter        log.LabelFilterer
	LabelFilter           log.LabelFilterer
	UnitFilter            log.LabelFilterer
	IPLabelFilter         log.LabelFilterer
	LineFormatExpr        *LineFmtExpr
	LabelFormatExpr       *LabelFmtExpr
	LabelFormat           log.LabelFmt
	LabelsFormat          []log.LabelFmt
	JSONExpressionParser  *JSONExpressionParser
	JSONExpression        log.JSONExpression
	JSONExpressionList    []log.JSONExpression
	UnwrapExpr            *UnwrapExpr
	OffsetExpr            *OffsetExpr
}

const BYTES = 57346
const IDENTIFIER = 57347
const STRING = 57348
const NUMBER = 57349
const DURATION = 57350
const RANGE = 57351
const MATCHERS = 57352
const LABELS = 57353
const EQ = 57354
const RE = 57355
const NRE = 57356
const OPEN_BRACE = 57357
const CLOSE_BRACE = 57358
const OPEN_BRACKET = 57359
const CLOSE_BRACKET = 57360
const COMMA = 57361
const DOT = 57362
const PIPE_MATCH = 57363
const PIPE_EXACT = 57364
const OPEN_PARENTHESIS = 57365
const CLOSE_PARENTHESIS = 57366
const BY = 57367
const WITHOUT = 57368
const COUNT_OVER_TIME = 57369
const RATE = 57370
const RATE_COUNTER = 57371
const SUM = 57372
const AVG = 57373
const MAX = 57374
const MIN = 57375
const COUNT = 57376
const STDDEV = 57377
const STDVAR = 57378
const BOTTOMK = 57379
const TOPK = 57380
const BYTES_OVER_TIME = 57381
const BYTES_RATE = 57382
const BOOL = 57383
const JSON = 57384
const REGEXP = 57385
const LOGFMT = 57386
const PIPE = 57387
const LINE_FMT = 57388
const LABEL_FMT = 57389
const UNWRAP = 57390
const AVG_OVER_TIME = 57391
const SUM_OVER_TIME = 57392
const MIN_OVER_TIME = 57393
const MAX_OVER_TIME = 57394
const STDVAR_OVER_TIME = 57395
const STDDEV_OVER_TIME = 57396
const QUANTILE_OVER_TIME = 57397
const BYTES_CONV = 57398
const DURATION_CONV = 57399
const DURATION_SECONDS_CONV = 57400
const FIRST_OVER_TIME = 57401
const LAST_OVER_TIME = 57402
const ABSENT_OVER_TIME = 57403
const LABEL_REPLACE = 57404
const UNPACK = 57405
const OFFSET = 57406
const PATTERN = 57407
const IP = 57408
const ON = 57409
const IGNORING = 57410
const GROUP_LEFT = 57411
const GROUP_RIGHT = 57412
const OR = 57413
const AND = 57414
const UNLESS = 57415
const CMP_EQ = 57416
const NEQ = 57417
const LT = 57418
const LTE = 57419
const GT = 57420
const GTE = 57421
const ADD = 57422
const SUB = 57423
const MUL = 57424
const DIV = 57425
const MOD = 57426
const POW = 57427

var exprToknames = [...]string{
	"$end",
	"error",
	"$unk",
	"BYTES",
	"IDENTIFIER",
	"STRING",
	"NUMBER",
	"DURATION",
	"RANGE",
	"MATCHERS",
	"LABELS",
	"EQ",
	"RE",
	"NRE",
	"OPEN_BRACE",
	"CLOSE_BRACE",
	"OPEN_BRACKET",
	"CLOSE_BRACKET",
	"COMMA",
	"DOT",
	"PIPE_MATCH",
	"PIPE_EXACT",
	"OPEN_PARENTHESIS",
	"CLOSE_PARENTHESIS",
	"BY",
	"WITHOUT",
	"COUNT_OVER_TIME",
	"RATE",
	"RATE_COUNTER",
	"SUM",
	"AVG",
	"MAX",
	"MIN",
	"COUNT",
	"STDDEV",
	"STDVAR",
	"BOTTOMK",
	"TOPK",
	"BYTES_OVER_TIME",
	"BYTES_RATE",
	"BOOL",
	"JSON",
	"REGEXP",
	"LOGFMT",
	"PIPE",
	"LINE_FMT",
	"LABEL_FMT",
	"UNWRAP",
	"AVG_OVER_TIME",
	"SUM_OVER_TIME",
	"MIN_OVER_TIME",
	"MAX_OVER_TIME",
	"STDVAR_OVER_TIME",
	"STDDEV_OVER_TIME",
	"QUANTILE_OVER_TIME",
	"BYTES_CONV",
	"DURATION_CONV",
	"DURATION_SECONDS_CONV",
	"FIRST_OVER_TIME",
	"LAST_OVER_TIME",
	"ABSENT_OVER_TIME",
	"LABEL_REPLACE",
	"UNPACK",
	"OFFSET",
	"PATTERN",
	"IP",
	"ON",
	"IGNORING",
	"GROUP_LEFT",
	"GROUP_RIGHT",
	"OR",
	"AND",
	"UNLESS",
	"CMP_EQ",
	"NEQ",
	"LT",
	"LTE",
	"GT",
	"GTE",
	"ADD",
	"SUB",
	"MUL",
	"DIV",
	"MOD",
	"POW",
}
var exprStatenames = [...]string{}

const exprEofCode = 1
const exprErrCode = 2
const exprInitialStackSize = 16


var exprExca = [...]int{
	-1, 1,
	1, -1,
	-2, 0,
}

const exprPrivate = 57344

const exprLast = 535

var exprAct = [...]int{

	249, 196, 77, 4, 177, 59, 165, 5, 170, 205,
	68, 113, 51, 58, 123, 136, 70, 2, 46, 47,
	48, 49, 50, 51, 73, 43, 44, 45, 52, 53,
	56, 57, 54, 55, 46, 47, 48, 49, 50, 51,
	44, 45, 52, 53, 56, 57, 54, 55, 46, 47,
	48, 49, 50, 51, 48, 49, 50, 51, 252, 132,
	134, 135, 66, 321, 101, 179, 134, 135, 105, 64,
	65, 149, 150, 229, 125, 189, 230, 228, 147, 148,
	140, 257, 252, 138, 295, 254, 145, 52, 53, 56,
	57, 54, 55, 46, 47, 48, 49, 50, 51, 324,
	146, 62, 295, 253, 151, 152, 153, 154, 155, 156,
	157, 158, 159, 160, 161, 162, 163, 164, 302, 321,
	254, 255, 133, 67, 174, 86, 66, 185, 180, 183,
	184, 181, 182, 64, 65, 227, 303, 66, 254, 254,
	78, 79, 187, 341, 64, 65, 203, 199, 195, 336,
	329, 266, 197, 66, 208, 200, 312, 198, 66, 255,
	64, 65, 102, 258, 66, 64, 65, 328, 198, 66,
	326, 64, 65, 215, 216, 217, 64, 65, 305, 225,
	195, 188, 226, 224, 198, 66, 296, 67, 76, 198,
	78, 79, 64, 65, 120, 198, 247, 250, 67, 256,
	61, 259, 138, 101, 262, 105, 263, 253, 252, 251,
	248, 286, 117, 260, 67, 120, 198, 120, 120, 67,
	264, 270, 272, 275, 277, 67, 280, 278, 192, 167,
	67, 167, 167, 117, 220, 117, 117, 298, 299, 300,
	266, 223, 266, 254, 266, 311, 67, 310, 207, 309,
	287, 288, 207, 290, 292, 207, 294, 101, 120, 207,
	266, 293, 304, 289, 201, 268, 101, 276, 120, 306,
	266, 274, 167, 207, 273, 267, 117, 192, 271, 207,
	12, 168, 166, 168, 166, 166, 117, 318, 139, 285,
	315, 316, 209, 192, 127, 101, 317, 126, 206, 261,
	284, 214, 319, 320, 137, 108, 110, 109, 325, 118,
	119, 257, 12, 213, 131, 193, 120, 15, 212, 211,
	139, 331, 186, 332, 333, 12, 111, 144, 112, 143,
	142, 82, 75, 6, 117, 337, 339, 19, 20, 21,
	34, 35, 37, 38, 36, 39, 40, 41, 42, 22,
	23, 335, 308, 108, 110, 109, 265, 118, 119, 24,
	25, 26, 27, 28, 29, 30, 129, 221, 218, 31,
	32, 33, 18, 204, 111, 210, 112, 202, 194, 222,
	128, 12, 219, 130, 244, 334, 323, 245, 243, 6,
	16, 17, 322, 19, 20, 21, 34, 35, 37, 38,
	36, 39, 40, 41, 42, 22, 23, 241, 301, 238,
	242, 240, 239, 237, 291, 24, 25, 26, 27, 28,
	29, 30, 282, 283, 340, 31, 32, 33, 18, 141,
	235, 81, 232, 236, 234, 233, 231, 12, 80, 338,
	83, 3, 327, 314, 313, 6, 16, 17, 69, 19,
	20, 21, 34, 35, 37, 38, 36, 39, 40, 41,
	42, 22, 23, 281, 279, 269, 178, 114, 246, 191,
	190, 24, 25, 26, 27, 28, 29, 30, 189, 188,
	175, 31, 32, 33, 18, 87, 88, 89, 90, 91,
	92, 93, 94, 95, 96, 97, 98, 99, 100, 173,
	172, 72, 16, 17, 74, 330, 307, 171, 74, 178,
	115, 169, 104, 176, 107, 106, 60, 121, 116, 122,
	103, 85, 84, 11, 10, 9, 124, 14, 8, 297,
	13, 7, 71, 63, 1,
}
var exprPact = [...]int{

	310, -1000, -46, -1000, -1000, 155, 310, -1000, -1000, -1000,
	-1000, -1000, 499, 309, 165, -1000, 431, 424, 308, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, 84, 84, 84, 84, 84, 84, 84,
	84, 84, 84, 84, 84, 84, 84, 84, 155, -1000,
	48, 311, -1000, 8, -1000, -1000, -1000, -1000, 273, 270,
	-46, 364, 298, -1000, 47, 297, 422, 307, 306, 304,
	-1000, -1000, 310, 310, 11, 2, -1000, 310, 310, 310,
	310, 310, 310, 310, 310, 310, 310, 310, 310, 310,
	310, -1000, -1000, -1000, -1000, 212, -1000, -1000, 502, -1000,
	494, -1000, 493, -1000, -1000, -1000, -1000, 189, 474, 504,
	53, -1000, -1000, -1000, 299, -1000, -1000, -1000, -1000, -1000,
	503, -1000, 473, 472, 464, 463, 291, 359, 171, 265,
	240, 358, 366, 274, 268, 356, -32, 296, 295, 290,
	278, 13, 13, -28, -28, -73, -73, -73, -73, -62,
	-62, -62, -62, -62, -62, 212, 189, 189, 189, 349,
	-1000, 370, -1000, -1000, 210, -1000, 348, -1000, 367, 175,
	69, 428, 426, 405, 403, 380, 462, -1000, -1000, -1000,
	-1000, -1000, -1000, 115, 265, 144, 198, 150, 263, 139,
	275, 115, 310, 196, 337, 251, -1000, -1000, 241, -1000,
	459, 254, 250, 247, 243, 253, 212, 213, 502, 458,
	-1000, 461, 417, 277, -1000, -1000, -1000, 266, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, 187, -1000, 226, 123,
	40, 123, 406, -6, 189, -6, 93, 181, 399, 94,
	112, -1000, -1000, 154, -1000, 310, 501, -1000, -1000, 333,
	225, -1000, 223, -1000, -1000, 221, -1000, 132, -1000, -1000,
	-1000, -1000, -1000, -1000, 438, 437, -1000, 115, 40, 123,
	40, -1000, -1000, 212, -1000, -6, -1000, 264, -1000, -1000,
	-1000, 18, 383, 377, 75, 115, 146, -1000, 436, -1000,
	-1000, -1000, -1000, 143, 126, -1000, 40, -1000, 500, 74,
	40, 33, -6, -6, 376, -1000, -1000, 332, -1000, -1000,
	125, 40, -1000, -1000, -6, 433, -1000, -1000, 317, 418,
	119, -1000,
}
var exprPgo = [...]int{

	0, 534, 16, 533, 2, 9, 441, 3, 15, 11,
	532, 531, 530, 529, 7, 528, 527, 526, 525, 524,
	523, 440, 522, 521, 520, 13, 5, 519, 518, 517,
	6, 516, 101, 515, 514, 4, 513, 512, 8, 511,
	1, 510, 467, 0,
}
var exprR1 = [...]int{

	0, 1, 2, 2, 7, 7, 7, 7, 7, 7,
	6, 6, 6, 8, 8, 8, 8, 8, 8, 8,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 40,
	40, 40, 13, 13, 13, 11, 11, 11, 11, 15,
	15, 15, 15, 15, 15, 20, 3, 3, 3, 3,
	14, 14, 14, 10, 10, 9, 9, 9, 9, 25,
	25, 26, 26, 26, 26, 26, 26, 17, 32, 32,
	31, 31, 24, 24, 24, 24, 24, 37, 33, 35,
	35, 36, 36, 36, 34, 30, 30, 30, 30, 30,
	30, 30, 30, 30, 38, 38, 39, 39, 42, 42,
	41, 41, 29, 29, 29, 29, 29, 29, 29, 27,
	27, 27, 27, 27, 27, 27, 28, 28, 28, 28,
	28, 28, 28, 18, 18, 18, 18, 18, 18, 18,
	18, 18, 18, 18, 18, 18, 18, 18, 22, 22,
	23, 23, 23, 23, 21, 21, 21, 21, 21, 21,
	21, 21, 19, 19, 19, 16, 16, 16, 16, 16,
	16, 16, 16, 16, 12, 12, 12, 12, 12, 12,
	12, 12, 12, 12, 12, 12, 12, 12, 12, 43,
	5, 5, 4, 4, 4, 4,
}
var exprR2 = [...]int{

	0, 1, 1, 1, 1, 1, 1, 1, 1, 3,
	1, 2, 3, 2, 3, 4, 5, 3, 4, 5,
	6, 3, 4, 5, 6, 3, 4, 5, 6, 4,
	5, 6, 7, 3, 4, 4, 5, 3, 2, 3,
	6, 3, 1, 1, 1, 4, 6, 5, 7, 4,
	5, 5, 6, 7, 7, 12, 1, 1, 1, 1,
	3, 3, 3, 1, 3, 3, 3, 3, 3, 1,
	2, 1, 2, 2, 2, 2, 2, 1, 2, 5,
	1, 2, 1, 1, 2, 1, 2, 2, 2, 3,
	3, 1, 3, 3, 2, 1, 1, 1, 1, 3,
	2, 3, 3, 3, 3, 1, 1, 3, 6, 6,
	1, 1, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 4, 4, 4, 4, 4, 4, 4,
	4, 4, 4, 4, 4, 4, 4, 4, 0, 1,
	5, 4, 5, 4, 1, 1, 2, 4, 5, 2,
	4, 5, 1, 2, 2, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 2,
	1, 3, 4, 4, 3, 3,
}
var exprChk = [...]int{

	-1000, -1, -2, -6, -7, -14, 23, -11, -15, -18,
	-19, -20, 15, -12, -16, 7, 80, 81, 62, 27,
	28, 29, 39, 40, 49, 50, 51, 52, 53, 54,
	55, 59, 60, 61, 30, 31, 34, 32, 33, 35,
	36, 37, 38, 71, 72, 73, 80, 81, 82, 83,
	84, 85, 74, 75, 78, 79, 76, 77, -25, -26,
	-31, 45, -32, -3, 21, 22, 14, 75, -7, -6,
	-2, -10, 2, -9, 5, 23, 23, -4, 25, 26,
	7, 7, 23, -21, -22, -23, 41, -21, -21, -21,
	-21, -21, -21, -21, -21, -21, -21, -21, -21, -21,
	-21, -26, -32, -24, -37, -30, -33, -34, 42, 44,
	43, 63, 65, -9, -42, -41, -28, 23, 46, 47,
	5, -29, -27, 6, -17, 66, 24, 24, 16, 2,
	19, 16, 12, 75, 13, 14, -8, 7, -14, 23,
	-7, 7, 23, 23, 23, -7, -2, 67, 68, 69,
	70, -2, -2, -2, -2, -2, -2, -2, -2, -2,
	-2, -2, -2, -2, -2, -30, 72, 19, 71, -39,
	-38, 5, 6, 6, -30, 6, -36, -35, 5, 12,
	75, 78, 79, 76, 77, 74, 23, -9, 6, 6,
	6, 6, 2, 24, 19, 9, -40, -25, 45, -14,
	-8, 24, 19, -7, 7, -5, 24, 5, -5, 24,
	19, 23, 23, 23, 23, -30, -30, -30, 19, 12,
	24, 19, 12, 66, 8, 4, 7, 66, 8, 4,
	7, 8, 4, 7, 8, 4, 7, 8, 4, 7,
	8, 4, 7, 8, 4, 7, 6, -4, -8, -43,
	-40, -25, 64, 9, 45, 9, -40, 48, 24, -40,
	-25, 24, -4, -7, 24, 19, 19, 24, 24, 6,
	-5, 24, -5, 24, 24, -5, 24, -5, -38, 6,
	-35, 2, 5, 6, 23, 23, 24, 24, -40, -25,
	-40, 8, -43, -30, -43, 9, 5, -13, 56, 57,
	58, 9, 24, 24, -40, 24, -7, 5, 19, 24,
	24, 24, 24, 6, 6, -4, -40, -43, 23, -43,
	-40, 45, 9, 9, 24, -4, 24, 6, 24, 24,
	5, -40, -43, -43, 9, 19, 24, -43, 6, 19,
	6, 24,
}
var exprDef = [...]int{

	0, -2, 1, 2, 3, 10, 0, 4, 5, 6,
	7, 8, 0, 0, 0, 162, 0, 0, 0, 174,
	175, 176, 177, 178, 179, 180, 181, 182, 183, 184,
	185, 186, 187, 188, 165, 166, 167, 168, 169, 170,
	171, 172, 173, 148, 148, 148, 148, 148, 148, 148,
	148, 148, 148, 148, 148, 148, 148, 148, 11, 69,
	71, 0, 80, 0, 56, 57, 58, 59, 3, 2,
	0, 0, 0, 63, 0, 0, 0, 0, 0, 0,
	163, 164, 0, 0, 154, 155, 149, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 70, 81, 72, 73, 74, 75, 76, 82, 83,
	0, 85, 0, 95, 96, 97, 98, 0, 0, 0,
	0, 110, 111, 78, 0, 77, 9, 12, 60, 61,
	0, 62, 0, 0, 0, 0, 0, 0, 0, 0,
	3, 162, 0, 0, 0, 3, 133, 0, 0, 156,
	159, 134, 135, 136, 137, 138, 139, 140, 141, 142,
	143, 144, 145, 146, 147, 100, 0, 0, 0, 87,
	106, 105, 84, 86, 0, 88, 94, 91, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 64, 65, 66,
	67, 68, 38, 45, 0, 13, 0, 0, 0, 0,
	0, 49, 0, 3, 162, 0, 194, 190, 0, 195,
	0, 0, 0, 0, 0, 101, 102, 103, 0, 0,
	99, 0, 0, 0, 117, 124, 131, 0, 116, 123,
	130, 112, 119, 126, 113, 120, 127, 114, 121, 128,
	115, 122, 129, 118, 125, 132, 0, 47, 0, 14,
	17, 33, 0, 21, 0, 25, 0, 0, 0, 0,
	0, 37, 51, 3, 50, 0, 0, 192, 193, 0,
	0, 151, 0, 153, 157, 0, 160, 0, 107, 104,
	92, 93, 89, 90, 0, 0, 79, 46, 18, 34,
	35, 189, 22, 41, 26, 29, 39, 0, 42, 43,
	44, 15, 0, 0, 0, 52, 3, 191, 0, 150,
	152, 158, 161, 0, 0, 48, 36, 30, 0, 16,
	19, 0, 23, 27, 0, 53, 54, 0, 108, 109,
	0, 20, 24, 28, 31, 0, 40, 32, 0, 0,
	0, 55,
}
var exprTok1 = [...]int{

	1,
}
var exprTok2 = [...]int{

	2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
	12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
	22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
	32, 33, 34, 35, 36, 37, 38, 39, 40, 41,
	42, 43, 44, 45, 46, 47, 48, 49, 50, 51,
	52, 53, 54, 55, 56, 57, 58, 59, 60, 61,
	62, 63, 64, 65, 66, 67, 68, 69, 70, 71,
	72, 73, 74, 75, 76, 77, 78, 79, 80, 81,
	82, 83, 84, 85,
}
var exprTok3 = [...]int{
	0,
}

var exprErrorMessages = [...]struct {
	state int
	token int
	msg   string
}{}


/*	parser for yacc output	*/

var (
	exprDebug        = 0
	exprErrorVerbose = false
)

type exprLexer interface {
	Lex(lval *exprSymType) int
	Error(s string)
}

type exprParser interface {
	Parse(exprLexer) int
	Lookahead() int
}

type exprParserImpl struct {
	lval  exprSymType
	stack [exprInitialStackSize]exprSymType
	char  int
}

func (p *exprParserImpl) Lookahead() int {
	return p.char
}

func exprNewParser() exprParser {
	return &exprParserImpl{}
}

const exprFlag = -1000

func exprTokname(c int) string {
	if c >= 1 && c-1 < len(exprToknames) {
		if exprToknames[c-1] != "" {
			return exprToknames[c-1]
		}
	}
	return __yyfmt__.Sprintf("tok-%v", c)
}

func exprStatname(s int) string {
	if s >= 0 && s < len(exprStatenames) {
		if exprStatenames[s] != "" {
			return exprStatenames[s]
		}
	}
	return __yyfmt__.Sprintf("state-%v", s)
}

func exprErrorMessage(state, lookAhead int) string {
	const TOKSTART = 4

	if !exprErrorVerbose {
		return "syntax error"
	}

	for _, e := range exprErrorMessages {
		if e.state == state && e.token == lookAhead {
			return "syntax error: " + e.msg
		}
	}

	res := "syntax error: unexpected " + exprTokname(lookAhead)

	// To match Bison, suggest at most four expected tokens.
	expected := make([]int, 0, 4)

	// Look for shiftable tokens.
	base := exprPact[state]
	for tok := TOKSTART; tok-1 < len(exprToknames); tok++ {
		if n := base + tok; n >= 0 && n < exprLast && exprChk[exprAct[n]] == tok {
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}
	}

	if exprDef[state] == -2 {
		i := 0
		for exprExca[i] != -1 || exprExca[i+1] != state {
			i += 2
		}

		// Look for tokens that we accept or reduce.
		for i += 2; exprExca[i] >= 0; i += 2 {
			tok := exprExca[i]
			if tok < TOKSTART || exprExca[i+1] == 0 {
				continue
			}
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}

		// If the default action is to accept or reduce, give up.
		if exprExca[i+1] != 0 {
			return res
		}
	}

	for i, tok := range expected {
		if i == 0 {
			res += ", expecting "
		} else {
			res += " or "
		}
		res += exprTokname(tok)
	}
	return res
}

func exprlex1(lex exprLexer, lval *exprSymType) (char, token int) {
	token = 0
	char = lex.Lex(lval)
	if char <= 0 {
		token = exprTok1[0]
		goto out
	}
	if char < len(exprTok1) {
		token = exprTok1[char]
		goto out
	}
	if char >= exprPrivate {
		if char < exprPrivate+len(exprTok2) {
			token = exprTok2[char-exprPrivate]
			goto out
		}
	}
	for i := 0; i < len(exprTok3); i += 2 {
		token = exprTok3[i+0]
		if token == char {
			token = exprTok3[i+1]
			goto out
		}
	}

out:
	if token == 0 {
		token = exprTok2[1] /* unknown char */
	}
	if exprDebug >= 3 {
		__yyfmt__.Printf("lex %s(%d)\n", exprTokname(token), uint(char))
	}
	return char, token
}

func exprParse(exprlex exprLexer) int {
	return exprNewParser().Parse(exprlex)
}

func (exprrcvr *exprParserImpl) Parse(exprlex exprLexer) int {
	var exprn int
	var exprVAL exprSymType
	var exprDollar []exprSymType
	_ = exprDollar // silence set and not used
	exprS := exprrcvr.stack[:]

	Nerrs := 0   /* number of errors */
	Errflag := 0 /* error recovery flag */
	exprstate := 0
	exprrcvr.char = -1
	exprtoken := -1 // exprrcvr.char translated into internal numbering
	defer func() {
		// Make sure we report no lookahead when not parsing.
		exprstate = -1
		exprrcvr.char = -1
		exprtoken = -1
	}()
	exprp := -1
	goto exprstack

ret0:
	return 0

ret1:
	return 1

exprstack:
	/* put a state and value onto the stack */
	if exprDebug >= 4 {
		__yyfmt__.Printf("char %v in %v\n", exprTokname(exprtoken), exprStatname(exprstate))
	}

	exprp++
	if exprp >= len(exprS) {
		nyys := make([]exprSymType, len(exprS)*2)
		copy(nyys, exprS)
		exprS = nyys
	}
	exprS[exprp] = exprVAL
	exprS[exprp].yys = exprstate

exprnewstate:
	exprn = exprPact[exprstate]
	if exprn <= exprFlag {
		goto exprdefault /* simple state */
	}
	if exprrcvr.char < 0 {
		exprrcvr.char, exprtoken = exprlex1(exprlex, &exprrcvr.lval)
	}
	exprn += exprtoken
	if exprn < 0 || exprn >= exprLast {
		goto exprdefault
	}
	exprn = exprAct[exprn]
	if exprChk[exprn] == exprtoken { /* valid shift */
		exprrcvr.char = -1
		exprtoken = -1
		exprVAL = exprrcvr.lval
		exprstate = exprn
		if Errflag > 0 {
			Errflag--
		}
		goto exprstack
	}

exprdefault:
	/* default state action */
	exprn = exprDef[exprstate]
	if exprn == -2 {
		if exprrcvr.char < 0 {
			exprrcvr.char, exprtoken = exprlex1(exprlex, &exprrcvr.lval)
		}

		/* look through exception table */
		xi := 0
		for {
			if exprExca[xi+0] == -1 && exprExca[xi+1] == exprstate {
				break
			}
			xi += 2
		}
		for xi += 2; ; xi += 2 {
			exprn = exprExca[xi+0]
			if exprn < 0 || exprn == exprtoken {
				break
			}
		}
		exprn = exprExca[xi+1]
		if exprn < 0 {
			goto ret0
		}
	}
	if exprn == 0 {
		/* error ... attempt to resume parsing */
		switch Errflag {
		case 0: /* brand new error */
			exprlex.Error(exprErrorMessage(exprstate, exprtoken))
			Nerrs++
			if exprDebug >= 1 {
				__yyfmt__.Printf("%s", exprStatname(exprstate))
				__yyfmt__.Printf(" saw %s\n", exprTokname(exprtoken))
			}
			fallthrough

		case 1, 2: /* incompletely recovered error ... try again */
			Errflag = 3

			/* find a state where "error" is a legal shift action */
			for exprp >= 0 {
				exprn = exprPact[exprS[exprp].yys] + exprErrCode
				if exprn >= 0 && exprn < exprLast {
					exprstate = exprAct[exprn] /* simulate a shift of "error" */
					if exprChk[exprstate] == exprErrCode {
						goto exprstack
					}
				}

				/* the current p has no shift on "error", pop stack */
				if exprDebug >= 2 {
					__yyfmt__.Printf("error recovery pops state %d\n", exprS[exprp].yys)
				}
				exprp--
			}
			/* there is no state on the stack with an error shift ... abort */
			goto ret1

		case 3: /* no shift yet; clobber input char */
			if exprDebug >= 2 {
				__yyfmt__.Printf("error recovery discards %s\n", exprTokname(exprtoken))
			}
			if exprtoken == exprEofCode {
				goto ret1
			}
			exprrcvr.char = -1
			exprtoken = -1
			goto exprnewstate /* try again in the same state */
		}
	}

	/* reduction by production exprn */
	if exprDebug >= 2 {
		__yyfmt__.Printf("reduce %v in:\n\t%v\n", exprn, exprStatname(exprstate))
	}

	exprnt := exprn
	exprpt := exprp
	_ = exprpt // guard against "declared and not used"

	exprp -= exprR2[exprn]
	// exprp is now the index of $0. Perform the default action. Iff the
	// reduced production is ε, $1 is possibly out of range.
	if exprp+1 >= len(exprS) {
		nyys := make([]exprSymType, len(exprS)*2)
		copy(nyys, exprS)
		exprS = nyys
	}
	exprVAL = exprS[exprp+1]

	/* consult goto table to find next state */
	exprn = exprR1[exprn]
	exprg := exprPgo[exprn]
	exprj := exprg + exprS[exprp].yys + 1

	if exprj >= exprLast {
		exprstate = exprAct[exprg]
	} else {
		exprstate = exprAct[exprj]
		if exprChk[exprstate] != -exprn {
			exprstate = exprAct[exprg]
		}
	}
	// dummy call; replaced with literal code
	switch exprnt {

	case 1:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprlex.(*parser).expr = exprDollar[1].Expr
		}
	case 2:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Expr = exprDollar[1].LogExpr
		}
	case 3:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Expr = exprDollar[1].MetricExpr
		}
	case 4:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.MetricExpr = exprDollar[1].RangeAggregationExpr
		}
	case 5:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.MetricExpr = exprDollar[1].VectorAggregationExpr
		}
	case 6:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.MetricExpr = exprDollar[1].BinOpExpr
		}
	case 7:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.MetricExpr = exprDollar[1].LiteralExpr
		}
	case 8:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.MetricExpr = exprDollar[1].LabelReplaceExpr
		}
	case 9:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.MetricExpr = exprDollar[2].MetricExpr
		}
	case 10:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LogExpr = newMatcherExpr(exprDollar[1].Selector)
		}
	case 11:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LogExpr = newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].PipelineExpr)
		}
	case 12:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogExpr = exprDollar[2].LogExpr
		}
	case 13:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].duration, nil, nil)
		}
	case 14:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].duration, nil, exprDollar[3].OffsetExpr)
		}
	case 15:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[2].Selector), exprDollar[4].duration, nil, nil)
		}
	case 16:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[2].Selector), exprDollar[4].duration, nil, exprDollar[5].OffsetExpr)
		}
	case 17:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].duration, exprDollar[3].UnwrapExpr, nil)
		}
	case 18:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].duration, exprDollar[4].UnwrapExpr, exprDollar[3].OffsetExpr)
		}
	case 19:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[2].Selector), exprDollar[4].duration, exprDollar[5].UnwrapExpr, nil)
		}
	case 20:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[2].Selector), exprDollar[4].duration, exprDollar[6].UnwrapExpr, exprDollar[5].OffsetExpr)
		}
	case 21:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[1].Selector), exprDollar[3].duration, exprDollar[2].UnwrapExpr, nil)
		}
	case 22:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[1].Selector), exprDollar[3].duration, exprDollar[2].UnwrapExpr, exprDollar[4].OffsetExpr)
		}
	case 23:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[2].Selector), exprDollar[5].duration, exprDollar[3].UnwrapExpr, nil)
		}
	case 24:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newMatcherExpr(exprDollar[2].Selector), exprDollar[5].duration, exprDollar[3].UnwrapExpr, exprDollar[6].OffsetExpr)
		}
	case 25:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].PipelineExpr), exprDollar[3].duration, nil, nil)
		}
	case 26:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].PipelineExpr), exprDollar[3].duration, nil, exprDollar[4].OffsetExpr)
		}
	case 27:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[2].Selector), exprDollar[3].PipelineExpr), exprDollar[5].duration, nil, nil)
		}
	case 28:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[2].Selector), exprDollar[3].PipelineExpr), exprDollar[5].duration, nil, exprDollar[6].OffsetExpr)
		}
	case 29:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].PipelineExpr), exprDollar[4].duration, exprDollar[3].UnwrapExpr, nil)
		}
	case 30:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[2].PipelineExpr), exprDollar[4].duration, exprDollar[3].UnwrapExpr, exprDollar[5].OffsetExpr)
		}
	case 31:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[2].Selector), exprDollar[3].PipelineExpr), exprDollar[6].duration, exprDollar[4].UnwrapExpr, nil)
		}
	case 32:
		exprDollar = exprS[exprpt-7 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[2].Selector), exprDollar[3].PipelineExpr), exprDollar[6].duration, exprDollar[4].UnwrapExpr, exprDollar[7].OffsetExpr)
		}
	case 33:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[3].PipelineExpr), exprDollar[2].duration, nil, nil)
		}
	case 34:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[4].PipelineExpr), exprDollar[2].duration, nil, exprDollar[3].OffsetExpr)
		}
	case 35:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[3].PipelineExpr), exprDollar[2].duration, exprDollar[4].UnwrapExpr, nil)
		}
	case 36:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LogRangeExpr = newLogRange(newPipelineExpr(newMatcherExpr(exprDollar[1].Selector), exprDollar[4].PipelineExpr), exprDollar[2].duration, exprDollar[5].UnwrapExpr, exprDollar[3].OffsetExpr)
		}
	case 37:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LogRangeExpr = exprDollar[2].LogRangeExpr
		}
	case 39:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.UnwrapExpr = newUnwrapExpr(exprDollar[3].str, "")
		}
	case 40:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.UnwrapExpr = newUnwrapExpr(exprDollar[5].str, exprDollar[3].ConvOp)
		}
	case 41:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.UnwrapExpr = exprDollar[1].UnwrapExpr.addPostFilter(exprDollar[3].LabelFilter)
		}
	case 42:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.ConvOp = OpConvBytes
		}
	case 43:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.ConvOp = OpConvDuration
		}
	case 44:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.ConvOp = OpConvDurationSeconds
		}
	case 45:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.RangeAggregationExpr = newRangeAggregationExpr(exprDollar[3].LogRangeExpr, exprDollar[1].RangeOp, nil, nil)
		}
	case 46:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.RangeAggregationExpr = newRangeAggregationExpr(exprDollar[5].LogRangeExpr, exprDollar[1].RangeOp, nil, &exprDollar[3].str)
		}
	case 47:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.RangeAggregationExpr = newRangeAggregationExpr(exprDollar[3].LogRangeExpr, exprDollar[1].RangeOp, exprDollar[5].Grouping, nil)
		}
	case 48:
		exprDollar = exprS[exprpt-7 : exprpt+1]
		{
			exprVAL.RangeAggregationExpr = newRangeAggregationExpr(exprDollar[5].LogRangeExpr, exprDollar[1].RangeOp, exprDollar[7].Grouping, &exprDollar[3].str)
		}
	case 49:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.VectorAggregationExpr = mustNewVectorAggregationExpr(exprDollar[3].MetricExpr, exprDollar[1].VectorOp, nil, nil)
		}
	case 50:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.VectorAggregationExpr = mustNewVectorAggregationExpr(exprDollar[4].MetricExpr, exprDollar[1].VectorOp, exprDollar[2].Grouping, nil)
		}
	case 51:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.VectorAggregationExpr = mustNewVectorAggregationExpr(exprDollar[3].MetricExpr, exprDollar[1].VectorOp, exprDollar[5].Grouping, nil)
		}
	case 52:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.VectorAggregationExpr = mustNewVectorAggregationExpr(exprDollar[5].MetricExpr, exprDollar[1].VectorOp, nil, &exprDollar[3].str)
		}
	case 53:
		exprDollar = exprS[exprpt-7 : exprpt+1]
		{
			exprVAL.VectorAggregationExpr = mustNewVectorAggregationExpr(exprDollar[5].MetricExpr, exprDollar[1].VectorOp, exprDollar[7].Grouping, &exprDollar[3].str)
		}
	case 54:
		exprDollar = exprS[exprpt-7 : exprpt+1]
		{
			exprVAL.VectorAggregationExpr = mustNewVectorAggregationExpr(exprDollar[6].MetricExpr, exprDollar[1].VectorOp, exprDollar[2].Grouping, &exprDollar[4].str)
		}
	case 55:
		exprDollar = exprS[exprpt-12 : exprpt+1]
		{
			exprVAL.LabelReplaceExpr = mustNewLabelReplaceExpr(exprDollar[3].MetricExpr, exprDollar[5].str, exprDollar[7].str, exprDollar[9].str, exprDollar[11].str)
		}
	case 56:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Filter = labels.MatchRegexp
		}
	case 57:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Filter = labels.MatchEqual
		}
	case 58:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Filter = labels.MatchNotRegexp
		}
	case 59:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Filter = labels.MatchNotEqual
		}
	case 60:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Selector = exprDollar[2].Matchers
		}
	case 61:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Selector = exprDollar[2].Matchers
		}
	case 62:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
		}
	case 63:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Matchers = []*labels.Matcher{exprDollar[1].Matcher}
		}
	case 64:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Matchers = append(exprDollar[1].Matchers, exprDollar[3].Matcher)
		}
	case 65:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Matcher = mustNewMatcher(labels.MatchEqual, exprDollar[1].str, exprDollar[3].str)
		}
	case 66:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Matcher = mustNewMatcher(labels.MatchNotEqual, exprDollar[1].str, exprDollar[3].str)
		}
	case 67:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Matcher = mustNewMatcher(labels.MatchRegexp, exprDollar[1].str, exprDollar[3].str)
		}
	case 68:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Matcher = mustNewMatcher(labels.MatchNotRegexp, exprDollar[1].str, exprDollar[3].str)
		}
	case 69:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.PipelineExpr = MultiStageExpr{exprDollar[1].PipelineStage}
		}
	case 70:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.PipelineExpr = append(exprDollar[1].PipelineExpr, exprDollar[2].PipelineStage)
		}
	case 71:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.PipelineStage = exprDollar[1].LineFilters
		}
	case 72:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.PipelineStage = exprDollar[2].LabelParser
		}
	case 73:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.PipelineStage = exprDollar[2].JSONExpressionParser
		}
	case 74:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.PipelineStage = &LabelFilterExpr{LabelFilterer: exprDollar[2].LabelFilter}
		}
	case 75:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.PipelineStage = exprDollar[2].LineFormatExpr
		}
	case 76:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.PipelineStage = exprDollar[2].LabelFormatExpr
		}
	case 77:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.FilterOp = OpFilterIP
		}
	case 78:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LineFilter = newLineFilterExpr(exprDollar[1].Filter, "", exprDollar[2].str)
		}
	case 79:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.LineFilter = newLineFilterExpr(exprDollar[1].Filter, exprDollar[2].FilterOp, exprDollar[4].str)
		}
	case 80:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LineFilters = exprDollar[1].LineFilter
		}
	case 81:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LineFilters = newNestedLineFilterExpr(exprDollar[1].LineFilters, exprDollar[2].LineFilter)
		}
	case 82:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelParser = newLabelParserExpr(OpParserTypeJSON, "")
		}
	case 83:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelParser = newLabelParserExpr(OpParserTypeLogfmt, "")
		}
	case 84:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LabelParser = newLabelParserExpr(OpParserTypeRegexp, exprDollar[2].str)
		}
	case 85:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelParser = newLabelParserExpr(OpParserTypeUnpack, "")
		}
	case 86:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LabelParser = newLabelParserExpr(OpParserTypePattern, exprDollar[2].str)
		}
	case 87:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.JSONExpressionParser = newJSONExpressionParser(exprDollar[2].JSONExpressionList)
		}
	case 88:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LineFormatExpr = newLineFmtExpr(exprDollar[2].str)
		}
	case 89:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelFormat = log.NewRenameLabelFmt(exprDollar[1].str, exprDollar[3].str)
		}
	case 90:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelFormat = log.NewTemplateLabelFmt(exprDollar[1].str, exprDollar[3].str)
		}
	case 91:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelsFormat = []log.LabelFmt{exprDollar[1].LabelFormat}
		}
	case 92:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelsFormat = append(exprDollar[1].LabelsFormat, exprDollar[3].LabelFormat)
		}
	case 94:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LabelFormatExpr = newLabelFmtExpr(exprDollar[2].LabelsFormat)
		}
	case 95:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelFilter = log.NewStringLabelFilter(exprDollar[1].Matcher)
		}
	case 96:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelFilter = exprDollar[1].IPLabelFilter
		}
	case 97:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelFilter = exprDollar[1].UnitFilter
		}
	case 98:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LabelFilter = exprDollar[1].NumberFilter
		}
	case 99:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelFilter = exprDollar[2].LabelFilter
		}
	case 100:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LabelFilter = log.NewAndLabelFilter(exprDollar[1].LabelFilter, exprDollar[2].LabelFilter)
		}
	case 101:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelFilter = log.NewAndLabelFilter(exprDollar[1].LabelFilter, exprDollar[3].LabelFilter)
		}
	case 102:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelFilter = log.NewAndLabelFilter(exprDollar[1].LabelFilter, exprDollar[3].LabelFilter)
		}
	case 103:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.LabelFilter = log.NewOrLabelFilter(exprDollar[1].LabelFilter, exprDollar[3].LabelFilter)
		}
	case 104:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.JSONExpression = log.NewJSONExpr(exprDollar[1].str, exprDollar[3].str)
		}
	case 105:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.JSONExpression = log.NewJSONExpr(exprDollar[1].str, exprDollar[1].str)
		}
	case 106:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.JSONExpressionList = []log.JSONExpression{exprDollar[1].JSONExpression}
		}
	case 107:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.JSONExpressionList = append(exprDollar[1].JSONExpressionList, exprDollar[3].JSONExpression)
		}
	case 108:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.IPLabelFilter = log.NewIPLabelFilter(exprDollar[5].str, exprDollar[1].str, log.LabelFilterEqual)
		}
	case 109:
		exprDollar = exprS[exprpt-6 : exprpt+1]
		{
			exprVAL.IPLabelFilter = log.NewIPLabelFilter(exprDollar[5].str, exprDollar[1].str, log.LabelFilterNotEqual)
		}
	case 110:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.UnitFilter = exprDollar[1].DurationFilter
		}
	case 111:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.UnitFilter = exprDollar[1].BytesFilter
		}
	case 112:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterGreaterThan, exprDollar[1].str, exprDollar[3].duration)
		}
	case 113:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterGreaterThanOrEqual, exprDollar[1].str, exprDollar[3].duration)
		}
	case 114:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterLesserThan, exprDollar[1].str, exprDollar[3].duration)
		}
	case 115:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterLesserThanOrEqual, exprDollar[1].str, exprDollar[3].duration)
		}
	case 116:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterNotEqual, exprDollar[1].str, exprDollar[3].duration)
		}
	case 117:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterEqual, exprDollar[1].str, exprDollar[3].duration)
		}
	case 118:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.DurationFilter = log.NewDurationLabelFilter(log.LabelFilterEqual, exprDollar[1].str, exprDollar[3].duration)
		}
	case 119:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterGreaterThan, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 120:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterGreaterThanOrEqual, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 121:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterLesserThan, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 122:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterLesserThanOrEqual, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 123:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterNotEqual, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 124:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterEqual, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 125:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.BytesFilter = log.NewBytesLabelFilter(log.LabelFilterEqual, exprDollar[1].str, exprDollar[3].bytes)
		}
	case 126:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterGreaterThan, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 127:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterGreaterThanOrEqual, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 128:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterLesserThan, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 129:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterLesserThanOrEqual, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 130:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterNotEqual, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 131:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterEqual, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 132:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.NumberFilter = log.NewNumericLabelFilter(log.LabelFilterEqual, exprDollar[1].str, mustNewFloat(exprDollar[3].str))
		}
	case 133:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("or", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 134:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("and", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 135:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("unless", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 136:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("+", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 137:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("-", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 138:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("*", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 139:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("/", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 140:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("%", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 141:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("^", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 142:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("==", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 143:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("!=", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 144:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr(">", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 145:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr(">=", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 146:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("<", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 147:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpExpr = mustNewBinOpExpr("<=", exprDollar[3].BinOpModifier, exprDollar[1].Expr, exprDollar[4].Expr)
		}
	case 148:
		exprDollar = exprS[exprpt-0 : exprpt+1]
		{
			exprVAL.BoolModifier = &BinOpOptions{VectorMatching: &VectorMatching{Card: CardOneToOne}}
		}
	case 149:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.BoolModifier = &BinOpOptions{VectorMatching: &VectorMatching{Card: CardOneToOne}, ReturnBool: true}
		}
	case 150:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.OnOrIgnoringModifier = exprDollar[1].BoolModifier
			exprVAL.OnOrIgnoringModifier.VectorMatching.On = true
			exprVAL.OnOrIgnoringModifier.VectorMatching.MatchingLabels = exprDollar[4].Labels
		}
	case 151:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.OnOrIgnoringModifier = exprDollar[1].BoolModifier
			exprVAL.OnOrIgnoringModifier.VectorMatching.On = true
		}
	case 152:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.OnOrIgnoringModifier = exprDollar[1].BoolModifier
			exprVAL.OnOrIgnoringModifier.VectorMatching.MatchingLabels = exprDollar[4].Labels
		}
	case 153:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.OnOrIgnoringModifier = exprDollar[1].BoolModifier
		}
	case 154:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].BoolModifier
		}
	case 155:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
		}
	case 156:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
			exprVAL.BinOpModifier.VectorMatching.Card = CardManyToOne
		}
	case 157:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
			exprVAL.BinOpModifier.VectorMatching.Card = CardManyToOne
		}
	case 158:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
			exprVAL.BinOpModifier.VectorMatching.Card = CardManyToOne
			exprVAL.BinOpModifier.VectorMatching.Include = exprDollar[4].Labels
		}
	case 159:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
			exprVAL.BinOpModifier.VectorMatching.Card = CardOneToMany
		}
	case 160:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
			exprVAL.BinOpModifier.VectorMatching.Card = CardOneToMany
		}
	case 161:
		exprDollar = exprS[exprpt-5 : exprpt+1]
		{
			exprVAL.BinOpModifier = exprDollar[1].OnOrIgnoringModifier
			exprVAL.BinOpModifier.VectorMatching.Card = CardOneToMany
			exprVAL.BinOpModifier.VectorMatching.Include = exprDollar[4].Labels
		}
	case 162:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.LiteralExpr = mustNewLiteralExpr(exprDollar[1].str, false)
		}
	case 163:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LiteralExpr = mustNewLiteralExpr(exprDollar[2].str, false)
		}
	case 164:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.LiteralExpr = mustNewLiteralExpr(exprDollar[2].str, true)
		}
	case 165:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeSum
		}
	case 166:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeAvg
		}
	case 167:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeCount
		}
	case 168:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeMax
		}
	case 169:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeMin
		}
	case 170:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeStddev
		}
	case 171:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeStdvar
		}
	case 172:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeBottomK
		}
	case 173:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.VectorOp = OpTypeTopK
		}
	case 174:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeCount
		}
	case 175:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeRate
		}
	case 176:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeRateCounter
		}
	case 177:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeBytes
		}
	case 178:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeBytesRate
		}
	case 179:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeAvg
		}
	case 180:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeSum
		}
	case 181:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeMin
		}
	case 182:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeMax
		}
	case 183:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeStdvar
		}
	case 184:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeStddev
		}
	case 185:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeQuantile
		}
	case 186:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeFirst
		}
	case 187:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeLast
		}
	case 188:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.RangeOp = OpRangeTypeAbsent
		}
	case 189:
		exprDollar = exprS[exprpt-2 : exprpt+1]
		{
			exprVAL.OffsetExpr = newOffsetExpr(exprDollar[2].duration)
		}
	case 190:
		exprDollar = exprS[exprpt-1 : exprpt+1]
		{
			exprVAL.Labels = []string{exprDollar[1].str}
		}
	case 191:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Labels = append(exprDollar[1].Labels, exprDollar[3].str)
		}
	case 192:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.Grouping = &Grouping{Without: false, Groups: exprDollar[3].Labels}
		}
	case 193:
		exprDollar = exprS[exprpt-4 : exprpt+1]
		{
			exprVAL.Grouping = &Grouping{Without: true, Groups: exprDollar[3].Labels}
		}
	case 194:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Grouping = &Grouping{Without: false, Groups: nil}
		}
	case 195:
		exprDollar = exprS[exprpt-3 : exprpt+1]
		{
			exprVAL.Grouping = &Grouping{Without: true, Groups: nil}
		}
	}
	goto exprstack /* stack new state and value */
}
