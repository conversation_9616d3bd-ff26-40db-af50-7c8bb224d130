{{- if gt (len .Values.alerting_groups) 0 }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "loki.fullname" . }}-alerting-rules
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "loki.name" . }}
    chart: {{ template "loki.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  {{ template "loki.fullname" . }}-alerting-rules.yaml: |-
    groups:
    {{- toYaml .Values.alerting_groups | nindent 6 }}
{{- end }}