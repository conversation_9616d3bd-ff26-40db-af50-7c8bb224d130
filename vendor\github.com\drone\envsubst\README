Go package for expanding variables in a string using ${var} syntax. Includes support for bash string replacement functions.

Documentation:

    http://godoc.org/github.com/drone/envsubst

Supported Functions:

    ${var^}
    ${var^^}
    ${var,}
    ${var,,}
    ${var:position}
    ${var:position:length}
    ${var#substring}
    ${var##substring}
    ${var%substring}
    ${var%%substring}
    ${var/substring/replacement}
    ${var//substring/replacement}
    ${var/#substring/replacement}
    ${var/%substring/replacement}
    ${#var}
    ${var=default}
    ${var:=default}
    ${var:-default}

Unsupported Functions:

    ${var-default}
    ${var+default}
    ${var:?default}
    ${var:+default}
