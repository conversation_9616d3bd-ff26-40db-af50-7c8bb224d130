// Code generated by protoc-gen-go. DO NOT EDIT.
// source: google.golang.org/appengine/internal/socket/socket_service.proto

package socket

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RemoteSocketServiceError_ErrorCode int32

const (
	RemoteSocketServiceError_SYSTEM_ERROR      RemoteSocketServiceError_ErrorCode = 1
	RemoteSocketServiceError_GAI_ERROR         RemoteSocketServiceError_ErrorCode = 2
	RemoteSocketServiceError_FAILURE           RemoteSocketServiceError_ErrorCode = 4
	RemoteSocketServiceError_PERMISSION_DENIED RemoteSocketServiceError_ErrorCode = 5
	RemoteSocketServiceError_INVALID_REQUEST   RemoteSocketServiceError_ErrorCode = 6
	RemoteSocketServiceError_SOCKET_CLOSED     RemoteSocketServiceError_ErrorCode = 7
)

var RemoteSocketServiceError_ErrorCode_name = map[int32]string{
	1: "SYSTEM_ERROR",
	2: "GAI_ERROR",
	4: "FAILURE",
	5: "PERMISSION_DENIED",
	6: "INVALID_REQUEST",
	7: "SOCKET_CLOSED",
}
var RemoteSocketServiceError_ErrorCode_value = map[string]int32{
	"SYSTEM_ERROR":      1,
	"GAI_ERROR":         2,
	"FAILURE":           4,
	"PERMISSION_DENIED": 5,
	"INVALID_REQUEST":   6,
	"SOCKET_CLOSED":     7,
}

func (x RemoteSocketServiceError_ErrorCode) Enum() *RemoteSocketServiceError_ErrorCode {
	p := new(RemoteSocketServiceError_ErrorCode)
	*p = x
	return p
}
func (x RemoteSocketServiceError_ErrorCode) String() string {
	return proto.EnumName(RemoteSocketServiceError_ErrorCode_name, int32(x))
}
func (x *RemoteSocketServiceError_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RemoteSocketServiceError_ErrorCode_value, data, "RemoteSocketServiceError_ErrorCode")
	if err != nil {
		return err
	}
	*x = RemoteSocketServiceError_ErrorCode(value)
	return nil
}
func (RemoteSocketServiceError_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{0, 0}
}

type RemoteSocketServiceError_SystemError int32

const (
	RemoteSocketServiceError_SYS_SUCCESS         RemoteSocketServiceError_SystemError = 0
	RemoteSocketServiceError_SYS_EPERM           RemoteSocketServiceError_SystemError = 1
	RemoteSocketServiceError_SYS_ENOENT          RemoteSocketServiceError_SystemError = 2
	RemoteSocketServiceError_SYS_ESRCH           RemoteSocketServiceError_SystemError = 3
	RemoteSocketServiceError_SYS_EINTR           RemoteSocketServiceError_SystemError = 4
	RemoteSocketServiceError_SYS_EIO             RemoteSocketServiceError_SystemError = 5
	RemoteSocketServiceError_SYS_ENXIO           RemoteSocketServiceError_SystemError = 6
	RemoteSocketServiceError_SYS_E2BIG           RemoteSocketServiceError_SystemError = 7
	RemoteSocketServiceError_SYS_ENOEXEC         RemoteSocketServiceError_SystemError = 8
	RemoteSocketServiceError_SYS_EBADF           RemoteSocketServiceError_SystemError = 9
	RemoteSocketServiceError_SYS_ECHILD          RemoteSocketServiceError_SystemError = 10
	RemoteSocketServiceError_SYS_EAGAIN          RemoteSocketServiceError_SystemError = 11
	RemoteSocketServiceError_SYS_EWOULDBLOCK     RemoteSocketServiceError_SystemError = 11
	RemoteSocketServiceError_SYS_ENOMEM          RemoteSocketServiceError_SystemError = 12
	RemoteSocketServiceError_SYS_EACCES          RemoteSocketServiceError_SystemError = 13
	RemoteSocketServiceError_SYS_EFAULT          RemoteSocketServiceError_SystemError = 14
	RemoteSocketServiceError_SYS_ENOTBLK         RemoteSocketServiceError_SystemError = 15
	RemoteSocketServiceError_SYS_EBUSY           RemoteSocketServiceError_SystemError = 16
	RemoteSocketServiceError_SYS_EEXIST          RemoteSocketServiceError_SystemError = 17
	RemoteSocketServiceError_SYS_EXDEV           RemoteSocketServiceError_SystemError = 18
	RemoteSocketServiceError_SYS_ENODEV          RemoteSocketServiceError_SystemError = 19
	RemoteSocketServiceError_SYS_ENOTDIR         RemoteSocketServiceError_SystemError = 20
	RemoteSocketServiceError_SYS_EISDIR          RemoteSocketServiceError_SystemError = 21
	RemoteSocketServiceError_SYS_EINVAL          RemoteSocketServiceError_SystemError = 22
	RemoteSocketServiceError_SYS_ENFILE          RemoteSocketServiceError_SystemError = 23
	RemoteSocketServiceError_SYS_EMFILE          RemoteSocketServiceError_SystemError = 24
	RemoteSocketServiceError_SYS_ENOTTY          RemoteSocketServiceError_SystemError = 25
	RemoteSocketServiceError_SYS_ETXTBSY         RemoteSocketServiceError_SystemError = 26
	RemoteSocketServiceError_SYS_EFBIG           RemoteSocketServiceError_SystemError = 27
	RemoteSocketServiceError_SYS_ENOSPC          RemoteSocketServiceError_SystemError = 28
	RemoteSocketServiceError_SYS_ESPIPE          RemoteSocketServiceError_SystemError = 29
	RemoteSocketServiceError_SYS_EROFS           RemoteSocketServiceError_SystemError = 30
	RemoteSocketServiceError_SYS_EMLINK          RemoteSocketServiceError_SystemError = 31
	RemoteSocketServiceError_SYS_EPIPE           RemoteSocketServiceError_SystemError = 32
	RemoteSocketServiceError_SYS_EDOM            RemoteSocketServiceError_SystemError = 33
	RemoteSocketServiceError_SYS_ERANGE          RemoteSocketServiceError_SystemError = 34
	RemoteSocketServiceError_SYS_EDEADLK         RemoteSocketServiceError_SystemError = 35
	RemoteSocketServiceError_SYS_EDEADLOCK       RemoteSocketServiceError_SystemError = 35
	RemoteSocketServiceError_SYS_ENAMETOOLONG    RemoteSocketServiceError_SystemError = 36
	RemoteSocketServiceError_SYS_ENOLCK          RemoteSocketServiceError_SystemError = 37
	RemoteSocketServiceError_SYS_ENOSYS          RemoteSocketServiceError_SystemError = 38
	RemoteSocketServiceError_SYS_ENOTEMPTY       RemoteSocketServiceError_SystemError = 39
	RemoteSocketServiceError_SYS_ELOOP           RemoteSocketServiceError_SystemError = 40
	RemoteSocketServiceError_SYS_ENOMSG          RemoteSocketServiceError_SystemError = 42
	RemoteSocketServiceError_SYS_EIDRM           RemoteSocketServiceError_SystemError = 43
	RemoteSocketServiceError_SYS_ECHRNG          RemoteSocketServiceError_SystemError = 44
	RemoteSocketServiceError_SYS_EL2NSYNC        RemoteSocketServiceError_SystemError = 45
	RemoteSocketServiceError_SYS_EL3HLT          RemoteSocketServiceError_SystemError = 46
	RemoteSocketServiceError_SYS_EL3RST          RemoteSocketServiceError_SystemError = 47
	RemoteSocketServiceError_SYS_ELNRNG          RemoteSocketServiceError_SystemError = 48
	RemoteSocketServiceError_SYS_EUNATCH         RemoteSocketServiceError_SystemError = 49
	RemoteSocketServiceError_SYS_ENOCSI          RemoteSocketServiceError_SystemError = 50
	RemoteSocketServiceError_SYS_EL2HLT          RemoteSocketServiceError_SystemError = 51
	RemoteSocketServiceError_SYS_EBADE           RemoteSocketServiceError_SystemError = 52
	RemoteSocketServiceError_SYS_EBADR           RemoteSocketServiceError_SystemError = 53
	RemoteSocketServiceError_SYS_EXFULL          RemoteSocketServiceError_SystemError = 54
	RemoteSocketServiceError_SYS_ENOANO          RemoteSocketServiceError_SystemError = 55
	RemoteSocketServiceError_SYS_EBADRQC         RemoteSocketServiceError_SystemError = 56
	RemoteSocketServiceError_SYS_EBADSLT         RemoteSocketServiceError_SystemError = 57
	RemoteSocketServiceError_SYS_EBFONT          RemoteSocketServiceError_SystemError = 59
	RemoteSocketServiceError_SYS_ENOSTR          RemoteSocketServiceError_SystemError = 60
	RemoteSocketServiceError_SYS_ENODATA         RemoteSocketServiceError_SystemError = 61
	RemoteSocketServiceError_SYS_ETIME           RemoteSocketServiceError_SystemError = 62
	RemoteSocketServiceError_SYS_ENOSR           RemoteSocketServiceError_SystemError = 63
	RemoteSocketServiceError_SYS_ENONET          RemoteSocketServiceError_SystemError = 64
	RemoteSocketServiceError_SYS_ENOPKG          RemoteSocketServiceError_SystemError = 65
	RemoteSocketServiceError_SYS_EREMOTE         RemoteSocketServiceError_SystemError = 66
	RemoteSocketServiceError_SYS_ENOLINK         RemoteSocketServiceError_SystemError = 67
	RemoteSocketServiceError_SYS_EADV            RemoteSocketServiceError_SystemError = 68
	RemoteSocketServiceError_SYS_ESRMNT          RemoteSocketServiceError_SystemError = 69
	RemoteSocketServiceError_SYS_ECOMM           RemoteSocketServiceError_SystemError = 70
	RemoteSocketServiceError_SYS_EPROTO          RemoteSocketServiceError_SystemError = 71
	RemoteSocketServiceError_SYS_EMULTIHOP       RemoteSocketServiceError_SystemError = 72
	RemoteSocketServiceError_SYS_EDOTDOT         RemoteSocketServiceError_SystemError = 73
	RemoteSocketServiceError_SYS_EBADMSG         RemoteSocketServiceError_SystemError = 74
	RemoteSocketServiceError_SYS_EOVERFLOW       RemoteSocketServiceError_SystemError = 75
	RemoteSocketServiceError_SYS_ENOTUNIQ        RemoteSocketServiceError_SystemError = 76
	RemoteSocketServiceError_SYS_EBADFD          RemoteSocketServiceError_SystemError = 77
	RemoteSocketServiceError_SYS_EREMCHG         RemoteSocketServiceError_SystemError = 78
	RemoteSocketServiceError_SYS_ELIBACC         RemoteSocketServiceError_SystemError = 79
	RemoteSocketServiceError_SYS_ELIBBAD         RemoteSocketServiceError_SystemError = 80
	RemoteSocketServiceError_SYS_ELIBSCN         RemoteSocketServiceError_SystemError = 81
	RemoteSocketServiceError_SYS_ELIBMAX         RemoteSocketServiceError_SystemError = 82
	RemoteSocketServiceError_SYS_ELIBEXEC        RemoteSocketServiceError_SystemError = 83
	RemoteSocketServiceError_SYS_EILSEQ          RemoteSocketServiceError_SystemError = 84
	RemoteSocketServiceError_SYS_ERESTART        RemoteSocketServiceError_SystemError = 85
	RemoteSocketServiceError_SYS_ESTRPIPE        RemoteSocketServiceError_SystemError = 86
	RemoteSocketServiceError_SYS_EUSERS          RemoteSocketServiceError_SystemError = 87
	RemoteSocketServiceError_SYS_ENOTSOCK        RemoteSocketServiceError_SystemError = 88
	RemoteSocketServiceError_SYS_EDESTADDRREQ    RemoteSocketServiceError_SystemError = 89
	RemoteSocketServiceError_SYS_EMSGSIZE        RemoteSocketServiceError_SystemError = 90
	RemoteSocketServiceError_SYS_EPROTOTYPE      RemoteSocketServiceError_SystemError = 91
	RemoteSocketServiceError_SYS_ENOPROTOOPT     RemoteSocketServiceError_SystemError = 92
	RemoteSocketServiceError_SYS_EPROTONOSUPPORT RemoteSocketServiceError_SystemError = 93
	RemoteSocketServiceError_SYS_ESOCKTNOSUPPORT RemoteSocketServiceError_SystemError = 94
	RemoteSocketServiceError_SYS_EOPNOTSUPP      RemoteSocketServiceError_SystemError = 95
	RemoteSocketServiceError_SYS_ENOTSUP         RemoteSocketServiceError_SystemError = 95
	RemoteSocketServiceError_SYS_EPFNOSUPPORT    RemoteSocketServiceError_SystemError = 96
	RemoteSocketServiceError_SYS_EAFNOSUPPORT    RemoteSocketServiceError_SystemError = 97
	RemoteSocketServiceError_SYS_EADDRINUSE      RemoteSocketServiceError_SystemError = 98
	RemoteSocketServiceError_SYS_EADDRNOTAVAIL   RemoteSocketServiceError_SystemError = 99
	RemoteSocketServiceError_SYS_ENETDOWN        RemoteSocketServiceError_SystemError = 100
	RemoteSocketServiceError_SYS_ENETUNREACH     RemoteSocketServiceError_SystemError = 101
	RemoteSocketServiceError_SYS_ENETRESET       RemoteSocketServiceError_SystemError = 102
	RemoteSocketServiceError_SYS_ECONNABORTED    RemoteSocketServiceError_SystemError = 103
	RemoteSocketServiceError_SYS_ECONNRESET      RemoteSocketServiceError_SystemError = 104
	RemoteSocketServiceError_SYS_ENOBUFS         RemoteSocketServiceError_SystemError = 105
	RemoteSocketServiceError_SYS_EISCONN         RemoteSocketServiceError_SystemError = 106
	RemoteSocketServiceError_SYS_ENOTCONN        RemoteSocketServiceError_SystemError = 107
	RemoteSocketServiceError_SYS_ESHUTDOWN       RemoteSocketServiceError_SystemError = 108
	RemoteSocketServiceError_SYS_ETOOMANYREFS    RemoteSocketServiceError_SystemError = 109
	RemoteSocketServiceError_SYS_ETIMEDOUT       RemoteSocketServiceError_SystemError = 110
	RemoteSocketServiceError_SYS_ECONNREFUSED    RemoteSocketServiceError_SystemError = 111
	RemoteSocketServiceError_SYS_EHOSTDOWN       RemoteSocketServiceError_SystemError = 112
	RemoteSocketServiceError_SYS_EHOSTUNREACH    RemoteSocketServiceError_SystemError = 113
	RemoteSocketServiceError_SYS_EALREADY        RemoteSocketServiceError_SystemError = 114
	RemoteSocketServiceError_SYS_EINPROGRESS     RemoteSocketServiceError_SystemError = 115
	RemoteSocketServiceError_SYS_ESTALE          RemoteSocketServiceError_SystemError = 116
	RemoteSocketServiceError_SYS_EUCLEAN         RemoteSocketServiceError_SystemError = 117
	RemoteSocketServiceError_SYS_ENOTNAM         RemoteSocketServiceError_SystemError = 118
	RemoteSocketServiceError_SYS_ENAVAIL         RemoteSocketServiceError_SystemError = 119
	RemoteSocketServiceError_SYS_EISNAM          RemoteSocketServiceError_SystemError = 120
	RemoteSocketServiceError_SYS_EREMOTEIO       RemoteSocketServiceError_SystemError = 121
	RemoteSocketServiceError_SYS_EDQUOT          RemoteSocketServiceError_SystemError = 122
	RemoteSocketServiceError_SYS_ENOMEDIUM       RemoteSocketServiceError_SystemError = 123
	RemoteSocketServiceError_SYS_EMEDIUMTYPE     RemoteSocketServiceError_SystemError = 124
	RemoteSocketServiceError_SYS_ECANCELED       RemoteSocketServiceError_SystemError = 125
	RemoteSocketServiceError_SYS_ENOKEY          RemoteSocketServiceError_SystemError = 126
	RemoteSocketServiceError_SYS_EKEYEXPIRED     RemoteSocketServiceError_SystemError = 127
	RemoteSocketServiceError_SYS_EKEYREVOKED     RemoteSocketServiceError_SystemError = 128
	RemoteSocketServiceError_SYS_EKEYREJECTED    RemoteSocketServiceError_SystemError = 129
	RemoteSocketServiceError_SYS_EOWNERDEAD      RemoteSocketServiceError_SystemError = 130
	RemoteSocketServiceError_SYS_ENOTRECOVERABLE RemoteSocketServiceError_SystemError = 131
	RemoteSocketServiceError_SYS_ERFKILL         RemoteSocketServiceError_SystemError = 132
)

var RemoteSocketServiceError_SystemError_name = map[int32]string{
	0:  "SYS_SUCCESS",
	1:  "SYS_EPERM",
	2:  "SYS_ENOENT",
	3:  "SYS_ESRCH",
	4:  "SYS_EINTR",
	5:  "SYS_EIO",
	6:  "SYS_ENXIO",
	7:  "SYS_E2BIG",
	8:  "SYS_ENOEXEC",
	9:  "SYS_EBADF",
	10: "SYS_ECHILD",
	11: "SYS_EAGAIN",
	// Duplicate value: 11: "SYS_EWOULDBLOCK",
	12: "SYS_ENOMEM",
	13: "SYS_EACCES",
	14: "SYS_EFAULT",
	15: "SYS_ENOTBLK",
	16: "SYS_EBUSY",
	17: "SYS_EEXIST",
	18: "SYS_EXDEV",
	19: "SYS_ENODEV",
	20: "SYS_ENOTDIR",
	21: "SYS_EISDIR",
	22: "SYS_EINVAL",
	23: "SYS_ENFILE",
	24: "SYS_EMFILE",
	25: "SYS_ENOTTY",
	26: "SYS_ETXTBSY",
	27: "SYS_EFBIG",
	28: "SYS_ENOSPC",
	29: "SYS_ESPIPE",
	30: "SYS_EROFS",
	31: "SYS_EMLINK",
	32: "SYS_EPIPE",
	33: "SYS_EDOM",
	34: "SYS_ERANGE",
	35: "SYS_EDEADLK",
	// Duplicate value: 35: "SYS_EDEADLOCK",
	36: "SYS_ENAMETOOLONG",
	37: "SYS_ENOLCK",
	38: "SYS_ENOSYS",
	39: "SYS_ENOTEMPTY",
	40: "SYS_ELOOP",
	42: "SYS_ENOMSG",
	43: "SYS_EIDRM",
	44: "SYS_ECHRNG",
	45: "SYS_EL2NSYNC",
	46: "SYS_EL3HLT",
	47: "SYS_EL3RST",
	48: "SYS_ELNRNG",
	49: "SYS_EUNATCH",
	50: "SYS_ENOCSI",
	51: "SYS_EL2HLT",
	52: "SYS_EBADE",
	53: "SYS_EBADR",
	54: "SYS_EXFULL",
	55: "SYS_ENOANO",
	56: "SYS_EBADRQC",
	57: "SYS_EBADSLT",
	59: "SYS_EBFONT",
	60: "SYS_ENOSTR",
	61: "SYS_ENODATA",
	62: "SYS_ETIME",
	63: "SYS_ENOSR",
	64: "SYS_ENONET",
	65: "SYS_ENOPKG",
	66: "SYS_EREMOTE",
	67: "SYS_ENOLINK",
	68: "SYS_EADV",
	69: "SYS_ESRMNT",
	70: "SYS_ECOMM",
	71: "SYS_EPROTO",
	72: "SYS_EMULTIHOP",
	73: "SYS_EDOTDOT",
	74: "SYS_EBADMSG",
	75: "SYS_EOVERFLOW",
	76: "SYS_ENOTUNIQ",
	77: "SYS_EBADFD",
	78: "SYS_EREMCHG",
	79: "SYS_ELIBACC",
	80: "SYS_ELIBBAD",
	81: "SYS_ELIBSCN",
	82: "SYS_ELIBMAX",
	83: "SYS_ELIBEXEC",
	84: "SYS_EILSEQ",
	85: "SYS_ERESTART",
	86: "SYS_ESTRPIPE",
	87: "SYS_EUSERS",
	88: "SYS_ENOTSOCK",
	89: "SYS_EDESTADDRREQ",
	90: "SYS_EMSGSIZE",
	91: "SYS_EPROTOTYPE",
	92: "SYS_ENOPROTOOPT",
	93: "SYS_EPROTONOSUPPORT",
	94: "SYS_ESOCKTNOSUPPORT",
	95: "SYS_EOPNOTSUPP",
	// Duplicate value: 95: "SYS_ENOTSUP",
	96:  "SYS_EPFNOSUPPORT",
	97:  "SYS_EAFNOSUPPORT",
	98:  "SYS_EADDRINUSE",
	99:  "SYS_EADDRNOTAVAIL",
	100: "SYS_ENETDOWN",
	101: "SYS_ENETUNREACH",
	102: "SYS_ENETRESET",
	103: "SYS_ECONNABORTED",
	104: "SYS_ECONNRESET",
	105: "SYS_ENOBUFS",
	106: "SYS_EISCONN",
	107: "SYS_ENOTCONN",
	108: "SYS_ESHUTDOWN",
	109: "SYS_ETOOMANYREFS",
	110: "SYS_ETIMEDOUT",
	111: "SYS_ECONNREFUSED",
	112: "SYS_EHOSTDOWN",
	113: "SYS_EHOSTUNREACH",
	114: "SYS_EALREADY",
	115: "SYS_EINPROGRESS",
	116: "SYS_ESTALE",
	117: "SYS_EUCLEAN",
	118: "SYS_ENOTNAM",
	119: "SYS_ENAVAIL",
	120: "SYS_EISNAM",
	121: "SYS_EREMOTEIO",
	122: "SYS_EDQUOT",
	123: "SYS_ENOMEDIUM",
	124: "SYS_EMEDIUMTYPE",
	125: "SYS_ECANCELED",
	126: "SYS_ENOKEY",
	127: "SYS_EKEYEXPIRED",
	128: "SYS_EKEYREVOKED",
	129: "SYS_EKEYREJECTED",
	130: "SYS_EOWNERDEAD",
	131: "SYS_ENOTRECOVERABLE",
	132: "SYS_ERFKILL",
}
var RemoteSocketServiceError_SystemError_value = map[string]int32{
	"SYS_SUCCESS":         0,
	"SYS_EPERM":           1,
	"SYS_ENOENT":          2,
	"SYS_ESRCH":           3,
	"SYS_EINTR":           4,
	"SYS_EIO":             5,
	"SYS_ENXIO":           6,
	"SYS_E2BIG":           7,
	"SYS_ENOEXEC":         8,
	"SYS_EBADF":           9,
	"SYS_ECHILD":          10,
	"SYS_EAGAIN":          11,
	"SYS_EWOULDBLOCK":     11,
	"SYS_ENOMEM":          12,
	"SYS_EACCES":          13,
	"SYS_EFAULT":          14,
	"SYS_ENOTBLK":         15,
	"SYS_EBUSY":           16,
	"SYS_EEXIST":          17,
	"SYS_EXDEV":           18,
	"SYS_ENODEV":          19,
	"SYS_ENOTDIR":         20,
	"SYS_EISDIR":          21,
	"SYS_EINVAL":          22,
	"SYS_ENFILE":          23,
	"SYS_EMFILE":          24,
	"SYS_ENOTTY":          25,
	"SYS_ETXTBSY":         26,
	"SYS_EFBIG":           27,
	"SYS_ENOSPC":          28,
	"SYS_ESPIPE":          29,
	"SYS_EROFS":           30,
	"SYS_EMLINK":          31,
	"SYS_EPIPE":           32,
	"SYS_EDOM":            33,
	"SYS_ERANGE":          34,
	"SYS_EDEADLK":         35,
	"SYS_EDEADLOCK":       35,
	"SYS_ENAMETOOLONG":    36,
	"SYS_ENOLCK":          37,
	"SYS_ENOSYS":          38,
	"SYS_ENOTEMPTY":       39,
	"SYS_ELOOP":           40,
	"SYS_ENOMSG":          42,
	"SYS_EIDRM":           43,
	"SYS_ECHRNG":          44,
	"SYS_EL2NSYNC":        45,
	"SYS_EL3HLT":          46,
	"SYS_EL3RST":          47,
	"SYS_ELNRNG":          48,
	"SYS_EUNATCH":         49,
	"SYS_ENOCSI":          50,
	"SYS_EL2HLT":          51,
	"SYS_EBADE":           52,
	"SYS_EBADR":           53,
	"SYS_EXFULL":          54,
	"SYS_ENOANO":          55,
	"SYS_EBADRQC":         56,
	"SYS_EBADSLT":         57,
	"SYS_EBFONT":          59,
	"SYS_ENOSTR":          60,
	"SYS_ENODATA":         61,
	"SYS_ETIME":           62,
	"SYS_ENOSR":           63,
	"SYS_ENONET":          64,
	"SYS_ENOPKG":          65,
	"SYS_EREMOTE":         66,
	"SYS_ENOLINK":         67,
	"SYS_EADV":            68,
	"SYS_ESRMNT":          69,
	"SYS_ECOMM":           70,
	"SYS_EPROTO":          71,
	"SYS_EMULTIHOP":       72,
	"SYS_EDOTDOT":         73,
	"SYS_EBADMSG":         74,
	"SYS_EOVERFLOW":       75,
	"SYS_ENOTUNIQ":        76,
	"SYS_EBADFD":          77,
	"SYS_EREMCHG":         78,
	"SYS_ELIBACC":         79,
	"SYS_ELIBBAD":         80,
	"SYS_ELIBSCN":         81,
	"SYS_ELIBMAX":         82,
	"SYS_ELIBEXEC":        83,
	"SYS_EILSEQ":          84,
	"SYS_ERESTART":        85,
	"SYS_ESTRPIPE":        86,
	"SYS_EUSERS":          87,
	"SYS_ENOTSOCK":        88,
	"SYS_EDESTADDRREQ":    89,
	"SYS_EMSGSIZE":        90,
	"SYS_EPROTOTYPE":      91,
	"SYS_ENOPROTOOPT":     92,
	"SYS_EPROTONOSUPPORT": 93,
	"SYS_ESOCKTNOSUPPORT": 94,
	"SYS_EOPNOTSUPP":      95,
	"SYS_ENOTSUP":         95,
	"SYS_EPFNOSUPPORT":    96,
	"SYS_EAFNOSUPPORT":    97,
	"SYS_EADDRINUSE":      98,
	"SYS_EADDRNOTAVAIL":   99,
	"SYS_ENETDOWN":        100,
	"SYS_ENETUNREACH":     101,
	"SYS_ENETRESET":       102,
	"SYS_ECONNABORTED":    103,
	"SYS_ECONNRESET":      104,
	"SYS_ENOBUFS":         105,
	"SYS_EISCONN":         106,
	"SYS_ENOTCONN":        107,
	"SYS_ESHUTDOWN":       108,
	"SYS_ETOOMANYREFS":    109,
	"SYS_ETIMEDOUT":       110,
	"SYS_ECONNREFUSED":    111,
	"SYS_EHOSTDOWN":       112,
	"SYS_EHOSTUNREACH":    113,
	"SYS_EALREADY":        114,
	"SYS_EINPROGRESS":     115,
	"SYS_ESTALE":          116,
	"SYS_EUCLEAN":         117,
	"SYS_ENOTNAM":         118,
	"SYS_ENAVAIL":         119,
	"SYS_EISNAM":          120,
	"SYS_EREMOTEIO":       121,
	"SYS_EDQUOT":          122,
	"SYS_ENOMEDIUM":       123,
	"SYS_EMEDIUMTYPE":     124,
	"SYS_ECANCELED":       125,
	"SYS_ENOKEY":          126,
	"SYS_EKEYEXPIRED":     127,
	"SYS_EKEYREVOKED":     128,
	"SYS_EKEYREJECTED":    129,
	"SYS_EOWNERDEAD":      130,
	"SYS_ENOTRECOVERABLE": 131,
	"SYS_ERFKILL":         132,
}

func (x RemoteSocketServiceError_SystemError) Enum() *RemoteSocketServiceError_SystemError {
	p := new(RemoteSocketServiceError_SystemError)
	*p = x
	return p
}
func (x RemoteSocketServiceError_SystemError) String() string {
	return proto.EnumName(RemoteSocketServiceError_SystemError_name, int32(x))
}
func (x *RemoteSocketServiceError_SystemError) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RemoteSocketServiceError_SystemError_value, data, "RemoteSocketServiceError_SystemError")
	if err != nil {
		return err
	}
	*x = RemoteSocketServiceError_SystemError(value)
	return nil
}
func (RemoteSocketServiceError_SystemError) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{0, 1}
}

type CreateSocketRequest_SocketFamily int32

const (
	CreateSocketRequest_IPv4 CreateSocketRequest_SocketFamily = 1
	CreateSocketRequest_IPv6 CreateSocketRequest_SocketFamily = 2
)

var CreateSocketRequest_SocketFamily_name = map[int32]string{
	1: "IPv4",
	2: "IPv6",
}
var CreateSocketRequest_SocketFamily_value = map[string]int32{
	"IPv4": 1,
	"IPv6": 2,
}

func (x CreateSocketRequest_SocketFamily) Enum() *CreateSocketRequest_SocketFamily {
	p := new(CreateSocketRequest_SocketFamily)
	*p = x
	return p
}
func (x CreateSocketRequest_SocketFamily) String() string {
	return proto.EnumName(CreateSocketRequest_SocketFamily_name, int32(x))
}
func (x *CreateSocketRequest_SocketFamily) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CreateSocketRequest_SocketFamily_value, data, "CreateSocketRequest_SocketFamily")
	if err != nil {
		return err
	}
	*x = CreateSocketRequest_SocketFamily(value)
	return nil
}
func (CreateSocketRequest_SocketFamily) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{2, 0}
}

type CreateSocketRequest_SocketProtocol int32

const (
	CreateSocketRequest_TCP CreateSocketRequest_SocketProtocol = 1
	CreateSocketRequest_UDP CreateSocketRequest_SocketProtocol = 2
)

var CreateSocketRequest_SocketProtocol_name = map[int32]string{
	1: "TCP",
	2: "UDP",
}
var CreateSocketRequest_SocketProtocol_value = map[string]int32{
	"TCP": 1,
	"UDP": 2,
}

func (x CreateSocketRequest_SocketProtocol) Enum() *CreateSocketRequest_SocketProtocol {
	p := new(CreateSocketRequest_SocketProtocol)
	*p = x
	return p
}
func (x CreateSocketRequest_SocketProtocol) String() string {
	return proto.EnumName(CreateSocketRequest_SocketProtocol_name, int32(x))
}
func (x *CreateSocketRequest_SocketProtocol) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CreateSocketRequest_SocketProtocol_value, data, "CreateSocketRequest_SocketProtocol")
	if err != nil {
		return err
	}
	*x = CreateSocketRequest_SocketProtocol(value)
	return nil
}
func (CreateSocketRequest_SocketProtocol) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{2, 1}
}

type SocketOption_SocketOptionLevel int32

const (
	SocketOption_SOCKET_SOL_IP     SocketOption_SocketOptionLevel = 0
	SocketOption_SOCKET_SOL_SOCKET SocketOption_SocketOptionLevel = 1
	SocketOption_SOCKET_SOL_TCP    SocketOption_SocketOptionLevel = 6
	SocketOption_SOCKET_SOL_UDP    SocketOption_SocketOptionLevel = 17
)

var SocketOption_SocketOptionLevel_name = map[int32]string{
	0:  "SOCKET_SOL_IP",
	1:  "SOCKET_SOL_SOCKET",
	6:  "SOCKET_SOL_TCP",
	17: "SOCKET_SOL_UDP",
}
var SocketOption_SocketOptionLevel_value = map[string]int32{
	"SOCKET_SOL_IP":     0,
	"SOCKET_SOL_SOCKET": 1,
	"SOCKET_SOL_TCP":    6,
	"SOCKET_SOL_UDP":    17,
}

func (x SocketOption_SocketOptionLevel) Enum() *SocketOption_SocketOptionLevel {
	p := new(SocketOption_SocketOptionLevel)
	*p = x
	return p
}
func (x SocketOption_SocketOptionLevel) String() string {
	return proto.EnumName(SocketOption_SocketOptionLevel_name, int32(x))
}
func (x *SocketOption_SocketOptionLevel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SocketOption_SocketOptionLevel_value, data, "SocketOption_SocketOptionLevel")
	if err != nil {
		return err
	}
	*x = SocketOption_SocketOptionLevel(value)
	return nil
}
func (SocketOption_SocketOptionLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{10, 0}
}

type SocketOption_SocketOptionName int32

const (
	SocketOption_SOCKET_SO_DEBUG         SocketOption_SocketOptionName = 1
	SocketOption_SOCKET_SO_REUSEADDR     SocketOption_SocketOptionName = 2
	SocketOption_SOCKET_SO_TYPE          SocketOption_SocketOptionName = 3
	SocketOption_SOCKET_SO_ERROR         SocketOption_SocketOptionName = 4
	SocketOption_SOCKET_SO_DONTROUTE     SocketOption_SocketOptionName = 5
	SocketOption_SOCKET_SO_BROADCAST     SocketOption_SocketOptionName = 6
	SocketOption_SOCKET_SO_SNDBUF        SocketOption_SocketOptionName = 7
	SocketOption_SOCKET_SO_RCVBUF        SocketOption_SocketOptionName = 8
	SocketOption_SOCKET_SO_KEEPALIVE     SocketOption_SocketOptionName = 9
	SocketOption_SOCKET_SO_OOBINLINE     SocketOption_SocketOptionName = 10
	SocketOption_SOCKET_SO_LINGER        SocketOption_SocketOptionName = 13
	SocketOption_SOCKET_SO_RCVTIMEO      SocketOption_SocketOptionName = 20
	SocketOption_SOCKET_SO_SNDTIMEO      SocketOption_SocketOptionName = 21
	SocketOption_SOCKET_IP_TOS           SocketOption_SocketOptionName = 1
	SocketOption_SOCKET_IP_TTL           SocketOption_SocketOptionName = 2
	SocketOption_SOCKET_IP_HDRINCL       SocketOption_SocketOptionName = 3
	SocketOption_SOCKET_IP_OPTIONS       SocketOption_SocketOptionName = 4
	SocketOption_SOCKET_TCP_NODELAY      SocketOption_SocketOptionName = 1
	SocketOption_SOCKET_TCP_MAXSEG       SocketOption_SocketOptionName = 2
	SocketOption_SOCKET_TCP_CORK         SocketOption_SocketOptionName = 3
	SocketOption_SOCKET_TCP_KEEPIDLE     SocketOption_SocketOptionName = 4
	SocketOption_SOCKET_TCP_KEEPINTVL    SocketOption_SocketOptionName = 5
	SocketOption_SOCKET_TCP_KEEPCNT      SocketOption_SocketOptionName = 6
	SocketOption_SOCKET_TCP_SYNCNT       SocketOption_SocketOptionName = 7
	SocketOption_SOCKET_TCP_LINGER2      SocketOption_SocketOptionName = 8
	SocketOption_SOCKET_TCP_DEFER_ACCEPT SocketOption_SocketOptionName = 9
	SocketOption_SOCKET_TCP_WINDOW_CLAMP SocketOption_SocketOptionName = 10
	SocketOption_SOCKET_TCP_INFO         SocketOption_SocketOptionName = 11
	SocketOption_SOCKET_TCP_QUICKACK     SocketOption_SocketOptionName = 12
)

var SocketOption_SocketOptionName_name = map[int32]string{
	1:  "SOCKET_SO_DEBUG",
	2:  "SOCKET_SO_REUSEADDR",
	3:  "SOCKET_SO_TYPE",
	4:  "SOCKET_SO_ERROR",
	5:  "SOCKET_SO_DONTROUTE",
	6:  "SOCKET_SO_BROADCAST",
	7:  "SOCKET_SO_SNDBUF",
	8:  "SOCKET_SO_RCVBUF",
	9:  "SOCKET_SO_KEEPALIVE",
	10: "SOCKET_SO_OOBINLINE",
	13: "SOCKET_SO_LINGER",
	20: "SOCKET_SO_RCVTIMEO",
	21: "SOCKET_SO_SNDTIMEO",
	// Duplicate value: 1: "SOCKET_IP_TOS",
	// Duplicate value: 2: "SOCKET_IP_TTL",
	// Duplicate value: 3: "SOCKET_IP_HDRINCL",
	// Duplicate value: 4: "SOCKET_IP_OPTIONS",
	// Duplicate value: 1: "SOCKET_TCP_NODELAY",
	// Duplicate value: 2: "SOCKET_TCP_MAXSEG",
	// Duplicate value: 3: "SOCKET_TCP_CORK",
	// Duplicate value: 4: "SOCKET_TCP_KEEPIDLE",
	// Duplicate value: 5: "SOCKET_TCP_KEEPINTVL",
	// Duplicate value: 6: "SOCKET_TCP_KEEPCNT",
	// Duplicate value: 7: "SOCKET_TCP_SYNCNT",
	// Duplicate value: 8: "SOCKET_TCP_LINGER2",
	// Duplicate value: 9: "SOCKET_TCP_DEFER_ACCEPT",
	// Duplicate value: 10: "SOCKET_TCP_WINDOW_CLAMP",
	11: "SOCKET_TCP_INFO",
	12: "SOCKET_TCP_QUICKACK",
}
var SocketOption_SocketOptionName_value = map[string]int32{
	"SOCKET_SO_DEBUG":         1,
	"SOCKET_SO_REUSEADDR":     2,
	"SOCKET_SO_TYPE":          3,
	"SOCKET_SO_ERROR":         4,
	"SOCKET_SO_DONTROUTE":     5,
	"SOCKET_SO_BROADCAST":     6,
	"SOCKET_SO_SNDBUF":        7,
	"SOCKET_SO_RCVBUF":        8,
	"SOCKET_SO_KEEPALIVE":     9,
	"SOCKET_SO_OOBINLINE":     10,
	"SOCKET_SO_LINGER":        13,
	"SOCKET_SO_RCVTIMEO":      20,
	"SOCKET_SO_SNDTIMEO":      21,
	"SOCKET_IP_TOS":           1,
	"SOCKET_IP_TTL":           2,
	"SOCKET_IP_HDRINCL":       3,
	"SOCKET_IP_OPTIONS":       4,
	"SOCKET_TCP_NODELAY":      1,
	"SOCKET_TCP_MAXSEG":       2,
	"SOCKET_TCP_CORK":         3,
	"SOCKET_TCP_KEEPIDLE":     4,
	"SOCKET_TCP_KEEPINTVL":    5,
	"SOCKET_TCP_KEEPCNT":      6,
	"SOCKET_TCP_SYNCNT":       7,
	"SOCKET_TCP_LINGER2":      8,
	"SOCKET_TCP_DEFER_ACCEPT": 9,
	"SOCKET_TCP_WINDOW_CLAMP": 10,
	"SOCKET_TCP_INFO":         11,
	"SOCKET_TCP_QUICKACK":     12,
}

func (x SocketOption_SocketOptionName) Enum() *SocketOption_SocketOptionName {
	p := new(SocketOption_SocketOptionName)
	*p = x
	return p
}
func (x SocketOption_SocketOptionName) String() string {
	return proto.EnumName(SocketOption_SocketOptionName_name, int32(x))
}
func (x *SocketOption_SocketOptionName) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SocketOption_SocketOptionName_value, data, "SocketOption_SocketOptionName")
	if err != nil {
		return err
	}
	*x = SocketOption_SocketOptionName(value)
	return nil
}
func (SocketOption_SocketOptionName) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{10, 1}
}

type ShutDownRequest_How int32

const (
	ShutDownRequest_SOCKET_SHUT_RD   ShutDownRequest_How = 1
	ShutDownRequest_SOCKET_SHUT_WR   ShutDownRequest_How = 2
	ShutDownRequest_SOCKET_SHUT_RDWR ShutDownRequest_How = 3
)

var ShutDownRequest_How_name = map[int32]string{
	1: "SOCKET_SHUT_RD",
	2: "SOCKET_SHUT_WR",
	3: "SOCKET_SHUT_RDWR",
}
var ShutDownRequest_How_value = map[string]int32{
	"SOCKET_SHUT_RD":   1,
	"SOCKET_SHUT_WR":   2,
	"SOCKET_SHUT_RDWR": 3,
}

func (x ShutDownRequest_How) Enum() *ShutDownRequest_How {
	p := new(ShutDownRequest_How)
	*p = x
	return p
}
func (x ShutDownRequest_How) String() string {
	return proto.EnumName(ShutDownRequest_How_name, int32(x))
}
func (x *ShutDownRequest_How) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ShutDownRequest_How_value, data, "ShutDownRequest_How")
	if err != nil {
		return err
	}
	*x = ShutDownRequest_How(value)
	return nil
}
func (ShutDownRequest_How) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{21, 0}
}

type ReceiveRequest_Flags int32

const (
	ReceiveRequest_MSG_OOB  ReceiveRequest_Flags = 1
	ReceiveRequest_MSG_PEEK ReceiveRequest_Flags = 2
)

var ReceiveRequest_Flags_name = map[int32]string{
	1: "MSG_OOB",
	2: "MSG_PEEK",
}
var ReceiveRequest_Flags_value = map[string]int32{
	"MSG_OOB":  1,
	"MSG_PEEK": 2,
}

func (x ReceiveRequest_Flags) Enum() *ReceiveRequest_Flags {
	p := new(ReceiveRequest_Flags)
	*p = x
	return p
}
func (x ReceiveRequest_Flags) String() string {
	return proto.EnumName(ReceiveRequest_Flags_name, int32(x))
}
func (x *ReceiveRequest_Flags) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ReceiveRequest_Flags_value, data, "ReceiveRequest_Flags")
	if err != nil {
		return err
	}
	*x = ReceiveRequest_Flags(value)
	return nil
}
func (ReceiveRequest_Flags) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{27, 0}
}

type PollEvent_PollEventFlag int32

const (
	PollEvent_SOCKET_POLLNONE   PollEvent_PollEventFlag = 0
	PollEvent_SOCKET_POLLIN     PollEvent_PollEventFlag = 1
	PollEvent_SOCKET_POLLPRI    PollEvent_PollEventFlag = 2
	PollEvent_SOCKET_POLLOUT    PollEvent_PollEventFlag = 4
	PollEvent_SOCKET_POLLERR    PollEvent_PollEventFlag = 8
	PollEvent_SOCKET_POLLHUP    PollEvent_PollEventFlag = 16
	PollEvent_SOCKET_POLLNVAL   PollEvent_PollEventFlag = 32
	PollEvent_SOCKET_POLLRDNORM PollEvent_PollEventFlag = 64
	PollEvent_SOCKET_POLLRDBAND PollEvent_PollEventFlag = 128
	PollEvent_SOCKET_POLLWRNORM PollEvent_PollEventFlag = 256
	PollEvent_SOCKET_POLLWRBAND PollEvent_PollEventFlag = 512
	PollEvent_SOCKET_POLLMSG    PollEvent_PollEventFlag = 1024
	PollEvent_SOCKET_POLLREMOVE PollEvent_PollEventFlag = 4096
	PollEvent_SOCKET_POLLRDHUP  PollEvent_PollEventFlag = 8192
)

var PollEvent_PollEventFlag_name = map[int32]string{
	0:    "SOCKET_POLLNONE",
	1:    "SOCKET_POLLIN",
	2:    "SOCKET_POLLPRI",
	4:    "SOCKET_POLLOUT",
	8:    "SOCKET_POLLERR",
	16:   "SOCKET_POLLHUP",
	32:   "SOCKET_POLLNVAL",
	64:   "SOCKET_POLLRDNORM",
	128:  "SOCKET_POLLRDBAND",
	256:  "SOCKET_POLLWRNORM",
	512:  "SOCKET_POLLWRBAND",
	1024: "SOCKET_POLLMSG",
	4096: "SOCKET_POLLREMOVE",
	8192: "SOCKET_POLLRDHUP",
}
var PollEvent_PollEventFlag_value = map[string]int32{
	"SOCKET_POLLNONE":   0,
	"SOCKET_POLLIN":     1,
	"SOCKET_POLLPRI":    2,
	"SOCKET_POLLOUT":    4,
	"SOCKET_POLLERR":    8,
	"SOCKET_POLLHUP":    16,
	"SOCKET_POLLNVAL":   32,
	"SOCKET_POLLRDNORM": 64,
	"SOCKET_POLLRDBAND": 128,
	"SOCKET_POLLWRNORM": 256,
	"SOCKET_POLLWRBAND": 512,
	"SOCKET_POLLMSG":    1024,
	"SOCKET_POLLREMOVE": 4096,
	"SOCKET_POLLRDHUP":  8192,
}

func (x PollEvent_PollEventFlag) Enum() *PollEvent_PollEventFlag {
	p := new(PollEvent_PollEventFlag)
	*p = x
	return p
}
func (x PollEvent_PollEventFlag) String() string {
	return proto.EnumName(PollEvent_PollEventFlag_name, int32(x))
}
func (x *PollEvent_PollEventFlag) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PollEvent_PollEventFlag_value, data, "PollEvent_PollEventFlag")
	if err != nil {
		return err
	}
	*x = PollEvent_PollEventFlag(value)
	return nil
}
func (PollEvent_PollEventFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{29, 0}
}

type ResolveReply_ErrorCode int32

const (
	ResolveReply_SOCKET_EAI_ADDRFAMILY ResolveReply_ErrorCode = 1
	ResolveReply_SOCKET_EAI_AGAIN      ResolveReply_ErrorCode = 2
	ResolveReply_SOCKET_EAI_BADFLAGS   ResolveReply_ErrorCode = 3
	ResolveReply_SOCKET_EAI_FAIL       ResolveReply_ErrorCode = 4
	ResolveReply_SOCKET_EAI_FAMILY     ResolveReply_ErrorCode = 5
	ResolveReply_SOCKET_EAI_MEMORY     ResolveReply_ErrorCode = 6
	ResolveReply_SOCKET_EAI_NODATA     ResolveReply_ErrorCode = 7
	ResolveReply_SOCKET_EAI_NONAME     ResolveReply_ErrorCode = 8
	ResolveReply_SOCKET_EAI_SERVICE    ResolveReply_ErrorCode = 9
	ResolveReply_SOCKET_EAI_SOCKTYPE   ResolveReply_ErrorCode = 10
	ResolveReply_SOCKET_EAI_SYSTEM     ResolveReply_ErrorCode = 11
	ResolveReply_SOCKET_EAI_BADHINTS   ResolveReply_ErrorCode = 12
	ResolveReply_SOCKET_EAI_PROTOCOL   ResolveReply_ErrorCode = 13
	ResolveReply_SOCKET_EAI_OVERFLOW   ResolveReply_ErrorCode = 14
	ResolveReply_SOCKET_EAI_MAX        ResolveReply_ErrorCode = 15
)

var ResolveReply_ErrorCode_name = map[int32]string{
	1:  "SOCKET_EAI_ADDRFAMILY",
	2:  "SOCKET_EAI_AGAIN",
	3:  "SOCKET_EAI_BADFLAGS",
	4:  "SOCKET_EAI_FAIL",
	5:  "SOCKET_EAI_FAMILY",
	6:  "SOCKET_EAI_MEMORY",
	7:  "SOCKET_EAI_NODATA",
	8:  "SOCKET_EAI_NONAME",
	9:  "SOCKET_EAI_SERVICE",
	10: "SOCKET_EAI_SOCKTYPE",
	11: "SOCKET_EAI_SYSTEM",
	12: "SOCKET_EAI_BADHINTS",
	13: "SOCKET_EAI_PROTOCOL",
	14: "SOCKET_EAI_OVERFLOW",
	15: "SOCKET_EAI_MAX",
}
var ResolveReply_ErrorCode_value = map[string]int32{
	"SOCKET_EAI_ADDRFAMILY": 1,
	"SOCKET_EAI_AGAIN":      2,
	"SOCKET_EAI_BADFLAGS":   3,
	"SOCKET_EAI_FAIL":       4,
	"SOCKET_EAI_FAMILY":     5,
	"SOCKET_EAI_MEMORY":     6,
	"SOCKET_EAI_NODATA":     7,
	"SOCKET_EAI_NONAME":     8,
	"SOCKET_EAI_SERVICE":    9,
	"SOCKET_EAI_SOCKTYPE":   10,
	"SOCKET_EAI_SYSTEM":     11,
	"SOCKET_EAI_BADHINTS":   12,
	"SOCKET_EAI_PROTOCOL":   13,
	"SOCKET_EAI_OVERFLOW":   14,
	"SOCKET_EAI_MAX":        15,
}

func (x ResolveReply_ErrorCode) Enum() *ResolveReply_ErrorCode {
	p := new(ResolveReply_ErrorCode)
	*p = x
	return p
}
func (x ResolveReply_ErrorCode) String() string {
	return proto.EnumName(ResolveReply_ErrorCode_name, int32(x))
}
func (x *ResolveReply_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ResolveReply_ErrorCode_value, data, "ResolveReply_ErrorCode")
	if err != nil {
		return err
	}
	*x = ResolveReply_ErrorCode(value)
	return nil
}
func (ResolveReply_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{33, 0}
}

type RemoteSocketServiceError struct {
	SystemError          *int32   `protobuf:"varint,1,opt,name=system_error,json=systemError,def=0" json:"system_error,omitempty"`
	ErrorDetail          *string  `protobuf:"bytes,2,opt,name=error_detail,json=errorDetail" json:"error_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoteSocketServiceError) Reset()         { *m = RemoteSocketServiceError{} }
func (m *RemoteSocketServiceError) String() string { return proto.CompactTextString(m) }
func (*RemoteSocketServiceError) ProtoMessage()    {}
func (*RemoteSocketServiceError) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{0}
}
func (m *RemoteSocketServiceError) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoteSocketServiceError.Unmarshal(m, b)
}
func (m *RemoteSocketServiceError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoteSocketServiceError.Marshal(b, m, deterministic)
}
func (dst *RemoteSocketServiceError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoteSocketServiceError.Merge(dst, src)
}
func (m *RemoteSocketServiceError) XXX_Size() int {
	return xxx_messageInfo_RemoteSocketServiceError.Size(m)
}
func (m *RemoteSocketServiceError) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoteSocketServiceError.DiscardUnknown(m)
}

var xxx_messageInfo_RemoteSocketServiceError proto.InternalMessageInfo

const Default_RemoteSocketServiceError_SystemError int32 = 0

func (m *RemoteSocketServiceError) GetSystemError() int32 {
	if m != nil && m.SystemError != nil {
		return *m.SystemError
	}
	return Default_RemoteSocketServiceError_SystemError
}

func (m *RemoteSocketServiceError) GetErrorDetail() string {
	if m != nil && m.ErrorDetail != nil {
		return *m.ErrorDetail
	}
	return ""
}

type AddressPort struct {
	Port                 *int32   `protobuf:"varint,1,req,name=port" json:"port,omitempty"`
	PackedAddress        []byte   `protobuf:"bytes,2,opt,name=packed_address,json=packedAddress" json:"packed_address,omitempty"`
	HostnameHint         *string  `protobuf:"bytes,3,opt,name=hostname_hint,json=hostnameHint" json:"hostname_hint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddressPort) Reset()         { *m = AddressPort{} }
func (m *AddressPort) String() string { return proto.CompactTextString(m) }
func (*AddressPort) ProtoMessage()    {}
func (*AddressPort) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{1}
}
func (m *AddressPort) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddressPort.Unmarshal(m, b)
}
func (m *AddressPort) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddressPort.Marshal(b, m, deterministic)
}
func (dst *AddressPort) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressPort.Merge(dst, src)
}
func (m *AddressPort) XXX_Size() int {
	return xxx_messageInfo_AddressPort.Size(m)
}
func (m *AddressPort) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressPort.DiscardUnknown(m)
}

var xxx_messageInfo_AddressPort proto.InternalMessageInfo

func (m *AddressPort) GetPort() int32 {
	if m != nil && m.Port != nil {
		return *m.Port
	}
	return 0
}

func (m *AddressPort) GetPackedAddress() []byte {
	if m != nil {
		return m.PackedAddress
	}
	return nil
}

func (m *AddressPort) GetHostnameHint() string {
	if m != nil && m.HostnameHint != nil {
		return *m.HostnameHint
	}
	return ""
}

type CreateSocketRequest struct {
	Family               *CreateSocketRequest_SocketFamily   `protobuf:"varint,1,req,name=family,enum=appengine.CreateSocketRequest_SocketFamily" json:"family,omitempty"`
	Protocol             *CreateSocketRequest_SocketProtocol `protobuf:"varint,2,req,name=protocol,enum=appengine.CreateSocketRequest_SocketProtocol" json:"protocol,omitempty"`
	SocketOptions        []*SocketOption                     `protobuf:"bytes,3,rep,name=socket_options,json=socketOptions" json:"socket_options,omitempty"`
	ProxyExternalIp      *AddressPort                        `protobuf:"bytes,4,opt,name=proxy_external_ip,json=proxyExternalIp" json:"proxy_external_ip,omitempty"`
	ListenBacklog        *int32                              `protobuf:"varint,5,opt,name=listen_backlog,json=listenBacklog,def=0" json:"listen_backlog,omitempty"`
	RemoteIp             *AddressPort                        `protobuf:"bytes,6,opt,name=remote_ip,json=remoteIp" json:"remote_ip,omitempty"`
	AppId                *string                             `protobuf:"bytes,9,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	ProjectId            *int64                              `protobuf:"varint,10,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *CreateSocketRequest) Reset()         { *m = CreateSocketRequest{} }
func (m *CreateSocketRequest) String() string { return proto.CompactTextString(m) }
func (*CreateSocketRequest) ProtoMessage()    {}
func (*CreateSocketRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{2}
}
func (m *CreateSocketRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSocketRequest.Unmarshal(m, b)
}
func (m *CreateSocketRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSocketRequest.Marshal(b, m, deterministic)
}
func (dst *CreateSocketRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSocketRequest.Merge(dst, src)
}
func (m *CreateSocketRequest) XXX_Size() int {
	return xxx_messageInfo_CreateSocketRequest.Size(m)
}
func (m *CreateSocketRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSocketRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSocketRequest proto.InternalMessageInfo

const Default_CreateSocketRequest_ListenBacklog int32 = 0

func (m *CreateSocketRequest) GetFamily() CreateSocketRequest_SocketFamily {
	if m != nil && m.Family != nil {
		return *m.Family
	}
	return CreateSocketRequest_IPv4
}

func (m *CreateSocketRequest) GetProtocol() CreateSocketRequest_SocketProtocol {
	if m != nil && m.Protocol != nil {
		return *m.Protocol
	}
	return CreateSocketRequest_TCP
}

func (m *CreateSocketRequest) GetSocketOptions() []*SocketOption {
	if m != nil {
		return m.SocketOptions
	}
	return nil
}

func (m *CreateSocketRequest) GetProxyExternalIp() *AddressPort {
	if m != nil {
		return m.ProxyExternalIp
	}
	return nil
}

func (m *CreateSocketRequest) GetListenBacklog() int32 {
	if m != nil && m.ListenBacklog != nil {
		return *m.ListenBacklog
	}
	return Default_CreateSocketRequest_ListenBacklog
}

func (m *CreateSocketRequest) GetRemoteIp() *AddressPort {
	if m != nil {
		return m.RemoteIp
	}
	return nil
}

func (m *CreateSocketRequest) GetAppId() string {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return ""
}

func (m *CreateSocketRequest) GetProjectId() int64 {
	if m != nil && m.ProjectId != nil {
		return *m.ProjectId
	}
	return 0
}

type CreateSocketReply struct {
	SocketDescriptor             *string      `protobuf:"bytes,1,opt,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	ServerAddress                *AddressPort `protobuf:"bytes,3,opt,name=server_address,json=serverAddress" json:"server_address,omitempty"`
	ProxyExternalIp              *AddressPort `protobuf:"bytes,4,opt,name=proxy_external_ip,json=proxyExternalIp" json:"proxy_external_ip,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}     `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *CreateSocketReply) Reset()         { *m = CreateSocketReply{} }
func (m *CreateSocketReply) String() string { return proto.CompactTextString(m) }
func (*CreateSocketReply) ProtoMessage()    {}
func (*CreateSocketReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{3}
}

var extRange_CreateSocketReply = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*CreateSocketReply) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_CreateSocketReply
}
func (m *CreateSocketReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSocketReply.Unmarshal(m, b)
}
func (m *CreateSocketReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSocketReply.Marshal(b, m, deterministic)
}
func (dst *CreateSocketReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSocketReply.Merge(dst, src)
}
func (m *CreateSocketReply) XXX_Size() int {
	return xxx_messageInfo_CreateSocketReply.Size(m)
}
func (m *CreateSocketReply) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSocketReply.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSocketReply proto.InternalMessageInfo

func (m *CreateSocketReply) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *CreateSocketReply) GetServerAddress() *AddressPort {
	if m != nil {
		return m.ServerAddress
	}
	return nil
}

func (m *CreateSocketReply) GetProxyExternalIp() *AddressPort {
	if m != nil {
		return m.ProxyExternalIp
	}
	return nil
}

type BindRequest struct {
	SocketDescriptor     *string      `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	ProxyExternalIp      *AddressPort `protobuf:"bytes,2,req,name=proxy_external_ip,json=proxyExternalIp" json:"proxy_external_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BindRequest) Reset()         { *m = BindRequest{} }
func (m *BindRequest) String() string { return proto.CompactTextString(m) }
func (*BindRequest) ProtoMessage()    {}
func (*BindRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{4}
}
func (m *BindRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindRequest.Unmarshal(m, b)
}
func (m *BindRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindRequest.Marshal(b, m, deterministic)
}
func (dst *BindRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindRequest.Merge(dst, src)
}
func (m *BindRequest) XXX_Size() int {
	return xxx_messageInfo_BindRequest.Size(m)
}
func (m *BindRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BindRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BindRequest proto.InternalMessageInfo

func (m *BindRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *BindRequest) GetProxyExternalIp() *AddressPort {
	if m != nil {
		return m.ProxyExternalIp
	}
	return nil
}

type BindReply struct {
	ProxyExternalIp      *AddressPort `protobuf:"bytes,1,opt,name=proxy_external_ip,json=proxyExternalIp" json:"proxy_external_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BindReply) Reset()         { *m = BindReply{} }
func (m *BindReply) String() string { return proto.CompactTextString(m) }
func (*BindReply) ProtoMessage()    {}
func (*BindReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{5}
}
func (m *BindReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindReply.Unmarshal(m, b)
}
func (m *BindReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindReply.Marshal(b, m, deterministic)
}
func (dst *BindReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindReply.Merge(dst, src)
}
func (m *BindReply) XXX_Size() int {
	return xxx_messageInfo_BindReply.Size(m)
}
func (m *BindReply) XXX_DiscardUnknown() {
	xxx_messageInfo_BindReply.DiscardUnknown(m)
}

var xxx_messageInfo_BindReply proto.InternalMessageInfo

func (m *BindReply) GetProxyExternalIp() *AddressPort {
	if m != nil {
		return m.ProxyExternalIp
	}
	return nil
}

type GetSocketNameRequest struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSocketNameRequest) Reset()         { *m = GetSocketNameRequest{} }
func (m *GetSocketNameRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocketNameRequest) ProtoMessage()    {}
func (*GetSocketNameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{6}
}
func (m *GetSocketNameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocketNameRequest.Unmarshal(m, b)
}
func (m *GetSocketNameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocketNameRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocketNameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocketNameRequest.Merge(dst, src)
}
func (m *GetSocketNameRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocketNameRequest.Size(m)
}
func (m *GetSocketNameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocketNameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocketNameRequest proto.InternalMessageInfo

func (m *GetSocketNameRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

type GetSocketNameReply struct {
	ProxyExternalIp      *AddressPort `protobuf:"bytes,2,opt,name=proxy_external_ip,json=proxyExternalIp" json:"proxy_external_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocketNameReply) Reset()         { *m = GetSocketNameReply{} }
func (m *GetSocketNameReply) String() string { return proto.CompactTextString(m) }
func (*GetSocketNameReply) ProtoMessage()    {}
func (*GetSocketNameReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{7}
}
func (m *GetSocketNameReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocketNameReply.Unmarshal(m, b)
}
func (m *GetSocketNameReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocketNameReply.Marshal(b, m, deterministic)
}
func (dst *GetSocketNameReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocketNameReply.Merge(dst, src)
}
func (m *GetSocketNameReply) XXX_Size() int {
	return xxx_messageInfo_GetSocketNameReply.Size(m)
}
func (m *GetSocketNameReply) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocketNameReply.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocketNameReply proto.InternalMessageInfo

func (m *GetSocketNameReply) GetProxyExternalIp() *AddressPort {
	if m != nil {
		return m.ProxyExternalIp
	}
	return nil
}

type GetPeerNameRequest struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPeerNameRequest) Reset()         { *m = GetPeerNameRequest{} }
func (m *GetPeerNameRequest) String() string { return proto.CompactTextString(m) }
func (*GetPeerNameRequest) ProtoMessage()    {}
func (*GetPeerNameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{8}
}
func (m *GetPeerNameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeerNameRequest.Unmarshal(m, b)
}
func (m *GetPeerNameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeerNameRequest.Marshal(b, m, deterministic)
}
func (dst *GetPeerNameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeerNameRequest.Merge(dst, src)
}
func (m *GetPeerNameRequest) XXX_Size() int {
	return xxx_messageInfo_GetPeerNameRequest.Size(m)
}
func (m *GetPeerNameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeerNameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeerNameRequest proto.InternalMessageInfo

func (m *GetPeerNameRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

type GetPeerNameReply struct {
	PeerIp               *AddressPort `protobuf:"bytes,2,opt,name=peer_ip,json=peerIp" json:"peer_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPeerNameReply) Reset()         { *m = GetPeerNameReply{} }
func (m *GetPeerNameReply) String() string { return proto.CompactTextString(m) }
func (*GetPeerNameReply) ProtoMessage()    {}
func (*GetPeerNameReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{9}
}
func (m *GetPeerNameReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeerNameReply.Unmarshal(m, b)
}
func (m *GetPeerNameReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeerNameReply.Marshal(b, m, deterministic)
}
func (dst *GetPeerNameReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeerNameReply.Merge(dst, src)
}
func (m *GetPeerNameReply) XXX_Size() int {
	return xxx_messageInfo_GetPeerNameReply.Size(m)
}
func (m *GetPeerNameReply) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeerNameReply.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeerNameReply proto.InternalMessageInfo

func (m *GetPeerNameReply) GetPeerIp() *AddressPort {
	if m != nil {
		return m.PeerIp
	}
	return nil
}

type SocketOption struct {
	Level                *SocketOption_SocketOptionLevel `protobuf:"varint,1,req,name=level,enum=appengine.SocketOption_SocketOptionLevel" json:"level,omitempty"`
	Option               *SocketOption_SocketOptionName  `protobuf:"varint,2,req,name=option,enum=appengine.SocketOption_SocketOptionName" json:"option,omitempty"`
	Value                []byte                          `protobuf:"bytes,3,req,name=value" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *SocketOption) Reset()         { *m = SocketOption{} }
func (m *SocketOption) String() string { return proto.CompactTextString(m) }
func (*SocketOption) ProtoMessage()    {}
func (*SocketOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{10}
}
func (m *SocketOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocketOption.Unmarshal(m, b)
}
func (m *SocketOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocketOption.Marshal(b, m, deterministic)
}
func (dst *SocketOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocketOption.Merge(dst, src)
}
func (m *SocketOption) XXX_Size() int {
	return xxx_messageInfo_SocketOption.Size(m)
}
func (m *SocketOption) XXX_DiscardUnknown() {
	xxx_messageInfo_SocketOption.DiscardUnknown(m)
}

var xxx_messageInfo_SocketOption proto.InternalMessageInfo

func (m *SocketOption) GetLevel() SocketOption_SocketOptionLevel {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return SocketOption_SOCKET_SOL_IP
}

func (m *SocketOption) GetOption() SocketOption_SocketOptionName {
	if m != nil && m.Option != nil {
		return *m.Option
	}
	return SocketOption_SOCKET_SO_DEBUG
}

func (m *SocketOption) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type SetSocketOptionsRequest struct {
	SocketDescriptor     *string         `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	Options              []*SocketOption `protobuf:"bytes,2,rep,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetSocketOptionsRequest) Reset()         { *m = SetSocketOptionsRequest{} }
func (m *SetSocketOptionsRequest) String() string { return proto.CompactTextString(m) }
func (*SetSocketOptionsRequest) ProtoMessage()    {}
func (*SetSocketOptionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{11}
}
func (m *SetSocketOptionsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSocketOptionsRequest.Unmarshal(m, b)
}
func (m *SetSocketOptionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSocketOptionsRequest.Marshal(b, m, deterministic)
}
func (dst *SetSocketOptionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSocketOptionsRequest.Merge(dst, src)
}
func (m *SetSocketOptionsRequest) XXX_Size() int {
	return xxx_messageInfo_SetSocketOptionsRequest.Size(m)
}
func (m *SetSocketOptionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSocketOptionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetSocketOptionsRequest proto.InternalMessageInfo

func (m *SetSocketOptionsRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *SetSocketOptionsRequest) GetOptions() []*SocketOption {
	if m != nil {
		return m.Options
	}
	return nil
}

type SetSocketOptionsReply struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSocketOptionsReply) Reset()         { *m = SetSocketOptionsReply{} }
func (m *SetSocketOptionsReply) String() string { return proto.CompactTextString(m) }
func (*SetSocketOptionsReply) ProtoMessage()    {}
func (*SetSocketOptionsReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{12}
}
func (m *SetSocketOptionsReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSocketOptionsReply.Unmarshal(m, b)
}
func (m *SetSocketOptionsReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSocketOptionsReply.Marshal(b, m, deterministic)
}
func (dst *SetSocketOptionsReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSocketOptionsReply.Merge(dst, src)
}
func (m *SetSocketOptionsReply) XXX_Size() int {
	return xxx_messageInfo_SetSocketOptionsReply.Size(m)
}
func (m *SetSocketOptionsReply) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSocketOptionsReply.DiscardUnknown(m)
}

var xxx_messageInfo_SetSocketOptionsReply proto.InternalMessageInfo

type GetSocketOptionsRequest struct {
	SocketDescriptor     *string         `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	Options              []*SocketOption `protobuf:"bytes,2,rep,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSocketOptionsRequest) Reset()         { *m = GetSocketOptionsRequest{} }
func (m *GetSocketOptionsRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocketOptionsRequest) ProtoMessage()    {}
func (*GetSocketOptionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{13}
}
func (m *GetSocketOptionsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocketOptionsRequest.Unmarshal(m, b)
}
func (m *GetSocketOptionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocketOptionsRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocketOptionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocketOptionsRequest.Merge(dst, src)
}
func (m *GetSocketOptionsRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocketOptionsRequest.Size(m)
}
func (m *GetSocketOptionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocketOptionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocketOptionsRequest proto.InternalMessageInfo

func (m *GetSocketOptionsRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *GetSocketOptionsRequest) GetOptions() []*SocketOption {
	if m != nil {
		return m.Options
	}
	return nil
}

type GetSocketOptionsReply struct {
	Options              []*SocketOption `protobuf:"bytes,2,rep,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSocketOptionsReply) Reset()         { *m = GetSocketOptionsReply{} }
func (m *GetSocketOptionsReply) String() string { return proto.CompactTextString(m) }
func (*GetSocketOptionsReply) ProtoMessage()    {}
func (*GetSocketOptionsReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{14}
}
func (m *GetSocketOptionsReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocketOptionsReply.Unmarshal(m, b)
}
func (m *GetSocketOptionsReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocketOptionsReply.Marshal(b, m, deterministic)
}
func (dst *GetSocketOptionsReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocketOptionsReply.Merge(dst, src)
}
func (m *GetSocketOptionsReply) XXX_Size() int {
	return xxx_messageInfo_GetSocketOptionsReply.Size(m)
}
func (m *GetSocketOptionsReply) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocketOptionsReply.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocketOptionsReply proto.InternalMessageInfo

func (m *GetSocketOptionsReply) GetOptions() []*SocketOption {
	if m != nil {
		return m.Options
	}
	return nil
}

type ConnectRequest struct {
	SocketDescriptor     *string      `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	RemoteIp             *AddressPort `protobuf:"bytes,2,req,name=remote_ip,json=remoteIp" json:"remote_ip,omitempty"`
	TimeoutSeconds       *float64     `protobuf:"fixed64,3,opt,name=timeout_seconds,json=timeoutSeconds,def=-1" json:"timeout_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConnectRequest) Reset()         { *m = ConnectRequest{} }
func (m *ConnectRequest) String() string { return proto.CompactTextString(m) }
func (*ConnectRequest) ProtoMessage()    {}
func (*ConnectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{15}
}
func (m *ConnectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConnectRequest.Unmarshal(m, b)
}
func (m *ConnectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConnectRequest.Marshal(b, m, deterministic)
}
func (dst *ConnectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectRequest.Merge(dst, src)
}
func (m *ConnectRequest) XXX_Size() int {
	return xxx_messageInfo_ConnectRequest.Size(m)
}
func (m *ConnectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectRequest proto.InternalMessageInfo

const Default_ConnectRequest_TimeoutSeconds float64 = -1

func (m *ConnectRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *ConnectRequest) GetRemoteIp() *AddressPort {
	if m != nil {
		return m.RemoteIp
	}
	return nil
}

func (m *ConnectRequest) GetTimeoutSeconds() float64 {
	if m != nil && m.TimeoutSeconds != nil {
		return *m.TimeoutSeconds
	}
	return Default_ConnectRequest_TimeoutSeconds
}

type ConnectReply struct {
	ProxyExternalIp              *AddressPort `protobuf:"bytes,1,opt,name=proxy_external_ip,json=proxyExternalIp" json:"proxy_external_ip,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}     `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *ConnectReply) Reset()         { *m = ConnectReply{} }
func (m *ConnectReply) String() string { return proto.CompactTextString(m) }
func (*ConnectReply) ProtoMessage()    {}
func (*ConnectReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{16}
}

var extRange_ConnectReply = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*ConnectReply) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_ConnectReply
}
func (m *ConnectReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConnectReply.Unmarshal(m, b)
}
func (m *ConnectReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConnectReply.Marshal(b, m, deterministic)
}
func (dst *ConnectReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectReply.Merge(dst, src)
}
func (m *ConnectReply) XXX_Size() int {
	return xxx_messageInfo_ConnectReply.Size(m)
}
func (m *ConnectReply) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectReply.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectReply proto.InternalMessageInfo

func (m *ConnectReply) GetProxyExternalIp() *AddressPort {
	if m != nil {
		return m.ProxyExternalIp
	}
	return nil
}

type ListenRequest struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	Backlog              *int32   `protobuf:"varint,2,req,name=backlog" json:"backlog,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListenRequest) Reset()         { *m = ListenRequest{} }
func (m *ListenRequest) String() string { return proto.CompactTextString(m) }
func (*ListenRequest) ProtoMessage()    {}
func (*ListenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{17}
}
func (m *ListenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListenRequest.Unmarshal(m, b)
}
func (m *ListenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListenRequest.Marshal(b, m, deterministic)
}
func (dst *ListenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListenRequest.Merge(dst, src)
}
func (m *ListenRequest) XXX_Size() int {
	return xxx_messageInfo_ListenRequest.Size(m)
}
func (m *ListenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListenRequest proto.InternalMessageInfo

func (m *ListenRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *ListenRequest) GetBacklog() int32 {
	if m != nil && m.Backlog != nil {
		return *m.Backlog
	}
	return 0
}

type ListenReply struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListenReply) Reset()         { *m = ListenReply{} }
func (m *ListenReply) String() string { return proto.CompactTextString(m) }
func (*ListenReply) ProtoMessage()    {}
func (*ListenReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{18}
}
func (m *ListenReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListenReply.Unmarshal(m, b)
}
func (m *ListenReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListenReply.Marshal(b, m, deterministic)
}
func (dst *ListenReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListenReply.Merge(dst, src)
}
func (m *ListenReply) XXX_Size() int {
	return xxx_messageInfo_ListenReply.Size(m)
}
func (m *ListenReply) XXX_DiscardUnknown() {
	xxx_messageInfo_ListenReply.DiscardUnknown(m)
}

var xxx_messageInfo_ListenReply proto.InternalMessageInfo

type AcceptRequest struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	TimeoutSeconds       *float64 `protobuf:"fixed64,2,opt,name=timeout_seconds,json=timeoutSeconds,def=-1" json:"timeout_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptRequest) Reset()         { *m = AcceptRequest{} }
func (m *AcceptRequest) String() string { return proto.CompactTextString(m) }
func (*AcceptRequest) ProtoMessage()    {}
func (*AcceptRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{19}
}
func (m *AcceptRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptRequest.Unmarshal(m, b)
}
func (m *AcceptRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptRequest.Marshal(b, m, deterministic)
}
func (dst *AcceptRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptRequest.Merge(dst, src)
}
func (m *AcceptRequest) XXX_Size() int {
	return xxx_messageInfo_AcceptRequest.Size(m)
}
func (m *AcceptRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptRequest proto.InternalMessageInfo

const Default_AcceptRequest_TimeoutSeconds float64 = -1

func (m *AcceptRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *AcceptRequest) GetTimeoutSeconds() float64 {
	if m != nil && m.TimeoutSeconds != nil {
		return *m.TimeoutSeconds
	}
	return Default_AcceptRequest_TimeoutSeconds
}

type AcceptReply struct {
	NewSocketDescriptor  []byte       `protobuf:"bytes,2,opt,name=new_socket_descriptor,json=newSocketDescriptor" json:"new_socket_descriptor,omitempty"`
	RemoteAddress        *AddressPort `protobuf:"bytes,3,opt,name=remote_address,json=remoteAddress" json:"remote_address,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AcceptReply) Reset()         { *m = AcceptReply{} }
func (m *AcceptReply) String() string { return proto.CompactTextString(m) }
func (*AcceptReply) ProtoMessage()    {}
func (*AcceptReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{20}
}
func (m *AcceptReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptReply.Unmarshal(m, b)
}
func (m *AcceptReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptReply.Marshal(b, m, deterministic)
}
func (dst *AcceptReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptReply.Merge(dst, src)
}
func (m *AcceptReply) XXX_Size() int {
	return xxx_messageInfo_AcceptReply.Size(m)
}
func (m *AcceptReply) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptReply.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptReply proto.InternalMessageInfo

func (m *AcceptReply) GetNewSocketDescriptor() []byte {
	if m != nil {
		return m.NewSocketDescriptor
	}
	return nil
}

func (m *AcceptReply) GetRemoteAddress() *AddressPort {
	if m != nil {
		return m.RemoteAddress
	}
	return nil
}

type ShutDownRequest struct {
	SocketDescriptor     *string              `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	How                  *ShutDownRequest_How `protobuf:"varint,2,req,name=how,enum=appengine.ShutDownRequest_How" json:"how,omitempty"`
	SendOffset           *int64               `protobuf:"varint,3,req,name=send_offset,json=sendOffset" json:"send_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ShutDownRequest) Reset()         { *m = ShutDownRequest{} }
func (m *ShutDownRequest) String() string { return proto.CompactTextString(m) }
func (*ShutDownRequest) ProtoMessage()    {}
func (*ShutDownRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{21}
}
func (m *ShutDownRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShutDownRequest.Unmarshal(m, b)
}
func (m *ShutDownRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShutDownRequest.Marshal(b, m, deterministic)
}
func (dst *ShutDownRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShutDownRequest.Merge(dst, src)
}
func (m *ShutDownRequest) XXX_Size() int {
	return xxx_messageInfo_ShutDownRequest.Size(m)
}
func (m *ShutDownRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShutDownRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShutDownRequest proto.InternalMessageInfo

func (m *ShutDownRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *ShutDownRequest) GetHow() ShutDownRequest_How {
	if m != nil && m.How != nil {
		return *m.How
	}
	return ShutDownRequest_SOCKET_SHUT_RD
}

func (m *ShutDownRequest) GetSendOffset() int64 {
	if m != nil && m.SendOffset != nil {
		return *m.SendOffset
	}
	return 0
}

type ShutDownReply struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShutDownReply) Reset()         { *m = ShutDownReply{} }
func (m *ShutDownReply) String() string { return proto.CompactTextString(m) }
func (*ShutDownReply) ProtoMessage()    {}
func (*ShutDownReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{22}
}
func (m *ShutDownReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShutDownReply.Unmarshal(m, b)
}
func (m *ShutDownReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShutDownReply.Marshal(b, m, deterministic)
}
func (dst *ShutDownReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShutDownReply.Merge(dst, src)
}
func (m *ShutDownReply) XXX_Size() int {
	return xxx_messageInfo_ShutDownReply.Size(m)
}
func (m *ShutDownReply) XXX_DiscardUnknown() {
	xxx_messageInfo_ShutDownReply.DiscardUnknown(m)
}

var xxx_messageInfo_ShutDownReply proto.InternalMessageInfo

type CloseRequest struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	SendOffset           *int64   `protobuf:"varint,2,opt,name=send_offset,json=sendOffset,def=-1" json:"send_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseRequest) Reset()         { *m = CloseRequest{} }
func (m *CloseRequest) String() string { return proto.CompactTextString(m) }
func (*CloseRequest) ProtoMessage()    {}
func (*CloseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{23}
}
func (m *CloseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseRequest.Unmarshal(m, b)
}
func (m *CloseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseRequest.Marshal(b, m, deterministic)
}
func (dst *CloseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseRequest.Merge(dst, src)
}
func (m *CloseRequest) XXX_Size() int {
	return xxx_messageInfo_CloseRequest.Size(m)
}
func (m *CloseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CloseRequest proto.InternalMessageInfo

const Default_CloseRequest_SendOffset int64 = -1

func (m *CloseRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *CloseRequest) GetSendOffset() int64 {
	if m != nil && m.SendOffset != nil {
		return *m.SendOffset
	}
	return Default_CloseRequest_SendOffset
}

type CloseReply struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseReply) Reset()         { *m = CloseReply{} }
func (m *CloseReply) String() string { return proto.CompactTextString(m) }
func (*CloseReply) ProtoMessage()    {}
func (*CloseReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{24}
}
func (m *CloseReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseReply.Unmarshal(m, b)
}
func (m *CloseReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseReply.Marshal(b, m, deterministic)
}
func (dst *CloseReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseReply.Merge(dst, src)
}
func (m *CloseReply) XXX_Size() int {
	return xxx_messageInfo_CloseReply.Size(m)
}
func (m *CloseReply) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseReply.DiscardUnknown(m)
}

var xxx_messageInfo_CloseReply proto.InternalMessageInfo

type SendRequest struct {
	SocketDescriptor     *string      `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	Data                 []byte       `protobuf:"bytes,2,req,name=data" json:"data,omitempty"`
	StreamOffset         *int64       `protobuf:"varint,3,req,name=stream_offset,json=streamOffset" json:"stream_offset,omitempty"`
	Flags                *int32       `protobuf:"varint,4,opt,name=flags,def=0" json:"flags,omitempty"`
	SendTo               *AddressPort `protobuf:"bytes,5,opt,name=send_to,json=sendTo" json:"send_to,omitempty"`
	TimeoutSeconds       *float64     `protobuf:"fixed64,6,opt,name=timeout_seconds,json=timeoutSeconds,def=-1" json:"timeout_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendRequest) Reset()         { *m = SendRequest{} }
func (m *SendRequest) String() string { return proto.CompactTextString(m) }
func (*SendRequest) ProtoMessage()    {}
func (*SendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{25}
}
func (m *SendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRequest.Unmarshal(m, b)
}
func (m *SendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRequest.Marshal(b, m, deterministic)
}
func (dst *SendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRequest.Merge(dst, src)
}
func (m *SendRequest) XXX_Size() int {
	return xxx_messageInfo_SendRequest.Size(m)
}
func (m *SendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendRequest proto.InternalMessageInfo

const Default_SendRequest_Flags int32 = 0
const Default_SendRequest_TimeoutSeconds float64 = -1

func (m *SendRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *SendRequest) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SendRequest) GetStreamOffset() int64 {
	if m != nil && m.StreamOffset != nil {
		return *m.StreamOffset
	}
	return 0
}

func (m *SendRequest) GetFlags() int32 {
	if m != nil && m.Flags != nil {
		return *m.Flags
	}
	return Default_SendRequest_Flags
}

func (m *SendRequest) GetSendTo() *AddressPort {
	if m != nil {
		return m.SendTo
	}
	return nil
}

func (m *SendRequest) GetTimeoutSeconds() float64 {
	if m != nil && m.TimeoutSeconds != nil {
		return *m.TimeoutSeconds
	}
	return Default_SendRequest_TimeoutSeconds
}

type SendReply struct {
	DataSent             *int32   `protobuf:"varint,1,opt,name=data_sent,json=dataSent" json:"data_sent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendReply) Reset()         { *m = SendReply{} }
func (m *SendReply) String() string { return proto.CompactTextString(m) }
func (*SendReply) ProtoMessage()    {}
func (*SendReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{26}
}
func (m *SendReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendReply.Unmarshal(m, b)
}
func (m *SendReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendReply.Marshal(b, m, deterministic)
}
func (dst *SendReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendReply.Merge(dst, src)
}
func (m *SendReply) XXX_Size() int {
	return xxx_messageInfo_SendReply.Size(m)
}
func (m *SendReply) XXX_DiscardUnknown() {
	xxx_messageInfo_SendReply.DiscardUnknown(m)
}

var xxx_messageInfo_SendReply proto.InternalMessageInfo

func (m *SendReply) GetDataSent() int32 {
	if m != nil && m.DataSent != nil {
		return *m.DataSent
	}
	return 0
}

type ReceiveRequest struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	DataSize             *int32   `protobuf:"varint,2,req,name=data_size,json=dataSize" json:"data_size,omitempty"`
	Flags                *int32   `protobuf:"varint,3,opt,name=flags,def=0" json:"flags,omitempty"`
	TimeoutSeconds       *float64 `protobuf:"fixed64,5,opt,name=timeout_seconds,json=timeoutSeconds,def=-1" json:"timeout_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRequest) Reset()         { *m = ReceiveRequest{} }
func (m *ReceiveRequest) String() string { return proto.CompactTextString(m) }
func (*ReceiveRequest) ProtoMessage()    {}
func (*ReceiveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{27}
}
func (m *ReceiveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRequest.Unmarshal(m, b)
}
func (m *ReceiveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRequest.Marshal(b, m, deterministic)
}
func (dst *ReceiveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRequest.Merge(dst, src)
}
func (m *ReceiveRequest) XXX_Size() int {
	return xxx_messageInfo_ReceiveRequest.Size(m)
}
func (m *ReceiveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRequest proto.InternalMessageInfo

const Default_ReceiveRequest_Flags int32 = 0
const Default_ReceiveRequest_TimeoutSeconds float64 = -1

func (m *ReceiveRequest) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *ReceiveRequest) GetDataSize() int32 {
	if m != nil && m.DataSize != nil {
		return *m.DataSize
	}
	return 0
}

func (m *ReceiveRequest) GetFlags() int32 {
	if m != nil && m.Flags != nil {
		return *m.Flags
	}
	return Default_ReceiveRequest_Flags
}

func (m *ReceiveRequest) GetTimeoutSeconds() float64 {
	if m != nil && m.TimeoutSeconds != nil {
		return *m.TimeoutSeconds
	}
	return Default_ReceiveRequest_TimeoutSeconds
}

type ReceiveReply struct {
	StreamOffset         *int64       `protobuf:"varint,2,opt,name=stream_offset,json=streamOffset" json:"stream_offset,omitempty"`
	Data                 []byte       `protobuf:"bytes,3,opt,name=data" json:"data,omitempty"`
	ReceivedFrom         *AddressPort `protobuf:"bytes,4,opt,name=received_from,json=receivedFrom" json:"received_from,omitempty"`
	BufferSize           *int32       `protobuf:"varint,5,opt,name=buffer_size,json=bufferSize" json:"buffer_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReceiveReply) Reset()         { *m = ReceiveReply{} }
func (m *ReceiveReply) String() string { return proto.CompactTextString(m) }
func (*ReceiveReply) ProtoMessage()    {}
func (*ReceiveReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{28}
}
func (m *ReceiveReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveReply.Unmarshal(m, b)
}
func (m *ReceiveReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveReply.Marshal(b, m, deterministic)
}
func (dst *ReceiveReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveReply.Merge(dst, src)
}
func (m *ReceiveReply) XXX_Size() int {
	return xxx_messageInfo_ReceiveReply.Size(m)
}
func (m *ReceiveReply) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveReply.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveReply proto.InternalMessageInfo

func (m *ReceiveReply) GetStreamOffset() int64 {
	if m != nil && m.StreamOffset != nil {
		return *m.StreamOffset
	}
	return 0
}

func (m *ReceiveReply) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ReceiveReply) GetReceivedFrom() *AddressPort {
	if m != nil {
		return m.ReceivedFrom
	}
	return nil
}

func (m *ReceiveReply) GetBufferSize() int32 {
	if m != nil && m.BufferSize != nil {
		return *m.BufferSize
	}
	return 0
}

type PollEvent struct {
	SocketDescriptor     *string  `protobuf:"bytes,1,req,name=socket_descriptor,json=socketDescriptor" json:"socket_descriptor,omitempty"`
	RequestedEvents      *int32   `protobuf:"varint,2,req,name=requested_events,json=requestedEvents" json:"requested_events,omitempty"`
	ObservedEvents       *int32   `protobuf:"varint,3,req,name=observed_events,json=observedEvents" json:"observed_events,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PollEvent) Reset()         { *m = PollEvent{} }
func (m *PollEvent) String() string { return proto.CompactTextString(m) }
func (*PollEvent) ProtoMessage()    {}
func (*PollEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{29}
}
func (m *PollEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PollEvent.Unmarshal(m, b)
}
func (m *PollEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PollEvent.Marshal(b, m, deterministic)
}
func (dst *PollEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PollEvent.Merge(dst, src)
}
func (m *PollEvent) XXX_Size() int {
	return xxx_messageInfo_PollEvent.Size(m)
}
func (m *PollEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PollEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PollEvent proto.InternalMessageInfo

func (m *PollEvent) GetSocketDescriptor() string {
	if m != nil && m.SocketDescriptor != nil {
		return *m.SocketDescriptor
	}
	return ""
}

func (m *PollEvent) GetRequestedEvents() int32 {
	if m != nil && m.RequestedEvents != nil {
		return *m.RequestedEvents
	}
	return 0
}

func (m *PollEvent) GetObservedEvents() int32 {
	if m != nil && m.ObservedEvents != nil {
		return *m.ObservedEvents
	}
	return 0
}

type PollRequest struct {
	Events               []*PollEvent `protobuf:"bytes,1,rep,name=events" json:"events,omitempty"`
	TimeoutSeconds       *float64     `protobuf:"fixed64,2,opt,name=timeout_seconds,json=timeoutSeconds,def=-1" json:"timeout_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PollRequest) Reset()         { *m = PollRequest{} }
func (m *PollRequest) String() string { return proto.CompactTextString(m) }
func (*PollRequest) ProtoMessage()    {}
func (*PollRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{30}
}
func (m *PollRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PollRequest.Unmarshal(m, b)
}
func (m *PollRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PollRequest.Marshal(b, m, deterministic)
}
func (dst *PollRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PollRequest.Merge(dst, src)
}
func (m *PollRequest) XXX_Size() int {
	return xxx_messageInfo_PollRequest.Size(m)
}
func (m *PollRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PollRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PollRequest proto.InternalMessageInfo

const Default_PollRequest_TimeoutSeconds float64 = -1

func (m *PollRequest) GetEvents() []*PollEvent {
	if m != nil {
		return m.Events
	}
	return nil
}

func (m *PollRequest) GetTimeoutSeconds() float64 {
	if m != nil && m.TimeoutSeconds != nil {
		return *m.TimeoutSeconds
	}
	return Default_PollRequest_TimeoutSeconds
}

type PollReply struct {
	Events               []*PollEvent `protobuf:"bytes,2,rep,name=events" json:"events,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PollReply) Reset()         { *m = PollReply{} }
func (m *PollReply) String() string { return proto.CompactTextString(m) }
func (*PollReply) ProtoMessage()    {}
func (*PollReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{31}
}
func (m *PollReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PollReply.Unmarshal(m, b)
}
func (m *PollReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PollReply.Marshal(b, m, deterministic)
}
func (dst *PollReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PollReply.Merge(dst, src)
}
func (m *PollReply) XXX_Size() int {
	return xxx_messageInfo_PollReply.Size(m)
}
func (m *PollReply) XXX_DiscardUnknown() {
	xxx_messageInfo_PollReply.DiscardUnknown(m)
}

var xxx_messageInfo_PollReply proto.InternalMessageInfo

func (m *PollReply) GetEvents() []*PollEvent {
	if m != nil {
		return m.Events
	}
	return nil
}

type ResolveRequest struct {
	Name                 *string                            `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	AddressFamilies      []CreateSocketRequest_SocketFamily `protobuf:"varint,2,rep,name=address_families,json=addressFamilies,enum=appengine.CreateSocketRequest_SocketFamily" json:"address_families,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *ResolveRequest) Reset()         { *m = ResolveRequest{} }
func (m *ResolveRequest) String() string { return proto.CompactTextString(m) }
func (*ResolveRequest) ProtoMessage()    {}
func (*ResolveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{32}
}
func (m *ResolveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResolveRequest.Unmarshal(m, b)
}
func (m *ResolveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResolveRequest.Marshal(b, m, deterministic)
}
func (dst *ResolveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResolveRequest.Merge(dst, src)
}
func (m *ResolveRequest) XXX_Size() int {
	return xxx_messageInfo_ResolveRequest.Size(m)
}
func (m *ResolveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ResolveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ResolveRequest proto.InternalMessageInfo

func (m *ResolveRequest) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ResolveRequest) GetAddressFamilies() []CreateSocketRequest_SocketFamily {
	if m != nil {
		return m.AddressFamilies
	}
	return nil
}

type ResolveReply struct {
	PackedAddress        [][]byte `protobuf:"bytes,2,rep,name=packed_address,json=packedAddress" json:"packed_address,omitempty"`
	CanonicalName        *string  `protobuf:"bytes,3,opt,name=canonical_name,json=canonicalName" json:"canonical_name,omitempty"`
	Aliases              []string `protobuf:"bytes,4,rep,name=aliases" json:"aliases,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResolveReply) Reset()         { *m = ResolveReply{} }
func (m *ResolveReply) String() string { return proto.CompactTextString(m) }
func (*ResolveReply) ProtoMessage()    {}
func (*ResolveReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_socket_service_b5f8f233dc327808, []int{33}
}
func (m *ResolveReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResolveReply.Unmarshal(m, b)
}
func (m *ResolveReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResolveReply.Marshal(b, m, deterministic)
}
func (dst *ResolveReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResolveReply.Merge(dst, src)
}
func (m *ResolveReply) XXX_Size() int {
	return xxx_messageInfo_ResolveReply.Size(m)
}
func (m *ResolveReply) XXX_DiscardUnknown() {
	xxx_messageInfo_ResolveReply.DiscardUnknown(m)
}

var xxx_messageInfo_ResolveReply proto.InternalMessageInfo

func (m *ResolveReply) GetPackedAddress() [][]byte {
	if m != nil {
		return m.PackedAddress
	}
	return nil
}

func (m *ResolveReply) GetCanonicalName() string {
	if m != nil && m.CanonicalName != nil {
		return *m.CanonicalName
	}
	return ""
}

func (m *ResolveReply) GetAliases() []string {
	if m != nil {
		return m.Aliases
	}
	return nil
}

func init() {
	proto.RegisterType((*RemoteSocketServiceError)(nil), "appengine.RemoteSocketServiceError")
	proto.RegisterType((*AddressPort)(nil), "appengine.AddressPort")
	proto.RegisterType((*CreateSocketRequest)(nil), "appengine.CreateSocketRequest")
	proto.RegisterType((*CreateSocketReply)(nil), "appengine.CreateSocketReply")
	proto.RegisterType((*BindRequest)(nil), "appengine.BindRequest")
	proto.RegisterType((*BindReply)(nil), "appengine.BindReply")
	proto.RegisterType((*GetSocketNameRequest)(nil), "appengine.GetSocketNameRequest")
	proto.RegisterType((*GetSocketNameReply)(nil), "appengine.GetSocketNameReply")
	proto.RegisterType((*GetPeerNameRequest)(nil), "appengine.GetPeerNameRequest")
	proto.RegisterType((*GetPeerNameReply)(nil), "appengine.GetPeerNameReply")
	proto.RegisterType((*SocketOption)(nil), "appengine.SocketOption")
	proto.RegisterType((*SetSocketOptionsRequest)(nil), "appengine.SetSocketOptionsRequest")
	proto.RegisterType((*SetSocketOptionsReply)(nil), "appengine.SetSocketOptionsReply")
	proto.RegisterType((*GetSocketOptionsRequest)(nil), "appengine.GetSocketOptionsRequest")
	proto.RegisterType((*GetSocketOptionsReply)(nil), "appengine.GetSocketOptionsReply")
	proto.RegisterType((*ConnectRequest)(nil), "appengine.ConnectRequest")
	proto.RegisterType((*ConnectReply)(nil), "appengine.ConnectReply")
	proto.RegisterType((*ListenRequest)(nil), "appengine.ListenRequest")
	proto.RegisterType((*ListenReply)(nil), "appengine.ListenReply")
	proto.RegisterType((*AcceptRequest)(nil), "appengine.AcceptRequest")
	proto.RegisterType((*AcceptReply)(nil), "appengine.AcceptReply")
	proto.RegisterType((*ShutDownRequest)(nil), "appengine.ShutDownRequest")
	proto.RegisterType((*ShutDownReply)(nil), "appengine.ShutDownReply")
	proto.RegisterType((*CloseRequest)(nil), "appengine.CloseRequest")
	proto.RegisterType((*CloseReply)(nil), "appengine.CloseReply")
	proto.RegisterType((*SendRequest)(nil), "appengine.SendRequest")
	proto.RegisterType((*SendReply)(nil), "appengine.SendReply")
	proto.RegisterType((*ReceiveRequest)(nil), "appengine.ReceiveRequest")
	proto.RegisterType((*ReceiveReply)(nil), "appengine.ReceiveReply")
	proto.RegisterType((*PollEvent)(nil), "appengine.PollEvent")
	proto.RegisterType((*PollRequest)(nil), "appengine.PollRequest")
	proto.RegisterType((*PollReply)(nil), "appengine.PollReply")
	proto.RegisterType((*ResolveRequest)(nil), "appengine.ResolveRequest")
	proto.RegisterType((*ResolveReply)(nil), "appengine.ResolveReply")
}

func init() {
	proto.RegisterFile("google.golang.org/appengine/internal/socket/socket_service.proto", fileDescriptor_socket_service_b5f8f233dc327808)
}

var fileDescriptor_socket_service_b5f8f233dc327808 = []byte{
	// 3088 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0x5f, 0x77, 0xe3, 0xc6,
	0x75, 0x37, 0x48, 0xfd, 0xe3, 0x90, 0x94, 0xee, 0x62, 0xa5, 0x5d, 0x25, 0x6e, 0x12, 0x05, 0x8e,
	0x1b, 0x25, 0x8e, 0x77, 0x6d, 0x39, 0x4d, 0x9b, 0xa4, 0x49, 0x16, 0x04, 0x86, 0x24, 0x4c, 0x00,
	0x03, 0xcd, 0x0c, 0x25, 0xd1, 0x6d, 0x8a, 0xd0, 0x22, 0xa4, 0x65, 0x4c, 0x11, 0x0c, 0xc9, 0xdd,
	0xf5, 0xba, 0x69, 0xaa, 0xfe, 0x39, 0xfd, 0x12, 0x7d, 0xe8, 0x73, 0x3f, 0x43, 0x4f, 0x4f, 0x5f,
	0xfa, 0xec, 0xc7, 0x7e, 0x84, 0x9e, 0xbe, 0xb4, 0x9f, 0xa1, 0x67, 0x06, 0xe0, 0x60, 0xc8, 0xd5,
	0xae, 0x77, 0x75, 0x72, 0x4e, 0x9e, 0xa4, 0xfb, 0xbb, 0x77, 0xee, 0xff, 0x99, 0xb9, 0x03, 0xa2,
	0x47, 0x97, 0x69, 0x7a, 0x39, 0x4a, 0x1e, 0x5c, 0xa6, 0xa3, 0xfe, 0xf8, 0xf2, 0x41, 0x3a, 0xbd,
	0x7c, 0xd8, 0x9f, 0x4c, 0x92, 0xf1, 0xe5, 0x70, 0x9c, 0x3c, 0x1c, 0x8e, 0xe7, 0xc9, 0x74, 0xdc,
	0x1f, 0x3d, 0x9c, 0xa5, 0xe7, 0x9f, 0x25, 0xf3, 0xfc, 0x4f, 0x3c, 0x4b, 0xa6, 0x4f, 0x87, 0xe7,
	0xc9, 0x83, 0xc9, 0x34, 0x9d, 0xa7, 0x66, 0x45, 0xc9, 0x5b, 0xff, 0xbc, 0x8b, 0xf6, 0x69, 0x72,
	0x95, 0xce, 0x13, 0x26, 0x25, 0x59, 0x26, 0x88, 0xa7, 0xd3, 0x74, 0x6a, 0x7e, 0x07, 0xd5, 0x66,
	0xcf, 0x67, 0xf3, 0xe4, 0x2a, 0x4e, 0x04, 0xbd, 0x6f, 0x1c, 0x18, 0x87, 0xeb, 0x3f, 0x31, 0x3e,
	0xa0, 0xd5, 0x0c, 0xce, 0xa4, 0xbe, 0x8d, 0x6a, 0x92, 0x1d, 0x0f, 0x92, 0x79, 0x7f, 0x38, 0xda,
	0x2f, 0x1d, 0x18, 0x87, 0x15, 0x5a, 0x95, 0x98, 0x2b, 0x21, 0xeb, 0x73, 0x54, 0x91, 0xb2, 0x4e,
	0x3a, 0x48, 0x4c, 0x40, 0x35, 0xd6, 0x63, 0x1c, 0x07, 0x31, 0xa6, 0x94, 0x50, 0x30, 0xcc, 0x3a,
	0xaa, 0xb4, 0x6c, 0x2f, 0x27, 0x4b, 0x66, 0x15, 0x6d, 0x36, 0x6d, 0xcf, 0xef, 0x52, 0x0c, 0x6b,
	0xe6, 0x1e, 0xba, 0x13, 0x61, 0x1a, 0x78, 0x8c, 0x79, 0x24, 0x8c, 0x5d, 0x1c, 0x7a, 0xd8, 0x85,
	0x75, 0xf3, 0x2e, 0xda, 0xf1, 0xc2, 0x13, 0xdb, 0xf7, 0xdc, 0x98, 0xe2, 0xe3, 0x2e, 0x66, 0x1c,
	0x36, 0xcc, 0x3b, 0xa8, 0xce, 0x88, 0xd3, 0xc1, 0x3c, 0x76, 0x7c, 0xc2, 0xb0, 0x0b, 0x9b, 0xd6,
	0xbf, 0x99, 0xa8, 0xca, 0x34, 0x67, 0x77, 0x50, 0x95, 0xf5, 0x58, 0xcc, 0xba, 0x8e, 0x83, 0x19,
	0x83, 0xb7, 0x84, 0x6d, 0x01, 0x60, 0x61, 0x04, 0x0c, 0x73, 0x1b, 0x21, 0x49, 0x86, 0x04, 0x87,
	0x1c, 0x4a, 0x8a, 0xcd, 0xa8, 0xd3, 0x86, 0xb2, 0x22, 0xbd, 0x90, 0x53, 0x58, 0x13, 0x9e, 0x66,
	0x24, 0x81, 0x75, 0xc5, 0x0b, 0xcf, 0x3c, 0x02, 0x1b, 0x8a, 0x3c, 0x6a, 0x78, 0x2d, 0xd8, 0x5c,
	0x18, 0x16, 0x8a, 0xcf, 0xb0, 0x03, 0x5b, 0x8a, 0xdf, 0xb0, 0xdd, 0x26, 0x54, 0x94, 0x61, 0xa7,
	0xed, 0xf9, 0x2e, 0x20, 0x45, 0xdb, 0x2d, 0xdb, 0x0b, 0xa1, 0x2a, 0x02, 0x96, 0xf4, 0x29, 0xe9,
	0xfa, 0x6e, 0xc3, 0x27, 0x4e, 0x07, 0xaa, 0x9a, 0xb7, 0x01, 0x0e, 0xa0, 0x56, 0x2c, 0x12, 0xd1,
	0x41, 0x5d, 0xd1, 0x4d, 0xbb, 0xeb, 0x73, 0xd8, 0xd6, 0x9c, 0xe0, 0x0d, 0xbf, 0x03, 0x3b, 0x85,
	0x13, 0x5d, 0xd6, 0x03, 0x50, 0xf2, 0xf8, 0xcc, 0x63, 0x1c, 0xee, 0x28, 0xf6, 0x99, 0x8b, 0x4f,
	0xc0, 0xd4, 0xcc, 0x09, 0xfa, 0xae, 0xae, 0xce, 0xf5, 0x28, 0xec, 0x2a, 0x01, 0x8f, 0x09, 0x7a,
	0xaf, 0xa0, 0x45, 0xa9, 0xe0, 0x5e, 0xa1, 0xa0, 0xe9, 0xf9, 0x18, 0xee, 0x2b, 0x3a, 0x90, 0xf4,
	0xbe, 0x66, 0x80, 0xf3, 0x1e, 0x7c, 0x4d, 0x19, 0xe0, 0x67, 0xbc, 0xc1, 0x7a, 0xf0, 0x75, 0xe5,
	0x50, 0x53, 0x24, 0xf5, 0x6d, 0x4d, 0x9e, 0x45, 0x0e, 0xfc, 0x91, 0xa2, 0x59, 0xe4, 0x45, 0x18,
	0xbe, 0xa1, 0xc4, 0x29, 0x69, 0x32, 0xf8, 0x66, 0x61, 0xce, 0xf7, 0xc2, 0x0e, 0x7c, 0xab, 0xa8,
	0xbd, 0x90, 0x3e, 0x30, 0x6b, 0x68, 0x4b, 0x92, 0x2e, 0x09, 0xe0, 0xdb, 0x4a, 0x98, 0xda, 0x61,
	0x0b, 0x83, 0xa5, 0x7c, 0x71, 0xb1, 0xed, 0xfa, 0x1d, 0x78, 0x47, 0x76, 0x9b, 0x02, 0x44, 0x3d,
	0xde, 0x31, 0x77, 0x11, 0x64, 0xfe, 0xd8, 0x01, 0xe6, 0x84, 0xf8, 0x24, 0x6c, 0xc1, 0x77, 0x34,
	0x2f, 0x7d, 0xa7, 0x03, 0xef, 0xea, 0x5e, 0xf7, 0x18, 0xfc, 0xb1, 0x52, 0x14, 0x12, 0x8e, 0x83,
	0x88, 0xf7, 0xe0, 0xbb, 0xca, 0x33, 0x9f, 0x90, 0x08, 0x0e, 0xf5, 0x3a, 0xb3, 0x16, 0x7c, 0xbf,
	0x68, 0x43, 0x97, 0x06, 0xf0, 0x9e, 0xd6, 0x3b, 0x34, 0x6c, 0xc1, 0x0f, 0xf2, 0x1d, 0x16, 0x63,
	0xff, 0x28, 0x64, 0xbd, 0xd0, 0x81, 0xf7, 0x95, 0x84, 0xff, 0x51, 0xdb, 0xe7, 0xf0, 0x40, 0xa3,
	0x29, 0xe3, 0xf0, 0xb0, 0xa0, 0x43, 0xa1, 0xe1, 0x03, 0x15, 0x6c, 0x37, 0xb4, 0xb9, 0xd3, 0x86,
	0x0f, 0x35, 0x0f, 0x1c, 0xe6, 0xc1, 0x51, 0xb1, 0xe0, 0x48, 0x28, 0xfc, 0x48, 0xef, 0x66, 0x0c,
	0x3f, 0xd4, 0x49, 0x0a, 0x7f, 0xa2, 0xa4, 0xcf, 0x9a, 0x5d, 0xdf, 0x87, 0x1f, 0x69, 0xda, 0xec,
	0x90, 0xc0, 0x9f, 0x2a, 0x73, 0x42, 0xfc, 0xd8, 0x81, 0x3f, 0xd3, 0x01, 0xe6, 0x73, 0xf8, 0xb1,
	0x5a, 0xd1, 0x68, 0x92, 0x90, 0xc3, 0x4f, 0xf5, 0x1c, 0x72, 0x0a, 0x7f, 0xae, 0xb5, 0xa2, 0x6b,
	0x73, 0x1b, 0x7e, 0xa6, 0x3c, 0xe0, 0x5e, 0x80, 0xe1, 0xe7, 0xc5, 0xe6, 0x24, 0x8c, 0xc2, 0x2f,
	0xb4, 0xe5, 0x21, 0xe6, 0xf0, 0x48, 0xa3, 0xa3, 0x4e, 0x0b, 0x6c, 0xa5, 0x8e, 0xe2, 0x80, 0x70,
	0x0c, 0x0d, 0x4d, 0xbf, 0xec, 0x1d, 0x47, 0x35, 0x8b, 0xed, 0x9e, 0x80, 0x5b, 0x34, 0x1e, 0x0d,
	0x42, 0x0e, 0x58, 0x99, 0x73, 0x48, 0x10, 0x40, 0x53, 0xb1, 0x23, 0x4a, 0x38, 0x81, 0x96, 0xaa,
	0x78, 0xd0, 0xf5, 0xb9, 0xd7, 0x26, 0x11, 0xb4, 0x8b, 0xf6, 0x22, 0xdc, 0x25, 0x1c, 0x3c, 0x3d,
	0x05, 0xa2, 0xe8, 0x1f, 0xab, 0x45, 0xe4, 0x04, 0xd3, 0xa6, 0x4f, 0x4e, 0xa1, 0xa3, 0x0a, 0x1d,
	0x12, 0xde, 0x0d, 0xbd, 0x63, 0xf0, 0x8b, 0x3c, 0xd9, 0x6e, 0xd3, 0x85, 0x40, 0x0f, 0xc4, 0x69,
	0xb7, 0x20, 0x54, 0x80, 0xef, 0x35, 0x6c, 0xc7, 0x01, 0xa2, 0x03, 0x0d, 0xdb, 0x85, 0x48, 0x07,
	0x98, 0x13, 0xc2, 0xb1, 0x0e, 0x04, 0xf6, 0x19, 0xd0, 0xa2, 0xbf, 0xbc, 0x86, 0x3c, 0xcc, 0x58,
	0xb1, 0xd1, 0x7d, 0x86, 0x8f, 0x81, 0x2b, 0x09, 0x8a, 0x19, 0xb7, 0x29, 0x87, 0xae, 0x42, 0x18,
	0xa7, 0x72, 0xbb, 0x9d, 0xa8, 0x35, 0x5d, 0x86, 0x29, 0x83, 0x53, 0x3d, 0x18, 0x71, 0x8a, 0xc3,
	0x99, 0xda, 0x4e, 0xae, 0xd0, 0xe2, 0xba, 0x94, 0xe2, 0x63, 0xe8, 0x29, 0xb9, 0x80, 0xb5, 0x98,
	0xf7, 0x09, 0x86, 0x4f, 0x4c, 0x13, 0x6d, 0x17, 0xe9, 0xe5, 0xbd, 0x08, 0xc3, 0x5f, 0xa8, 0xf3,
	0x32, 0x24, 0x12, 0x25, 0x11, 0x87, 0xbf, 0x34, 0xef, 0xa3, 0xbb, 0x85, 0x60, 0x48, 0x58, 0x37,
	0x8a, 0x08, 0xe5, 0xf0, 0x4b, 0xc5, 0x10, 0x86, 0x79, 0xc1, 0xf8, 0x2b, 0xa5, 0x9a, 0x44, 0xc2,
	0xad, 0x6e, 0x14, 0x41, 0xac, 0x1f, 0x7b, 0xac, 0x2b, 0x80, 0x85, 0x9f, 0x51, 0xb3, 0x58, 0xfa,
	0x2b, 0x85, 0xda, 0x1a, 0xda, 0x57, 0x0a, 0x45, 0x3c, 0x5e, 0xd8, 0x65, 0x18, 0x3e, 0x15, 0x77,
	0x9c, 0xc2, 0x42, 0xc2, 0xed, 0x13, 0xdb, 0xf3, 0xe1, 0xbc, 0x48, 0x08, 0xe6, 0x2e, 0x39, 0x0d,
	0x61, 0x50, 0x04, 0x85, 0x79, 0x37, 0xa4, 0xd8, 0x76, 0xda, 0x90, 0x14, 0xc7, 0x07, 0xe6, 0x14,
	0x33, 0xcc, 0xe1, 0x42, 0x99, 0x76, 0x48, 0x18, 0xda, 0x0d, 0x42, 0x39, 0x76, 0xe1, 0x52, 0x99,
	0x16, 0x68, 0x26, 0xf9, 0x58, 0x8b, 0xa5, 0xd1, 0x6d, 0x32, 0x18, 0x2a, 0xc0, 0x63, 0x42, 0x0c,
	0x7e, 0xad, 0x97, 0x45, 0x22, 0x9f, 0x29, 0x83, 0xac, 0xdd, 0xcd, 0x1c, 0x1b, 0x29, 0x83, 0x9c,
	0x90, 0xc0, 0x0e, 0x7b, 0x14, 0x37, 0x19, 0x5c, 0x29, 0x41, 0xb1, 0x07, 0x5d, 0xd2, 0xe5, 0x30,
	0x5e, 0xf2, 0x8c, 0xe2, 0x66, 0x57, 0xdc, 0xd2, 0xa9, 0x12, 0x6c, 0x13, 0x96, 0x69, 0x9c, 0x28,
	0x41, 0x01, 0x2d, 0x62, 0xfd, 0x8d, 0x72, 0xc6, 0xf6, 0x29, 0xb6, 0xdd, 0x1e, 0x4c, 0x55, 0x4a,
	0xbc, 0x30, 0xa2, 0xa4, 0x45, 0xc5, 0xa5, 0x3e, 0x2b, 0xb6, 0x23, 0xb7, 0x7d, 0x0c, 0xf3, 0xe2,
	0x38, 0x73, 0x7c, 0x6c, 0x87, 0xf0, 0x44, 0x2f, 0x61, 0x68, 0x07, 0xf0, 0xb4, 0x00, 0xb2, 0xe4,
	0x3f, 0xd3, 0xae, 0x32, 0x21, 0xf0, 0xb9, 0x72, 0x31, 0x3b, 0x11, 0x3c, 0x02, 0xcf, 0x95, 0x88,
	0x7b, 0xdc, 0x25, 0x1c, 0xbe, 0xd0, 0xce, 0xf1, 0x00, 0xbb, 0x5e, 0x37, 0x80, 0xbf, 0x56, 0xde,
	0x65, 0x80, 0x6c, 0xcd, 0xdf, 0x2a, 0x39, 0xc7, 0x0e, 0x1d, 0xec, 0x63, 0x17, 0xfe, 0x46, 0x3b,
	0x7f, 0x3a, 0xb8, 0x07, 0xbf, 0x53, 0xeb, 0x3a, 0xb8, 0x87, 0xcf, 0x22, 0x8f, 0x62, 0x17, 0xfe,
	0xd6, 0xdc, 0x2d, 0x40, 0x8a, 0x4f, 0x48, 0x07, 0xbb, 0x70, 0x6d, 0x98, 0x7b, 0x79, 0xa2, 0x24,
	0xfa, 0x31, 0x76, 0x44, 0xad, 0xff, 0xce, 0x30, 0xef, 0x2e, 0x1a, 0xf7, 0x34, 0xc4, 0x54, 0x5c,
	0x51, 0xf0, 0xf7, 0x86, 0xb9, 0x9f, 0xb7, 0x79, 0x48, 0x38, 0xc5, 0x8e, 0x38, 0x48, 0xec, 0x86,
	0x8f, 0xe1, 0x1f, 0x0c, 0x13, 0x16, 0xe7, 0x44, 0xb3, 0xe3, 0xf9, 0x3e, 0xfc, 0xa3, 0xf1, 0xf5,
	0x12, 0x18, 0xd6, 0x15, 0xaa, 0xda, 0x83, 0xc1, 0x34, 0x99, 0xcd, 0xa2, 0x74, 0x3a, 0x37, 0x4d,
	0xb4, 0x36, 0x49, 0xa7, 0xf3, 0x7d, 0xe3, 0xa0, 0x74, 0xb8, 0x4e, 0xe5, 0xff, 0xe6, 0xbb, 0x68,
	0x7b, 0xd2, 0x3f, 0xff, 0x2c, 0x19, 0xc4, 0xfd, 0x4c, 0x52, 0xce, 0x7f, 0x35, 0x5a, 0xcf, 0xd0,
	0x7c, 0xb9, 0xf9, 0x0e, 0xaa, 0x3f, 0x4e, 0x67, 0xf3, 0x71, 0xff, 0x2a, 0x89, 0x1f, 0x0f, 0xc7,
	0xf3, 0xfd, 0xb2, 0x9c, 0x12, 0x6b, 0x0b, 0xb0, 0x3d, 0x1c, 0xcf, 0xad, 0x7f, 0x5a, 0x43, 0x77,
	0x9d, 0x69, 0xd2, 0x5f, 0x0c, 0xa3, 0x34, 0xf9, 0xcd, 0x93, 0x64, 0x36, 0x37, 0x1d, 0xb4, 0x71,
	0xd1, 0xbf, 0x1a, 0x8e, 0x9e, 0x4b, 0xcb, 0xdb, 0x47, 0xef, 0x3d, 0x50, 0x03, 0xec, 0x83, 0x1b,
	0xe4, 0x1f, 0x64, 0x54, 0x53, 0x2e, 0xa1, 0xf9, 0x52, 0xd3, 0x43, 0x5b, 0x72, 0xfa, 0x3d, 0x4f,
	0xc5, 0x88, 0x2a, 0xd4, 0xbc, 0xff, 0x5a, 0x6a, 0xa2, 0x7c, 0x11, 0x55, 0xcb, 0xcd, 0x9f, 0xa3,
	0xed, 0x7c, 0xae, 0x4e, 0x27, 0xf3, 0x61, 0x3a, 0x9e, 0xed, 0x97, 0x0f, 0xca, 0x87, 0xd5, 0xa3,
	0xfb, 0x9a, 0xc2, 0x6c, 0x31, 0x91, 0x7c, 0x5a, 0x9f, 0x69, 0xd4, 0xcc, 0x6c, 0xa0, 0x3b, 0x93,
	0x69, 0xfa, 0xf9, 0xf3, 0x38, 0xf9, 0x3c, 0x9b, 0xd6, 0xe3, 0xe1, 0x64, 0x7f, 0xed, 0xc0, 0x38,
	0xac, 0x1e, 0xdd, 0xd3, 0x54, 0x68, 0xa9, 0xa7, 0x3b, 0x72, 0x01, 0xce, 0xe5, 0xbd, 0x89, 0x79,
	0x88, 0xb6, 0x47, 0xc3, 0xd9, 0x3c, 0x19, 0xc7, 0x9f, 0xf6, 0xcf, 0x3f, 0x1b, 0xa5, 0x97, 0xfb,
	0xeb, 0x8b, 0xe9, 0xbc, 0x9e, 0x31, 0x1a, 0x19, 0x6e, 0x7e, 0x84, 0x2a, 0x53, 0x39, 0xe1, 0x0b,
	0x2b, 0x1b, 0xaf, 0xb4, 0xb2, 0x95, 0x09, 0x7a, 0x13, 0x73, 0x0f, 0x6d, 0xf4, 0x27, 0x93, 0x78,
	0x38, 0xd8, 0xaf, 0xc8, 0x42, 0xad, 0xf7, 0x27, 0x13, 0x6f, 0x60, 0x7e, 0x03, 0xa1, 0xc9, 0x34,
	0xfd, 0x75, 0x72, 0x3e, 0x17, 0x2c, 0x74, 0x60, 0x1c, 0x96, 0x69, 0x25, 0x47, 0xbc, 0x81, 0x65,
	0xa1, 0x9a, 0x9e, 0x7b, 0x73, 0x0b, 0xad, 0x79, 0xd1, 0xd3, 0x1f, 0x82, 0x91, 0xff, 0xf7, 0x23,
	0x28, 0x59, 0x16, 0xda, 0x5e, 0x4e, 0xac, 0xb9, 0x89, 0xca, 0xdc, 0x89, 0xc0, 0x10, 0xff, 0x74,
	0xdd, 0x08, 0x4a, 0xd6, 0x97, 0x06, 0xba, 0xb3, 0x5c, 0x91, 0xc9, 0xe8, 0xb9, 0xf9, 0x1e, 0xba,
	0x93, 0xa7, 0x7d, 0x90, 0xcc, 0xce, 0xa7, 0xc3, 0xc9, 0x3c, 0x7f, 0x93, 0x54, 0x28, 0x64, 0x0c,
	0x57, 0xe1, 0xe6, 0xcf, 0xd0, 0xb6, 0x78, 0xf4, 0x24, 0x53, 0xd5, 0x97, 0xe5, 0x57, 0x86, 0x5e,
	0xcf, 0xa4, 0x17, 0xfd, 0xfa, 0x7b, 0x28, 0xd1, 0xf7, 0x2b, 0x5b, 0xff, 0xb3, 0x09, 0xd7, 0xd7,
	0xd7, 0xd7, 0x25, 0xeb, 0x77, 0xa8, 0xda, 0x18, 0x8e, 0x07, 0x8b, 0x86, 0x7e, 0x49, 0x24, 0xa5,
	0x1b, 0x23, 0xb9, 0xd1, 0x15, 0xd1, 0xc1, 0xaf, 0xef, 0x8a, 0x45, 0x50, 0x25, 0xb3, 0x2f, 0xf2,
	0x78, 0xa3, 0x42, 0xe3, 0x8d, 0x62, 0xb3, 0x1c, 0xb4, 0xdb, 0x4a, 0xe6, 0x59, 0x75, 0xc2, 0xfe,
	0x55, 0x72, 0x9b, 0xc8, 0xac, 0x33, 0x64, 0xae, 0x28, 0x79, 0xa9, 0x7b, 0xa5, 0x37, 0x73, 0xcf,
	0x96, 0x9a, 0xa3, 0x24, 0x99, 0xde, 0xda, 0x39, 0x07, 0xc1, 0x92, 0x0a, 0xe1, 0xda, 0x43, 0xb4,
	0x39, 0x49, 0x92, 0xe9, 0x57, 0x3b, 0xb4, 0x21, 0xc4, 0xbc, 0x89, 0xf5, 0xe5, 0xe6, 0x62, 0x47,
	0x64, 0x7b, 0xdf, 0xfc, 0x05, 0x5a, 0x1f, 0x25, 0x4f, 0x93, 0x51, 0x7e, 0x92, 0x7d, 0xef, 0x25,
	0x27, 0xc6, 0x12, 0xe1, 0x8b, 0x05, 0x34, 0x5b, 0x67, 0x3e, 0x42, 0x1b, 0xd9, 0xa1, 0x93, 0x1f,
	0x62, 0x87, 0xaf, 0xa3, 0x41, 0x46, 0x90, 0xaf, 0x33, 0x77, 0xd1, 0xfa, 0xd3, 0xfe, 0xe8, 0x49,
	0xb2, 0x5f, 0x3e, 0x28, 0x1d, 0xd6, 0x68, 0x46, 0x58, 0x09, 0xba, 0xf3, 0x82, 0x4d, 0xed, 0x41,
	0xcd, 0x88, 0x1f, 0x7b, 0x11, 0xbc, 0x25, 0x67, 0x95, 0x02, 0xca, 0xfe, 0x05, 0x43, 0xce, 0x16,
	0x05, 0x2c, 0xb6, 0xf3, 0xc6, 0x0a, 0x26, 0x76, 0xf6, 0x1d, 0xeb, 0xdf, 0xd7, 0x11, 0xac, 0x7a,
	0x26, 0x6f, 0xbb, 0x85, 0x60, 0xec, 0xe2, 0x46, 0xb7, 0x05, 0x86, 0x1c, 0xc9, 0x14, 0x48, 0xc5,
	0x94, 0x28, 0xc6, 0x23, 0x28, 0x2d, 0xa9, 0x8d, 0xe5, 0x95, 0x5a, 0x5e, 0xd6, 0x90, 0x7d, 0x47,
	0x58, 0x5b, 0xd6, 0xe0, 0x92, 0x90, 0x53, 0xd2, 0xe5, 0x18, 0xd6, 0x97, 0x19, 0x0d, 0x4a, 0x6c,
	0xd7, 0xb1, 0xe5, 0x07, 0x04, 0x31, 0x74, 0x28, 0x06, 0x0b, 0xdd, 0x46, 0xb7, 0x09, 0x9b, 0xcb,
	0x28, 0x75, 0x4e, 0x04, 0xba, 0xb5, 0xac, 0xa4, 0x83, 0x71, 0x64, 0xfb, 0xde, 0x09, 0x86, 0xca,
	0x32, 0x83, 0x90, 0x86, 0x17, 0xfa, 0x5e, 0x88, 0x01, 0x2d, 0xeb, 0xf1, 0xbd, 0xb0, 0x85, 0x29,
	0xd4, 0xcd, 0x7b, 0xc8, 0x5c, 0xd2, 0x2e, 0x86, 0x25, 0x02, 0xbb, 0xcb, 0x38, 0x0b, 0xdd, 0x0c,
	0xdf, 0xd3, 0x6a, 0xe2, 0x45, 0x31, 0x27, 0x0c, 0x8c, 0x15, 0x88, 0xfb, 0x50, 0xd2, 0xca, 0xe4,
	0x45, 0x71, 0x5b, 0x8c, 0x9a, 0x8e, 0x0f, 0xe5, 0x65, 0x98, 0x44, 0xdc, 0x23, 0x21, 0x83, 0x35,
	0xcd, 0x16, 0x77, 0xa2, 0x58, 0x3c, 0xef, 0x7d, 0xbb, 0x07, 0x86, 0x26, 0x2e, 0xf0, 0xc0, 0x3e,
	0x63, 0xb8, 0x05, 0x25, 0x2d, 0xdb, 0x02, 0x76, 0x08, 0xed, 0x40, 0x59, 0x0b, 0x5b, 0x80, 0x22,
	0x21, 0x9e, 0xeb, 0x63, 0x58, 0x33, 0xf7, 0xd1, 0xee, 0x2a, 0x23, 0xe4, 0x27, 0x3e, 0xac, 0xaf,
	0x98, 0x15, 0x1c, 0x27, 0x14, 0x65, 0x58, 0x36, 0x2b, 0x9e, 0xb0, 0x21, 0x87, 0xcd, 0x15, 0xf1,
	0x2c, 0x81, 0x47, 0xb0, 0x65, 0xbe, 0x8d, 0xee, 0x6b, 0xb8, 0x8b, 0x9b, 0x98, 0xc6, 0xb6, 0xe3,
	0xe0, 0x88, 0x43, 0x65, 0x85, 0x79, 0xea, 0x85, 0x2e, 0x39, 0x8d, 0x1d, 0xdf, 0x0e, 0x22, 0x40,
	0x2b, 0x81, 0x78, 0x61, 0x93, 0x40, 0x75, 0x25, 0x90, 0xe3, 0xae, 0xe7, 0x74, 0x6c, 0xa7, 0x03,
	0x35, 0x39, 0x11, 0x3d, 0x47, 0xf7, 0xd9, 0xe2, 0xc8, 0xca, 0xaf, 0xf3, 0x5b, 0x1d, 0xea, 0x1f,
	0xa2, 0xcd, 0xc5, 0xec, 0x50, 0x7a, 0xf5, 0xec, 0xb0, 0x90, 0xb3, 0xee, 0xa3, 0xbd, 0x17, 0x4d,
	0x4f, 0x46, 0xcf, 0x85, 0x4f, 0xad, 0x3f, 0x90, 0x4f, 0x1f, 0xa3, 0xbd, 0xd6, 0x4d, 0x3e, 0xdd,
	0x46, 0xd7, 0xbf, 0x18, 0x68, 0xdb, 0x49, 0xc7, 0xe3, 0xe4, 0x7c, 0x7e, 0x2b, 0xf7, 0x97, 0xe6,
	0x9c, 0x57, 0xdf, 0x8f, 0xc5, 0x9c, 0xf3, 0x1e, 0xda, 0x99, 0x0f, 0xaf, 0x92, 0xf4, 0xc9, 0x3c,
	0x9e, 0x25, 0xe7, 0xe9, 0x78, 0x90, 0xcd, 0x09, 0xc6, 0x4f, 0x4a, 0xef, 0x7f, 0x48, 0xb7, 0x73,
	0x16, 0xcb, 0x38, 0xd6, 0x2f, 0x51, 0x4d, 0x39, 0xf8, 0x7b, 0xba, 0x48, 0xf5, 0x21, 0xe1, 0x04,
	0xd5, 0x7d, 0x39, 0xb9, 0xdd, 0x2a, 0xfc, 0x7d, 0xb4, 0xb9, 0x98, 0x04, 0x4b, 0x72, 0x3e, 0x5f,
	0x90, 0x56, 0x1d, 0x55, 0x17, 0x7a, 0x45, 0xbb, 0x0c, 0x51, 0xdd, 0x3e, 0x3f, 0x4f, 0x26, 0xb7,
	0xcb, 0xf2, 0x0d, 0x09, 0x2b, 0xbd, 0x34, 0x61, 0xd7, 0x06, 0xaa, 0x2e, 0x6c, 0x89, 0x84, 0x1d,
	0xa1, 0xbd, 0x71, 0xf2, 0x2c, 0x7e, 0xd1, 0x5a, 0xf6, 0x66, 0xb8, 0x3b, 0x4e, 0x9e, 0xb1, 0x1b,
	0x06, 0xb9, 0xbc, 0xac, 0xaf, 0x39, 0xc8, 0x65, 0xd2, 0x39, 0x64, 0xfd, 0x97, 0x81, 0x76, 0xd8,
	0xe3, 0x27, 0x73, 0x37, 0x7d, 0x76, 0xbb, 0xbc, 0x7e, 0x80, 0xca, 0x8f, 0xd3, 0x67, 0xf9, 0x6d,
	0xfb, 0x4d, 0xbd, 0x8b, 0x97, 0xb5, 0x3e, 0x68, 0xa7, 0xcf, 0xa8, 0x10, 0x35, 0xbf, 0x85, 0xaa,
	0xb3, 0x64, 0x3c, 0x88, 0xd3, 0x8b, 0x8b, 0x59, 0x32, 0x97, 0xd7, 0x6c, 0x99, 0x22, 0x01, 0x11,
	0x89, 0x58, 0x0e, 0x2a, 0xb7, 0xd3, 0x67, 0xfa, 0x45, 0xd6, 0xee, 0xf2, 0x98, 0xba, 0xcb, 0xf7,
	0xa8, 0xc0, 0x4e, 0xc5, 0x85, 0xa7, 0xdd, 0x1b, 0x99, 0xdc, 0x29, 0x85, 0xb2, 0xb5, 0x83, 0xea,
	0x85, 0x07, 0xa2, 0xae, 0xbf, 0x42, 0x35, 0x67, 0x94, 0xce, 0x6e, 0x35, 0xed, 0x98, 0xef, 0x2c,
	0xfb, 0x2c, 0xea, 0x51, 0x96, 0x25, 0xd5, 0xfd, 0xae, 0x21, 0x94, 0x5b, 0x10, 0xf6, 0xfe, 0xcf,
	0x40, 0x55, 0x96, 0xdc, 0x72, 0xa8, 0xbd, 0x87, 0xd6, 0x06, 0xfd, 0x79, 0x5f, 0xa6, 0xb5, 0xd6,
	0x28, 0x6d, 0x19, 0x54, 0xd2, 0xe2, 0x9d, 0x38, 0x9b, 0x4f, 0x93, 0xfe, 0xd5, 0x72, 0xf6, 0x6a,
	0x19, 0x98, 0xf9, 0x61, 0xde, 0x47, 0xeb, 0x17, 0xa3, 0xfe, 0xe5, 0x4c, 0x0e, 0xe4, 0xf2, 0xc9,
	0x93, 0xd1, 0x62, 0x3e, 0x93, 0x51, 0xcc, 0x53, 0xf9, 0x1a, 0x7a, 0xc5, 0x7c, 0x26, 0xc4, 0x78,
	0x7a, 0x53, 0x37, 0x6f, 0xbc, 0xb4, 0x9b, 0x0f, 0x51, 0x25, 0x8b, 0x57, 0xb4, 0xf2, 0xdb, 0xa8,
	0x22, 0x1c, 0x8e, 0x67, 0xc9, 0x78, 0x9e, 0xfd, 0x30, 0x42, 0xb7, 0x04, 0xc0, 0x92, 0xf1, 0xdc,
	0xfa, 0x4f, 0x03, 0x6d, 0xd3, 0xe4, 0x3c, 0x19, 0x3e, 0xbd, 0x5d, 0x35, 0x94, 0xf2, 0xe1, 0x17,
	0x49, 0xbe, 0x9b, 0x33, 0xe5, 0xc3, 0x2f, 0x92, 0x22, 0xfa, 0xf2, 0x4a, 0xf4, 0x37, 0x04, 0xb3,
	0xfe, 0xd2, 0x60, 0x2c, 0xb4, 0xde, 0x94, 0xab, 0xaa, 0x68, 0x33, 0x60, 0x2d, 0x31, 0xa8, 0x80,
	0x61, 0xd6, 0xd0, 0x96, 0x20, 0x22, 0x8c, 0x3b, 0x50, 0xb2, 0xfe, 0xd5, 0x40, 0x35, 0x15, 0x86,
	0x08, 0xfa, 0x85, 0xea, 0xc8, 0x3e, 0x59, 0xa9, 0xce, 0xa2, 0xb4, 0xc2, 0x3d, 0xbd, 0xb4, 0x3f,
	0x45, 0xf5, 0x69, 0xa6, 0x6c, 0x10, 0x5f, 0x4c, 0xd3, 0xab, 0xaf, 0x78, 0x4e, 0xd5, 0x16, 0xc2,
	0xcd, 0x69, 0x7a, 0x25, 0xf6, 0xd4, 0xa7, 0x4f, 0x2e, 0x2e, 0x92, 0x69, 0x96, 0x13, 0xf9, 0xd6,
	0xa5, 0x28, 0x83, 0x44, 0x56, 0xac, 0x2f, 0xcb, 0xa8, 0x12, 0xa5, 0xa3, 0x11, 0x7e, 0x9a, 0x8c,
	0xdf, 0x30, 0xdb, 0xdf, 0x43, 0x30, 0xcd, 0xaa, 0x94, 0x0c, 0xe2, 0x44, 0xac, 0x9f, 0xe5, 0x49,
	0xdf, 0x51, 0xb8, 0x54, 0x3b, 0x33, 0xbf, 0x8b, 0x76, 0xd2, 0x4f, 0xe5, 0x4b, 0x51, 0x49, 0x96,
	0xa5, 0xe4, 0xf6, 0x02, 0xce, 0x04, 0xad, 0xff, 0x28, 0xa1, 0xba, 0x72, 0x47, 0x24, 0x5a, 0x9b,
	0x35, 0x22, 0xe2, 0xfb, 0x21, 0x09, 0x31, 0xbc, 0xa5, 0x4d, 0x6e, 0x02, 0xf4, 0xc2, 0xa5, 0x13,
	0x40, 0x40, 0x11, 0xf5, 0x96, 0x46, 0x5e, 0x81, 0x91, 0x2e, 0x87, 0xb5, 0x15, 0x0c, 0x53, 0x0a,
	0x5b, 0x2b, 0x58, 0xbb, 0x1b, 0x01, 0xac, 0xda, 0x3d, 0xb1, 0x7d, 0x38, 0xd0, 0x26, 0x2c, 0x01,
	0x52, 0x37, 0x24, 0x34, 0x80, 0x47, 0xe6, 0xbd, 0x15, 0xb8, 0x61, 0x87, 0xf2, 0x1b, 0xd3, 0x32,
	0x7e, 0x4a, 0xa5, 0xf8, 0x75, 0xe9, 0x05, 0x3c, 0x93, 0x5f, 0x93, 0x1f, 0x9f, 0x0a, 0x3c, 0x60,
	0x2d, 0xb8, 0xde, 0x5a, 0x55, 0x8e, 0x03, 0x72, 0x82, 0xe1, 0xfa, 0x40, 0x7e, 0xc0, 0xd2, 0x8d,
	0x0a, 0xb7, 0xaf, 0x1f, 0x59, 0x8f, 0x51, 0x55, 0x24, 0x70, 0xb1, 0x7f, 0x7e, 0x80, 0x36, 0xf2,
	0x84, 0x1b, 0x72, 0x9e, 0xd8, 0xd5, 0xda, 0x46, 0x25, 0x9a, 0xe6, 0x32, 0x6f, 0x76, 0x4b, 0xfd,
	0x38, 0xeb, 0x9c, 0xac, 0xc5, 0x0b, 0x3b, 0xa5, 0xaf, 0xb6, 0x63, 0xfd, 0x56, 0xec, 0xf3, 0x59,
	0x3a, 0x2a, 0xf6, 0xb9, 0x89, 0xd6, 0xc6, 0xfd, 0xab, 0x24, 0x6f, 0x36, 0xf9, 0xbf, 0x79, 0x82,
	0x20, 0xbf, 0xbb, 0x62, 0xf9, 0x31, 0x6a, 0x98, 0x64, 0xda, 0xdf, 0xf0, 0x4b, 0xd6, 0x4e, 0xae,
	0xa4, 0x99, 0xeb, 0xb0, 0xfe, 0xbb, 0x2c, 0xf6, 0x67, 0x6e, 0x5e, 0x38, 0x7f, 0xd3, 0xc7, 0xb8,
	0xf2, 0x8b, 0x1f, 0xe3, 0xde, 0x45, 0xdb, 0xe7, 0xfd, 0x71, 0x3a, 0x1e, 0x9e, 0xf7, 0x47, 0xb1,
	0xf4, 0x36, 0xfb, 0x1a, 0x57, 0x57, 0xa8, 0x7c, 0x96, 0xed, 0xa3, 0xcd, 0xfe, 0x68, 0xd8, 0x9f,
	0x25, 0xe2, 0xa0, 0x2d, 0x1f, 0x56, 0xe8, 0x82, 0xb4, 0xfe, 0xb7, 0xa4, 0xff, 0xa0, 0xfb, 0x35,
	0xb4, 0x97, 0x17, 0x10, 0xdb, 0x5e, 0x2c, 0x5e, 0x69, 0x4d, 0x3b, 0xf0, 0x7c, 0xf1, 0x80, 0x28,
	0xae, 0x2e, 0xc9, 0x92, 0xbf, 0x65, 0x96, 0xb4, 0x09, 0x5b, 0xa0, 0x0d, 0xdb, 0x6d, 0xfa, 0x76,
	0x8b, 0x2d, 0x3d, 0xe3, 0x04, 0xa3, 0x69, 0x7b, 0x7e, 0xf6, 0x0b, 0xf0, 0x12, 0x28, 0x55, 0xaf,
	0xaf, 0xc0, 0x01, 0x0e, 0x08, 0xed, 0x2d, 0xbd, 0x1d, 0x04, 0x9c, 0xff, 0x1c, 0xb4, 0xf9, 0x02,
	0x1c, 0xda, 0x01, 0x86, 0x2d, 0xed, 0x49, 0x21, 0x60, 0x86, 0xe9, 0x89, 0xe7, 0x2c, 0xbf, 0xe1,
	0x24, 0x4e, 0x9c, 0x8e, 0x7c, 0x68, 0xa2, 0x15, 0x3d, 0xd9, 0xef, 0xd8, 0x4b, 0x6f, 0x86, 0x3c,
	0xa2, 0xb6, 0x17, 0x72, 0x06, 0xb5, 0x15, 0x86, 0xfc, 0xdd, 0xc1, 0x21, 0x3e, 0xd4, 0x57, 0x18,
	0xea, 0x37, 0x9d, 0x6d, 0x6d, 0x0f, 0xcb, 0xb8, 0xec, 0x33, 0xd8, 0x69, 0x6c, 0x7d, 0xb2, 0x91,
	0x9d, 0x5a, 0xff, 0x1f, 0x00, 0x00, 0xff, 0xff, 0x31, 0x03, 0x4e, 0xbd, 0xfd, 0x1f, 0x00, 0x00,
}
