/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// LocalVolumeSourceApplyConfiguration represents an declarative configuration of the LocalVolumeSource type for use
// with apply.
type LocalVolumeSourceApplyConfiguration struct {
	Path   *string `json:"path,omitempty"`
	FSType *string `json:"fsType,omitempty"`
}

// LocalVolumeSourceApplyConfiguration constructs an declarative configuration of the LocalVolumeSource type for use with
// apply.
func LocalVolumeSource() *LocalVolumeSourceApplyConfiguration {
	return &LocalVolumeSourceApplyConfiguration{}
}

// WithP<PERSON> sets the Path field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Path field is set to the value of the last call.
func (b *LocalVolumeSourceApplyConfiguration) WithPath(value string) *LocalVolumeSourceApplyConfiguration {
	b.Path = &value
	return b
}

// WithFSType sets the FSType field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FSType field is set to the value of the last call.
func (b *LocalVolumeSourceApplyConfiguration) WithFSType(value string) *LocalVolumeSourceApplyConfiguration {
	b.FSType = &value
	return b
}
