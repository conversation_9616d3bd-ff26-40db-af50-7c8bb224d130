{{- if .Values.rbac.create }}
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
    app: {{ template "promtail.name" . }}
    chart: {{ template "promtail.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  name: {{ template "promtail.fullname" . }}-clusterrole
rules:
- apiGroups: [""] # "" indicates the core API group
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "watch", "list"]
{{- end }}
