/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// EnvFromSourceApplyConfiguration represents an declarative configuration of the EnvFromSource type for use
// with apply.
type EnvFromSourceApplyConfiguration struct {
	Prefix       *string                               `json:"prefix,omitempty"`
	ConfigMapRef *ConfigMapEnvSourceApplyConfiguration `json:"configMapRef,omitempty"`
	SecretRef    *SecretEnvSourceApplyConfiguration    `json:"secretRef,omitempty"`
}

// EnvFromSourceApplyConfiguration constructs an declarative configuration of the EnvFromSource type for use with
// apply.
func EnvFromSource() *EnvFromSourceApplyConfiguration {
	return &EnvFromSourceApplyConfiguration{}
}

// WithPrefix sets the Prefix field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Prefix field is set to the value of the last call.
func (b *EnvFromSourceApplyConfiguration) WithPrefix(value string) *EnvFromSourceApplyConfiguration {
	b.Prefix = &value
	return b
}

// WithConfigMapRef sets the ConfigMapRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ConfigMapRef field is set to the value of the last call.
func (b *EnvFromSourceApplyConfiguration) WithConfigMapRef(value *ConfigMapEnvSourceApplyConfiguration) *EnvFromSourceApplyConfiguration {
	b.ConfigMapRef = value
	return b
}

// WithSecretRef sets the SecretRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecretRef field is set to the value of the last call.
func (b *EnvFromSourceApplyConfiguration) WithSecretRef(value *SecretEnvSourceApplyConfiguration) *EnvFromSourceApplyConfiguration {
	b.SecretRef = value
	return b
}
