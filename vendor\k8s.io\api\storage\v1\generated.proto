/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.storage.v1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// CSIDriver captures information about a Container Storage Interface (CSI)
// volume driver deployed on the cluster.
// Kubernetes attach detach controller uses this object to determine whether attach is required.
// Kubelet uses this object to determine whether pod information needs to be passed on mount.
// CSIDriver objects are non-namespaced.
message CSIDriver {
  // Standard object metadata.
  // metadata.Name indicates the name of the CSI driver that this object
  // refers to; it MUST be the same name returned by the CSI GetPluginName()
  // call for that driver.
  // The driver name must be 63 characters or less, beginning and ending with
  // an alphanumeric character ([a-z0-9A-Z]) with dashes (-), dots (.), and
  // alphanumerics between.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the CSI Driver.
  optional CSIDriverSpec spec = 2;
}

// CSIDriverList is a collection of CSIDriver objects.
message CSIDriverList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of CSIDriver
  repeated CSIDriver items = 2;
}

// CSIDriverSpec is the specification of a CSIDriver.
message CSIDriverSpec {
  // attachRequired indicates this CSI volume driver requires an attach
  // operation (because it implements the CSI ControllerPublishVolume()
  // method), and that the Kubernetes attach detach controller should call
  // the attach volume interface which checks the volumeattachment status
  // and waits until the volume is attached before proceeding to mounting.
  // The CSI external-attacher coordinates with CSI volume driver and updates
  // the volumeattachment status when the attach operation is complete.
  // If the CSIDriverRegistry feature gate is enabled and the value is
  // specified to false, the attach operation will be skipped.
  // Otherwise the attach operation will be called.
  //
  // This field is immutable.
  //
  // +optional
  optional bool attachRequired = 1;

  // If set to true, podInfoOnMount indicates this CSI volume driver
  // requires additional pod information (like podName, podUID, etc.) during
  // mount operations.
  // If set to false, pod information will not be passed on mount.
  // Default is false.
  // The CSI driver specifies podInfoOnMount as part of driver deployment.
  // If true, Kubelet will pass pod information as VolumeContext in the CSI
  // NodePublishVolume() calls.
  // The CSI driver is responsible for parsing and validating the information
  // passed in as VolumeContext.
  // The following VolumeConext will be passed if podInfoOnMount is set to true.
  // This list might grow, but the prefix will be used.
  // "csi.storage.k8s.io/pod.name": pod.Name
  // "csi.storage.k8s.io/pod.namespace": pod.Namespace
  // "csi.storage.k8s.io/pod.uid": string(pod.UID)
  // "csi.storage.k8s.io/ephemeral": "true" if the volume is an ephemeral inline volume
  //                                 defined by a CSIVolumeSource, otherwise "false"
  //
  // "csi.storage.k8s.io/ephemeral" is a new feature in Kubernetes 1.16. It is only
  // required for drivers which support both the "Persistent" and "Ephemeral" VolumeLifecycleMode.
  // Other drivers can leave pod info disabled and/or ignore this field.
  // As Kubernetes 1.15 doesn't support this field, drivers can only support one mode when
  // deployed on such a cluster and the deployment determines which mode that is, for example
  // via a command line parameter of the driver.
  //
  // This field is immutable.
  //
  // +optional
  optional bool podInfoOnMount = 2;

  // volumeLifecycleModes defines what kind of volumes this CSI volume driver supports.
  // The default if the list is empty is "Persistent", which is the usage
  // defined by the CSI specification and implemented in Kubernetes via the usual
  // PV/PVC mechanism.
  // The other mode is "Ephemeral". In this mode, volumes are defined inline
  // inside the pod spec with CSIVolumeSource and their lifecycle is tied to
  // the lifecycle of that pod. A driver has to be aware of this
  // because it is only going to get a NodePublishVolume call for such a volume.
  // For more information about implementing this mode, see
  // https://kubernetes-csi.github.io/docs/ephemeral-local-volumes.html
  // A driver can support one or more of these modes and
  // more modes may be added in the future.
  // This field is beta.
  //
  // This field is immutable.
  //
  // +optional
  // +listType=set
  repeated string volumeLifecycleModes = 3;

  // If set to true, storageCapacity indicates that the CSI
  // volume driver wants pod scheduling to consider the storage
  // capacity that the driver deployment will report by creating
  // CSIStorageCapacity objects with capacity information.
  //
  // The check can be enabled immediately when deploying a driver.
  // In that case, provisioning new volumes with late binding
  // will pause until the driver deployment has published
  // some suitable CSIStorageCapacity object.
  //
  // Alternatively, the driver can be deployed with the field
  // unset or false and it can be flipped later when storage
  // capacity information has been published.
  //
  // This field was immutable in Kubernetes <= 1.22 and now is mutable.
  //
  // This is a beta field and only available when the CSIStorageCapacity
  // feature is enabled. The default is false.
  //
  // +optional
  // +featureGate=CSIStorageCapacity
  optional bool storageCapacity = 4;

  // Defines if the underlying volume supports changing ownership and
  // permission of the volume before being mounted.
  // Refer to the specific FSGroupPolicy values for additional details.
  //
  // This field is immutable.
  //
  // Defaults to ReadWriteOnceWithFSType, which will examine each volume
  // to determine if Kubernetes should modify ownership and permissions of the volume.
  // With the default policy the defined fsGroup will only be applied
  // if a fstype is defined and the volume's access mode contains ReadWriteOnce.
  // +optional
  optional string fsGroupPolicy = 5;

  // TokenRequests indicates the CSI driver needs pods' service account
  // tokens it is mounting volume for to do necessary authentication. Kubelet
  // will pass the tokens in VolumeContext in the CSI NodePublishVolume calls.
  // The CSI driver should parse and validate the following VolumeContext:
  // "csi.storage.k8s.io/serviceAccount.tokens": {
  //   "<audience>": {
  //     "token": <token>,
  //     "expirationTimestamp": <expiration timestamp in RFC3339>,
  //   },
  //   ...
  // }
  //
  // Note: Audience in each TokenRequest should be different and at
  // most one token is empty string. To receive a new token after expiry,
  // RequiresRepublish can be used to trigger NodePublishVolume periodically.
  //
  // +optional
  // +listType=atomic
  repeated TokenRequest tokenRequests = 6;

  // RequiresRepublish indicates the CSI driver wants `NodePublishVolume`
  // being periodically called to reflect any possible change in the mounted
  // volume. This field defaults to false.
  //
  // Note: After a successful initial NodePublishVolume call, subsequent calls
  // to NodePublishVolume should only update the contents of the volume. New
  // mount points will not be seen by a running container.
  //
  // +optional
  optional bool requiresRepublish = 7;
}

// CSINode holds information about all CSI drivers installed on a node.
// CSI drivers do not need to create the CSINode object directly. As long as
// they use the node-driver-registrar sidecar container, the kubelet will
// automatically populate the CSINode object for the CSI driver as part of
// kubelet plugin registration.
// CSINode has the same name as a node. If the object is missing, it means either
// there are no CSI Drivers available on the node, or the Kubelet version is low
// enough that it doesn't create this object.
// CSINode has an OwnerReference that points to the corresponding node object.
message CSINode {
  // metadata.name must be the Kubernetes node name.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the specification of CSINode
  optional CSINodeSpec spec = 2;
}

// CSINodeDriver holds information about the specification of one CSI driver installed on a node
message CSINodeDriver {
  // This is the name of the CSI driver that this object refers to.
  // This MUST be the same name returned by the CSI GetPluginName() call for
  // that driver.
  optional string name = 1;

  // nodeID of the node from the driver point of view.
  // This field enables Kubernetes to communicate with storage systems that do
  // not share the same nomenclature for nodes. For example, Kubernetes may
  // refer to a given node as "node1", but the storage system may refer to
  // the same node as "nodeA". When Kubernetes issues a command to the storage
  // system to attach a volume to a specific node, it can use this field to
  // refer to the node name using the ID that the storage system will
  // understand, e.g. "nodeA" instead of "node1". This field is required.
  optional string nodeID = 2;

  // topologyKeys is the list of keys supported by the driver.
  // When a driver is initialized on a cluster, it provides a set of topology
  // keys that it understands (e.g. "company.com/zone", "company.com/region").
  // When a driver is initialized on a node, it provides the same topology keys
  // along with values. Kubelet will expose these topology keys as labels
  // on its own node object.
  // When Kubernetes does topology aware provisioning, it can use this list to
  // determine which labels it should retrieve from the node object and pass
  // back to the driver.
  // It is possible for different nodes to use different topology keys.
  // This can be empty if driver does not support topology.
  // +optional
  repeated string topologyKeys = 3;

  // allocatable represents the volume resources of a node that are available for scheduling.
  // This field is beta.
  // +optional
  optional VolumeNodeResources allocatable = 4;
}

// CSINodeList is a collection of CSINode objects.
message CSINodeList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of CSINode
  repeated CSINode items = 2;
}

// CSINodeSpec holds information about the specification of all CSI drivers installed on a node
message CSINodeSpec {
  // drivers is a list of information of all CSI Drivers existing on a node.
  // If all drivers in the list are uninstalled, this can become empty.
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated CSINodeDriver drivers = 1;
}

// StorageClass describes the parameters for a class of storage for
// which PersistentVolumes can be dynamically provisioned.
//
// StorageClasses are non-namespaced; the name of the storage class
// according to etcd is in ObjectMeta.Name.
message StorageClass {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Provisioner indicates the type of the provisioner.
  optional string provisioner = 2;

  // Parameters holds the parameters for the provisioner that should
  // create volumes of this storage class.
  // +optional
  map<string, string> parameters = 3;

  // Dynamically provisioned PersistentVolumes of this storage class are
  // created with this reclaimPolicy. Defaults to Delete.
  // +optional
  optional string reclaimPolicy = 4;

  // Dynamically provisioned PersistentVolumes of this storage class are
  // created with these mountOptions, e.g. ["ro", "soft"]. Not validated -
  // mount of the PVs will simply fail if one is invalid.
  // +optional
  repeated string mountOptions = 5;

  // AllowVolumeExpansion shows whether the storage class allow volume expand
  // +optional
  optional bool allowVolumeExpansion = 6;

  // VolumeBindingMode indicates how PersistentVolumeClaims should be
  // provisioned and bound.  When unset, VolumeBindingImmediate is used.
  // This field is only honored by servers that enable the VolumeScheduling feature.
  // +optional
  optional string volumeBindingMode = 7;

  // Restrict the node topologies where volumes can be dynamically provisioned.
  // Each volume plugin defines its own supported topology specifications.
  // An empty TopologySelectorTerm list means there is no topology restriction.
  // This field is only honored by servers that enable the VolumeScheduling feature.
  // +optional
  // +listType=atomic
  repeated k8s.io.api.core.v1.TopologySelectorTerm allowedTopologies = 8;
}

// StorageClassList is a collection of storage classes.
message StorageClassList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of StorageClasses
  repeated StorageClass items = 2;
}

// TokenRequest contains parameters of a service account token.
message TokenRequest {
  // Audience is the intended audience of the token in "TokenRequestSpec".
  // It will default to the audiences of kube apiserver.
  optional string audience = 1;

  // ExpirationSeconds is the duration of validity of the token in "TokenRequestSpec".
  // It has the same default value of "ExpirationSeconds" in "TokenRequestSpec".
  //
  // +optional
  optional int64 expirationSeconds = 2;
}

// VolumeAttachment captures the intent to attach or detach the specified volume
// to/from the specified node.
//
// VolumeAttachment objects are non-namespaced.
message VolumeAttachment {
  // Standard object metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired attach/detach volume behavior.
  // Populated by the Kubernetes system.
  optional VolumeAttachmentSpec spec = 2;

  // Status of the VolumeAttachment request.
  // Populated by the entity completing the attach or detach
  // operation, i.e. the external-attacher.
  // +optional
  optional VolumeAttachmentStatus status = 3;
}

// VolumeAttachmentList is a collection of VolumeAttachment objects.
message VolumeAttachmentList {
  // Standard list metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of VolumeAttachments
  repeated VolumeAttachment items = 2;
}

// VolumeAttachmentSource represents a volume that should be attached.
// Right now only PersistenVolumes can be attached via external attacher,
// in future we may allow also inline volumes in pods.
// Exactly one member can be set.
message VolumeAttachmentSource {
  // Name of the persistent volume to attach.
  // +optional
  optional string persistentVolumeName = 1;

  // inlineVolumeSpec contains all the information necessary to attach
  // a persistent volume defined by a pod's inline VolumeSource. This field
  // is populated only for the CSIMigration feature. It contains
  // translated fields from a pod's inline VolumeSource to a
  // PersistentVolumeSpec. This field is beta-level and is only
  // honored by servers that enabled the CSIMigration feature.
  // +optional
  optional k8s.io.api.core.v1.PersistentVolumeSpec inlineVolumeSpec = 2;
}

// VolumeAttachmentSpec is the specification of a VolumeAttachment request.
message VolumeAttachmentSpec {
  // Attacher indicates the name of the volume driver that MUST handle this
  // request. This is the name returned by GetPluginName().
  optional string attacher = 1;

  // Source represents the volume that should be attached.
  optional VolumeAttachmentSource source = 2;

  // The node that the volume should be attached to.
  optional string nodeName = 3;
}

// VolumeAttachmentStatus is the status of a VolumeAttachment request.
message VolumeAttachmentStatus {
  // Indicates the volume is successfully attached.
  // This field must only be set by the entity completing the attach
  // operation, i.e. the external-attacher.
  optional bool attached = 1;

  // Upon successful attach, this field is populated with any
  // information returned by the attach operation that must be passed
  // into subsequent WaitForAttach or Mount calls.
  // This field must only be set by the entity completing the attach
  // operation, i.e. the external-attacher.
  // +optional
  map<string, string> attachmentMetadata = 2;

  // The last error encountered during attach operation, if any.
  // This field must only be set by the entity completing the attach
  // operation, i.e. the external-attacher.
  // +optional
  optional VolumeError attachError = 3;

  // The last error encountered during detach operation, if any.
  // This field must only be set by the entity completing the detach
  // operation, i.e. the external-attacher.
  // +optional
  optional VolumeError detachError = 4;
}

// VolumeError captures an error encountered during a volume operation.
message VolumeError {
  // Time the error was encountered.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time time = 1;

  // String detailing the error encountered during Attach or Detach operation.
  // This string may be logged, so it should not contain sensitive
  // information.
  // +optional
  optional string message = 2;
}

// VolumeNodeResources is a set of resource limits for scheduling of volumes.
message VolumeNodeResources {
  // Maximum number of unique volumes managed by the CSI driver that can be used on a node.
  // A volume that is both attached and mounted on a node is considered to be used once, not twice.
  // The same rule applies for a unique volume that is shared among multiple pods on the same node.
  // If this field is not specified, then the supported number of volumes on this node is unbounded.
  // +optional
  optional int32 count = 1;
}

