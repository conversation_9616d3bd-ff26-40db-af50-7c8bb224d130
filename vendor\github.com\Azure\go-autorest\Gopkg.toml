# Gopkg.toml example
#
# Refer to https://golang.github.io/dep/docs/Gopkg.toml.html
# for detailed Gopkg.toml documentation.
#
# required = ["github.com/user/thing/cmd/thing"]
# ignored = ["github.com/user/project/pkgX", "bitbucket.org/user/project/pkgA/pkgY"]
#
# [[constraint]]
#   name = "github.com/user/project"
#   version = "1.0.0"
#
# [[constraint]]
#   name = "github.com/user/project2"
#   branch = "dev"
#   source = "github.com/myfork/project2"
#
# [[override]]
#   name = "github.com/x/y"
#   version = "2.4.0"
#
# [prune]
#   non-go = false
#   go-tests = true
#   unused-packages = true

required = ["golang.org/x/lint/golint"]

[prune]
  go-tests = true
  unused-packages = true

[[constraint]]
  name = "contrib.go.opencensus.io/exporter/ocagent"
  version = "0.6.0"

[[constraint]]
  name = "github.com/dgrijalva/jwt-go"
  version = "3.2.0"

[[constraint]]
  name = "github.com/dimchansky/utfbom"
  version = "1.1.0"

[[constraint]]
  name = "github.com/mitchellh/go-homedir"
  version = "1.1.0"

[[constraint]]
  name = "github.com/stretchr/testify"
  version = "1.3.0"

[[constraint]]
  name = "go.opencensus.io"
  version = "0.22.0"

[[constraint]]
  branch = "master"
  name = "golang.org/x/crypto"
