# This file contains all available configuration options
# with their default values.

# options for analysis running
run:
  # default concurrency is a available CPU number
  concurrency: 16

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 5m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  # list of build tags, all linters use it. Default is empty list.
  build-tags: []

  # which dirs to skip: they won't be analyzed;
  # can use regexp here: generated.*, regexp is applied on full path;
  # default value is empty list, but next dirs are always skipped independently
  # from this option's value:
  #   	vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  skip-dirs:
    - win_eventlog$
  # which files to skip: they will be analyzed, but issues from them
  # won't be reported. Default value is empty list, but there is
  # no need to include all autogenerated files, we confidently recognize
  # autogenerated files. If it's not please let us know.
  skip-files:
  - .*.pb.go
  - .*.y.go
  - .*.rl.go
# output configuration options
output:
  # colored-line-number|line-number|json|tab|checkstyle, default is "colored-line-number"
  format: colored-line-number

  # print lines of code with issue, default is true
  print-issued-lines: true

  # print linter name in the end of issue text, default is true
  print-linter-name: true

linters-settings:
  goimports:
    local-prefixes: github.com/grafana/loki/pkg,github.com/grafana/loki/tools

  depguard:
    list-type: blacklist
    include-go-root: true
    packages-with-error-message:
      - github.com/go-kit/kit/log: "Use github.com/go-kit/log instead of github.com/go-kit/kit/log"

linters:
  enable:
    - deadcode
    - errcheck
    - goconst
    - gofmt
    - goimports
    - revive
    - gosimple
    - ineffassign
    - staticcheck
    - misspell
    - structcheck
    - unconvert
    - unparam
    - varcheck
    - govet
    - unused
    - typecheck
    - depguard
    - exportloopref

issues:
  exclude:
    - Error return value of .*log\.Logger\)\.Log\x60 is not checked
    - Error return value of .*.Log.* is not checked
    - Error return value of `` is not checked
