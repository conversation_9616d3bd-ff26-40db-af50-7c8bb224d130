/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.apimachinery.pkg.apis.meta.v1;

import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// APIGroup contains the name, the supported versions, and the preferred version
// of a group.
message APIGroup {
  // name is the name of the group.
  optional string name = 1;

  // versions are the versions supported in this group.
  repeated GroupVersionForDiscovery versions = 2;

  // preferredVersion is the version preferred by the API server, which
  // probably is the storage version.
  // +optional
  optional GroupVersionForDiscovery preferredVersion = 3;

  // a map of client CIDR to server address that is serving this group.
  // This is to help clients reach servers in the most network-efficient way possible.
  // Clients can use the appropriate server address as per the CIDR that they match.
  // In case of multiple matches, clients should use the longest matching CIDR.
  // The server returns only those CIDRs that it thinks that the client can match.
  // For example: the master will return an internal IP CIDR only, if the client reaches the server using an internal IP.
  // Server looks at X-Forwarded-For header or X-Real-Ip header or request.RemoteAddr (in that order) to get the client IP.
  // +optional
  repeated ServerAddressByClientCIDR serverAddressByClientCIDRs = 4;
}

// APIGroupList is a list of APIGroup, to allow clients to discover the API at
// /apis.
message APIGroupList {
  // groups is a list of APIGroup.
  repeated APIGroup groups = 1;
}

// APIResource specifies the name of a resource and whether it is namespaced.
message APIResource {
  // name is the plural name of the resource.
  optional string name = 1;

  // singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely.
  // The singularName is more correct for reporting status on a single item and both singular and plural are allowed
  // from the kubectl CLI interface.
  optional string singularName = 6;

  // namespaced indicates if a resource is namespaced or not.
  optional bool namespaced = 2;

  // group is the preferred group of the resource.  Empty implies the group of the containing resource list.
  // For subresources, this may have a different value, for example: Scale".
  optional string group = 8;

  // version is the preferred version of the resource.  Empty implies the version of the containing resource list
  // For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)".
  optional string version = 9;

  // kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')
  optional string kind = 3;

  // verbs is a list of supported kube verbs (this includes get, list, watch, create,
  // update, patch, delete, deletecollection, and proxy)
  optional Verbs verbs = 4;

  // shortNames is a list of suggested short names of the resource.
  repeated string shortNames = 5;

  // categories is a list of the grouped resources this resource belongs to (e.g. 'all')
  repeated string categories = 7;

  // The hash value of the storage version, the version this resource is
  // converted to when written to the data store. Value must be treated
  // as opaque by clients. Only equality comparison on the value is valid.
  // This is an alpha feature and may change or be removed in the future.
  // The field is populated by the apiserver only if the
  // StorageVersionHash feature gate is enabled.
  // This field will remain optional even if it graduates.
  // +optional
  optional string storageVersionHash = 10;
}

// APIResourceList is a list of APIResource, it is used to expose the name of the
// resources supported in a specific group and version, and if the resource
// is namespaced.
message APIResourceList {
  // groupVersion is the group and version this APIResourceList is for.
  optional string groupVersion = 1;

  // resources contains the name of the resources and if they are namespaced.
  repeated APIResource resources = 2;
}

// APIVersions lists the versions that are available, to allow clients to
// discover the API at /api, which is the root path of the legacy v1 API.
//
// +protobuf.options.(gogoproto.goproto_stringer)=false
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
message APIVersions {
  // versions are the api versions that are available.
  repeated string versions = 1;

  // a map of client CIDR to server address that is serving this group.
  // This is to help clients reach servers in the most network-efficient way possible.
  // Clients can use the appropriate server address as per the CIDR that they match.
  // In case of multiple matches, clients should use the longest matching CIDR.
  // The server returns only those CIDRs that it thinks that the client can match.
  // For example: the master will return an internal IP CIDR only, if the client reaches the server using an internal IP.
  // Server looks at X-Forwarded-For header or X-Real-Ip header or request.RemoteAddr (in that order) to get the client IP.
  repeated ServerAddressByClientCIDR serverAddressByClientCIDRs = 2;
}

// ApplyOptions may be provided when applying an API object.
// FieldManager is required for apply requests.
// ApplyOptions is equivalent to PatchOptions. It is provided as a convenience with documentation
// that speaks specifically to how the options fields relate to apply.
message ApplyOptions {
  // When present, indicates that modifications should not be
  // persisted. An invalid or unrecognized dryRun directive will
  // result in an error response and no further processing of the
  // request. Valid values are:
  // - All: all dry run stages will be processed
  // +optional
  repeated string dryRun = 1;

  // Force is going to "force" Apply requests. It means user will
  // re-acquire conflicting fields owned by other people.
  optional bool force = 2;

  // fieldManager is a name associated with the actor or entity
  // that is making these changes. The value must be less than or
  // 128 characters long, and only contain printable characters,
  // as defined by https://golang.org/pkg/unicode/#IsPrint. This
  // field is required.
  optional string fieldManager = 3;
}

// Condition contains details for one aspect of the current state of this API Resource.
// ---
// This struct is intended for direct use as an array at the field path .status.conditions.  For example,
// type FooStatus struct{
//     // Represents the observations of a foo's current state.
//     // Known .status.conditions.type are: "Available", "Progressing", and "Degraded"
//     // +patchMergeKey=type
//     // +patchStrategy=merge
//     // +listType=map
//     // +listMapKey=type
//     Conditions []metav1.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,1,rep,name=conditions"`
//
//     // other fields
// }
message Condition {
  // type of condition in CamelCase or in foo.example.com/CamelCase.
  // ---
  // Many .condition.type values are consistent across resources like Available, but because arbitrary conditions can be
  // useful (see .node.status.conditions), the ability to deconflict is important.
  // The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
  // +required
  // +kubebuilder:validation:Required
  // +kubebuilder:validation:Pattern=`^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$`
  // +kubebuilder:validation:MaxLength=316
  optional string type = 1;

  // status of the condition, one of True, False, Unknown.
  // +required
  // +kubebuilder:validation:Required
  // +kubebuilder:validation:Enum=True;False;Unknown
  optional string status = 2;

  // observedGeneration represents the .metadata.generation that the condition was set based upon.
  // For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
  // with respect to the current state of the instance.
  // +optional
  // +kubebuilder:validation:Minimum=0
  optional int64 observedGeneration = 3;

  // lastTransitionTime is the last time the condition transitioned from one status to another.
  // This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
  // +required
  // +kubebuilder:validation:Required
  // +kubebuilder:validation:Type=string
  // +kubebuilder:validation:Format=date-time
  optional Time lastTransitionTime = 4;

  // reason contains a programmatic identifier indicating the reason for the condition's last transition.
  // Producers of specific condition types may define expected values and meanings for this field,
  // and whether the values are considered a guaranteed API.
  // The value should be a CamelCase string.
  // This field may not be empty.
  // +required
  // +kubebuilder:validation:Required
  // +kubebuilder:validation:MaxLength=1024
  // +kubebuilder:validation:MinLength=1
  // +kubebuilder:validation:Pattern=`^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$`
  optional string reason = 5;

  // message is a human readable message indicating details about the transition.
  // This may be an empty string.
  // +required
  // +kubebuilder:validation:Required
  // +kubebuilder:validation:MaxLength=32768
  optional string message = 6;
}

// CreateOptions may be provided when creating an API object.
message CreateOptions {
  // When present, indicates that modifications should not be
  // persisted. An invalid or unrecognized dryRun directive will
  // result in an error response and no further processing of the
  // request. Valid values are:
  // - All: all dry run stages will be processed
  // +optional
  repeated string dryRun = 1;

  // fieldManager is a name associated with the actor or entity
  // that is making these changes. The value must be less than or
  // 128 characters long, and only contain printable characters,
  // as defined by https://golang.org/pkg/unicode/#IsPrint.
  // +optional
  optional string fieldManager = 3;

  // fieldValidation determines how the server should respond to
  // unknown/duplicate fields in the object in the request.
  // Introduced as alpha in 1.23, older servers or servers with the
  // `ServerSideFieldValidation` feature disabled will discard valid values
  // specified in  this param and not perform any server side field validation.
  // Valid values are:
  // - Ignore: ignores unknown/duplicate fields.
  // - Warn: responds with a warning for each
  // unknown/duplicate field, but successfully serves the request.
  // - Strict: fails the request on unknown/duplicate fields.
  // +optional
  optional string fieldValidation = 4;
}

// DeleteOptions may be provided when deleting an API object.
message DeleteOptions {
  // The duration in seconds before the object should be deleted. Value must be non-negative integer.
  // The value zero indicates delete immediately. If this value is nil, the default grace period for the
  // specified type will be used.
  // Defaults to a per object value if not specified. zero means delete immediately.
  // +optional
  optional int64 gracePeriodSeconds = 1;

  // Must be fulfilled before a deletion is carried out. If not possible, a 409 Conflict status will be
  // returned.
  // +k8s:conversion-gen=false
  // +optional
  optional Preconditions preconditions = 2;

  // Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7.
  // Should the dependent objects be orphaned. If true/false, the "orphan"
  // finalizer will be added to/removed from the object's finalizers list.
  // Either this field or PropagationPolicy may be set, but not both.
  // +optional
  optional bool orphanDependents = 3;

  // Whether and how garbage collection will be performed.
  // Either this field or OrphanDependents may be set, but not both.
  // The default policy is decided by the existing finalizer set in the
  // metadata.finalizers and the resource-specific default policy.
  // Acceptable values are: 'Orphan' - orphan the dependents; 'Background' -
  // allow the garbage collector to delete the dependents in the background;
  // 'Foreground' - a cascading policy that deletes all dependents in the
  // foreground.
  // +optional
  optional string propagationPolicy = 4;

  // When present, indicates that modifications should not be
  // persisted. An invalid or unrecognized dryRun directive will
  // result in an error response and no further processing of the
  // request. Valid values are:
  // - All: all dry run stages will be processed
  // +optional
  repeated string dryRun = 5;
}

// Duration is a wrapper around time.Duration which supports correct
// marshaling to YAML and JSON. In particular, it marshals into strings, which
// can be used as map keys in json.
message Duration {
  optional int64 duration = 1;
}

// FieldsV1 stores a set of fields in a data structure like a Trie, in JSON format.
//
// Each key is either a '.' representing the field itself, and will always map to an empty set,
// or a string representing a sub-field or item. The string will follow one of these four formats:
// 'f:<name>', where <name> is the name of a field in a struct, or key in a map
// 'v:<value>', where <value> is the exact json formatted value of a list item
// 'i:<index>', where <index> is position of a item in a list
// 'k:<keys>', where <keys> is a map of  a list item's key fields to their unique values
// If a key maps to an empty Fields value, the field that key represents is part of the set.
//
// The exact format is defined in sigs.k8s.io/structured-merge-diff
// +protobuf.options.(gogoproto.goproto_stringer)=false
message FieldsV1 {
  // Raw is the underlying serialization of this object.
  optional bytes Raw = 1;
}

// GetOptions is the standard query options to the standard REST get call.
message GetOptions {
  // resourceVersion sets a constraint on what resource versions a request may be served from.
  // See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for
  // details.
  //
  // Defaults to unset
  // +optional
  optional string resourceVersion = 1;
}

// GroupKind specifies a Group and a Kind, but does not force a version.  This is useful for identifying
// concepts during lookup stages without having partially valid types
//
// +protobuf.options.(gogoproto.goproto_stringer)=false
message GroupKind {
  optional string group = 1;

  optional string kind = 2;
}

// GroupResource specifies a Group and a Resource, but does not force a version.  This is useful for identifying
// concepts during lookup stages without having partially valid types
//
// +protobuf.options.(gogoproto.goproto_stringer)=false
message GroupResource {
  optional string group = 1;

  optional string resource = 2;
}

// GroupVersion contains the "group" and the "version", which uniquely identifies the API.
//
// +protobuf.options.(gogoproto.goproto_stringer)=false
message GroupVersion {
  optional string group = 1;

  optional string version = 2;
}

// GroupVersion contains the "group/version" and "version" string of a version.
// It is made a struct to keep extensibility.
message GroupVersionForDiscovery {
  // groupVersion specifies the API group and version in the form "group/version"
  optional string groupVersion = 1;

  // version specifies the version in the form of "version". This is to save
  // the clients the trouble of splitting the GroupVersion.
  optional string version = 2;
}

// GroupVersionKind unambiguously identifies a kind.  It doesn't anonymously include GroupVersion
// to avoid automatic coercion.  It doesn't use a GroupVersion to avoid custom marshalling
//
// +protobuf.options.(gogoproto.goproto_stringer)=false
message GroupVersionKind {
  optional string group = 1;

  optional string version = 2;

  optional string kind = 3;
}

// GroupVersionResource unambiguously identifies a resource.  It doesn't anonymously include GroupVersion
// to avoid automatic coercion.  It doesn't use a GroupVersion to avoid custom marshalling
//
// +protobuf.options.(gogoproto.goproto_stringer)=false
message GroupVersionResource {
  optional string group = 1;

  optional string version = 2;

  optional string resource = 3;
}

// A label selector is a label query over a set of resources. The result of matchLabels and
// matchExpressions are ANDed. An empty label selector matches all objects. A null
// label selector matches no objects.
// +structType=atomic
message LabelSelector {
  // matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
  // map is equivalent to an element of matchExpressions, whose key field is "key", the
  // operator is "In", and the values array contains only "value". The requirements are ANDed.
  // +optional
  map<string, string> matchLabels = 1;

  // matchExpressions is a list of label selector requirements. The requirements are ANDed.
  // +optional
  repeated LabelSelectorRequirement matchExpressions = 2;
}

// A label selector requirement is a selector that contains values, a key, and an operator that
// relates the key and values.
message LabelSelectorRequirement {
  // key is the label key that the selector applies to.
  // +patchMergeKey=key
  // +patchStrategy=merge
  optional string key = 1;

  // operator represents a key's relationship to a set of values.
  // Valid operators are In, NotIn, Exists and DoesNotExist.
  optional string operator = 2;

  // values is an array of string values. If the operator is In or NotIn,
  // the values array must be non-empty. If the operator is Exists or DoesNotExist,
  // the values array must be empty. This array is replaced during a strategic
  // merge patch.
  // +optional
  repeated string values = 3;
}

// List holds a list of objects, which may not be known by the server.
message List {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional ListMeta metadata = 1;

  // List of objects
  repeated k8s.io.apimachinery.pkg.runtime.RawExtension items = 2;
}

// ListMeta describes metadata that synthetic resources must have, including lists and
// various status objects. A resource may have only one of {ObjectMeta, ListMeta}.
message ListMeta {
  // selfLink is a URL representing this object.
  // Populated by the system.
  // Read-only.
  //
  // DEPRECATED
  // Kubernetes will stop propagating this field in 1.20 release and the field is planned
  // to be removed in 1.21 release.
  // +optional
  optional string selfLink = 1;

  // String that identifies the server's internal version of this object that
  // can be used by clients to determine when objects have changed.
  // Value must be treated as opaque by clients and passed unmodified back to the server.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
  // +optional
  optional string resourceVersion = 2;

  // continue may be set if the user set a limit on the number of items returned, and indicates that
  // the server has more data available. The value is opaque and may be used to issue another request
  // to the endpoint that served this list to retrieve the next set of available objects. Continuing a
  // consistent list may not be possible if the server configuration has changed or more than a few
  // minutes have passed. The resourceVersion field returned when using this continue value will be
  // identical to the value in the first response, unless you have received this token from an error
  // message.
  optional string continue = 3;

  // remainingItemCount is the number of subsequent items in the list which are not included in this
  // list response. If the list request contained label or field selectors, then the number of
  // remaining items is unknown and the field will be left unset and omitted during serialization.
  // If the list is complete (either because it is not chunking or because this is the last chunk),
  // then there are no more remaining items and this field will be left unset and omitted during
  // serialization.
  // Servers older than v1.15 do not set this field.
  // The intended use of the remainingItemCount is *estimating* the size of a collection. Clients
  // should not rely on the remainingItemCount to be set or to be exact.
  // +optional
  optional int64 remainingItemCount = 4;
}

// ListOptions is the query options to a standard REST list call.
message ListOptions {
  // A selector to restrict the list of returned objects by their labels.
  // Defaults to everything.
  // +optional
  optional string labelSelector = 1;

  // A selector to restrict the list of returned objects by their fields.
  // Defaults to everything.
  // +optional
  optional string fieldSelector = 2;

  // Watch for changes to the described resources and return them as a stream of
  // add, update, and remove notifications. Specify resourceVersion.
  // +optional
  optional bool watch = 3;

  // allowWatchBookmarks requests watch events with type "BOOKMARK".
  // Servers that do not implement bookmarks may ignore this flag and
  // bookmarks are sent at the server's discretion. Clients should not
  // assume bookmarks are returned at any specific interval, nor may they
  // assume the server will send any BOOKMARK event during a session.
  // If this is not a watch, this field is ignored.
  // +optional
  optional bool allowWatchBookmarks = 9;

  // resourceVersion sets a constraint on what resource versions a request may be served from.
  // See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for
  // details.
  //
  // Defaults to unset
  // +optional
  optional string resourceVersion = 4;

  // resourceVersionMatch determines how resourceVersion is applied to list calls.
  // It is highly recommended that resourceVersionMatch be set for list calls where
  // resourceVersion is set
  // See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for
  // details.
  //
  // Defaults to unset
  // +optional
  optional string resourceVersionMatch = 10;

  // Timeout for the list/watch call.
  // This limits the duration of the call, regardless of any activity or inactivity.
  // +optional
  optional int64 timeoutSeconds = 5;

  // limit is a maximum number of responses to return for a list call. If more items exist, the
  // server will set the `continue` field on the list metadata to a value that can be used with the
  // same initial query to retrieve the next set of results. Setting a limit may return fewer than
  // the requested amount of items (up to zero items) in the event all requested objects are
  // filtered out and clients should only use the presence of the continue field to determine whether
  // more results are available. Servers may choose not to support the limit argument and will return
  // all of the available results. If limit is specified and the continue field is empty, clients may
  // assume that no more results are available. This field is not supported if watch is true.
  //
  // The server guarantees that the objects returned when using continue will be identical to issuing
  // a single list call without a limit - that is, no objects created, modified, or deleted after the
  // first request is issued will be included in any subsequent continued requests. This is sometimes
  // referred to as a consistent snapshot, and ensures that a client that is using limit to receive
  // smaller chunks of a very large result can ensure they see all possible objects. If objects are
  // updated during a chunked list the version of the object that was present at the time the first list
  // result was calculated is returned.
  optional int64 limit = 7;

  // The continue option should be set when retrieving more results from the server. Since this value is
  // server defined, clients may only use the continue value from a previous query result with identical
  // query parameters (except for the value of continue) and the server may reject a continue value it
  // does not recognize. If the specified continue value is no longer valid whether due to expiration
  // (generally five to fifteen minutes) or a configuration change on the server, the server will
  // respond with a 410 ResourceExpired error together with a continue token. If the client needs a
  // consistent list, it must restart their list without the continue field. Otherwise, the client may
  // send another list request with the token received with the 410 error, the server will respond with
  // a list starting from the next key, but from the latest snapshot, which is inconsistent from the
  // previous list results - objects that are created, modified, or deleted after the first list request
  // will be included in the response, as long as their keys are after the "next key".
  //
  // This field is not supported when watch is true. Clients may start a watch from the last
  // resourceVersion value returned by the server and not miss any modifications.
  optional string continue = 8;
}

// ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource
// that the fieldset applies to.
message ManagedFieldsEntry {
  // Manager is an identifier of the workflow managing these fields.
  optional string manager = 1;

  // Operation is the type of operation which lead to this ManagedFieldsEntry being created.
  // The only valid values for this field are 'Apply' and 'Update'.
  optional string operation = 2;

  // APIVersion defines the version of this resource that this field set
  // applies to. The format is "group/version" just like the top-level
  // APIVersion field. It is necessary to track the version of a field
  // set because it cannot be automatically converted.
  optional string apiVersion = 3;

  // Time is timestamp of when these fields were set. It should always be empty if Operation is 'Apply'
  // +optional
  optional Time time = 4;

  // FieldsType is the discriminator for the different fields format and version.
  // There is currently only one possible value: "FieldsV1"
  optional string fieldsType = 6;

  // FieldsV1 holds the first JSON version format as described in the "FieldsV1" type.
  // +optional
  optional FieldsV1 fieldsV1 = 7;

  // Subresource is the name of the subresource used to update that object, or
  // empty string if the object was updated through the main resource. The
  // value of this field is used to distinguish between managers, even if they
  // share the same name. For example, a status update will be distinct from a
  // regular update using the same manager name.
  // Note that the APIVersion field is not related to the Subresource field and
  // it always corresponds to the version of the main resource.
  optional string subresource = 8;
}

// MicroTime is version of Time with microsecond level precision.
//
// +protobuf.options.marshal=false
// +protobuf.as=Timestamp
// +protobuf.options.(gogoproto.goproto_stringer)=false
message MicroTime {
  // Represents seconds of UTC time since Unix epoch
  // 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
  // 9999-12-31T23:59:59Z inclusive.
  optional int64 seconds = 1;

  // Non-negative fractions of a second at nanosecond resolution. Negative
  // second values with fractions must still have non-negative nanos values
  // that count forward in time. Must be from 0 to 999,999,999
  // inclusive. This field may be limited in precision depending on context.
  optional int32 nanos = 2;
}

// ObjectMeta is metadata that all persisted resources must have, which includes all objects
// users must create.
message ObjectMeta {
  // Name must be unique within a namespace. Is required when creating resources, although
  // some resources may allow a client to request the generation of an appropriate name
  // automatically. Name is primarily intended for creation idempotence and configuration
  // definition.
  // Cannot be updated.
  // More info: http://kubernetes.io/docs/user-guide/identifiers#names
  // +optional
  optional string name = 1;

  // GenerateName is an optional prefix, used by the server, to generate a unique
  // name ONLY IF the Name field has not been provided.
  // If this field is used, the name returned to the client will be different
  // than the name passed. This value will also be combined with a unique suffix.
  // The provided value has the same validation rules as the Name field,
  // and may be truncated by the length of the suffix required to make the value
  // unique on the server.
  //
  // If this field is specified and the generated name exists, the server will
  // NOT return a 409 - instead, it will either return 201 Created or 500 with Reason
  // ServerTimeout indicating a unique name could not be found in the time allotted, and the client
  // should retry (optionally after the time indicated in the Retry-After header).
  //
  // Applied only if Name is not specified.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency
  // +optional
  optional string generateName = 2;

  // Namespace defines the space within which each name must be unique. An empty namespace is
  // equivalent to the "default" namespace, but "default" is the canonical representation.
  // Not all objects are required to be scoped to a namespace - the value of this field for
  // those objects will be empty.
  //
  // Must be a DNS_LABEL.
  // Cannot be updated.
  // More info: http://kubernetes.io/docs/user-guide/namespaces
  // +optional
  optional string namespace = 3;

  // SelfLink is a URL representing this object.
  // Populated by the system.
  // Read-only.
  //
  // DEPRECATED
  // Kubernetes will stop propagating this field in 1.20 release and the field is planned
  // to be removed in 1.21 release.
  // +optional
  optional string selfLink = 4;

  // UID is the unique in time and space value for this object. It is typically generated by
  // the server on successful creation of a resource and is not allowed to change on PUT
  // operations.
  //
  // Populated by the system.
  // Read-only.
  // More info: http://kubernetes.io/docs/user-guide/identifiers#uids
  // +optional
  optional string uid = 5;

  // An opaque value that represents the internal version of this object that can
  // be used by clients to determine when objects have changed. May be used for optimistic
  // concurrency, change detection, and the watch operation on a resource or set of resources.
  // Clients must treat these values as opaque and passed unmodified back to the server.
  // They may only be valid for a particular resource or set of resources.
  //
  // Populated by the system.
  // Read-only.
  // Value must be treated as opaque by clients and .
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
  // +optional
  optional string resourceVersion = 6;

  // A sequence number representing a specific generation of the desired state.
  // Populated by the system. Read-only.
  // +optional
  optional int64 generation = 7;

  // CreationTimestamp is a timestamp representing the server time when this object was
  // created. It is not guaranteed to be set in happens-before order across separate operations.
  // Clients may not set this value. It is represented in RFC3339 form and is in UTC.
  //
  // Populated by the system.
  // Read-only.
  // Null for lists.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional Time creationTimestamp = 8;

  // DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This
  // field is set by the server when a graceful deletion is requested by the user, and is not
  // directly settable by a client. The resource is expected to be deleted (no longer visible
  // from resource lists, and not reachable by name) after the time in this field, once the
  // finalizers list is empty. As long as the finalizers list contains items, deletion is blocked.
  // Once the deletionTimestamp is set, this value may not be unset or be set further into the
  // future, although it may be shortened or the resource may be deleted prior to this time.
  // For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react
  // by sending a graceful termination signal to the containers in the pod. After that 30 seconds,
  // the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup,
  // remove the pod from the API. In the presence of network partitions, this object may still
  // exist after this timestamp, until an administrator or automated process can determine the
  // resource is fully terminated.
  // If not set, graceful deletion of the object has not been requested.
  //
  // Populated by the system when a graceful deletion is requested.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional Time deletionTimestamp = 9;

  // Number of seconds allowed for this object to gracefully terminate before
  // it will be removed from the system. Only set when deletionTimestamp is also set.
  // May only be shortened.
  // Read-only.
  // +optional
  optional int64 deletionGracePeriodSeconds = 10;

  // Map of string keys and values that can be used to organize and categorize
  // (scope and select) objects. May match selectors of replication controllers
  // and services.
  // More info: http://kubernetes.io/docs/user-guide/labels
  // +optional
  map<string, string> labels = 11;

  // Annotations is an unstructured key value map stored with a resource that may be
  // set by external tools to store and retrieve arbitrary metadata. They are not
  // queryable and should be preserved when modifying objects.
  // More info: http://kubernetes.io/docs/user-guide/annotations
  // +optional
  map<string, string> annotations = 12;

  // List of objects depended by this object. If ALL objects in the list have
  // been deleted, this object will be garbage collected. If this object is managed by a controller,
  // then an entry in this list will point to this controller, with the controller field set to true.
  // There cannot be more than one managing controller.
  // +optional
  // +patchMergeKey=uid
  // +patchStrategy=merge
  repeated OwnerReference ownerReferences = 13;

  // Must be empty before the object is deleted from the registry. Each entry
  // is an identifier for the responsible component that will remove the entry
  // from the list. If the deletionTimestamp of the object is non-nil, entries
  // in this list can only be removed.
  // Finalizers may be processed and removed in any order.  Order is NOT enforced
  // because it introduces significant risk of stuck finalizers.
  // finalizers is a shared field, any actor with permission can reorder it.
  // If the finalizer list is processed in order, then this can lead to a situation
  // in which the component responsible for the first finalizer in the list is
  // waiting for a signal (field value, external system, or other) produced by a
  // component responsible for a finalizer later in the list, resulting in a deadlock.
  // Without enforced ordering finalizers are free to order amongst themselves and
  // are not vulnerable to ordering changes in the list.
  // +optional
  // +patchStrategy=merge
  repeated string finalizers = 14;

  // The name of the cluster which the object belongs to.
  // This is used to distinguish resources with same name and namespace in different clusters.
  // This field is not set anywhere right now and apiserver is going to ignore it if set in create or update request.
  // +optional
  optional string clusterName = 15;

  // ManagedFields maps workflow-id and version to the set of fields
  // that are managed by that workflow. This is mostly for internal
  // housekeeping, and users typically shouldn't need to set or
  // understand this field. A workflow can be the user's name, a
  // controller's name, or the name of a specific apply path like
  // "ci-cd". The set of fields is always in the version that the
  // workflow used when modifying the object.
  //
  // +optional
  repeated ManagedFieldsEntry managedFields = 17;
}

// OwnerReference contains enough information to let you identify an owning
// object. An owning object must be in the same namespace as the dependent, or
// be cluster-scoped, so there is no namespace field.
// +structType=atomic
message OwnerReference {
  // API version of the referent.
  optional string apiVersion = 5;

  // Kind of the referent.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  optional string kind = 1;

  // Name of the referent.
  // More info: http://kubernetes.io/docs/user-guide/identifiers#names
  optional string name = 3;

  // UID of the referent.
  // More info: http://kubernetes.io/docs/user-guide/identifiers#uids
  optional string uid = 4;

  // If true, this reference points to the managing controller.
  // +optional
  optional bool controller = 6;

  // If true, AND if the owner has the "foregroundDeletion" finalizer, then
  // the owner cannot be deleted from the key-value store until this
  // reference is removed.
  // Defaults to false.
  // To set this field, a user needs "delete" permission of the owner,
  // otherwise 422 (Unprocessable Entity) will be returned.
  // +optional
  optional bool blockOwnerDeletion = 7;
}

// PartialObjectMetadata is a generic representation of any object with ObjectMeta. It allows clients
// to get access to a particular ObjectMeta schema without knowing the details of the version.
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
message PartialObjectMetadata {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional ObjectMeta metadata = 1;
}

// PartialObjectMetadataList contains a list of objects containing only their metadata
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
message PartialObjectMetadataList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional ListMeta metadata = 1;

  // items contains each of the included items.
  repeated PartialObjectMetadata items = 2;
}

// Patch is provided to give a concrete name and type to the Kubernetes PATCH request body.
message Patch {
}

// PatchOptions may be provided when patching an API object.
// PatchOptions is meant to be a superset of UpdateOptions.
message PatchOptions {
  // When present, indicates that modifications should not be
  // persisted. An invalid or unrecognized dryRun directive will
  // result in an error response and no further processing of the
  // request. Valid values are:
  // - All: all dry run stages will be processed
  // +optional
  repeated string dryRun = 1;

  // Force is going to "force" Apply requests. It means user will
  // re-acquire conflicting fields owned by other people. Force
  // flag must be unset for non-apply patch requests.
  // +optional
  optional bool force = 2;

  // fieldManager is a name associated with the actor or entity
  // that is making these changes. The value must be less than or
  // 128 characters long, and only contain printable characters,
  // as defined by https://golang.org/pkg/unicode/#IsPrint. This
  // field is required for apply requests
  // (application/apply-patch) but optional for non-apply patch
  // types (JsonPatch, MergePatch, StrategicMergePatch).
  // +optional
  optional string fieldManager = 3;

  // fieldValidation determines how the server should respond to
  // unknown/duplicate fields in the object in the request.
  // Introduced as alpha in 1.23, older servers or servers with the
  // `ServerSideFieldValidation` feature disabled will discard valid values
  // specified in  this param and not perform any server side field validation.
  // Valid values are:
  // - Ignore: ignores unknown/duplicate fields.
  // - Warn: responds with a warning for each
  // unknown/duplicate field, but successfully serves the request.
  // - Strict: fails the request on unknown/duplicate fields.
  // +optional
  optional string fieldValidation = 4;
}

// Preconditions must be fulfilled before an operation (update, delete, etc.) is carried out.
message Preconditions {
  // Specifies the target UID.
  // +optional
  optional string uid = 1;

  // Specifies the target ResourceVersion
  // +optional
  optional string resourceVersion = 2;
}

// RootPaths lists the paths available at root.
// For example: "/healthz", "/apis".
message RootPaths {
  // paths are the paths available at root.
  repeated string paths = 1;
}

// ServerAddressByClientCIDR helps the client to determine the server address that they should use, depending on the clientCIDR that they match.
message ServerAddressByClientCIDR {
  // The CIDR with which clients can match their IP to figure out the server address that they should use.
  optional string clientCIDR = 1;

  // Address of this server, suitable for a client that matches the above CIDR.
  // This can be a hostname, hostname:port, IP or IP:port.
  optional string serverAddress = 2;
}

// Status is a return value for calls that don't return other objects.
message Status {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional ListMeta metadata = 1;

  // Status of the operation.
  // One of: "Success" or "Failure".
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional string status = 2;

  // A human-readable description of the status of this operation.
  // +optional
  optional string message = 3;

  // A machine-readable description of why this operation is in the
  // "Failure" status. If this value is empty there
  // is no information available. A Reason clarifies an HTTP status
  // code but does not override it.
  // +optional
  optional string reason = 4;

  // Extended data associated with the reason.  Each reason may define its
  // own extended details. This field is optional and the data returned
  // is not guaranteed to conform to any schema except that defined by
  // the reason type.
  // +optional
  optional StatusDetails details = 5;

  // Suggested HTTP return code for this status, 0 if not set.
  // +optional
  optional int32 code = 6;
}

// StatusCause provides more information about an api.Status failure, including
// cases when multiple errors are encountered.
message StatusCause {
  // A machine-readable description of the cause of the error. If this value is
  // empty there is no information available.
  // +optional
  optional string reason = 1;

  // A human-readable description of the cause of the error.  This field may be
  // presented as-is to a reader.
  // +optional
  optional string message = 2;

  // The field of the resource that has caused this error, as named by its JSON
  // serialization. May include dot and postfix notation for nested attributes.
  // Arrays are zero-indexed.  Fields may appear more than once in an array of
  // causes due to fields having multiple errors.
  // Optional.
  //
  // Examples:
  //   "name" - the field "name" on the current resource
  //   "items[0].name" - the field "name" on the first array entry in "items"
  // +optional
  optional string field = 3;
}

// StatusDetails is a set of additional properties that MAY be set by the
// server to provide additional information about a response. The Reason
// field of a Status object defines what attributes will be set. Clients
// must ignore fields that do not match the defined type of each attribute,
// and should assume that any attribute may be empty, invalid, or under
// defined.
message StatusDetails {
  // The name attribute of the resource associated with the status StatusReason
  // (when there is a single name which can be described).
  // +optional
  optional string name = 1;

  // The group attribute of the resource associated with the status StatusReason.
  // +optional
  optional string group = 2;

  // The kind attribute of the resource associated with the status StatusReason.
  // On some operations may differ from the requested resource Kind.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional string kind = 3;

  // UID of the resource.
  // (when there is a single resource which can be described).
  // More info: http://kubernetes.io/docs/user-guide/identifiers#uids
  // +optional
  optional string uid = 6;

  // The Causes array includes more details associated with the StatusReason
  // failure. Not all StatusReasons may provide detailed causes.
  // +optional
  repeated StatusCause causes = 4;

  // If specified, the time in seconds before the operation should be retried. Some errors may indicate
  // the client must take an alternate action - for those errors this field may indicate how long to wait
  // before taking the alternate action.
  // +optional
  optional int32 retryAfterSeconds = 5;
}

// TableOptions are used when a Table is requested by the caller.
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
message TableOptions {
  // includeObject decides whether to include each object along with its columnar information.
  // Specifying "None" will return no object, specifying "Object" will return the full object contents, and
  // specifying "Metadata" (the default) will return the object's metadata in the PartialObjectMetadata kind
  // in version v1beta1 of the meta.k8s.io API group.
  optional string includeObject = 1;
}

// Time is a wrapper around time.Time which supports correct
// marshaling to YAML and JSON.  Wrappers are provided for many
// of the factory methods that the time package offers.
//
// +protobuf.options.marshal=false
// +protobuf.as=Timestamp
// +protobuf.options.(gogoproto.goproto_stringer)=false
message Time {
  // Represents seconds of UTC time since Unix epoch
  // 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
  // 9999-12-31T23:59:59Z inclusive.
  optional int64 seconds = 1;

  // Non-negative fractions of a second at nanosecond resolution. Negative
  // second values with fractions must still have non-negative nanos values
  // that count forward in time. Must be from 0 to 999,999,999
  // inclusive. This field may be limited in precision depending on context.
  optional int32 nanos = 2;
}

// Timestamp is a struct that is equivalent to Time, but intended for
// protobuf marshalling/unmarshalling. It is generated into a serialization
// that matches Time. Do not use in Go structs.
message Timestamp {
  // Represents seconds of UTC time since Unix epoch
  // 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
  // 9999-12-31T23:59:59Z inclusive.
  optional int64 seconds = 1;

  // Non-negative fractions of a second at nanosecond resolution. Negative
  // second values with fractions must still have non-negative nanos values
  // that count forward in time. Must be from 0 to 999,999,999
  // inclusive. This field may be limited in precision depending on context.
  optional int32 nanos = 2;
}

// TypeMeta describes an individual object in an API response or request
// with strings representing the type of the object and its API schema version.
// Structures that are versioned or persisted should inline TypeMeta.
//
// +k8s:deepcopy-gen=false
message TypeMeta {
  // Kind is a string value representing the REST resource this object represents.
  // Servers may infer this from the endpoint the client submits requests to.
  // Cannot be updated.
  // In CamelCase.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional string kind = 1;

  // APIVersion defines the versioned schema of this representation of an object.
  // Servers should convert recognized schemas to the latest internal value, and
  // may reject unrecognized values.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
  // +optional
  optional string apiVersion = 2;
}

// UpdateOptions may be provided when updating an API object.
// All fields in UpdateOptions should also be present in PatchOptions.
message UpdateOptions {
  // When present, indicates that modifications should not be
  // persisted. An invalid or unrecognized dryRun directive will
  // result in an error response and no further processing of the
  // request. Valid values are:
  // - All: all dry run stages will be processed
  // +optional
  repeated string dryRun = 1;

  // fieldManager is a name associated with the actor or entity
  // that is making these changes. The value must be less than or
  // 128 characters long, and only contain printable characters,
  // as defined by https://golang.org/pkg/unicode/#IsPrint.
  // +optional
  optional string fieldManager = 2;

  // fieldValidation determines how the server should respond to
  // unknown/duplicate fields in the object in the request.
  // Introduced as alpha in 1.23, older servers or servers with the
  // `ServerSideFieldValidation` feature disabled will discard valid values
  // specified in  this param and not perform any server side field validation.
  // Valid values are:
  // - Ignore: ignores unknown/duplicate fields.
  // - Warn: responds with a warning for each
  // unknown/duplicate field, but successfully serves the request.
  // - Strict: fails the request on unknown/duplicate fields.
  // +optional
  optional string fieldValidation = 3;
}

// Verbs masks the value so protobuf can generate
//
// +protobuf.nullable=true
// +protobuf.options.(gogoproto.goproto_stringer)=false
message Verbs {
  // items, if empty, will result in an empty slice

  repeated string items = 1;
}

// Event represents a single event to a watched resource.
//
// +protobuf=true
// +k8s:deepcopy-gen=true
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
message WatchEvent {
  optional string type = 1;

  // Object is:
  //  * If Type is Added or Modified: the new state of the object.
  //  * If Type is Deleted: the state of the object immediately before deletion.
  //  * If Type is Error: *Status is recommended; other types may make sense
  //    depending on context.
  optional k8s.io.apimachinery.pkg.runtime.RawExtension object = 2;
}

