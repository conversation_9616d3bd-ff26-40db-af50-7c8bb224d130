// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package lightsail provides the client and types for making API
// requests to Amazon Lightsail.
//
// Amazon Lightsail is the easiest way to get started with Amazon Web Services
// (AWS) for developers who need to build websites or web applications. It includes
// everything you need to launch your project quickly - instances (virtual private
// servers), container services, storage buckets, managed databases, SSD-based
// block storage, static IP addresses, load balancers, content delivery network
// (CDN) distributions, DNS management of registered domains, and resource snapshots
// (backups) - for a low, predictable monthly price.
//
// You can manage your Lightsail resources using the Lightsail console, Lightsail
// API, AWS Command Line Interface (AWS CLI), or SDKs. For more information
// about Lightsail concepts and tasks, see the Amazon Lightsail Developer Guide
// (https://lightsail.aws.amazon.com/ls/docs/en_us/articles/lightsail-how-to-set-up-access-keys-to-use-sdk-api-cli).
//
// This API Reference provides detailed information about the actions, data
// types, parameters, and errors of the Lightsail service. For more information
// about the supported AWS Regions, endpoints, and service quotas of the Lightsail
// service, see Amazon Lightsail Endpoints and Quotas (https://docs.aws.amazon.com/general/latest/gr/lightsail.html)
// in the AWS General Reference.
//
// See https://docs.aws.amazon.com/goto/WebAPI/lightsail-2016-11-28 for more information on this service.
//
// See lightsail package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/lightsail/
//
// Using the Client
//
// To contact Amazon Lightsail with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the Amazon Lightsail client Lightsail for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/lightsail/#New
package lightsail
