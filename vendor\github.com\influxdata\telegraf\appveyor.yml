version: "{build}"

image: Visual Studio 2019

cache:
 - C:\gopath\pkg\mod -> go.sum
 - C:\ProgramData\chocolatey\bin -> appveyor.yml
 - C:\ProgramData\chocolatey\lib -> appveyor.yml

clone_folder: C:\gopath\src\github.com\influxdata\telegraf

environment:
  GOPATH: C:\gopath

stack: go 1.14

platform: x64

install:
  - choco install make
  - cd "%GOPATH%\src\github.com\influxdata\telegraf"
  - git config --system core.longpaths true
  - go version
  - go env

build_script:
  - make deps
  - make telegraf

test_script:
  - make check
  - make test-windows

artifacts:
  - path: telegraf.exe
