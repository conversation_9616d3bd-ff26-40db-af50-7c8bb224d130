# version format
version: "{build}"

# Operating system (build VM template)
os: Windows Server 2012 R2

# Platform.
platform: x64

clone_folder: c:\gopath\src\github.com\minio\sha256-simd

# environment variables
environment:
  GOPATH: c:\gopath
  GO15VENDOREXPERIMENT: 1

# scripts that run after cloning repository
install:
  - set PATH=%GOPATH%\bin;c:\go\bin;%PATH%
  - go version
  - go env

# to run your custom scripts instead of automatic MSBuild
build_script:
  - go test .
  - go test -race .

# to disable automatic tests
test: off

# to disable deployment
deploy: off
