linters-settings:
  govet:
    check-shadowing: true
  golint:
    min-confidence: 0
  gocyclo:
    min-complexity: 31
  maligned:
    suggest-new: true
  dupl:
    threshold: 100
  goconst:
    min-len: 2
    min-occurrences: 4

linters:
  enable-all: true
  disable:
    - maligned
    - lll
    - gochecknoinits
    - gochecknoglobals
    - godox
    - gocognit
    - whitespace
    - wsl
    - funlen
    - wrapcheck
    - testpackage
    - nlreturn
    - gofumpt
    - goerr113
    - gci
    - gomnd
    - godot
    - exhaustivestruct
    - paralleltest
    - varnamelen
    - ireturn
    #- thelper

issues:
  exclude-rules:
    - path: bson.go
      text: "should be .*ObjectID"
      linters:
        - golint
        - stylecheck

