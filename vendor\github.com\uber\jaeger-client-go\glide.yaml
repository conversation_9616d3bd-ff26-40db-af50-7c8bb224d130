package: github.com/uber/jaeger-client-go
import:
- package: github.com/opentracing/opentracing-go
  version: ^1.2
  subpackages:
  - ext
  - log
- package: github.com/crossdock/crossdock-go
- package: github.com/uber/jaeger-lib
  version: ^2.3.0
  subpackages:
  - metrics
- package: github.com/pkg/errors
  version: ~0.8.0
- package: go.uber.org/zap
  source: https://github.com/uber-go/zap.git
  version: ^1
- package: github.com/uber-go/atomic
  version: ^1
- package: github.com/prometheus/client_golang
  version: 1.1
- package: github.com/prometheus/procfs
  version: 0.0.6
testImport:
- package: github.com/stretchr/testify
  subpackages:
  - assert
  - require
  - suite
- package: github.com/golang/mock
