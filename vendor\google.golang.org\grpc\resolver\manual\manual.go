/*
 *
 * Copyright 2017 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Package manual defines a resolver that can be used to manually send resolved
// addresses to ClientConn.
package manual

import (
	"google.golang.org/grpc/resolver"
)

// NewBuilderWithScheme creates a new test resolver builder with the given scheme.
func NewBuilderWithScheme(scheme string) *Resolver {
	return &Resolver{
		BuildCallback:      func(resolver.Target, resolver.ClientConn, resolver.BuildOptions) {},
		ResolveNowCallback: func(resolver.ResolveNowOptions) {},
		CloseCallback:      func() {},
		scheme:             scheme,
	}
}

// <PERSON>sol<PERSON> is also a resolver builder.
// It's build() function always returns itself.
type Resolver struct {
	// BuildCallback is called when the Build method is called.  Must not be
	// nil.  Must not be changed after the resolver may be built.
	BuildCallback func(resolver.Target, resolver.ClientConn, resolver.BuildOptions)
	// ResolveNowCallback is called when the ResolveNow method is called on the
	// resolver.  Must not be nil.  Must not be changed after the resolver may
	// be built.
	ResolveNowCallback func(resolver.ResolveNowOptions)
	// CloseCallback is called when the Close method is called.  Must not be
	// nil.  Must not be changed after the resolver may be built.
	CloseCallback func()
	scheme        string

	// Fields actually belong to the resolver.
	CC             resolver.ClientConn
	bootstrapState *resolver.State
}

// InitialState adds initial state to the resolver so that UpdateState doesn't
// need to be explicitly called after Dial.
func (r *Resolver) InitialState(s resolver.State) {
	r.bootstrapState = &s
}

// Build returns itself for Resolver, because it's both a builder and a resolver.
func (r *Resolver) Build(target resolver.Target, cc resolver.ClientConn, opts resolver.BuildOptions) (resolver.Resolver, error) {
	r.BuildCallback(target, cc, opts)
	r.CC = cc
	if r.bootstrapState != nil {
		r.UpdateState(*r.bootstrapState)
	}
	return r, nil
}

// Scheme returns the test scheme.
func (r *Resolver) Scheme() string {
	return r.scheme
}

// ResolveNow is a noop for Resolver.
func (r *Resolver) ResolveNow(o resolver.ResolveNowOptions) {
	r.ResolveNowCallback(o)
}

// Close is a noop for Resolver.
func (r *Resolver) Close() {
	r.CloseCallback()
}

// UpdateState calls CC.UpdateState.
func (r *Resolver) UpdateState(s resolver.State) {
	r.CC.UpdateState(s)
}

// ReportError calls CC.ReportError.
func (r *Resolver) ReportError(err error) {
	r.CC.ReportError(err)
}
