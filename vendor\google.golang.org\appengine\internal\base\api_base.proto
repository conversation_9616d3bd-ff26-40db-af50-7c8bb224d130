// Built-in base types for API calls. Primarily useful as return types.

syntax = "proto2";
option go_package = "base";

package appengine.base;

message StringProto {
  required string value = 1;
}

message Integer32Proto {
  required int32 value = 1;
}

message Integer64Proto {
  required int64 value = 1;
}

message Bool<PERSON>roto {
  required bool value = 1;
}

message DoubleProto {
  required double value = 1;
}

message BytesProto {
  required bytes value = 1 [ctype=CORD];
}

message VoidProto {
}
