/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1beta1

// RuntimeClassStrategyOptionsApplyConfiguration represents an declarative configuration of the RuntimeClassStrategyOptions type for use
// with apply.
type RuntimeClassStrategyOptionsApplyConfiguration struct {
	AllowedRuntimeClassNames []string `json:"allowedRuntimeClassNames,omitempty"`
	DefaultRuntimeClassName  *string  `json:"defaultRuntimeClassName,omitempty"`
}

// RuntimeClassStrategyOptionsApplyConfiguration constructs an declarative configuration of the RuntimeClassStrategyOptions type for use with
// apply.
func RuntimeClassStrategyOptions() *RuntimeClassStrategyOptionsApplyConfiguration {
	return &RuntimeClassStrategyOptionsApplyConfiguration{}
}

// WithAllowedRuntimeClassNames adds the given value to the AllowedRuntimeClassNames field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AllowedRuntimeClassNames field.
func (b *RuntimeClassStrategyOptionsApplyConfiguration) WithAllowedRuntimeClassNames(values ...string) *RuntimeClassStrategyOptionsApplyConfiguration {
	for i := range values {
		b.AllowedRuntimeClassNames = append(b.AllowedRuntimeClassNames, values[i])
	}
	return b
}

// WithDefaultRuntimeClassName sets the DefaultRuntimeClassName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DefaultRuntimeClassName field is set to the value of the last call.
func (b *RuntimeClassStrategyOptionsApplyConfiguration) WithDefaultRuntimeClassName(value string) *RuntimeClassStrategyOptionsApplyConfiguration {
	b.DefaultRuntimeClassName = &value
	return b
}
