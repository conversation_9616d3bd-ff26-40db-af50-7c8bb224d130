/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// IngressTLSApplyConfiguration represents an declarative configuration of the IngressTLS type for use
// with apply.
type IngressTLSApplyConfiguration struct {
	Hosts      []string `json:"hosts,omitempty"`
	SecretName *string  `json:"secretName,omitempty"`
}

// IngressTLSApplyConfiguration constructs an declarative configuration of the IngressTLS type for use with
// apply.
func IngressTLS() *IngressTLSApplyConfiguration {
	return &IngressTLSApplyConfiguration{}
}

// WithHosts adds the given value to the Hosts field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Hosts field.
func (b *IngressTLSApplyConfiguration) WithHosts(values ...string) *IngressTLSApplyConfiguration {
	for i := range values {
		b.Hosts = append(b.Hosts, values[i])
	}
	return b
}

// WithSecretName sets the SecretName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecretName field is set to the value of the last call.
func (b *IngressTLSApplyConfiguration) WithSecretName(value string) *IngressTLSApplyConfiguration {
	b.SecretName = &value
	return b
}
