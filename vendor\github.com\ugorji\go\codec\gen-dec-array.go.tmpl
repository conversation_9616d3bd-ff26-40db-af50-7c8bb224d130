{{var "v"}} := {{if not isArray}}*{{end}}{{ .Varname }}
{{var "h"}}, {{var "l"}} := z.DecSliceHelperStart() {{/* // helper, containerLenS */}}
{{if not isArray -}}
var {{var "c"}} bool {{/* // changed */}}
_ = {{var "c"}}
if {{var "h"}}.IsNil {
	if {{var "v"}} != nil {
		{{var "v"}} = nil
		{{var "c"}} = true
	}
} else {{end -}}
if {{var "l"}} == 0 {
	{{if isSlice -}}
	if {{var "v"}} == nil {
		{{var "v"}} = []{{ .Typ }}{}
		{{var "c"}} = true
	} else if len({{var "v"}}) != 0 {
		{{var "v"}} = {{var "v"}}[:0]
		{{var "c"}} = true
	} {{else if is<PERSON>han }}if {{var "v"}} == nil {
		{{var "v"}} = make({{ .CTyp }}, 0)
		{{var "c"}} = true
	}
    {{end -}}
} else {
	{{var "hl"}} := {{var "l"}} > 0
	var {{var "rl"}} int
	_ =  {{var "rl"}}
	{{if isSlice }} if {{var "hl"}} {
	if {{var "l"}} > cap({{var "v"}}) {
		{{var "rl"}} = z.DecInferLen({{var "l"}}, z.DecBasicHandle().MaxInitLen, {{ .Size }})
		if {{var "rl"}} <= cap({{var "v"}}) {
			{{var "v"}} = {{var "v"}}[:{{var "rl"}}]
		} else {
			{{var "v"}} = make([]{{ .Typ }}, {{var "rl"}})
		}
		{{var "c"}} = true
	} else if {{var "l"}} != len({{var "v"}}) {
		{{var "v"}} = {{var "v"}}[:{{var "l"}}]
		{{var "c"}} = true
	}
	}
    {{end -}}
	var {{var "j"}} int 
    {{/* // var {{var "dn"}} bool */ -}}
	for {{var "j"}} = 0; ({{var "hl"}} && {{var "j"}} < {{var "l"}}) || !({{var "hl"}} || z.DecCheckBreak()); {{var "j"}}++ { // bounds-check-elimination
		{{if not isArray}} if {{var "j"}} == 0 && {{var "v"}} == nil {
			if {{var "hl"}} {
				{{var "rl"}} = z.DecInferLen({{var "l"}}, z.DecBasicHandle().MaxInitLen, {{ .Size }})
			} else {
				{{var "rl"}} = {{if isSlice}}8{{else if isChan}}64{{end}}
			}
			{{var "v"}} = make({{if isSlice}}[]{{ .Typ }}{{else if isChan}}{{.CTyp}}{{end}}, {{var "rl"}})
			{{var "c"}} = true 
		}
        {{end -}}
		{{var "h"}}.ElemContainerState({{var "j"}})
        {{/* {{var "dn"}} = r.TryDecodeAsNil() */}}{{/* commented out, as decLineVar handles this already each time */ -}}
        {{if isChan}}{{ $x := printf "%[1]vvcx%[2]v" .TempVar .Rand }}var {{$x}} {{ .Typ }}
		{{ decLineVar $x -}}
		{{var "v"}} <- {{ $x }}
        {{else}}{{/* // if indefinite, etc, then expand the slice if necessary */ -}}
		var {{var "db"}} bool
		if {{var "j"}} >= len({{var "v"}}) {
			{{if isSlice }} {{var "v"}} = append({{var "v"}}, {{ zero }})
			{{var "c"}} = true
			{{else}} z.DecArrayCannotExpand(len(v), {{var "j"}}+1); {{var "db"}} = true
			{{end -}}
		}
		if {{var "db"}} {
			z.DecSwallow()
		} else {
			{{ $x := printf "%[1]vv%[2]v[%[1]vj%[2]v]" .TempVar .Rand }}{{ decLineVar $x -}}
		}
        {{end -}}
	}
	{{if isSlice}} if {{var "j"}} < len({{var "v"}}) {
		{{var "v"}} = {{var "v"}}[:{{var "j"}}]
		{{var "c"}} = true
	} else if {{var "j"}} == 0 && {{var "v"}} == nil {
		{{var "v"}} = make([]{{ .Typ }}, 0)
		{{var "c"}} = true
	}
    {{end -}}
}
{{var "h"}}.End()
{{if not isArray }}if {{var "c"}} { 
	*{{ .Varname }} = {{var "v"}}
}
{{end -}}
