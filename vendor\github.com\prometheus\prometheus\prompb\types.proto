// Copyright 2017 Prometheus Team
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";
package prometheus;

option go_package = "prompb";

import "gogoproto/gogo.proto";

message MetricMetadata {
  enum MetricType {
    UNKNOWN        = 0;
    COUNTER        = 1;
    GAUGE          = 2;
    HISTOGRAM      = 3;
    GAUGEHISTOGRAM = 4;
    SUMMARY        = 5;
    INFO           = 6;
    STATESET       = 7;
  }

  // Represents the metric type, these match the set from Prometheus.
  // Refer to pkg/textparse/interface.go for details.
  MetricType type = 1;
  string metric_family_name = 2;
  string help = 4;
  string unit = 5;
}

message Sample {
  double value    = 1;
  // timestamp is in ms format, see pkg/timestamp/timestamp.go for
  // conversion from time.Time to Prometheus timestamp.
  int64 timestamp = 2;
}

message Exemplar {
  // Optional, can be empty.
  repeated Label labels = 1 [(gogoproto.nullable) = false];
  double value = 2;
  // timestamp is in ms format, see pkg/timestamp/timestamp.go for
  // conversion from time.Time to Prometheus timestamp.
  int64 timestamp = 3;
}

// TimeSeries represents samples and labels for a single time series.
message TimeSeries {
  // For a timeseries to be valid, and for the samples and exemplars
  // to be ingested by the remote system properly, the labels field is required.
  repeated Label labels   = 1 [(gogoproto.nullable) = false];
  repeated Sample samples = 2 [(gogoproto.nullable) = false];
  repeated Exemplar exemplars = 3 [(gogoproto.nullable) = false];
}

message Label {
  string name  = 1;
  string value = 2;
}

message Labels {
  repeated Label labels = 1 [(gogoproto.nullable) = false];
}

// Matcher specifies a rule, which can match or set of labels or not.
message LabelMatcher {
  enum Type {
    EQ  = 0;
    NEQ = 1;
    RE  = 2;
    NRE = 3;
  }
  Type type    = 1;
  string name  = 2;
  string value = 3;
}

message ReadHints {
  int64 step_ms = 1;  // Query step size in milliseconds.
  string func = 2;    // String representation of surrounding function or aggregation.
  int64 start_ms = 3; // Start time in milliseconds.
  int64 end_ms = 4;   // End time in milliseconds.
  repeated string grouping = 5; // List of label names used in aggregation.
  bool by = 6; // Indicate whether it is without or by.
  int64 range_ms = 7; // Range vector selector range in milliseconds.
}

// Chunk represents a TSDB chunk.
// Time range [min, max] is inclusive.
message Chunk {
  int64 min_time_ms = 1;
  int64 max_time_ms = 2;

  // We require this to match chunkenc.Encoding.
  enum Encoding {
    UNKNOWN = 0;
    XOR     = 1;
  }
  Encoding type  = 3;
  bytes data     = 4;
}

// ChunkedSeries represents single, encoded time series.
message ChunkedSeries {
  // Labels should be sorted.
  repeated Label labels = 1 [(gogoproto.nullable) = false];
  // Chunks will be in start time order and may overlap.
  repeated Chunk chunks = 2 [(gogoproto.nullable) = false];
}
