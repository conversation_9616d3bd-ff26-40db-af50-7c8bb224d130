// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

package ianaindex

import "golang.org/x/text/encoding/internal/identifier"

const (
	enc3 = iota
	enc4
	enc5
	enc6
	enc7
	enc8
	enc9
	enc10
	enc11
	enc12
	enc13
	enc14
	enc15
	enc16
	enc17
	enc18
	enc19
	enc20
	enc21
	enc22
	enc23
	enc24
	enc25
	enc26
	enc27
	enc28
	enc29
	enc30
	enc31
	enc32
	enc33
	enc34
	enc35
	enc36
	enc37
	enc38
	enc39
	enc40
	enc41
	enc42
	enc43
	enc44
	enc45
	enc46
	enc47
	enc48
	enc49
	enc50
	enc51
	enc52
	enc53
	enc54
	enc55
	enc56
	enc57
	enc58
	enc59
	enc60
	enc61
	enc62
	enc63
	enc64
	enc65
	enc66
	enc67
	enc68
	enc69
	enc70
	enc71
	enc72
	enc73
	enc74
	enc75
	enc76
	enc77
	enc78
	enc79
	enc80
	enc81
	enc82
	enc83
	enc84
	enc85
	enc86
	enc87
	enc88
	enc89
	enc90
	enc91
	enc92
	enc93
	enc94
	enc95
	enc96
	enc97
	enc98
	enc99
	enc100
	enc101
	enc102
	enc103
	enc104
	enc105
	enc106
	enc109
	enc110
	enc111
	enc112
	enc113
	enc114
	enc115
	enc116
	enc117
	enc118
	enc119
	enc1000
	enc1001
	enc1002
	enc1003
	enc1004
	enc1005
	enc1006
	enc1007
	enc1008
	enc1009
	enc1010
	enc1011
	enc1012
	enc1013
	enc1014
	enc1015
	enc1016
	enc1017
	enc1018
	enc1019
	enc1020
	enc2000
	enc2001
	enc2002
	enc2003
	enc2004
	enc2005
	enc2006
	enc2007
	enc2008
	enc2009
	enc2010
	enc2011
	enc2012
	enc2013
	enc2014
	enc2015
	enc2016
	enc2017
	enc2018
	enc2019
	enc2020
	enc2021
	enc2022
	enc2023
	enc2024
	enc2025
	enc2026
	enc2027
	enc2028
	enc2029
	enc2030
	enc2031
	enc2032
	enc2033
	enc2034
	enc2035
	enc2036
	enc2037
	enc2038
	enc2039
	enc2040
	enc2041
	enc2042
	enc2043
	enc2044
	enc2045
	enc2046
	enc2047
	enc2048
	enc2049
	enc2050
	enc2051
	enc2052
	enc2053
	enc2054
	enc2055
	enc2056
	enc2057
	enc2058
	enc2059
	enc2060
	enc2061
	enc2062
	enc2063
	enc2064
	enc2065
	enc2066
	enc2067
	enc2068
	enc2069
	enc2070
	enc2071
	enc2072
	enc2073
	enc2074
	enc2075
	enc2076
	enc2077
	enc2078
	enc2079
	enc2080
	enc2081
	enc2082
	enc2083
	enc2084
	enc2085
	enc2086
	enc2087
	enc2088
	enc2089
	enc2090
	enc2091
	enc2092
	enc2093
	enc2094
	enc2095
	enc2096
	enc2097
	enc2098
	enc2099
	enc2100
	enc2101
	enc2102
	enc2103
	enc2104
	enc2105
	enc2106
	enc2107
	enc2108
	enc2109
	enc2250
	enc2251
	enc2252
	enc2253
	enc2254
	enc2255
	enc2256
	enc2257
	enc2258
	enc2259
	enc2260
	numIANA
)

var ianaToMIB = []identifier.MIB{ // 257 elements
	// Entry 0 - 3F
	0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0008, 0x0009, 0x000a,
	0x000b, 0x000c, 0x000d, 0x000e, 0x000f, 0x0010, 0x0011, 0x0012,
	0x0013, 0x0014, 0x0015, 0x0016, 0x0017, 0x0018, 0x0019, 0x001a,
	0x001b, 0x001c, 0x001d, 0x001e, 0x001f, 0x0020, 0x0021, 0x0022,
	0x0023, 0x0024, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002a,
	0x002b, 0x002c, 0x002d, 0x002e, 0x002f, 0x0030, 0x0031, 0x0032,
	0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003a,
	0x003b, 0x003c, 0x003d, 0x003e, 0x003f, 0x0040, 0x0041, 0x0042,
	// Entry 40 - 7F
	0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004a,
	0x004b, 0x004c, 0x004d, 0x004e, 0x004f, 0x0050, 0x0051, 0x0052,
	0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005a,
	0x005b, 0x005c, 0x005d, 0x005e, 0x005f, 0x0060, 0x0061, 0x0062,
	0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006a,
	0x006d, 0x006e, 0x006f, 0x0070, 0x0071, 0x0072, 0x0073, 0x0074,
	0x0075, 0x0076, 0x0077, 0x03e8, 0x03e9, 0x03ea, 0x03eb, 0x03ec,
	0x03ed, 0x03ee, 0x03ef, 0x03f0, 0x03f1, 0x03f2, 0x03f3, 0x03f4,
	// Entry 80 - BF
	0x03f5, 0x03f6, 0x03f7, 0x03f8, 0x03f9, 0x03fa, 0x03fb, 0x03fc,
	0x07d0, 0x07d1, 0x07d2, 0x07d3, 0x07d4, 0x07d5, 0x07d6, 0x07d7,
	0x07d8, 0x07d9, 0x07da, 0x07db, 0x07dc, 0x07dd, 0x07de, 0x07df,
	0x07e0, 0x07e1, 0x07e2, 0x07e3, 0x07e4, 0x07e5, 0x07e6, 0x07e7,
	0x07e8, 0x07e9, 0x07ea, 0x07eb, 0x07ec, 0x07ed, 0x07ee, 0x07ef,
	0x07f0, 0x07f1, 0x07f2, 0x07f3, 0x07f4, 0x07f5, 0x07f6, 0x07f7,
	0x07f8, 0x07f9, 0x07fa, 0x07fb, 0x07fc, 0x07fd, 0x07fe, 0x07ff,
	0x0800, 0x0801, 0x0802, 0x0803, 0x0804, 0x0805, 0x0806, 0x0807,
	// Entry C0 - FF
	0x0808, 0x0809, 0x080a, 0x080b, 0x080c, 0x080d, 0x080e, 0x080f,
	0x0810, 0x0811, 0x0812, 0x0813, 0x0814, 0x0815, 0x0816, 0x0817,
	0x0818, 0x0819, 0x081a, 0x081b, 0x081c, 0x081d, 0x081e, 0x081f,
	0x0820, 0x0821, 0x0822, 0x0823, 0x0824, 0x0825, 0x0826, 0x0827,
	0x0828, 0x0829, 0x082a, 0x082b, 0x082c, 0x082d, 0x082e, 0x082f,
	0x0830, 0x0831, 0x0832, 0x0833, 0x0834, 0x0835, 0x0836, 0x0837,
	0x0838, 0x0839, 0x083a, 0x083b, 0x083c, 0x083d, 0x08ca, 0x08cb,
	0x08cc, 0x08cd, 0x08ce, 0x08cf, 0x08d0, 0x08d1, 0x08d2, 0x08d3,
	// Entry 100 - 13F
	0x08d4,
} // Size: 538 bytes

var ianaNames = []string{ // 257 elements
	"US-ASCII",
	"\vISO-8859-1ISO_8859-1:1987",
	"\vISO-8859-2ISO_8859-2:1987",
	"\vISO-8859-3ISO_8859-3:1988",
	"\vISO-8859-4ISO_8859-4:1988",
	"\vISO-8859-5ISO_8859-5:1988",
	"\vISO-8859-6ISO_8859-6:1987",
	"\vISO-8859-7ISO_8859-7:1987",
	"\vISO-8859-8ISO_8859-8:1988",
	"\vISO-8859-9ISO_8859-9:1989",
	"ISO-8859-10",
	"ISO_6937-2-add",
	"JIS_X0201",
	"JIS_Encoding",
	"Shift_JIS",
	"\x07EUC-JPExtended_UNIX_Code_Packed_Format_for_Japanese",
	"Extended_UNIX_Code_Fixed_Width_for_Japanese",
	"BS_4730",
	"SEN_850200_C",
	"IT",
	"ES",
	"DIN_66003",
	"NS_4551-1",
	"NF_Z_62-010",
	"ISO-10646-UTF-1",
	"ISO_646.basic:1983",
	"INVARIANT",
	"ISO_646.irv:1983",
	"NATS-SEFI",
	"NATS-SEFI-ADD",
	"NATS-DANO",
	"NATS-DANO-ADD",
	"SEN_850200_B",
	"KS_C_5601-1987",
	"ISO-2022-KR",
	"EUC-KR",
	"ISO-2022-JP",
	"ISO-2022-JP-2",
	"JIS_C6220-1969-jp",
	"JIS_C6220-1969-ro",
	"PT",
	"greek7-old",
	"latin-greek",
	"NF_Z_62-010_(1973)",
	"Latin-greek-1",
	"ISO_5427",
	"JIS_C6226-1978",
	"BS_viewdata",
	"INIS",
	"INIS-8",
	"INIS-cyrillic",
	"ISO_5427:1981",
	"ISO_5428:1980",
	"GB_1988-80",
	"GB_2312-80",
	"NS_4551-2",
	"videotex-suppl",
	"PT2",
	"ES2",
	"MSZ_7795.3",
	"JIS_C6226-1983",
	"greek7",
	"ASMO_449",
	"iso-ir-90",
	"JIS_C6229-1984-a",
	"JIS_C6229-1984-b",
	"JIS_C6229-1984-b-add",
	"JIS_C6229-1984-hand",
	"JIS_C6229-1984-hand-add",
	"JIS_C6229-1984-kana",
	"ISO_2033-1983",
	"ANSI_X3.110-1983",
	"T.61-7bit",
	"T.61-8bit",
	"ECMA-cyrillic",
	"CSA_Z243.4-1985-1",
	"CSA_Z243.4-1985-2",
	"CSA_Z243.4-1985-gr",
	"\rISO-8859-6-EISO_8859-6-E",
	"\rISO-8859-6-IISO_8859-6-I",
	"T.101-G2",
	"\rISO-8859-8-EISO_8859-8-E",
	"\rISO-8859-8-IISO_8859-8-I",
	"CSN_369103",
	"JUS_I.B1.002",
	"IEC_P27-1",
	"JUS_I.B1.003-serb",
	"JUS_I.B1.003-mac",
	"greek-ccitt",
	"NC_NC00-10:81",
	"ISO_6937-2-25",
	"GOST_19768-74",
	"ISO_8859-supp",
	"ISO_10367-box",
	"latin-lap",
	"JIS_X0212-1990",
	"DS_2089",
	"us-dk",
	"dk-us",
	"KSC5636",
	"UNICODE-1-1-UTF-7",
	"ISO-2022-CN",
	"ISO-2022-CN-EXT",
	"UTF-8",
	"ISO-8859-13",
	"ISO-8859-14",
	"ISO-8859-15",
	"ISO-8859-16",
	"GBK",
	"GB18030",
	"OSD_EBCDIC_DF04_15",
	"OSD_EBCDIC_DF03_IRV",
	"OSD_EBCDIC_DF04_1",
	"ISO-11548-1",
	"KZ-1048",
	"ISO-10646-UCS-2",
	"ISO-10646-UCS-4",
	"ISO-10646-UCS-Basic",
	"ISO-10646-Unicode-Latin1",
	"ISO-10646-J-1",
	"ISO-Unicode-IBM-1261",
	"ISO-Unicode-IBM-1268",
	"ISO-Unicode-IBM-1276",
	"ISO-Unicode-IBM-1264",
	"ISO-Unicode-IBM-1265",
	"UNICODE-1-1",
	"SCSU",
	"UTF-7",
	"UTF-16BE",
	"UTF-16LE",
	"UTF-16",
	"CESU-8",
	"UTF-32",
	"UTF-32BE",
	"UTF-32LE",
	"BOCU-1",
	"ISO-8859-1-Windows-3.0-Latin-1",
	"ISO-8859-1-Windows-3.1-Latin-1",
	"ISO-8859-2-Windows-Latin-2",
	"ISO-8859-9-Windows-Latin-5",
	"hp-roman8",
	"Adobe-Standard-Encoding",
	"Ventura-US",
	"Ventura-International",
	"DEC-MCS",
	"IBM850",
	"IBM852",
	"IBM437",
	"PC8-Danish-Norwegian",
	"IBM862",
	"PC8-Turkish",
	"IBM-Symbols",
	"IBM-Thai",
	"HP-Legal",
	"HP-Pi-font",
	"HP-Math8",
	"Adobe-Symbol-Encoding",
	"HP-DeskTop",
	"Ventura-Math",
	"Microsoft-Publishing",
	"Windows-31J",
	"GB2312",
	"Big5",
	"macintosh",
	"IBM037",
	"IBM038",
	"IBM273",
	"IBM274",
	"IBM275",
	"IBM277",
	"IBM278",
	"IBM280",
	"IBM281",
	"IBM284",
	"IBM285",
	"IBM290",
	"IBM297",
	"IBM420",
	"IBM423",
	"IBM424",
	"IBM500",
	"IBM851",
	"IBM855",
	"IBM857",
	"IBM860",
	"IBM861",
	"IBM863",
	"IBM864",
	"IBM865",
	"IBM868",
	"IBM869",
	"IBM870",
	"IBM871",
	"IBM880",
	"IBM891",
	"IBM903",
	"IBM904",
	"IBM905",
	"IBM918",
	"IBM1026",
	"EBCDIC-AT-DE",
	"EBCDIC-AT-DE-A",
	"EBCDIC-CA-FR",
	"EBCDIC-DK-NO",
	"EBCDIC-DK-NO-A",
	"EBCDIC-FI-SE",
	"EBCDIC-FI-SE-A",
	"EBCDIC-FR",
	"EBCDIC-IT",
	"EBCDIC-PT",
	"EBCDIC-ES",
	"EBCDIC-ES-A",
	"EBCDIC-ES-S",
	"EBCDIC-UK",
	"EBCDIC-US",
	"UNKNOWN-8BIT",
	"MNEMONIC",
	"MNEM",
	"VISCII",
	"VIQR",
	"KOI8-R",
	"HZ-GB-2312",
	"IBM866",
	"IBM775",
	"KOI8-U",
	"IBM00858",
	"IBM00924",
	"IBM01140",
	"IBM01141",
	"IBM01142",
	"IBM01143",
	"IBM01144",
	"IBM01145",
	"IBM01146",
	"IBM01147",
	"IBM01148",
	"IBM01149",
	"Big5-HKSCS",
	"IBM1047",
	"PTCP154",
	"Amiga-1251",
	"KOI7-switched",
	"BRF",
	"TSCII",
	"CP51932",
	"windows-874",
	"windows-1250",
	"windows-1251",
	"windows-1252",
	"windows-1253",
	"windows-1254",
	"windows-1255",
	"windows-1256",
	"windows-1257",
	"windows-1258",
	"TIS-620",
	"CP50220",
} // Size: 7088 bytes

var mibNames = []string{ // 257 elements
	"ASCII",
	"ISOLatin1",
	"ISOLatin2",
	"ISOLatin3",
	"ISOLatin4",
	"ISOLatinCyrillic",
	"ISOLatinArabic",
	"ISOLatinGreek",
	"ISOLatinHebrew",
	"ISOLatin5",
	"ISOLatin6",
	"ISOTextComm",
	"HalfWidthKatakana",
	"JISEncoding",
	"ShiftJIS",
	"EUCPkdFmtJapanese",
	"EUCFixWidJapanese",
	"ISO4UnitedKingdom",
	"ISO11SwedishForNames",
	"ISO15Italian",
	"ISO17Spanish",
	"ISO21German",
	"ISO60Norwegian1",
	"ISO69French",
	"ISO10646UTF1",
	"ISO646basic1983",
	"INVARIANT",
	"ISO2IntlRefVersion",
	"NATSSEFI",
	"NATSSEFIADD",
	"NATSDANO",
	"NATSDANOADD",
	"ISO10Swedish",
	"KSC56011987",
	"ISO2022KR",
	"EUCKR",
	"ISO2022JP",
	"ISO2022JP2",
	"ISO13JISC6220jp",
	"ISO14JISC6220ro",
	"ISO16Portuguese",
	"ISO18Greek7Old",
	"ISO19LatinGreek",
	"ISO25French",
	"ISO27LatinGreek1",
	"ISO5427Cyrillic",
	"ISO42JISC62261978",
	"ISO47BSViewdata",
	"ISO49INIS",
	"ISO50INIS8",
	"ISO51INISCyrillic",
	"ISO54271981",
	"ISO5428Greek",
	"ISO57GB1988",
	"ISO58GB231280",
	"ISO61Norwegian2",
	"ISO70VideotexSupp1",
	"ISO84Portuguese2",
	"ISO85Spanish2",
	"ISO86Hungarian",
	"ISO87JISX0208",
	"ISO88Greek7",
	"ISO89ASMO449",
	"ISO90",
	"ISO91JISC62291984a",
	"ISO92JISC62991984b",
	"ISO93JIS62291984badd",
	"ISO94JIS62291984hand",
	"ISO95JIS62291984handadd",
	"ISO96JISC62291984kana",
	"ISO2033",
	"ISO99NAPLPS",
	"ISO102T617bit",
	"ISO103T618bit",
	"ISO111ECMACyrillic",
	"ISO121Canadian1",
	"ISO122Canadian2",
	"ISO123CSAZ24341985gr",
	"ISO88596E",
	"ISO88596I",
	"ISO128T101G2",
	"ISO88598E",
	"ISO88598I",
	"ISO139CSN369103",
	"ISO141JUSIB1002",
	"ISO143IECP271",
	"ISO146Serbian",
	"ISO147Macedonian",
	"ISO150GreekCCITT",
	"ISO151Cuba",
	"ISO6937Add",
	"ISO153GOST1976874",
	"ISO8859Supp",
	"ISO10367Box",
	"ISO158Lap",
	"ISO159JISX02121990",
	"ISO646Danish",
	"USDK",
	"DKUS",
	"KSC5636",
	"Unicode11UTF7",
	"ISO2022CN",
	"ISO2022CNEXT",
	"UTF8",
	"ISO885913",
	"ISO885914",
	"ISO885915",
	"ISO885916",
	"GBK",
	"GB18030",
	"OSDEBCDICDF0415",
	"OSDEBCDICDF03IRV",
	"OSDEBCDICDF041",
	"ISO115481",
	"KZ1048",
	"Unicode",
	"UCS4",
	"UnicodeASCII",
	"UnicodeLatin1",
	"UnicodeJapanese",
	"UnicodeIBM1261",
	"UnicodeIBM1268",
	"UnicodeIBM1276",
	"UnicodeIBM1264",
	"UnicodeIBM1265",
	"Unicode11",
	"SCSU",
	"UTF7",
	"UTF16BE",
	"UTF16LE",
	"UTF16",
	"CESU-8",
	"UTF32",
	"UTF32BE",
	"UTF32LE",
	"BOCU-1",
	"Windows30Latin1",
	"Windows31Latin1",
	"Windows31Latin2",
	"Windows31Latin5",
	"HPRoman8",
	"AdobeStandardEncoding",
	"VenturaUS",
	"VenturaInternational",
	"DECMCS",
	"PC850Multilingual",
	"PCp852",
	"PC8CodePage437",
	"PC8DanishNorwegian",
	"PC862LatinHebrew",
	"PC8Turkish",
	"IBMSymbols",
	"IBMThai",
	"HPLegal",
	"HPPiFont",
	"HPMath8",
	"HPPSMath",
	"HPDesktop",
	"VenturaMath",
	"MicrosoftPublishing",
	"Windows31J",
	"GB2312",
	"Big5",
	"Macintosh",
	"IBM037",
	"IBM038",
	"IBM273",
	"IBM274",
	"IBM275",
	"IBM277",
	"IBM278",
	"IBM280",
	"IBM281",
	"IBM284",
	"IBM285",
	"IBM290",
	"IBM297",
	"IBM420",
	"IBM423",
	"IBM424",
	"IBM500",
	"IBM851",
	"IBM855",
	"IBM857",
	"IBM860",
	"IBM861",
	"IBM863",
	"IBM864",
	"IBM865",
	"IBM868",
	"IBM869",
	"IBM870",
	"IBM871",
	"IBM880",
	"IBM891",
	"IBM903",
	"IBBM904",
	"IBM905",
	"IBM918",
	"IBM1026",
	"IBMEBCDICATDE",
	"EBCDICATDEA",
	"EBCDICCAFR",
	"EBCDICDKNO",
	"EBCDICDKNOA",
	"EBCDICFISE",
	"EBCDICFISEA",
	"EBCDICFR",
	"EBCDICIT",
	"EBCDICPT",
	"EBCDICES",
	"EBCDICESA",
	"EBCDICESS",
	"EBCDICUK",
	"EBCDICUS",
	"Unknown8BiT",
	"Mnemonic",
	"Mnem",
	"VISCII",
	"VIQR",
	"KOI8R",
	"HZ-GB-2312",
	"IBM866",
	"PC775Baltic",
	"KOI8U",
	"IBM00858",
	"IBM00924",
	"IBM01140",
	"IBM01141",
	"IBM01142",
	"IBM01143",
	"IBM01144",
	"IBM01145",
	"IBM01146",
	"IBM01147",
	"IBM01148",
	"IBM01149",
	"Big5HKSCS",
	"IBM1047",
	"PTCP154",
	"Amiga1251\n(Aliases",
	"KOI7switched",
	"BRF",
	"TSCII",
	"CP51932",
	"windows874",
	"windows1250",
	"windows1251",
	"windows1252",
	"windows1253",
	"windows1254",
	"windows1255",
	"windows1256",
	"windows1257",
	"windows1258",
	"TIS620",
	"CP50220",
} // Size: 6776 bytes

// TODO: Instead of using a map, we could use binary search strings doing
// on-the fly lower-casing per character. This allows to always avoid
// allocation and will be considerably more compact.
var ianaAliases = map[string]int{
	"US-ASCII":            enc3,
	"us-ascii":            enc3,
	"iso-ir-6":            enc3,
	"ANSI_X3.4-1968":      enc3,
	"ansi_x3.4-1968":      enc3,
	"ANSI_X3.4-1986":      enc3,
	"ansi_x3.4-1986":      enc3,
	"ISO_646.irv:1991":    enc3,
	"iso_646.irv:1991":    enc3,
	"ISO646-US":           enc3,
	"iso646-us":           enc3,
	"us":                  enc3,
	"IBM367":              enc3,
	"ibm367":              enc3,
	"cp367":               enc3,
	"csASCII":             enc3,
	"csascii":             enc3,
	"ISO_8859-1:1987":     enc4,
	"iso_8859-1:1987":     enc4,
	"iso-ir-100":          enc4,
	"ISO_8859-1":          enc4,
	"iso_8859-1":          enc4,
	"ISO-8859-1":          enc4,
	"iso-8859-1":          enc4,
	"latin1":              enc4,
	"l1":                  enc4,
	"IBM819":              enc4,
	"ibm819":              enc4,
	"CP819":               enc4,
	"cp819":               enc4,
	"csISOLatin1":         enc4,
	"csisolatin1":         enc4,
	"ISO_8859-2:1987":     enc5,
	"iso_8859-2:1987":     enc5,
	"iso-ir-101":          enc5,
	"ISO_8859-2":          enc5,
	"iso_8859-2":          enc5,
	"ISO-8859-2":          enc5,
	"iso-8859-2":          enc5,
	"latin2":              enc5,
	"l2":                  enc5,
	"csISOLatin2":         enc5,
	"csisolatin2":         enc5,
	"ISO_8859-3:1988":     enc6,
	"iso_8859-3:1988":     enc6,
	"iso-ir-109":          enc6,
	"ISO_8859-3":          enc6,
	"iso_8859-3":          enc6,
	"ISO-8859-3":          enc6,
	"iso-8859-3":          enc6,
	"latin3":              enc6,
	"l3":                  enc6,
	"csISOLatin3":         enc6,
	"csisolatin3":         enc6,
	"ISO_8859-4:1988":     enc7,
	"iso_8859-4:1988":     enc7,
	"iso-ir-110":          enc7,
	"ISO_8859-4":          enc7,
	"iso_8859-4":          enc7,
	"ISO-8859-4":          enc7,
	"iso-8859-4":          enc7,
	"latin4":              enc7,
	"l4":                  enc7,
	"csISOLatin4":         enc7,
	"csisolatin4":         enc7,
	"ISO_8859-5:1988":     enc8,
	"iso_8859-5:1988":     enc8,
	"iso-ir-144":          enc8,
	"ISO_8859-5":          enc8,
	"iso_8859-5":          enc8,
	"ISO-8859-5":          enc8,
	"iso-8859-5":          enc8,
	"cyrillic":            enc8,
	"csISOLatinCyrillic":  enc8,
	"csisolatincyrillic":  enc8,
	"ISO_8859-6:1987":     enc9,
	"iso_8859-6:1987":     enc9,
	"iso-ir-127":          enc9,
	"ISO_8859-6":          enc9,
	"iso_8859-6":          enc9,
	"ISO-8859-6":          enc9,
	"iso-8859-6":          enc9,
	"ECMA-114":            enc9,
	"ecma-114":            enc9,
	"ASMO-708":            enc9,
	"asmo-708":            enc9,
	"arabic":              enc9,
	"csISOLatinArabic":    enc9,
	"csisolatinarabic":    enc9,
	"ISO_8859-7:1987":     enc10,
	"iso_8859-7:1987":     enc10,
	"iso-ir-126":          enc10,
	"ISO_8859-7":          enc10,
	"iso_8859-7":          enc10,
	"ISO-8859-7":          enc10,
	"iso-8859-7":          enc10,
	"ELOT_928":            enc10,
	"elot_928":            enc10,
	"ECMA-118":            enc10,
	"ecma-118":            enc10,
	"greek":               enc10,
	"greek8":              enc10,
	"csISOLatinGreek":     enc10,
	"csisolatingreek":     enc10,
	"ISO_8859-8:1988":     enc11,
	"iso_8859-8:1988":     enc11,
	"iso-ir-138":          enc11,
	"ISO_8859-8":          enc11,
	"iso_8859-8":          enc11,
	"ISO-8859-8":          enc11,
	"iso-8859-8":          enc11,
	"hebrew":              enc11,
	"csISOLatinHebrew":    enc11,
	"csisolatinhebrew":    enc11,
	"ISO_8859-9:1989":     enc12,
	"iso_8859-9:1989":     enc12,
	"iso-ir-148":          enc12,
	"ISO_8859-9":          enc12,
	"iso_8859-9":          enc12,
	"ISO-8859-9":          enc12,
	"iso-8859-9":          enc12,
	"latin5":              enc12,
	"l5":                  enc12,
	"csISOLatin5":         enc12,
	"csisolatin5":         enc12,
	"ISO-8859-10":         enc13,
	"iso-8859-10":         enc13,
	"iso-ir-157":          enc13,
	"l6":                  enc13,
	"ISO_8859-10:1992":    enc13,
	"iso_8859-10:1992":    enc13,
	"csISOLatin6":         enc13,
	"csisolatin6":         enc13,
	"latin6":              enc13,
	"ISO_6937-2-add":      enc14,
	"iso_6937-2-add":      enc14,
	"iso-ir-142":          enc14,
	"csISOTextComm":       enc14,
	"csisotextcomm":       enc14,
	"JIS_X0201":           enc15,
	"jis_x0201":           enc15,
	"X0201":               enc15,
	"x0201":               enc15,
	"csHalfWidthKatakana": enc15,
	"cshalfwidthkatakana": enc15,
	"JIS_Encoding":        enc16,
	"jis_encoding":        enc16,
	"csJISEncoding":       enc16,
	"csjisencoding":       enc16,
	"Shift_JIS":           enc17,
	"shift_jis":           enc17,
	"MS_Kanji":            enc17,
	"ms_kanji":            enc17,
	"csShiftJIS":          enc17,
	"csshiftjis":          enc17,
	"Extended_UNIX_Code_Packed_Format_for_Japanese": enc18,
	"extended_unix_code_packed_format_for_japanese": enc18,
	"csEUCPkdFmtJapanese":                           enc18,
	"cseucpkdfmtjapanese":                           enc18,
	"EUC-JP":                                        enc18,
	"euc-jp":                                        enc18,
	"Extended_UNIX_Code_Fixed_Width_for_Japanese":   enc19,
	"extended_unix_code_fixed_width_for_japanese":   enc19,
	"csEUCFixWidJapanese":                           enc19,
	"cseucfixwidjapanese":                           enc19,
	"BS_4730":                                       enc20,
	"bs_4730":                                       enc20,
	"iso-ir-4":                                      enc20,
	"ISO646-GB":                                     enc20,
	"iso646-gb":                                     enc20,
	"gb":                                            enc20,
	"uk":                                            enc20,
	"csISO4UnitedKingdom":                           enc20,
	"csiso4unitedkingdom":                           enc20,
	"SEN_850200_C":                                  enc21,
	"sen_850200_c":                                  enc21,
	"iso-ir-11":                                     enc21,
	"ISO646-SE2":                                    enc21,
	"iso646-se2":                                    enc21,
	"se2":                                           enc21,
	"csISO11SwedishForNames":                        enc21,
	"csiso11swedishfornames":                        enc21,
	"IT":                                            enc22,
	"it":                                            enc22,
	"iso-ir-15":                                     enc22,
	"ISO646-IT":                                     enc22,
	"iso646-it":                                     enc22,
	"csISO15Italian":                                enc22,
	"csiso15italian":                                enc22,
	"ES":                                            enc23,
	"es":                                            enc23,
	"iso-ir-17":                                     enc23,
	"ISO646-ES":                                     enc23,
	"iso646-es":                                     enc23,
	"csISO17Spanish":                                enc23,
	"csiso17spanish":                                enc23,
	"DIN_66003":                                     enc24,
	"din_66003":                                     enc24,
	"iso-ir-21":                                     enc24,
	"de":                                            enc24,
	"ISO646-DE":                                     enc24,
	"iso646-de":                                     enc24,
	"csISO21German":                                 enc24,
	"csiso21german":                                 enc24,
	"NS_4551-1":                                     enc25,
	"ns_4551-1":                                     enc25,
	"iso-ir-60":                                     enc25,
	"ISO646-NO":                                     enc25,
	"iso646-no":                                     enc25,
	"no":                                            enc25,
	"csISO60DanishNorwegian":                        enc25,
	"csiso60danishnorwegian":                        enc25,
	"csISO60Norwegian1":                             enc25,
	"csiso60norwegian1":                             enc25,
	"NF_Z_62-010":                                   enc26,
	"nf_z_62-010":                                   enc26,
	"iso-ir-69":                                     enc26,
	"ISO646-FR":                                     enc26,
	"iso646-fr":                                     enc26,
	"fr":                                            enc26,
	"csISO69French":                                 enc26,
	"csiso69french":                                 enc26,
	"ISO-10646-UTF-1":                               enc27,
	"iso-10646-utf-1":                               enc27,
	"csISO10646UTF1":                                enc27,
	"csiso10646utf1":                                enc27,
	"ISO_646.basic:1983":                            enc28,
	"iso_646.basic:1983":                            enc28,
	"ref":                                           enc28,
	"csISO646basic1983":                             enc28,
	"csiso646basic1983":                             enc28,
	"INVARIANT":                                     enc29,
	"invariant":                                     enc29,
	"csINVARIANT":                                   enc29,
	"csinvariant":                                   enc29,
	"ISO_646.irv:1983":                              enc30,
	"iso_646.irv:1983":                              enc30,
	"iso-ir-2":                                      enc30,
	"irv":                                           enc30,
	"csISO2IntlRefVersion":                          enc30,
	"csiso2intlrefversion":                          enc30,
	"NATS-SEFI":                                     enc31,
	"nats-sefi":                                     enc31,
	"iso-ir-8-1":                                    enc31,
	"csNATSSEFI":                                    enc31,
	"csnatssefi":                                    enc31,
	"NATS-SEFI-ADD":                                 enc32,
	"nats-sefi-add":                                 enc32,
	"iso-ir-8-2":                                    enc32,
	"csNATSSEFIADD":                                 enc32,
	"csnatssefiadd":                                 enc32,
	"NATS-DANO":                                     enc33,
	"nats-dano":                                     enc33,
	"iso-ir-9-1":                                    enc33,
	"csNATSDANO":                                    enc33,
	"csnatsdano":                                    enc33,
	"NATS-DANO-ADD":                                 enc34,
	"nats-dano-add":                                 enc34,
	"iso-ir-9-2":                                    enc34,
	"csNATSDANOADD":                                 enc34,
	"csnatsdanoadd":                                 enc34,
	"SEN_850200_B":                                  enc35,
	"sen_850200_b":                                  enc35,
	"iso-ir-10":                                     enc35,
	"FI":                                            enc35,
	"fi":                                            enc35,
	"ISO646-FI":                                     enc35,
	"iso646-fi":                                     enc35,
	"ISO646-SE":                                     enc35,
	"iso646-se":                                     enc35,
	"se":                                            enc35,
	"csISO10Swedish":                                enc35,
	"csiso10swedish":                                enc35,
	"KS_C_5601-1987":                                enc36,
	"ks_c_5601-1987":                                enc36,
	"iso-ir-149":                                    enc36,
	"KS_C_5601-1989":                                enc36,
	"ks_c_5601-1989":                                enc36,
	"KSC_5601":                                      enc36,
	"ksc_5601":                                      enc36,
	"korean":                                        enc36,
	"csKSC56011987":                                 enc36,
	"csksc56011987":                                 enc36,
	"ISO-2022-KR":                                   enc37,
	"iso-2022-kr":                                   enc37,
	"csISO2022KR":                                   enc37,
	"csiso2022kr":                                   enc37,
	"EUC-KR":                                        enc38,
	"euc-kr":                                        enc38,
	"csEUCKR":                                       enc38,
	"cseuckr":                                       enc38,
	"ISO-2022-JP":                                   enc39,
	"iso-2022-jp":                                   enc39,
	"csISO2022JP":                                   enc39,
	"csiso2022jp":                                   enc39,
	"ISO-2022-JP-2":                                 enc40,
	"iso-2022-jp-2":                                 enc40,
	"csISO2022JP2":                                  enc40,
	"csiso2022jp2":                                  enc40,
	"JIS_C6220-1969-jp":                             enc41,
	"jis_c6220-1969-jp":                             enc41,
	"JIS_C6220-1969":                                enc41,
	"jis_c6220-1969":                                enc41,
	"iso-ir-13":                                     enc41,
	"katakana":                                      enc41,
	"x0201-7":                                       enc41,
	"csISO13JISC6220jp":                             enc41,
	"csiso13jisc6220jp":                             enc41,
	"JIS_C6220-1969-ro":                             enc42,
	"jis_c6220-1969-ro":                             enc42,
	"iso-ir-14":                                     enc42,
	"jp":                                            enc42,
	"ISO646-JP":                                     enc42,
	"iso646-jp":                                     enc42,
	"csISO14JISC6220ro":                             enc42,
	"csiso14jisc6220ro":                             enc42,
	"PT":                                            enc43,
	"pt":                                            enc43,
	"iso-ir-16":                                     enc43,
	"ISO646-PT":                                     enc43,
	"iso646-pt":                                     enc43,
	"csISO16Portuguese":                             enc43,
	"csiso16portuguese":                             enc43,
	"greek7-old":                                    enc44,
	"iso-ir-18":                                     enc44,
	"csISO18Greek7Old":                              enc44,
	"csiso18greek7old":                              enc44,
	"latin-greek":                                   enc45,
	"iso-ir-19":                                     enc45,
	"csISO19LatinGreek":                             enc45,
	"csiso19latingreek":                             enc45,
	"NF_Z_62-010_(1973)":                            enc46,
	"nf_z_62-010_(1973)":                            enc46,
	"iso-ir-25":                                     enc46,
	"ISO646-FR1":                                    enc46,
	"iso646-fr1":                                    enc46,
	"csISO25French":                                 enc46,
	"csiso25french":                                 enc46,
	"Latin-greek-1":                                 enc47,
	"latin-greek-1":                                 enc47,
	"iso-ir-27":                                     enc47,
	"csISO27LatinGreek1":                            enc47,
	"csiso27latingreek1":                            enc47,
	"ISO_5427":                                      enc48,
	"iso_5427":                                      enc48,
	"iso-ir-37":                                     enc48,
	"csISO5427Cyrillic":                             enc48,
	"csiso5427cyrillic":                             enc48,
	"JIS_C6226-1978":                                enc49,
	"jis_c6226-1978":                                enc49,
	"iso-ir-42":                                     enc49,
	"csISO42JISC62261978":                           enc49,
	"csiso42jisc62261978":                           enc49,
	"BS_viewdata":                                   enc50,
	"bs_viewdata":                                   enc50,
	"iso-ir-47":                                     enc50,
	"csISO47BSViewdata":                             enc50,
	"csiso47bsviewdata":                             enc50,
	"INIS":                                          enc51,
	"inis":                                          enc51,
	"iso-ir-49":                                     enc51,
	"csISO49INIS":                                   enc51,
	"csiso49inis":                                   enc51,
	"INIS-8":                                        enc52,
	"inis-8":                                        enc52,
	"iso-ir-50":                                     enc52,
	"csISO50INIS8":                                  enc52,
	"csiso50inis8":                                  enc52,
	"INIS-cyrillic":                                 enc53,
	"inis-cyrillic":                                 enc53,
	"iso-ir-51":                                     enc53,
	"csISO51INISCyrillic":                           enc53,
	"csiso51iniscyrillic":                           enc53,
	"ISO_5427:1981":                                 enc54,
	"iso_5427:1981":                                 enc54,
	"iso-ir-54":                                     enc54,
	"ISO5427Cyrillic1981":                           enc54,
	"iso5427cyrillic1981":                           enc54,
	"csISO54271981":                                 enc54,
	"csiso54271981":                                 enc54,
	"ISO_5428:1980":                                 enc55,
	"iso_5428:1980":                                 enc55,
	"iso-ir-55":                                     enc55,
	"csISO5428Greek":                                enc55,
	"csiso5428greek":                                enc55,
	"GB_1988-80":                                    enc56,
	"gb_1988-80":                                    enc56,
	"iso-ir-57":                                     enc56,
	"cn":                                            enc56,
	"ISO646-CN":                                     enc56,
	"iso646-cn":                                     enc56,
	"csISO57GB1988":                                 enc56,
	"csiso57gb1988":                                 enc56,
	"GB_2312-80":                                    enc57,
	"gb_2312-80":                                    enc57,
	"iso-ir-58":                                     enc57,
	"chinese":                                       enc57,
	"csISO58GB231280":                               enc57,
	"csiso58gb231280":                               enc57,
	"NS_4551-2":                                     enc58,
	"ns_4551-2":                                     enc58,
	"ISO646-NO2":                                    enc58,
	"iso646-no2":                                    enc58,
	"iso-ir-61":                                     enc58,
	"no2":                                           enc58,
	"csISO61Norwegian2":                             enc58,
	"csiso61norwegian2":                             enc58,
	"videotex-suppl":                                enc59,
	"iso-ir-70":                                     enc59,
	"csISO70VideotexSupp1":                          enc59,
	"csiso70videotexsupp1":                          enc59,
	"PT2":                                           enc60,
	"pt2":                                           enc60,
	"iso-ir-84":                                     enc60,
	"ISO646-PT2":                                    enc60,
	"iso646-pt2":                                    enc60,
	"csISO84Portuguese2":                            enc60,
	"csiso84portuguese2":                            enc60,
	"ES2":                                           enc61,
	"es2":                                           enc61,
	"iso-ir-85":                                     enc61,
	"ISO646-ES2":                                    enc61,
	"iso646-es2":                                    enc61,
	"csISO85Spanish2":                               enc61,
	"csiso85spanish2":                               enc61,
	"MSZ_7795.3":                                    enc62,
	"msz_7795.3":                                    enc62,
	"iso-ir-86":                                     enc62,
	"ISO646-HU":                                     enc62,
	"iso646-hu":                                     enc62,
	"hu":                                            enc62,
	"csISO86Hungarian":                              enc62,
	"csiso86hungarian":                              enc62,
	"JIS_C6226-1983":                                enc63,
	"jis_c6226-1983":                                enc63,
	"iso-ir-87":                                     enc63,
	"x0208":                                         enc63,
	"JIS_X0208-1983":                                enc63,
	"jis_x0208-1983":                                enc63,
	"csISO87JISX0208":                               enc63,
	"csiso87jisx0208":                               enc63,
	"greek7":                                        enc64,
	"iso-ir-88":                                     enc64,
	"csISO88Greek7":                                 enc64,
	"csiso88greek7":                                 enc64,
	"ASMO_449":                                      enc65,
	"asmo_449":                                      enc65,
	"ISO_9036":                                      enc65,
	"iso_9036":                                      enc65,
	"arabic7":                                       enc65,
	"iso-ir-89":                                     enc65,
	"csISO89ASMO449":                                enc65,
	"csiso89asmo449":                                enc65,
	"iso-ir-90":                                     enc66,
	"csISO90":                                       enc66,
	"csiso90":                                       enc66,
	"JIS_C6229-1984-a":                              enc67,
	"jis_c6229-1984-a":                              enc67,
	"iso-ir-91":                                     enc67,
	"jp-ocr-a":                                      enc67,
	"csISO91JISC62291984a":                          enc67,
	"csiso91jisc62291984a":                          enc67,
	"JIS_C6229-1984-b":                              enc68,
	"jis_c6229-1984-b":                              enc68,
	"iso-ir-92":                                     enc68,
	"ISO646-JP-OCR-B":                               enc68,
	"iso646-jp-ocr-b":                               enc68,
	"jp-ocr-b":                                      enc68,
	"csISO92JISC62991984b":                          enc68,
	"csiso92jisc62991984b":                          enc68,
	"JIS_C6229-1984-b-add":                          enc69,
	"jis_c6229-1984-b-add":                          enc69,
	"iso-ir-93":                                     enc69,
	"jp-ocr-b-add":                                  enc69,
	"csISO93JIS62291984badd":                        enc69,
	"csiso93jis62291984badd":                        enc69,
	"JIS_C6229-1984-hand":                           enc70,
	"jis_c6229-1984-hand":                           enc70,
	"iso-ir-94":                                     enc70,
	"jp-ocr-hand":                                   enc70,
	"csISO94JIS62291984hand":                        enc70,
	"csiso94jis62291984hand":                        enc70,
	"JIS_C6229-1984-hand-add":                       enc71,
	"jis_c6229-1984-hand-add":                       enc71,
	"iso-ir-95":                                     enc71,
	"jp-ocr-hand-add":                               enc71,
	"csISO95JIS62291984handadd":                     enc71,
	"csiso95jis62291984handadd":                     enc71,
	"JIS_C6229-1984-kana":                           enc72,
	"jis_c6229-1984-kana":                           enc72,
	"iso-ir-96":                                     enc72,
	"csISO96JISC62291984kana":                       enc72,
	"csiso96jisc62291984kana":                       enc72,
	"ISO_2033-1983":                                 enc73,
	"iso_2033-1983":                                 enc73,
	"iso-ir-98":                                     enc73,
	"e13b":                                          enc73,
	"csISO2033":                                     enc73,
	"csiso2033":                                     enc73,
	"ANSI_X3.110-1983":                              enc74,
	"ansi_x3.110-1983":                              enc74,
	"iso-ir-99":                                     enc74,
	"CSA_T500-1983":                                 enc74,
	"csa_t500-1983":                                 enc74,
	"NAPLPS":                                        enc74,
	"naplps":                                        enc74,
	"csISO99NAPLPS":                                 enc74,
	"csiso99naplps":                                 enc74,
	"T.61-7bit":                                     enc75,
	"t.61-7bit":                                     enc75,
	"iso-ir-102":                                    enc75,
	"csISO102T617bit":                               enc75,
	"csiso102t617bit":                               enc75,
	"T.61-8bit":                                     enc76,
	"t.61-8bit":                                     enc76,
	"T.61":                                          enc76,
	"t.61":                                          enc76,
	"iso-ir-103":                                    enc76,
	"csISO103T618bit":                               enc76,
	"csiso103t618bit":                               enc76,
	"ECMA-cyrillic":                                 enc77,
	"ecma-cyrillic":                                 enc77,
	"iso-ir-111":                                    enc77,
	"KOI8-E":                                        enc77,
	"koi8-e":                                        enc77,
	"csISO111ECMACyrillic":                          enc77,
	"csiso111ecmacyrillic":                          enc77,
	"CSA_Z243.4-1985-1":                             enc78,
	"csa_z243.4-1985-1":                             enc78,
	"iso-ir-121":                                    enc78,
	"ISO646-CA":                                     enc78,
	"iso646-ca":                                     enc78,
	"csa7-1":                                        enc78,
	"csa71":                                         enc78,
	"ca":                                            enc78,
	"csISO121Canadian1":                             enc78,
	"csiso121canadian1":                             enc78,
	"CSA_Z243.4-1985-2":                             enc79,
	"csa_z243.4-1985-2":                             enc79,
	"iso-ir-122":                                    enc79,
	"ISO646-CA2":                                    enc79,
	"iso646-ca2":                                    enc79,
	"csa7-2":                                        enc79,
	"csa72":                                         enc79,
	"csISO122Canadian2":                             enc79,
	"csiso122canadian2":                             enc79,
	"CSA_Z243.4-1985-gr":                            enc80,
	"csa_z243.4-1985-gr":                            enc80,
	"iso-ir-123":                                    enc80,
	"csISO123CSAZ24341985gr":                        enc80,
	"csiso123csaz24341985gr":                        enc80,
	"ISO_8859-6-E":                                  enc81,
	"iso_8859-6-e":                                  enc81,
	"csISO88596E":                                   enc81,
	"csiso88596e":                                   enc81,
	"ISO-8859-6-E":                                  enc81,
	"iso-8859-6-e":                                  enc81,
	"ISO_8859-6-I":                                  enc82,
	"iso_8859-6-i":                                  enc82,
	"csISO88596I":                                   enc82,
	"csiso88596i":                                   enc82,
	"ISO-8859-6-I":                                  enc82,
	"iso-8859-6-i":                                  enc82,
	"T.101-G2":                                      enc83,
	"t.101-g2":                                      enc83,
	"iso-ir-128":                                    enc83,
	"csISO128T101G2":                                enc83,
	"csiso128t101g2":                                enc83,
	"ISO_8859-8-E":                                  enc84,
	"iso_8859-8-e":                                  enc84,
	"csISO88598E":                                   enc84,
	"csiso88598e":                                   enc84,
	"ISO-8859-8-E":                                  enc84,
	"iso-8859-8-e":                                  enc84,
	"ISO_8859-8-I":                                  enc85,
	"iso_8859-8-i":                                  enc85,
	"csISO88598I":                                   enc85,
	"csiso88598i":                                   enc85,
	"ISO-8859-8-I":                                  enc85,
	"iso-8859-8-i":                                  enc85,
	"CSN_369103":                                    enc86,
	"csn_369103":                                    enc86,
	"iso-ir-139":                                    enc86,
	"csISO139CSN369103":                             enc86,
	"csiso139csn369103":                             enc86,
	"JUS_I.B1.002":                                  enc87,
	"jus_i.b1.002":                                  enc87,
	"iso-ir-141":                                    enc87,
	"ISO646-YU":                                     enc87,
	"iso646-yu":                                     enc87,
	"js":                                            enc87,
	"yu":                                            enc87,
	"csISO141JUSIB1002":                             enc87,
	"csiso141jusib1002":                             enc87,
	"IEC_P27-1":                                     enc88,
	"iec_p27-1":                                     enc88,
	"iso-ir-143":                                    enc88,
	"csISO143IECP271":                               enc88,
	"csiso143iecp271":                               enc88,
	"JUS_I.B1.003-serb":                             enc89,
	"jus_i.b1.003-serb":                             enc89,
	"iso-ir-146":                                    enc89,
	"serbian":                                       enc89,
	"csISO146Serbian":                               enc89,
	"csiso146serbian":                               enc89,
	"JUS_I.B1.003-mac":                              enc90,
	"jus_i.b1.003-mac":                              enc90,
	"macedonian":                                    enc90,
	"iso-ir-147":                                    enc90,
	"csISO147Macedonian":                            enc90,
	"csiso147macedonian":                            enc90,
	"greek-ccitt":                                   enc91,
	"iso-ir-150":                                    enc91,
	"csISO150":                                      enc91,
	"csiso150":                                      enc91,
	"csISO150GreekCCITT":                            enc91,
	"csiso150greekccitt":                            enc91,
	"NC_NC00-10:81":                                 enc92,
	"nc_nc00-10:81":                                 enc92,
	"cuba":                                          enc92,
	"iso-ir-151":                                    enc92,
	"ISO646-CU":                                     enc92,
	"iso646-cu":                                     enc92,
	"csISO151Cuba":                                  enc92,
	"csiso151cuba":                                  enc92,
	"ISO_6937-2-25":                                 enc93,
	"iso_6937-2-25":                                 enc93,
	"iso-ir-152":                                    enc93,
	"csISO6937Add":                                  enc93,
	"csiso6937add":                                  enc93,
	"GOST_19768-74":                                 enc94,
	"gost_19768-74":                                 enc94,
	"ST_SEV_358-88":                                 enc94,
	"st_sev_358-88":                                 enc94,
	"iso-ir-153":                                    enc94,
	"csISO153GOST1976874":                           enc94,
	"csiso153gost1976874":                           enc94,
	"ISO_8859-supp":                                 enc95,
	"iso_8859-supp":                                 enc95,
	"iso-ir-154":                                    enc95,
	"latin1-2-5":                                    enc95,
	"csISO8859Supp":                                 enc95,
	"csiso8859supp":                                 enc95,
	"ISO_10367-box":                                 enc96,
	"iso_10367-box":                                 enc96,
	"iso-ir-155":                                    enc96,
	"csISO10367Box":                                 enc96,
	"csiso10367box":                                 enc96,
	"latin-lap":                                     enc97,
	"lap":                                           enc97,
	"iso-ir-158":                                    enc97,
	"csISO158Lap":                                   enc97,
	"csiso158lap":                                   enc97,
	"JIS_X0212-1990":                                enc98,
	"jis_x0212-1990":                                enc98,
	"x0212":                                         enc98,
	"iso-ir-159":                                    enc98,
	"csISO159JISX02121990":                          enc98,
	"csiso159jisx02121990":                          enc98,
	"DS_2089":                                       enc99,
	"ds_2089":                                       enc99,
	"DS2089":                                        enc99,
	"ds2089":                                        enc99,
	"ISO646-DK":                                     enc99,
	"iso646-dk":                                     enc99,
	"dk":                                            enc99,
	"csISO646Danish":                                enc99,
	"csiso646danish":                                enc99,
	"us-dk":                                         enc100,
	"csUSDK":                                        enc100,
	"csusdk":                                        enc100,
	"dk-us":                                         enc101,
	"csDKUS":                                        enc101,
	"csdkus":                                        enc101,
	"KSC5636":                                       enc102,
	"ksc5636":                                       enc102,
	"ISO646-KR":                                     enc102,
	"iso646-kr":                                     enc102,
	"csKSC5636":                                     enc102,
	"csksc5636":                                     enc102,
	"UNICODE-1-1-UTF-7":                             enc103,
	"unicode-1-1-utf-7":                             enc103,
	"csUnicode11UTF7":                               enc103,
	"csunicode11utf7":                               enc103,
	"ISO-2022-CN":                                   enc104,
	"iso-2022-cn":                                   enc104,
	"csISO2022CN":                                   enc104,
	"csiso2022cn":                                   enc104,
	"ISO-2022-CN-EXT":                               enc105,
	"iso-2022-cn-ext":                               enc105,
	"csISO2022CNEXT":                                enc105,
	"csiso2022cnext":                                enc105,
	"UTF-8":                                         enc106,
	"utf-8":                                         enc106,
	"csUTF8":                                        enc106,
	"csutf8":                                        enc106,
	"ISO-8859-13":                                   enc109,
	"iso-8859-13":                                   enc109,
	"csISO885913":                                   enc109,
	"csiso885913":                                   enc109,
	"ISO-8859-14":                                   enc110,
	"iso-8859-14":                                   enc110,
	"iso-ir-199":                                    enc110,
	"ISO_8859-14:1998":                              enc110,
	"iso_8859-14:1998":                              enc110,
	"ISO_8859-14":                                   enc110,
	"iso_8859-14":                                   enc110,
	"latin8":                                        enc110,
	"iso-celtic":                                    enc110,
	"l8":                                            enc110,
	"csISO885914":                                   enc110,
	"csiso885914":                                   enc110,
	"ISO-8859-15":                                   enc111,
	"iso-8859-15":                                   enc111,
	"ISO_8859-15":                                   enc111,
	"iso_8859-15":                                   enc111,
	"Latin-9":                                       enc111,
	"latin-9":                                       enc111,
	"csISO885915":                                   enc111,
	"csiso885915":                                   enc111,
	"ISO-8859-16":                                   enc112,
	"iso-8859-16":                                   enc112,
	"iso-ir-226":                                    enc112,
	"ISO_8859-16:2001":                              enc112,
	"iso_8859-16:2001":                              enc112,
	"ISO_8859-16":                                   enc112,
	"iso_8859-16":                                   enc112,
	"latin10":                                       enc112,
	"l10":                                           enc112,
	"csISO885916":                                   enc112,
	"csiso885916":                                   enc112,
	"GBK":                                           enc113,
	"gbk":                                           enc113,
	"CP936":                                         enc113,
	"cp936":                                         enc113,
	"MS936":                                         enc113,
	"ms936":                                         enc113,
	"windows-936":                                   enc113,
	"csGBK":                                         enc113,
	"csgbk":                                         enc113,
	"GB18030":                                       enc114,
	"gb18030":                                       enc114,
	"csGB18030":                                     enc114,
	"csgb18030":                                     enc114,
	"OSD_EBCDIC_DF04_15":                            enc115,
	"osd_ebcdic_df04_15":                            enc115,
	"csOSDEBCDICDF0415":                             enc115,
	"csosdebcdicdf0415":                             enc115,
	"OSD_EBCDIC_DF03_IRV":                           enc116,
	"osd_ebcdic_df03_irv":                           enc116,
	"csOSDEBCDICDF03IRV":                            enc116,
	"csosdebcdicdf03irv":                            enc116,
	"OSD_EBCDIC_DF04_1":                             enc117,
	"osd_ebcdic_df04_1":                             enc117,
	"csOSDEBCDICDF041":                              enc117,
	"csosdebcdicdf041":                              enc117,
	"ISO-11548-1":                                   enc118,
	"iso-11548-1":                                   enc118,
	"ISO_11548-1":                                   enc118,
	"iso_11548-1":                                   enc118,
	"ISO_TR_11548-1":                                enc118,
	"iso_tr_11548-1":                                enc118,
	"csISO115481":                                   enc118,
	"csiso115481":                                   enc118,
	"KZ-1048":                                       enc119,
	"kz-1048":                                       enc119,
	"STRK1048-2002":                                 enc119,
	"strk1048-2002":                                 enc119,
	"RK1048":                                        enc119,
	"rk1048":                                        enc119,
	"csKZ1048":                                      enc119,
	"cskz1048":                                      enc119,
	"ISO-10646-UCS-2":                               enc1000,
	"iso-10646-ucs-2":                               enc1000,
	"csUnicode":                                     enc1000,
	"csunicode":                                     enc1000,
	"ISO-10646-UCS-4":                               enc1001,
	"iso-10646-ucs-4":                               enc1001,
	"csUCS4":                                        enc1001,
	"csucs4":                                        enc1001,
	"ISO-10646-UCS-Basic":                           enc1002,
	"iso-10646-ucs-basic":                           enc1002,
	"csUnicodeASCII":                                enc1002,
	"csunicodeascii":                                enc1002,
	"ISO-10646-Unicode-Latin1":                      enc1003,
	"iso-10646-unicode-latin1":                      enc1003,
	"csUnicodeLatin1":                               enc1003,
	"csunicodelatin1":                               enc1003,
	"ISO-10646":                                     enc1003,
	"iso-10646":                                     enc1003,
	"ISO-10646-J-1":                                 enc1004,
	"iso-10646-j-1":                                 enc1004,
	"csUnicodeJapanese":                             enc1004,
	"csunicodejapanese":                             enc1004,
	"ISO-Unicode-IBM-1261":                          enc1005,
	"iso-unicode-ibm-1261":                          enc1005,
	"csUnicodeIBM1261":                              enc1005,
	"csunicodeibm1261":                              enc1005,
	"ISO-Unicode-IBM-1268":                          enc1006,
	"iso-unicode-ibm-1268":                          enc1006,
	"csUnicodeIBM1268":                              enc1006,
	"csunicodeibm1268":                              enc1006,
	"ISO-Unicode-IBM-1276":                          enc1007,
	"iso-unicode-ibm-1276":                          enc1007,
	"csUnicodeIBM1276":                              enc1007,
	"csunicodeibm1276":                              enc1007,
	"ISO-Unicode-IBM-1264":                          enc1008,
	"iso-unicode-ibm-1264":                          enc1008,
	"csUnicodeIBM1264":                              enc1008,
	"csunicodeibm1264":                              enc1008,
	"ISO-Unicode-IBM-1265":                          enc1009,
	"iso-unicode-ibm-1265":                          enc1009,
	"csUnicodeIBM1265":                              enc1009,
	"csunicodeibm1265":                              enc1009,
	"UNICODE-1-1":                                   enc1010,
	"unicode-1-1":                                   enc1010,
	"csUnicode11":                                   enc1010,
	"csunicode11":                                   enc1010,
	"SCSU":                                          enc1011,
	"scsu":                                          enc1011,
	"csSCSU":                                        enc1011,
	"csscsu":                                        enc1011,
	"UTF-7":                                         enc1012,
	"utf-7":                                         enc1012,
	"csUTF7":                                        enc1012,
	"csutf7":                                        enc1012,
	"UTF-16BE":                                      enc1013,
	"utf-16be":                                      enc1013,
	"csUTF16BE":                                     enc1013,
	"csutf16be":                                     enc1013,
	"UTF-16LE":                                      enc1014,
	"utf-16le":                                      enc1014,
	"csUTF16LE":                                     enc1014,
	"csutf16le":                                     enc1014,
	"UTF-16":                                        enc1015,
	"utf-16":                                        enc1015,
	"csUTF16":                                       enc1015,
	"csutf16":                                       enc1015,
	"CESU-8":                                        enc1016,
	"cesu-8":                                        enc1016,
	"csCESU8":                                       enc1016,
	"cscesu8":                                       enc1016,
	"csCESU-8":                                      enc1016,
	"cscesu-8":                                      enc1016,
	"UTF-32":                                        enc1017,
	"utf-32":                                        enc1017,
	"csUTF32":                                       enc1017,
	"csutf32":                                       enc1017,
	"UTF-32BE":                                      enc1018,
	"utf-32be":                                      enc1018,
	"csUTF32BE":                                     enc1018,
	"csutf32be":                                     enc1018,
	"UTF-32LE":                                      enc1019,
	"utf-32le":                                      enc1019,
	"csUTF32LE":                                     enc1019,
	"csutf32le":                                     enc1019,
	"BOCU-1":                                        enc1020,
	"bocu-1":                                        enc1020,
	"csBOCU1":                                       enc1020,
	"csbocu1":                                       enc1020,
	"csBOCU-1":                                      enc1020,
	"csbocu-1":                                      enc1020,
	"ISO-8859-1-Windows-3.0-Latin-1":                enc2000,
	"iso-8859-1-windows-3.0-latin-1":                enc2000,
	"csWindows30Latin1":                             enc2000,
	"cswindows30latin1":                             enc2000,
	"ISO-8859-1-Windows-3.1-Latin-1":                enc2001,
	"iso-8859-1-windows-3.1-latin-1":                enc2001,
	"csWindows31Latin1":                             enc2001,
	"cswindows31latin1":                             enc2001,
	"ISO-8859-2-Windows-Latin-2":                    enc2002,
	"iso-8859-2-windows-latin-2":                    enc2002,
	"csWindows31Latin2":                             enc2002,
	"cswindows31latin2":                             enc2002,
	"ISO-8859-9-Windows-Latin-5":                    enc2003,
	"iso-8859-9-windows-latin-5":                    enc2003,
	"csWindows31Latin5":                             enc2003,
	"cswindows31latin5":                             enc2003,
	"hp-roman8":                                     enc2004,
	"roman8":                                        enc2004,
	"r8":                                            enc2004,
	"csHPRoman8":                                    enc2004,
	"cshproman8":                                    enc2004,
	"Adobe-Standard-Encoding":                       enc2005,
	"adobe-standard-encoding":                       enc2005,
	"csAdobeStandardEncoding":                       enc2005,
	"csadobestandardencoding":                       enc2005,
	"Ventura-US":                                    enc2006,
	"ventura-us":                                    enc2006,
	"csVenturaUS":                                   enc2006,
	"csventuraus":                                   enc2006,
	"Ventura-International":                         enc2007,
	"ventura-international":                         enc2007,
	"csVenturaInternational":                        enc2007,
	"csventurainternational":                        enc2007,
	"DEC-MCS":                                       enc2008,
	"dec-mcs":                                       enc2008,
	"dec":                                           enc2008,
	"csDECMCS":                                      enc2008,
	"csdecmcs":                                      enc2008,
	"IBM850":                                        enc2009,
	"ibm850":                                        enc2009,
	"cp850":                                         enc2009,
	"850":                                           enc2009,
	"csPC850Multilingual":                           enc2009,
	"cspc850multilingual":                           enc2009,
	"PC8-Danish-Norwegian":                          enc2012,
	"pc8-danish-norwegian":                          enc2012,
	"csPC8DanishNorwegian":                          enc2012,
	"cspc8danishnorwegian":                          enc2012,
	"IBM862":                                        enc2013,
	"ibm862":                                        enc2013,
	"cp862":                                         enc2013,
	"862":                                           enc2013,
	"csPC862LatinHebrew":                            enc2013,
	"cspc862latinhebrew":                            enc2013,
	"PC8-Turkish":                                   enc2014,
	"pc8-turkish":                                   enc2014,
	"csPC8Turkish":                                  enc2014,
	"cspc8turkish":                                  enc2014,
	"IBM-Symbols":                                   enc2015,
	"ibm-symbols":                                   enc2015,
	"csIBMSymbols":                                  enc2015,
	"csibmsymbols":                                  enc2015,
	"IBM-Thai":                                      enc2016,
	"ibm-thai":                                      enc2016,
	"csIBMThai":                                     enc2016,
	"csibmthai":                                     enc2016,
	"HP-Legal":                                      enc2017,
	"hp-legal":                                      enc2017,
	"csHPLegal":                                     enc2017,
	"cshplegal":                                     enc2017,
	"HP-Pi-font":                                    enc2018,
	"hp-pi-font":                                    enc2018,
	"csHPPiFont":                                    enc2018,
	"cshppifont":                                    enc2018,
	"HP-Math8":                                      enc2019,
	"hp-math8":                                      enc2019,
	"csHPMath8":                                     enc2019,
	"cshpmath8":                                     enc2019,
	"Adobe-Symbol-Encoding":                         enc2020,
	"adobe-symbol-encoding":                         enc2020,
	"csHPPSMath":                                    enc2020,
	"cshppsmath":                                    enc2020,
	"HP-DeskTop":                                    enc2021,
	"hp-desktop":                                    enc2021,
	"csHPDesktop":                                   enc2021,
	"cshpdesktop":                                   enc2021,
	"Ventura-Math":                                  enc2022,
	"ventura-math":                                  enc2022,
	"csVenturaMath":                                 enc2022,
	"csventuramath":                                 enc2022,
	"Microsoft-Publishing":                          enc2023,
	"microsoft-publishing":                          enc2023,
	"csMicrosoftPublishing":                         enc2023,
	"csmicrosoftpublishing":                         enc2023,
	"Windows-31J":                                   enc2024,
	"windows-31j":                                   enc2024,
	"csWindows31J":                                  enc2024,
	"cswindows31j":                                  enc2024,
	"GB2312":                                        enc2025,
	"gb2312":                                        enc2025,
	"csGB2312":                                      enc2025,
	"csgb2312":                                      enc2025,
	"Big5":                                          enc2026,
	"big5":                                          enc2026,
	"csBig5":                                        enc2026,
	"csbig5":                                        enc2026,
	"macintosh":                                     enc2027,
	"mac":                                           enc2027,
	"csMacintosh":                                   enc2027,
	"csmacintosh":                                   enc2027,
	"IBM037":                                        enc2028,
	"ibm037":                                        enc2028,
	"cp037":                                         enc2028,
	"ebcdic-cp-us":                                  enc2028,
	"ebcdic-cp-ca":                                  enc2028,
	"ebcdic-cp-wt":                                  enc2028,
	"ebcdic-cp-nl":                                  enc2028,
	"csIBM037":                                      enc2028,
	"csibm037":                                      enc2028,
	"IBM038":                                        enc2029,
	"ibm038":                                        enc2029,
	"EBCDIC-INT":                                    enc2029,
	"ebcdic-int":                                    enc2029,
	"cp038":                                         enc2029,
	"csIBM038":                                      enc2029,
	"csibm038":                                      enc2029,
	"IBM273":                                        enc2030,
	"ibm273":                                        enc2030,
	"CP273":                                         enc2030,
	"cp273":                                         enc2030,
	"csIBM273":                                      enc2030,
	"csibm273":                                      enc2030,
	"IBM274":                                        enc2031,
	"ibm274":                                        enc2031,
	"EBCDIC-BE":                                     enc2031,
	"ebcdic-be":                                     enc2031,
	"CP274":                                         enc2031,
	"cp274":                                         enc2031,
	"csIBM274":                                      enc2031,
	"csibm274":                                      enc2031,
	"IBM275":                                        enc2032,
	"ibm275":                                        enc2032,
	"EBCDIC-BR":                                     enc2032,
	"ebcdic-br":                                     enc2032,
	"cp275":                                         enc2032,
	"csIBM275":                                      enc2032,
	"csibm275":                                      enc2032,
	"IBM277":                                        enc2033,
	"ibm277":                                        enc2033,
	"EBCDIC-CP-DK":                                  enc2033,
	"ebcdic-cp-dk":                                  enc2033,
	"EBCDIC-CP-NO":                                  enc2033,
	"ebcdic-cp-no":                                  enc2033,
	"csIBM277":                                      enc2033,
	"csibm277":                                      enc2033,
	"IBM278":                                        enc2034,
	"ibm278":                                        enc2034,
	"CP278":                                         enc2034,
	"cp278":                                         enc2034,
	"ebcdic-cp-fi":                                  enc2034,
	"ebcdic-cp-se":                                  enc2034,
	"csIBM278":                                      enc2034,
	"csibm278":                                      enc2034,
	"IBM280":                                        enc2035,
	"ibm280":                                        enc2035,
	"CP280":                                         enc2035,
	"cp280":                                         enc2035,
	"ebcdic-cp-it":                                  enc2035,
	"csIBM280":                                      enc2035,
	"csibm280":                                      enc2035,
	"IBM281":                                        enc2036,
	"ibm281":                                        enc2036,
	"EBCDIC-JP-E":                                   enc2036,
	"ebcdic-jp-e":                                   enc2036,
	"cp281":                                         enc2036,
	"csIBM281":                                      enc2036,
	"csibm281":                                      enc2036,
	"IBM284":                                        enc2037,
	"ibm284":                                        enc2037,
	"CP284":                                         enc2037,
	"cp284":                                         enc2037,
	"ebcdic-cp-es":                                  enc2037,
	"csIBM284":                                      enc2037,
	"csibm284":                                      enc2037,
	"IBM285":                                        enc2038,
	"ibm285":                                        enc2038,
	"CP285":                                         enc2038,
	"cp285":                                         enc2038,
	"ebcdic-cp-gb":                                  enc2038,
	"csIBM285":                                      enc2038,
	"csibm285":                                      enc2038,
	"IBM290":                                        enc2039,
	"ibm290":                                        enc2039,
	"cp290":                                         enc2039,
	"EBCDIC-JP-kana":                                enc2039,
	"ebcdic-jp-kana":                                enc2039,
	"csIBM290":                                      enc2039,
	"csibm290":                                      enc2039,
	"IBM297":                                        enc2040,
	"ibm297":                                        enc2040,
	"cp297":                                         enc2040,
	"ebcdic-cp-fr":                                  enc2040,
	"csIBM297":                                      enc2040,
	"csibm297":                                      enc2040,
	"IBM420":                                        enc2041,
	"ibm420":                                        enc2041,
	"cp420":                                         enc2041,
	"ebcdic-cp-ar1":                                 enc2041,
	"csIBM420":                                      enc2041,
	"csibm420":                                      enc2041,
	"IBM423":                                        enc2042,
	"ibm423":                                        enc2042,
	"cp423":                                         enc2042,
	"ebcdic-cp-gr":                                  enc2042,
	"csIBM423":                                      enc2042,
	"csibm423":                                      enc2042,
	"IBM424":                                        enc2043,
	"ibm424":                                        enc2043,
	"cp424":                                         enc2043,
	"ebcdic-cp-he":                                  enc2043,
	"csIBM424":                                      enc2043,
	"csibm424":                                      enc2043,
	"IBM437":                                        enc2011,
	"ibm437":                                        enc2011,
	"cp437":                                         enc2011,
	"437":                                           enc2011,
	"csPC8CodePage437":                              enc2011,
	"cspc8codepage437":                              enc2011,
	"IBM500":                                        enc2044,
	"ibm500":                                        enc2044,
	"CP500":                                         enc2044,
	"cp500":                                         enc2044,
	"ebcdic-cp-be":                                  enc2044,
	"ebcdic-cp-ch":                                  enc2044,
	"csIBM500":                                      enc2044,
	"csibm500":                                      enc2044,
	"IBM851":                                        enc2045,
	"ibm851":                                        enc2045,
	"cp851":                                         enc2045,
	"851":                                           enc2045,
	"csIBM851":                                      enc2045,
	"csibm851":                                      enc2045,
	"IBM852":                                        enc2010,
	"ibm852":                                        enc2010,
	"cp852":                                         enc2010,
	"852":                                           enc2010,
	"csPCp852":                                      enc2010,
	"cspcp852":                                      enc2010,
	"IBM855":                                        enc2046,
	"ibm855":                                        enc2046,
	"cp855":                                         enc2046,
	"855":                                           enc2046,
	"csIBM855":                                      enc2046,
	"csibm855":                                      enc2046,
	"IBM857":                                        enc2047,
	"ibm857":                                        enc2047,
	"cp857":                                         enc2047,
	"857":                                           enc2047,
	"csIBM857":                                      enc2047,
	"csibm857":                                      enc2047,
	"IBM860":                                        enc2048,
	"ibm860":                                        enc2048,
	"cp860":                                         enc2048,
	"860":                                           enc2048,
	"csIBM860":                                      enc2048,
	"csibm860":                                      enc2048,
	"IBM861":                                        enc2049,
	"ibm861":                                        enc2049,
	"cp861":                                         enc2049,
	"861":                                           enc2049,
	"cp-is":                                         enc2049,
	"csIBM861":                                      enc2049,
	"csibm861":                                      enc2049,
	"IBM863":                                        enc2050,
	"ibm863":                                        enc2050,
	"cp863":                                         enc2050,
	"863":                                           enc2050,
	"csIBM863":                                      enc2050,
	"csibm863":                                      enc2050,
	"IBM864":                                        enc2051,
	"ibm864":                                        enc2051,
	"cp864":                                         enc2051,
	"csIBM864":                                      enc2051,
	"csibm864":                                      enc2051,
	"IBM865":                                        enc2052,
	"ibm865":                                        enc2052,
	"cp865":                                         enc2052,
	"865":                                           enc2052,
	"csIBM865":                                      enc2052,
	"csibm865":                                      enc2052,
	"IBM868":                                        enc2053,
	"ibm868":                                        enc2053,
	"CP868":                                         enc2053,
	"cp868":                                         enc2053,
	"cp-ar":                                         enc2053,
	"csIBM868":                                      enc2053,
	"csibm868":                                      enc2053,
	"IBM869":                                        enc2054,
	"ibm869":                                        enc2054,
	"cp869":                                         enc2054,
	"869":                                           enc2054,
	"cp-gr":                                         enc2054,
	"csIBM869":                                      enc2054,
	"csibm869":                                      enc2054,
	"IBM870":                                        enc2055,
	"ibm870":                                        enc2055,
	"CP870":                                         enc2055,
	"cp870":                                         enc2055,
	"ebcdic-cp-roece":                               enc2055,
	"ebcdic-cp-yu":                                  enc2055,
	"csIBM870":                                      enc2055,
	"csibm870":                                      enc2055,
	"IBM871":                                        enc2056,
	"ibm871":                                        enc2056,
	"CP871":                                         enc2056,
	"cp871":                                         enc2056,
	"ebcdic-cp-is":                                  enc2056,
	"csIBM871":                                      enc2056,
	"csibm871":                                      enc2056,
	"IBM880":                                        enc2057,
	"ibm880":                                        enc2057,
	"cp880":                                         enc2057,
	"EBCDIC-Cyrillic":                               enc2057,
	"ebcdic-cyrillic":                               enc2057,
	"csIBM880":                                      enc2057,
	"csibm880":                                      enc2057,
	"IBM891":                                        enc2058,
	"ibm891":                                        enc2058,
	"cp891":                                         enc2058,
	"csIBM891":                                      enc2058,
	"csibm891":                                      enc2058,
	"IBM903":                                        enc2059,
	"ibm903":                                        enc2059,
	"cp903":                                         enc2059,
	"csIBM903":                                      enc2059,
	"csibm903":                                      enc2059,
	"IBM904":                                        enc2060,
	"ibm904":                                        enc2060,
	"cp904":                                         enc2060,
	"904":                                           enc2060,
	"csIBBM904":                                     enc2060,
	"csibbm904":                                     enc2060,
	"IBM905":                                        enc2061,
	"ibm905":                                        enc2061,
	"CP905":                                         enc2061,
	"cp905":                                         enc2061,
	"ebcdic-cp-tr":                                  enc2061,
	"csIBM905":                                      enc2061,
	"csibm905":                                      enc2061,
	"IBM918":                                        enc2062,
	"ibm918":                                        enc2062,
	"CP918":                                         enc2062,
	"cp918":                                         enc2062,
	"ebcdic-cp-ar2":                                 enc2062,
	"csIBM918":                                      enc2062,
	"csibm918":                                      enc2062,
	"IBM1026":                                       enc2063,
	"ibm1026":                                       enc2063,
	"CP1026":                                        enc2063,
	"cp1026":                                        enc2063,
	"csIBM1026":                                     enc2063,
	"csibm1026":                                     enc2063,
	"EBCDIC-AT-DE":                                  enc2064,
	"ebcdic-at-de":                                  enc2064,
	"csIBMEBCDICATDE":                               enc2064,
	"csibmebcdicatde":                               enc2064,
	"EBCDIC-AT-DE-A":                                enc2065,
	"ebcdic-at-de-a":                                enc2065,
	"csEBCDICATDEA":                                 enc2065,
	"csebcdicatdea":                                 enc2065,
	"EBCDIC-CA-FR":                                  enc2066,
	"ebcdic-ca-fr":                                  enc2066,
	"csEBCDICCAFR":                                  enc2066,
	"csebcdiccafr":                                  enc2066,
	"EBCDIC-DK-NO":                                  enc2067,
	"ebcdic-dk-no":                                  enc2067,
	"csEBCDICDKNO":                                  enc2067,
	"csebcdicdkno":                                  enc2067,
	"EBCDIC-DK-NO-A":                                enc2068,
	"ebcdic-dk-no-a":                                enc2068,
	"csEBCDICDKNOA":                                 enc2068,
	"csebcdicdknoa":                                 enc2068,
	"EBCDIC-FI-SE":                                  enc2069,
	"ebcdic-fi-se":                                  enc2069,
	"csEBCDICFISE":                                  enc2069,
	"csebcdicfise":                                  enc2069,
	"EBCDIC-FI-SE-A":                                enc2070,
	"ebcdic-fi-se-a":                                enc2070,
	"csEBCDICFISEA":                                 enc2070,
	"csebcdicfisea":                                 enc2070,
	"EBCDIC-FR":                                     enc2071,
	"ebcdic-fr":                                     enc2071,
	"csEBCDICFR":                                    enc2071,
	"csebcdicfr":                                    enc2071,
	"EBCDIC-IT":                                     enc2072,
	"ebcdic-it":                                     enc2072,
	"csEBCDICIT":                                    enc2072,
	"csebcdicit":                                    enc2072,
	"EBCDIC-PT":                                     enc2073,
	"ebcdic-pt":                                     enc2073,
	"csEBCDICPT":                                    enc2073,
	"csebcdicpt":                                    enc2073,
	"EBCDIC-ES":                                     enc2074,
	"ebcdic-es":                                     enc2074,
	"csEBCDICES":                                    enc2074,
	"csebcdices":                                    enc2074,
	"EBCDIC-ES-A":                                   enc2075,
	"ebcdic-es-a":                                   enc2075,
	"csEBCDICESA":                                   enc2075,
	"csebcdicesa":                                   enc2075,
	"EBCDIC-ES-S":                                   enc2076,
	"ebcdic-es-s":                                   enc2076,
	"csEBCDICESS":                                   enc2076,
	"csebcdicess":                                   enc2076,
	"EBCDIC-UK":                                     enc2077,
	"ebcdic-uk":                                     enc2077,
	"csEBCDICUK":                                    enc2077,
	"csebcdicuk":                                    enc2077,
	"EBCDIC-US":                                     enc2078,
	"ebcdic-us":                                     enc2078,
	"csEBCDICUS":                                    enc2078,
	"csebcdicus":                                    enc2078,
	"UNKNOWN-8BIT":                                  enc2079,
	"unknown-8bit":                                  enc2079,
	"csUnknown8BiT":                                 enc2079,
	"csunknown8bit":                                 enc2079,
	"MNEMONIC":                                      enc2080,
	"mnemonic":                                      enc2080,
	"csMnemonic":                                    enc2080,
	"csmnemonic":                                    enc2080,
	"MNEM":                                          enc2081,
	"mnem":                                          enc2081,
	"csMnem":                                        enc2081,
	"csmnem":                                        enc2081,
	"VISCII":                                        enc2082,
	"viscii":                                        enc2082,
	"csVISCII":                                      enc2082,
	"csviscii":                                      enc2082,
	"VIQR":                                          enc2083,
	"viqr":                                          enc2083,
	"csVIQR":                                        enc2083,
	"csviqr":                                        enc2083,
	"KOI8-R":                                        enc2084,
	"koi8-r":                                        enc2084,
	"csKOI8R":                                       enc2084,
	"cskoi8r":                                       enc2084,
	"HZ-GB-2312":                                    enc2085,
	"hz-gb-2312":                                    enc2085,
	"IBM866":                                        enc2086,
	"ibm866":                                        enc2086,
	"cp866":                                         enc2086,
	"866":                                           enc2086,
	"csIBM866":                                      enc2086,
	"csibm866":                                      enc2086,
	"IBM775":                                        enc2087,
	"ibm775":                                        enc2087,
	"cp775":                                         enc2087,
	"csPC775Baltic":                                 enc2087,
	"cspc775baltic":                                 enc2087,
	"KOI8-U":                                        enc2088,
	"koi8-u":                                        enc2088,
	"csKOI8U":                                       enc2088,
	"cskoi8u":                                       enc2088,
	"IBM00858":                                      enc2089,
	"ibm00858":                                      enc2089,
	"CCSID00858":                                    enc2089,
	"ccsid00858":                                    enc2089,
	"CP00858":                                       enc2089,
	"cp00858":                                       enc2089,
	"PC-Multilingual-850+euro":                      enc2089,
	"pc-multilingual-850+euro":                      enc2089,
	"csIBM00858":                                    enc2089,
	"csibm00858":                                    enc2089,
	"IBM00924":                                      enc2090,
	"ibm00924":                                      enc2090,
	"CCSID00924":                                    enc2090,
	"ccsid00924":                                    enc2090,
	"CP00924":                                       enc2090,
	"cp00924":                                       enc2090,
	"ebcdic-Latin9--euro":                           enc2090,
	"ebcdic-latin9--euro":                           enc2090,
	"csIBM00924":                                    enc2090,
	"csibm00924":                                    enc2090,
	"IBM01140":                                      enc2091,
	"ibm01140":                                      enc2091,
	"CCSID01140":                                    enc2091,
	"ccsid01140":                                    enc2091,
	"CP01140":                                       enc2091,
	"cp01140":                                       enc2091,
	"ebcdic-us-37+euro":                             enc2091,
	"csIBM01140":                                    enc2091,
	"csibm01140":                                    enc2091,
	"IBM01141":                                      enc2092,
	"ibm01141":                                      enc2092,
	"CCSID01141":                                    enc2092,
	"ccsid01141":                                    enc2092,
	"CP01141":                                       enc2092,
	"cp01141":                                       enc2092,
	"ebcdic-de-273+euro":                            enc2092,
	"csIBM01141":                                    enc2092,
	"csibm01141":                                    enc2092,
	"IBM01142":                                      enc2093,
	"ibm01142":                                      enc2093,
	"CCSID01142":                                    enc2093,
	"ccsid01142":                                    enc2093,
	"CP01142":                                       enc2093,
	"cp01142":                                       enc2093,
	"ebcdic-dk-277+euro":                            enc2093,
	"ebcdic-no-277+euro":                            enc2093,
	"csIBM01142":                                    enc2093,
	"csibm01142":                                    enc2093,
	"IBM01143":                                      enc2094,
	"ibm01143":                                      enc2094,
	"CCSID01143":                                    enc2094,
	"ccsid01143":                                    enc2094,
	"CP01143":                                       enc2094,
	"cp01143":                                       enc2094,
	"ebcdic-fi-278+euro":                            enc2094,
	"ebcdic-se-278+euro":                            enc2094,
	"csIBM01143":                                    enc2094,
	"csibm01143":                                    enc2094,
	"IBM01144":                                      enc2095,
	"ibm01144":                                      enc2095,
	"CCSID01144":                                    enc2095,
	"ccsid01144":                                    enc2095,
	"CP01144":                                       enc2095,
	"cp01144":                                       enc2095,
	"ebcdic-it-280+euro":                            enc2095,
	"csIBM01144":                                    enc2095,
	"csibm01144":                                    enc2095,
	"IBM01145":                                      enc2096,
	"ibm01145":                                      enc2096,
	"CCSID01145":                                    enc2096,
	"ccsid01145":                                    enc2096,
	"CP01145":                                       enc2096,
	"cp01145":                                       enc2096,
	"ebcdic-es-284+euro":                            enc2096,
	"csIBM01145":                                    enc2096,
	"csibm01145":                                    enc2096,
	"IBM01146":                                      enc2097,
	"ibm01146":                                      enc2097,
	"CCSID01146":                                    enc2097,
	"ccsid01146":                                    enc2097,
	"CP01146":                                       enc2097,
	"cp01146":                                       enc2097,
	"ebcdic-gb-285+euro":                            enc2097,
	"csIBM01146":                                    enc2097,
	"csibm01146":                                    enc2097,
	"IBM01147":                                      enc2098,
	"ibm01147":                                      enc2098,
	"CCSID01147":                                    enc2098,
	"ccsid01147":                                    enc2098,
	"CP01147":                                       enc2098,
	"cp01147":                                       enc2098,
	"ebcdic-fr-297+euro":                            enc2098,
	"csIBM01147":                                    enc2098,
	"csibm01147":                                    enc2098,
	"IBM01148":                                      enc2099,
	"ibm01148":                                      enc2099,
	"CCSID01148":                                    enc2099,
	"ccsid01148":                                    enc2099,
	"CP01148":                                       enc2099,
	"cp01148":                                       enc2099,
	"ebcdic-international-500+euro":                 enc2099,
	"csIBM01148":                                    enc2099,
	"csibm01148":                                    enc2099,
	"IBM01149":                                      enc2100,
	"ibm01149":                                      enc2100,
	"CCSID01149":                                    enc2100,
	"ccsid01149":                                    enc2100,
	"CP01149":                                       enc2100,
	"cp01149":                                       enc2100,
	"ebcdic-is-871+euro":                            enc2100,
	"csIBM01149":                                    enc2100,
	"csibm01149":                                    enc2100,
	"Big5-HKSCS":                                    enc2101,
	"big5-hkscs":                                    enc2101,
	"csBig5HKSCS":                                   enc2101,
	"csbig5hkscs":                                   enc2101,
	"IBM1047":                                       enc2102,
	"ibm1047":                                       enc2102,
	"IBM-1047":                                      enc2102,
	"ibm-1047":                                      enc2102,
	"csIBM1047":                                     enc2102,
	"csibm1047":                                     enc2102,
	"PTCP154":                                       enc2103,
	"ptcp154":                                       enc2103,
	"csPTCP154":                                     enc2103,
	"csptcp154":                                     enc2103,
	"PT154":                                         enc2103,
	"pt154":                                         enc2103,
	"CP154":                                         enc2103,
	"cp154":                                         enc2103,
	"Cyrillic-Asian":                                enc2103,
	"cyrillic-asian":                                enc2103,
	"Amiga-1251":                                    enc2104,
	"amiga-1251":                                    enc2104,
	"Ami1251":                                       enc2104,
	"ami1251":                                       enc2104,
	"Amiga1251":                                     enc2104,
	"amiga1251":                                     enc2104,
	"Ami-1251":                                      enc2104,
	"ami-1251":                                      enc2104,
	"csAmiga1251\n(Aliases":                         enc2104,
	"csamiga1251\n(aliases":                         enc2104,
	"KOI7-switched":                                 enc2105,
	"koi7-switched":                                 enc2105,
	"csKOI7switched":                                enc2105,
	"cskoi7switched":                                enc2105,
	"BRF":                                           enc2106,
	"brf":                                           enc2106,
	"csBRF":                                         enc2106,
	"csbrf":                                         enc2106,
	"TSCII":                                         enc2107,
	"tscii":                                         enc2107,
	"csTSCII":                                       enc2107,
	"cstscii":                                       enc2107,
	"CP51932":                                       enc2108,
	"cp51932":                                       enc2108,
	"csCP51932":                                     enc2108,
	"cscp51932":                                     enc2108,
	"windows-874":                                   enc2109,
	"cswindows874":                                  enc2109,
	"windows-1250":                                  enc2250,
	"cswindows1250":                                 enc2250,
	"windows-1251":                                  enc2251,
	"cswindows1251":                                 enc2251,
	"windows-1252":                                  enc2252,
	"cswindows1252":                                 enc2252,
	"windows-1253":                                  enc2253,
	"cswindows1253":                                 enc2253,
	"windows-1254":                                  enc2254,
	"cswindows1254":                                 enc2254,
	"windows-1255":                                  enc2255,
	"cswindows1255":                                 enc2255,
	"windows-1256":                                  enc2256,
	"cswindows1256":                                 enc2256,
	"windows-1257":                                  enc2257,
	"cswindows1257":                                 enc2257,
	"windows-1258":                                  enc2258,
	"cswindows1258":                                 enc2258,
	"TIS-620":                                       enc2259,
	"tis-620":                                       enc2259,
	"csTIS620":                                      enc2259,
	"cstis620":                                      enc2259,
	"ISO-8859-11":                                   enc2259,
	"iso-8859-11":                                   enc2259,
	"CP50220":                                       enc2260,
	"cp50220":                                       enc2260,
	"csCP50220":                                     enc2260,
	"cscp50220":                                     enc2260,
}

// Total table size 14402 bytes (14KiB); checksum: CEBAA10C
