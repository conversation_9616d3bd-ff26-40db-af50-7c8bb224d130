// Code generated by "stringer -type=BlockSize,CompressionLevel -output options_gen.go"; DO NOT EDIT.

package lz4

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Block64Kb-65536]
	_ = x[Block256Kb-262144]
	_ = x[Block1Mb-1048576]
	_ = x[Block4Mb-4194304]
}

const (
	_BlockSize_name_0 = "Block64Kb"
	_BlockSize_name_1 = "Block256Kb"
	_BlockSize_name_2 = "Block1Mb"
	_BlockSize_name_3 = "Block4Mb"
)

func (i BlockSize) String() string {
	switch {
	case i == 65536:
		return _BlockSize_name_0
	case i == 262144:
		return _BlockSize_name_1
	case i == 1048576:
		return _BlockSize_name_2
	case i == 4194304:
		return _BlockSize_name_3
	default:
		return "BlockSize(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Fast-0]
	_ = x[Level1-512]
	_ = x[Level2-1024]
	_ = x[Level3-2048]
	_ = x[Level4-4096]
	_ = x[Level5-8192]
	_ = x[Level6-16384]
	_ = x[Level7-32768]
	_ = x[Level8-65536]
	_ = x[Level9-131072]
}

const (
	_CompressionLevel_name_0 = "Fast"
	_CompressionLevel_name_1 = "Level1"
	_CompressionLevel_name_2 = "Level2"
	_CompressionLevel_name_3 = "Level3"
	_CompressionLevel_name_4 = "Level4"
	_CompressionLevel_name_5 = "Level5"
	_CompressionLevel_name_6 = "Level6"
	_CompressionLevel_name_7 = "Level7"
	_CompressionLevel_name_8 = "Level8"
	_CompressionLevel_name_9 = "Level9"
)

func (i CompressionLevel) String() string {
	switch {
	case i == 0:
		return _CompressionLevel_name_0
	case i == 512:
		return _CompressionLevel_name_1
	case i == 1024:
		return _CompressionLevel_name_2
	case i == 2048:
		return _CompressionLevel_name_3
	case i == 4096:
		return _CompressionLevel_name_4
	case i == 8192:
		return _CompressionLevel_name_5
	case i == 16384:
		return _CompressionLevel_name_6
	case i == 32768:
		return _CompressionLevel_name_7
	case i == 65536:
		return _CompressionLevel_name_8
	case i == 131072:
		return _CompressionLevel_name_9
	default:
		return "CompressionLevel(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
