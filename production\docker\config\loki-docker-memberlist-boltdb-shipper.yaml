auth_enabled: false

http_prefix:

server:
  http_listen_address: 0.0.0.0
  grpc_listen_address: 0.0.0.0
  http_listen_port: 3100
  grpc_listen_port: 9095
  log_level: info

memberlist:
  join_members: ["loki-1", "loki-2", "loki-3"]
  dead_node_reclaim_time: 30s
  gossip_to_dead_nodes_time: 15s
  left_ingesters_timeout: 30s
  bind_addr: ['0.0.0.0']
  bind_port: 7946

ingester:
  lifecycler:
    join_after: 60s
    observe_period: 5s
    ring:
      replication_factor: 2
      kvstore:
        store: memberlist
    final_sleep: 0s
  chunk_idle_period: 1h
  wal:
    enabled: true
    dir: /loki/wal
  max_chunk_age: 1h
  chunk_retain_period: 30s
  chunk_encoding: snappy
  chunk_target_size: 0
  chunk_block_size: 262144
  # chunk_target_size: 1.572864e+06

# Only needed for global rate strategy
# distributor:
#  ring:
#    kvstore:
#      store: memberlist

schema_config:
  configs:
  - from: 2020-08-01
    store: boltdb-shipper
    object_store: filesystem
    schema: v11
    index:
      prefix: index_
      period: 24h

storage_config:
  boltdb_shipper:
    # shared_store: s3
    shared_store: filesystem
    active_index_directory: /loki/index
    cache_location: /loki/boltdb-cache

  #aws:
  #  s3: s3://us-east-1/mybucket
  #  sse_encryption: true
  #  insecure: false
  #  s3forcepathstyle: true
  filesystem:
    directory: /loki/chunks


limits_config:
  max_cache_freshness_per_query: '10m'
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 30m
  ingestion_rate_mb: 10
  ingestion_burst_size_mb: 20
  # parallelize queries in 15min intervals
  split_queries_by_interval: 15m

chunk_store_config:
  max_look_back_period: 336h

table_manager:
  retention_deletes_enabled: true
  retention_period: 336h

query_range:
  # make queries more cache-able by aligning them with their step intervals
  align_queries_with_step: true
  max_retries: 5
  parallelise_shardable_queries: true
  cache_results: true

  results_cache:
    cache:
      # We're going to use the in-process "FIFO" cache
      enable_fifocache: true
      fifocache:
        size: 1024
        validity: 24h

frontend:
  log_queries_longer_than: 5s
  compress_responses: true

querier:
  query_ingesters_within: 2h

compactor:
  working_directory: /loki/compactor
  shared_store: filesystem
