{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "consul"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "jaeger-agent-mixin"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "ksonnet-util"}}, "version": "master"}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "memcached"}}, "version": "master"}], "legacyImports": true}