// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/ruler/base/ruler.proto

package base

import (
	context "context"
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	_ "github.com/golang/protobuf/ptypes/duration"
	_ "github.com/grafana/loki/pkg/logproto"
	github_com_grafana_loki_pkg_logproto "github.com/grafana/loki/pkg/logproto"
	rulespb "github.com/grafana/loki/pkg/ruler/rulespb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type RulesRequest struct {
}

func (m *RulesRequest) Reset()      { *m = RulesRequest{} }
func (*RulesRequest) ProtoMessage() {}
func (*RulesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ca810a0fd7057a73, []int{0}
}
func (m *RulesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RulesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RulesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RulesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RulesRequest.Merge(m, src)
}
func (m *RulesRequest) XXX_Size() int {
	return m.Size()
}
func (m *RulesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RulesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RulesRequest proto.InternalMessageInfo

type RulesResponse struct {
	Groups []*GroupStateDesc `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (m *RulesResponse) Reset()      { *m = RulesResponse{} }
func (*RulesResponse) ProtoMessage() {}
func (*RulesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ca810a0fd7057a73, []int{1}
}
func (m *RulesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RulesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RulesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RulesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RulesResponse.Merge(m, src)
}
func (m *RulesResponse) XXX_Size() int {
	return m.Size()
}
func (m *RulesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RulesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RulesResponse proto.InternalMessageInfo

func (m *RulesResponse) GetGroups() []*GroupStateDesc {
	if m != nil {
		return m.Groups
	}
	return nil
}

// GroupStateDesc is a proto representation of a rule group
type GroupStateDesc struct {
	Group               *rulespb.RuleGroupDesc `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	ActiveRules         []*RuleStateDesc       `protobuf:"bytes,2,rep,name=active_rules,json=activeRules,proto3" json:"active_rules,omitempty"`
	EvaluationTimestamp time.Time              `protobuf:"bytes,3,opt,name=evaluationTimestamp,proto3,stdtime" json:"evaluationTimestamp"`
	EvaluationDuration  time.Duration          `protobuf:"bytes,4,opt,name=evaluationDuration,proto3,stdduration" json:"evaluationDuration"`
}

func (m *GroupStateDesc) Reset()      { *m = GroupStateDesc{} }
func (*GroupStateDesc) ProtoMessage() {}
func (*GroupStateDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_ca810a0fd7057a73, []int{2}
}
func (m *GroupStateDesc) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GroupStateDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GroupStateDesc.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GroupStateDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupStateDesc.Merge(m, src)
}
func (m *GroupStateDesc) XXX_Size() int {
	return m.Size()
}
func (m *GroupStateDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupStateDesc.DiscardUnknown(m)
}

var xxx_messageInfo_GroupStateDesc proto.InternalMessageInfo

func (m *GroupStateDesc) GetGroup() *rulespb.RuleGroupDesc {
	if m != nil {
		return m.Group
	}
	return nil
}

func (m *GroupStateDesc) GetActiveRules() []*RuleStateDesc {
	if m != nil {
		return m.ActiveRules
	}
	return nil
}

func (m *GroupStateDesc) GetEvaluationTimestamp() time.Time {
	if m != nil {
		return m.EvaluationTimestamp
	}
	return time.Time{}
}

func (m *GroupStateDesc) GetEvaluationDuration() time.Duration {
	if m != nil {
		return m.EvaluationDuration
	}
	return 0
}

// RuleStateDesc is a proto representation of a Prometheus Rule
type RuleStateDesc struct {
	Rule                *rulespb.RuleDesc `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
	State               string            `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Health              string            `protobuf:"bytes,3,opt,name=health,proto3" json:"health,omitempty"`
	LastError           string            `protobuf:"bytes,4,opt,name=lastError,proto3" json:"lastError,omitempty"`
	Alerts              []*AlertStateDesc `protobuf:"bytes,5,rep,name=alerts,proto3" json:"alerts,omitempty"`
	EvaluationTimestamp time.Time         `protobuf:"bytes,6,opt,name=evaluationTimestamp,proto3,stdtime" json:"evaluationTimestamp"`
	EvaluationDuration  time.Duration     `protobuf:"bytes,7,opt,name=evaluationDuration,proto3,stdduration" json:"evaluationDuration"`
}

func (m *RuleStateDesc) Reset()      { *m = RuleStateDesc{} }
func (*RuleStateDesc) ProtoMessage() {}
func (*RuleStateDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_ca810a0fd7057a73, []int{3}
}
func (m *RuleStateDesc) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RuleStateDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RuleStateDesc.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RuleStateDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleStateDesc.Merge(m, src)
}
func (m *RuleStateDesc) XXX_Size() int {
	return m.Size()
}
func (m *RuleStateDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleStateDesc.DiscardUnknown(m)
}

var xxx_messageInfo_RuleStateDesc proto.InternalMessageInfo

func (m *RuleStateDesc) GetRule() *rulespb.RuleDesc {
	if m != nil {
		return m.Rule
	}
	return nil
}

func (m *RuleStateDesc) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *RuleStateDesc) GetHealth() string {
	if m != nil {
		return m.Health
	}
	return ""
}

func (m *RuleStateDesc) GetLastError() string {
	if m != nil {
		return m.LastError
	}
	return ""
}

func (m *RuleStateDesc) GetAlerts() []*AlertStateDesc {
	if m != nil {
		return m.Alerts
	}
	return nil
}

func (m *RuleStateDesc) GetEvaluationTimestamp() time.Time {
	if m != nil {
		return m.EvaluationTimestamp
	}
	return time.Time{}
}

func (m *RuleStateDesc) GetEvaluationDuration() time.Duration {
	if m != nil {
		return m.EvaluationDuration
	}
	return 0
}

type AlertStateDesc struct {
	State       string                                              `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Labels      []github_com_grafana_loki_pkg_logproto.LabelAdapter `protobuf:"bytes,2,rep,name=labels,proto3,customtype=github.com/grafana/loki/pkg/logproto.LabelAdapter" json:"labels"`
	Annotations []github_com_grafana_loki_pkg_logproto.LabelAdapter `protobuf:"bytes,3,rep,name=annotations,proto3,customtype=github.com/grafana/loki/pkg/logproto.LabelAdapter" json:"annotations"`
	Value       float64                                             `protobuf:"fixed64,4,opt,name=value,proto3" json:"value,omitempty"`
	ActiveAt    time.Time                                           `protobuf:"bytes,5,opt,name=active_at,json=activeAt,proto3,stdtime" json:"active_at"`
	FiredAt     time.Time                                           `protobuf:"bytes,6,opt,name=fired_at,json=firedAt,proto3,stdtime" json:"fired_at"`
	ResolvedAt  time.Time                                           `protobuf:"bytes,7,opt,name=resolved_at,json=resolvedAt,proto3,stdtime" json:"resolved_at"`
	LastSentAt  time.Time                                           `protobuf:"bytes,8,opt,name=last_sent_at,json=lastSentAt,proto3,stdtime" json:"last_sent_at"`
	ValidUntil  time.Time                                           `protobuf:"bytes,9,opt,name=valid_until,json=validUntil,proto3,stdtime" json:"valid_until"`
}

func (m *AlertStateDesc) Reset()      { *m = AlertStateDesc{} }
func (*AlertStateDesc) ProtoMessage() {}
func (*AlertStateDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_ca810a0fd7057a73, []int{4}
}
func (m *AlertStateDesc) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AlertStateDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AlertStateDesc.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AlertStateDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlertStateDesc.Merge(m, src)
}
func (m *AlertStateDesc) XXX_Size() int {
	return m.Size()
}
func (m *AlertStateDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_AlertStateDesc.DiscardUnknown(m)
}

var xxx_messageInfo_AlertStateDesc proto.InternalMessageInfo

func (m *AlertStateDesc) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *AlertStateDesc) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *AlertStateDesc) GetActiveAt() time.Time {
	if m != nil {
		return m.ActiveAt
	}
	return time.Time{}
}

func (m *AlertStateDesc) GetFiredAt() time.Time {
	if m != nil {
		return m.FiredAt
	}
	return time.Time{}
}

func (m *AlertStateDesc) GetResolvedAt() time.Time {
	if m != nil {
		return m.ResolvedAt
	}
	return time.Time{}
}

func (m *AlertStateDesc) GetLastSentAt() time.Time {
	if m != nil {
		return m.LastSentAt
	}
	return time.Time{}
}

func (m *AlertStateDesc) GetValidUntil() time.Time {
	if m != nil {
		return m.ValidUntil
	}
	return time.Time{}
}

func init() {
	proto.RegisterType((*RulesRequest)(nil), "base.RulesRequest")
	proto.RegisterType((*RulesResponse)(nil), "base.RulesResponse")
	proto.RegisterType((*GroupStateDesc)(nil), "base.GroupStateDesc")
	proto.RegisterType((*RuleStateDesc)(nil), "base.RuleStateDesc")
	proto.RegisterType((*AlertStateDesc)(nil), "base.AlertStateDesc")
}

func init() { proto.RegisterFile("pkg/ruler/base/ruler.proto", fileDescriptor_ca810a0fd7057a73) }

var fileDescriptor_ca810a0fd7057a73 = []byte{
	// 688 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0x4f, 0x6f, 0xd3, 0x3e,
	0x18, 0x8e, 0xbb, 0xb6, 0x6b, 0xdd, 0xfd, 0xf6, 0x93, 0xbc, 0x09, 0x65, 0x05, 0xdc, 0xa9, 0x5c,
	0x26, 0x04, 0xa9, 0x18, 0x08, 0x09, 0x21, 0x84, 0x3a, 0x6d, 0x70, 0xd9, 0x01, 0x65, 0xc0, 0x75,
	0x72, 0x5a, 0x2f, 0x8b, 0xe6, 0xc5, 0xc1, 0x76, 0x2a, 0x71, 0xe3, 0x23, 0xec, 0xc8, 0x95, 0x1b,
	0x1f, 0x65, 0xc7, 0x1d, 0x27, 0x90, 0x06, 0xcb, 0x2e, 0x1c, 0xf7, 0x01, 0x38, 0x20, 0xdb, 0xc9,
	0x92, 0xc1, 0x38, 0x54, 0x68, 0x97, 0xd6, 0xef, 0x9f, 0xe7, 0x79, 0xdf, 0xf7, 0xb1, 0xdf, 0xc0,
	0x6e, 0xb2, 0x17, 0x0e, 0x44, 0xca, 0xa8, 0x18, 0x04, 0x44, 0x52, 0x7b, 0xf4, 0x12, 0xc1, 0x15,
	0x47, 0x75, 0xed, 0xe9, 0xde, 0x0f, 0x23, 0xb5, 0x9b, 0x06, 0xde, 0x88, 0xef, 0x0f, 0x42, 0x1e,
	0xf2, 0x81, 0x09, 0x06, 0xe9, 0x8e, 0xb1, 0x8c, 0x61, 0x4e, 0x16, 0xd4, 0xc5, 0x21, 0xe7, 0x21,
	0xa3, 0x65, 0xd6, 0x38, 0x15, 0x44, 0x45, 0x3c, 0xce, 0xe3, 0xbd, 0xdf, 0xe3, 0x2a, 0xda, 0xa7,
	0x52, 0x91, 0xfd, 0x24, 0x4f, 0xb8, 0xa9, 0x3b, 0x62, 0x3c, 0xb4, 0xcc, 0xc5, 0x21, 0x0f, 0xde,
	0x2e, 0xdb, 0xd5, 0xbf, 0x32, 0x09, 0xec, 0xbf, 0x0d, 0xf7, 0xe7, 0xe1, 0x9c, 0xaf, 0x4d, 0x9f,
	0xbe, 0x4b, 0xa9, 0x54, 0xfd, 0x67, 0xf0, 0xbf, 0xdc, 0x96, 0x09, 0x8f, 0x25, 0x45, 0xf7, 0x60,
	0x33, 0x14, 0x3c, 0x4d, 0xa4, 0x0b, 0x96, 0x67, 0x56, 0x3a, 0xab, 0x8b, 0x9e, 0x9e, 0xd1, 0x7b,
	0xa9, 0x7d, 0x5b, 0x8a, 0x28, 0xba, 0x4e, 0xe5, 0xc8, 0xcf, 0x73, 0xfa, 0x9f, 0x6a, 0x70, 0xfe,
	0x72, 0x08, 0xdd, 0x85, 0x0d, 0x13, 0x74, 0xc1, 0x32, 0x30, 0x78, 0x5b, 0x5e, 0x57, 0x31, 0x99,
	0x06, 0x6f, 0x53, 0xd0, 0x63, 0x38, 0x47, 0x46, 0x2a, 0x9a, 0xd0, 0x6d, 0x93, 0xe4, 0xd6, 0x4c,
	0xc9, 0x05, 0x5b, 0x52, 0x23, 0xca, 0x8a, 0x1d, 0x9b, 0x68, 0x9a, 0x45, 0x6f, 0xe1, 0x02, 0x9d,
	0x10, 0x96, 0x1a, 0xd9, 0x5e, 0x17, 0xf2, 0xb8, 0x33, 0xa6, 0x62, 0xd7, 0xb3, 0x02, 0x7a, 0x85,
	0x80, 0xde, 0x45, 0xc6, 0x5a, 0xeb, 0xf0, 0xa4, 0xe7, 0x1c, 0x7c, 0xeb, 0x01, 0xff, 0x2a, 0x02,
	0xb4, 0x05, 0x51, 0xe9, 0x5e, 0xcf, 0xaf, 0xc5, 0xad, 0x1b, 0xda, 0xa5, 0x3f, 0x68, 0x8b, 0x04,
	0xcb, 0xfa, 0x51, 0xb3, 0x5e, 0x01, 0xef, 0x7f, 0xad, 0x59, 0x8d, 0x4b, 0x89, 0xee, 0xc0, 0xba,
	0x9e, 0x37, 0x57, 0xe8, 0xff, 0x8a, 0x42, 0x66, 0x54, 0x13, 0x44, 0x8b, 0xb0, 0x21, 0x35, 0xc2,
	0xad, 0x2d, 0x83, 0x95, 0xb6, 0x6f, 0x0d, 0x74, 0x03, 0x36, 0x77, 0x29, 0x61, 0x6a, 0xd7, 0x0c,
	0xdb, 0xf6, 0x73, 0x0b, 0xdd, 0x82, 0x6d, 0x46, 0xa4, 0xda, 0x10, 0x82, 0x0b, 0xd3, 0x70, 0xdb,
	0x2f, 0x1d, 0xfa, 0x52, 0x09, 0xa3, 0x42, 0x49, 0xb7, 0x51, 0xbd, 0xd4, 0xa1, 0xf6, 0x55, 0x2e,
	0xd5, 0xe6, 0xfc, 0x4d, 0xdd, 0xe6, 0xf5, 0xa8, 0x3b, 0xfb, 0x6f, 0xea, 0xfe, 0xac, 0xc3, 0xf9,
	0xcb, 0x73, 0x94, 0xca, 0x81, 0xaa, 0x72, 0x0c, 0x36, 0x19, 0x09, 0x28, 0x2b, 0x5e, 0xd9, 0x92,
	0x77, 0xb1, 0x39, 0x9b, 0x34, 0x24, 0xa3, 0xf7, 0x9b, 0x3a, 0xfa, 0x8a, 0x44, 0x62, 0xed, 0x89,
	0xae, 0xf8, 0xe5, 0xa4, 0xf7, 0xa0, 0xba, 0xd8, 0x82, 0xec, 0x90, 0x98, 0x0c, 0x18, 0xdf, 0x8b,
	0x06, 0xd5, 0x05, 0xf4, 0x0c, 0x6e, 0x38, 0x26, 0x89, 0xa2, 0xc2, 0xcf, 0x6b, 0xa0, 0x09, 0xec,
	0x90, 0x38, 0xe6, 0xca, 0x34, 0x29, 0xdd, 0x99, 0x6b, 0x2c, 0x59, 0x2d, 0xa4, 0x67, 0xd7, 0x1a,
	0x51, 0xf3, 0x06, 0x80, 0x6f, 0x0d, 0x34, 0x84, 0xed, 0x7c, 0xcf, 0x88, 0x72, 0x1b, 0x53, 0xdc,
	0x63, 0xcb, 0xc2, 0x86, 0x0a, 0x3d, 0x87, 0xad, 0x9d, 0x48, 0xd0, 0xb1, 0x66, 0x98, 0xe6, 0x25,
	0xcc, 0x1a, 0xd4, 0x50, 0xa1, 0x0d, 0xd8, 0x11, 0x54, 0x72, 0x36, 0xb1, 0x1c, 0xb3, 0x53, 0x70,
	0xc0, 0x02, 0x38, 0x54, 0xe8, 0x05, 0x9c, 0xd3, 0xef, 0x7a, 0x5b, 0xd2, 0x58, 0x69, 0x9e, 0xd6,
	0x34, 0x3c, 0x1a, 0xb9, 0x45, 0x63, 0x65, 0xdb, 0x99, 0x10, 0x16, 0x8d, 0xb7, 0xd3, 0x58, 0x45,
	0xcc, 0x6d, 0x4f, 0x43, 0x63, 0x80, 0x6f, 0x34, 0x6e, 0xf5, 0x29, 0x6c, 0xe8, 0xbd, 0x15, 0x68,
	0xd5, 0x1e, 0x24, 0x42, 0xe5, 0xd7, 0xab, 0xf8, 0xca, 0x76, 0x17, 0x2e, 0xf9, 0xec, 0x97, 0xb6,
	0xef, 0xac, 0x3d, 0x3a, 0x3a, 0xc5, 0xce, 0xf1, 0x29, 0x76, 0xce, 0x4f, 0x31, 0xf8, 0x90, 0x61,
	0xf0, 0x39, 0xc3, 0xe0, 0x30, 0xc3, 0xe0, 0x28, 0xc3, 0xe0, 0x7b, 0x86, 0xc1, 0x8f, 0x0c, 0x3b,
	0xe7, 0x19, 0x06, 0x07, 0x67, 0xd8, 0x39, 0x3a, 0xc3, 0xce, 0xf1, 0x19, 0x76, 0x82, 0xa6, 0x69,
	0xee, 0xe1, 0xaf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x55, 0x94, 0x9f, 0xfc, 0x99, 0x06, 0x00, 0x00,
}

func (this *RulesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*RulesRequest)
	if !ok {
		that2, ok := that.(RulesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *RulesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*RulesResponse)
	if !ok {
		that2, ok := that.(RulesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Groups) != len(that1.Groups) {
		return false
	}
	for i := range this.Groups {
		if !this.Groups[i].Equal(that1.Groups[i]) {
			return false
		}
	}
	return true
}
func (this *GroupStateDesc) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GroupStateDesc)
	if !ok {
		that2, ok := that.(GroupStateDesc)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Group.Equal(that1.Group) {
		return false
	}
	if len(this.ActiveRules) != len(that1.ActiveRules) {
		return false
	}
	for i := range this.ActiveRules {
		if !this.ActiveRules[i].Equal(that1.ActiveRules[i]) {
			return false
		}
	}
	if !this.EvaluationTimestamp.Equal(that1.EvaluationTimestamp) {
		return false
	}
	if this.EvaluationDuration != that1.EvaluationDuration {
		return false
	}
	return true
}
func (this *RuleStateDesc) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*RuleStateDesc)
	if !ok {
		that2, ok := that.(RuleStateDesc)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Rule.Equal(that1.Rule) {
		return false
	}
	if this.State != that1.State {
		return false
	}
	if this.Health != that1.Health {
		return false
	}
	if this.LastError != that1.LastError {
		return false
	}
	if len(this.Alerts) != len(that1.Alerts) {
		return false
	}
	for i := range this.Alerts {
		if !this.Alerts[i].Equal(that1.Alerts[i]) {
			return false
		}
	}
	if !this.EvaluationTimestamp.Equal(that1.EvaluationTimestamp) {
		return false
	}
	if this.EvaluationDuration != that1.EvaluationDuration {
		return false
	}
	return true
}
func (this *AlertStateDesc) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*AlertStateDesc)
	if !ok {
		that2, ok := that.(AlertStateDesc)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.State != that1.State {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	if len(this.Annotations) != len(that1.Annotations) {
		return false
	}
	for i := range this.Annotations {
		if !this.Annotations[i].Equal(that1.Annotations[i]) {
			return false
		}
	}
	if this.Value != that1.Value {
		return false
	}
	if !this.ActiveAt.Equal(that1.ActiveAt) {
		return false
	}
	if !this.FiredAt.Equal(that1.FiredAt) {
		return false
	}
	if !this.ResolvedAt.Equal(that1.ResolvedAt) {
		return false
	}
	if !this.LastSentAt.Equal(that1.LastSentAt) {
		return false
	}
	if !this.ValidUntil.Equal(that1.ValidUntil) {
		return false
	}
	return true
}
func (this *RulesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&base.RulesRequest{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *RulesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&base.RulesResponse{")
	if this.Groups != nil {
		s = append(s, "Groups: "+fmt.Sprintf("%#v", this.Groups)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GroupStateDesc) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&base.GroupStateDesc{")
	if this.Group != nil {
		s = append(s, "Group: "+fmt.Sprintf("%#v", this.Group)+",\n")
	}
	if this.ActiveRules != nil {
		s = append(s, "ActiveRules: "+fmt.Sprintf("%#v", this.ActiveRules)+",\n")
	}
	s = append(s, "EvaluationTimestamp: "+fmt.Sprintf("%#v", this.EvaluationTimestamp)+",\n")
	s = append(s, "EvaluationDuration: "+fmt.Sprintf("%#v", this.EvaluationDuration)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *RuleStateDesc) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 11)
	s = append(s, "&base.RuleStateDesc{")
	if this.Rule != nil {
		s = append(s, "Rule: "+fmt.Sprintf("%#v", this.Rule)+",\n")
	}
	s = append(s, "State: "+fmt.Sprintf("%#v", this.State)+",\n")
	s = append(s, "Health: "+fmt.Sprintf("%#v", this.Health)+",\n")
	s = append(s, "LastError: "+fmt.Sprintf("%#v", this.LastError)+",\n")
	if this.Alerts != nil {
		s = append(s, "Alerts: "+fmt.Sprintf("%#v", this.Alerts)+",\n")
	}
	s = append(s, "EvaluationTimestamp: "+fmt.Sprintf("%#v", this.EvaluationTimestamp)+",\n")
	s = append(s, "EvaluationDuration: "+fmt.Sprintf("%#v", this.EvaluationDuration)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *AlertStateDesc) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 13)
	s = append(s, "&base.AlertStateDesc{")
	s = append(s, "State: "+fmt.Sprintf("%#v", this.State)+",\n")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	s = append(s, "Annotations: "+fmt.Sprintf("%#v", this.Annotations)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "ActiveAt: "+fmt.Sprintf("%#v", this.ActiveAt)+",\n")
	s = append(s, "FiredAt: "+fmt.Sprintf("%#v", this.FiredAt)+",\n")
	s = append(s, "ResolvedAt: "+fmt.Sprintf("%#v", this.ResolvedAt)+",\n")
	s = append(s, "LastSentAt: "+fmt.Sprintf("%#v", this.LastSentAt)+",\n")
	s = append(s, "ValidUntil: "+fmt.Sprintf("%#v", this.ValidUntil)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringRuler(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RulerClient is the client API for Ruler service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RulerClient interface {
	Rules(ctx context.Context, in *RulesRequest, opts ...grpc.CallOption) (*RulesResponse, error)
}

type rulerClient struct {
	cc *grpc.ClientConn
}

func NewRulerClient(cc *grpc.ClientConn) RulerClient {
	return &rulerClient{cc}
}

func (c *rulerClient) Rules(ctx context.Context, in *RulesRequest, opts ...grpc.CallOption) (*RulesResponse, error) {
	out := new(RulesResponse)
	err := c.cc.Invoke(ctx, "/base.Ruler/Rules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RulerServer is the server API for Ruler service.
type RulerServer interface {
	Rules(context.Context, *RulesRequest) (*RulesResponse, error)
}

// UnimplementedRulerServer can be embedded to have forward compatible implementations.
type UnimplementedRulerServer struct {
}

func (*UnimplementedRulerServer) Rules(ctx context.Context, req *RulesRequest) (*RulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Rules not implemented")
}

func RegisterRulerServer(s *grpc.Server, srv RulerServer) {
	s.RegisterService(&_Ruler_serviceDesc, srv)
}

func _Ruler_Rules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RulerServer).Rules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/base.Ruler/Rules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RulerServer).Rules(ctx, req.(*RulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Ruler_serviceDesc = grpc.ServiceDesc{
	ServiceName: "base.Ruler",
	HandlerType: (*RulerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Rules",
			Handler:    _Ruler_Rules_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pkg/ruler/base/ruler.proto",
}

func (m *RulesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RulesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RulesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *RulesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RulesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RulesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Groups) > 0 {
		for iNdEx := len(m.Groups) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Groups[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRuler(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GroupStateDesc) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupStateDesc) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GroupStateDesc) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	n1, err1 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.EvaluationDuration, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.EvaluationDuration):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintRuler(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x22
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EvaluationTimestamp, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EvaluationTimestamp):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintRuler(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x1a
	if len(m.ActiveRules) > 0 {
		for iNdEx := len(m.ActiveRules) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ActiveRules[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRuler(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Group != nil {
		{
			size, err := m.Group.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRuler(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RuleStateDesc) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RuleStateDesc) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RuleStateDesc) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	n4, err4 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.EvaluationDuration, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.EvaluationDuration):])
	if err4 != nil {
		return 0, err4
	}
	i -= n4
	i = encodeVarintRuler(dAtA, i, uint64(n4))
	i--
	dAtA[i] = 0x3a
	n5, err5 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EvaluationTimestamp, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EvaluationTimestamp):])
	if err5 != nil {
		return 0, err5
	}
	i -= n5
	i = encodeVarintRuler(dAtA, i, uint64(n5))
	i--
	dAtA[i] = 0x32
	if len(m.Alerts) > 0 {
		for iNdEx := len(m.Alerts) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Alerts[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRuler(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.LastError) > 0 {
		i -= len(m.LastError)
		copy(dAtA[i:], m.LastError)
		i = encodeVarintRuler(dAtA, i, uint64(len(m.LastError)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Health) > 0 {
		i -= len(m.Health)
		copy(dAtA[i:], m.Health)
		i = encodeVarintRuler(dAtA, i, uint64(len(m.Health)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.State) > 0 {
		i -= len(m.State)
		copy(dAtA[i:], m.State)
		i = encodeVarintRuler(dAtA, i, uint64(len(m.State)))
		i--
		dAtA[i] = 0x12
	}
	if m.Rule != nil {
		{
			size, err := m.Rule.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRuler(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AlertStateDesc) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AlertStateDesc) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AlertStateDesc) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	n7, err7 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.ValidUntil, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.ValidUntil):])
	if err7 != nil {
		return 0, err7
	}
	i -= n7
	i = encodeVarintRuler(dAtA, i, uint64(n7))
	i--
	dAtA[i] = 0x4a
	n8, err8 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.LastSentAt, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.LastSentAt):])
	if err8 != nil {
		return 0, err8
	}
	i -= n8
	i = encodeVarintRuler(dAtA, i, uint64(n8))
	i--
	dAtA[i] = 0x42
	n9, err9 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.ResolvedAt, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.ResolvedAt):])
	if err9 != nil {
		return 0, err9
	}
	i -= n9
	i = encodeVarintRuler(dAtA, i, uint64(n9))
	i--
	dAtA[i] = 0x3a
	n10, err10 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.FiredAt, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.FiredAt):])
	if err10 != nil {
		return 0, err10
	}
	i -= n10
	i = encodeVarintRuler(dAtA, i, uint64(n10))
	i--
	dAtA[i] = 0x32
	n11, err11 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.ActiveAt, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.ActiveAt):])
	if err11 != nil {
		return 0, err11
	}
	i -= n11
	i = encodeVarintRuler(dAtA, i, uint64(n11))
	i--
	dAtA[i] = 0x2a
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x21
	}
	if len(m.Annotations) > 0 {
		for iNdEx := len(m.Annotations) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Annotations[iNdEx].Size()
				i -= size
				if _, err := m.Annotations[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintRuler(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintRuler(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.State) > 0 {
		i -= len(m.State)
		copy(dAtA[i:], m.State)
		i = encodeVarintRuler(dAtA, i, uint64(len(m.State)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintRuler(dAtA []byte, offset int, v uint64) int {
	offset -= sovRuler(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RulesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *RulesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Groups) > 0 {
		for _, e := range m.Groups {
			l = e.Size()
			n += 1 + l + sovRuler(uint64(l))
		}
	}
	return n
}

func (m *GroupStateDesc) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Group != nil {
		l = m.Group.Size()
		n += 1 + l + sovRuler(uint64(l))
	}
	if len(m.ActiveRules) > 0 {
		for _, e := range m.ActiveRules {
			l = e.Size()
			n += 1 + l + sovRuler(uint64(l))
		}
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EvaluationTimestamp)
	n += 1 + l + sovRuler(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.EvaluationDuration)
	n += 1 + l + sovRuler(uint64(l))
	return n
}

func (m *RuleStateDesc) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Rule != nil {
		l = m.Rule.Size()
		n += 1 + l + sovRuler(uint64(l))
	}
	l = len(m.State)
	if l > 0 {
		n += 1 + l + sovRuler(uint64(l))
	}
	l = len(m.Health)
	if l > 0 {
		n += 1 + l + sovRuler(uint64(l))
	}
	l = len(m.LastError)
	if l > 0 {
		n += 1 + l + sovRuler(uint64(l))
	}
	if len(m.Alerts) > 0 {
		for _, e := range m.Alerts {
			l = e.Size()
			n += 1 + l + sovRuler(uint64(l))
		}
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EvaluationTimestamp)
	n += 1 + l + sovRuler(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.EvaluationDuration)
	n += 1 + l + sovRuler(uint64(l))
	return n
}

func (m *AlertStateDesc) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.State)
	if l > 0 {
		n += 1 + l + sovRuler(uint64(l))
	}
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovRuler(uint64(l))
		}
	}
	if len(m.Annotations) > 0 {
		for _, e := range m.Annotations {
			l = e.Size()
			n += 1 + l + sovRuler(uint64(l))
		}
	}
	if m.Value != 0 {
		n += 9
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.ActiveAt)
	n += 1 + l + sovRuler(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.FiredAt)
	n += 1 + l + sovRuler(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.ResolvedAt)
	n += 1 + l + sovRuler(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.LastSentAt)
	n += 1 + l + sovRuler(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.ValidUntil)
	n += 1 + l + sovRuler(uint64(l))
	return n
}

func sovRuler(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozRuler(x uint64) (n int) {
	return sovRuler(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *RulesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&RulesRequest{`,
		`}`,
	}, "")
	return s
}
func (this *RulesResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForGroups := "[]*GroupStateDesc{"
	for _, f := range this.Groups {
		repeatedStringForGroups += strings.Replace(f.String(), "GroupStateDesc", "GroupStateDesc", 1) + ","
	}
	repeatedStringForGroups += "}"
	s := strings.Join([]string{`&RulesResponse{`,
		`Groups:` + repeatedStringForGroups + `,`,
		`}`,
	}, "")
	return s
}
func (this *GroupStateDesc) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForActiveRules := "[]*RuleStateDesc{"
	for _, f := range this.ActiveRules {
		repeatedStringForActiveRules += strings.Replace(f.String(), "RuleStateDesc", "RuleStateDesc", 1) + ","
	}
	repeatedStringForActiveRules += "}"
	s := strings.Join([]string{`&GroupStateDesc{`,
		`Group:` + strings.Replace(fmt.Sprintf("%v", this.Group), "RuleGroupDesc", "rulespb.RuleGroupDesc", 1) + `,`,
		`ActiveRules:` + repeatedStringForActiveRules + `,`,
		`EvaluationTimestamp:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EvaluationTimestamp), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`EvaluationDuration:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EvaluationDuration), "Duration", "duration.Duration", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *RuleStateDesc) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForAlerts := "[]*AlertStateDesc{"
	for _, f := range this.Alerts {
		repeatedStringForAlerts += strings.Replace(f.String(), "AlertStateDesc", "AlertStateDesc", 1) + ","
	}
	repeatedStringForAlerts += "}"
	s := strings.Join([]string{`&RuleStateDesc{`,
		`Rule:` + strings.Replace(fmt.Sprintf("%v", this.Rule), "RuleDesc", "rulespb.RuleDesc", 1) + `,`,
		`State:` + fmt.Sprintf("%v", this.State) + `,`,
		`Health:` + fmt.Sprintf("%v", this.Health) + `,`,
		`LastError:` + fmt.Sprintf("%v", this.LastError) + `,`,
		`Alerts:` + repeatedStringForAlerts + `,`,
		`EvaluationTimestamp:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EvaluationTimestamp), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`EvaluationDuration:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.EvaluationDuration), "Duration", "duration.Duration", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AlertStateDesc) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AlertStateDesc{`,
		`State:` + fmt.Sprintf("%v", this.State) + `,`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Annotations:` + fmt.Sprintf("%v", this.Annotations) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`ActiveAt:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ActiveAt), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`FiredAt:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.FiredAt), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`ResolvedAt:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ResolvedAt), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`LastSentAt:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.LastSentAt), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`ValidUntil:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ValidUntil), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringRuler(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *RulesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRuler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RulesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RulesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRuler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RulesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRuler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RulesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RulesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Groups", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Groups = append(m.Groups, &GroupStateDesc{})
			if err := m.Groups[len(m.Groups)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRuler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupStateDesc) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRuler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GroupStateDesc: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GroupStateDesc: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Group == nil {
				m.Group = &rulespb.RuleGroupDesc{}
			}
			if err := m.Group.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActiveRules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ActiveRules = append(m.ActiveRules, &RuleStateDesc{})
			if err := m.ActiveRules[len(m.ActiveRules)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EvaluationTimestamp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EvaluationTimestamp, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EvaluationDuration", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.EvaluationDuration, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRuler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RuleStateDesc) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRuler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RuleStateDesc: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RuleStateDesc: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rule == nil {
				m.Rule = &rulespb.RuleDesc{}
			}
			if err := m.Rule.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.State = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Health", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Health = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastError", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastError = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alerts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Alerts = append(m.Alerts, &AlertStateDesc{})
			if err := m.Alerts[len(m.Alerts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EvaluationTimestamp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EvaluationTimestamp, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EvaluationDuration", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.EvaluationDuration, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRuler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AlertStateDesc) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRuler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AlertStateDesc: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AlertStateDesc: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.State = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, github_com_grafana_loki_pkg_logproto.LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Annotations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Annotations = append(m.Annotations, github_com_grafana_loki_pkg_logproto.LabelAdapter{})
			if err := m.Annotations[len(m.Annotations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActiveAt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.ActiveAt, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FiredAt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.FiredAt, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedAt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.ResolvedAt, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastSentAt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.LastSentAt, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValidUntil", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRuler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRuler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.ValidUntil, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRuler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRuler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRuler(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRuler
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRuler
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthRuler
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthRuler
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRuler
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRuler(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthRuler
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRuler = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRuler   = fmt.Errorf("proto: integer overflow")
)
