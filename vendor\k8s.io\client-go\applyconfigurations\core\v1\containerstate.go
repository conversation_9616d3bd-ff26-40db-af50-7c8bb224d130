/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// ContainerStateApplyConfiguration represents an declarative configuration of the ContainerState type for use
// with apply.
type ContainerStateApplyConfiguration struct {
	Waiting    *ContainerStateWaitingApplyConfiguration    `json:"waiting,omitempty"`
	Running    *ContainerStateRunningApplyConfiguration    `json:"running,omitempty"`
	Terminated *ContainerStateTerminatedApplyConfiguration `json:"terminated,omitempty"`
}

// ContainerStateApplyConfiguration constructs an declarative configuration of the ContainerState type for use with
// apply.
func ContainerState() *ContainerStateApplyConfiguration {
	return &ContainerStateApplyConfiguration{}
}

// WithWaiting sets the Waiting field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Waiting field is set to the value of the last call.
func (b *ContainerStateApplyConfiguration) WithWaiting(value *ContainerStateWaitingApplyConfiguration) *ContainerStateApplyConfiguration {
	b.Waiting = value
	return b
}

// WithRunning sets the Running field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Running field is set to the value of the last call.
func (b *ContainerStateApplyConfiguration) WithRunning(value *ContainerStateRunningApplyConfiguration) *ContainerStateApplyConfiguration {
	b.Running = value
	return b
}

// WithTerminated sets the Terminated field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Terminated field is set to the value of the last call.
func (b *ContainerStateApplyConfiguration) WithTerminated(value *ContainerStateTerminatedApplyConfiguration) *ContainerStateApplyConfiguration {
	b.Terminated = value
	return b
}
