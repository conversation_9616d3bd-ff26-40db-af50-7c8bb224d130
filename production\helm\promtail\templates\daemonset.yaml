apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ template "promtail.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "promtail.name" . }}
    chart: {{ template "promtail.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  annotations:
    {{- toYaml .Values.annotations | nindent 4 }}
spec:
  selector:
    matchLabels:
      app: {{ template "promtail.name" . }}
      release: {{ .Release.Name }}
  updateStrategy:
    {{- toYaml .Values.deploymentStrategy | nindent 4 }}
  template:
    metadata:
      labels:
        app: {{ template "promtail.name" . }}
        release: {{ .Release.Name }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ template "promtail.serviceAccountName" . }}
    {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
    {{- end }}
    {{- if .Values.initContainer.enabled }}
      initContainers:
      - name: init
        image: busybox
        command:
        - sh
        - -c
        - sysctl -w fs.inotify.max_user_instances={{ .Values.initContainer.fsInotifyMaxUserInstances }}
        securityContext:
          privileged: true
    {{- end }}
      {{- if .Values.image.pullSecrets }}
      imagePullSecrets:
      {{- range .Values.image.pullSecrets }}
        - name: {{ . }}
      {{- end}}
      {{- end }}
      containers:
        - name: promtail
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - "-config.file=/etc/promtail/promtail.yaml"
            {{- if not (or .Values.config.client.url .Values.config.clients) }}
            {{- if and .Values.loki.user .Values.loki.password }}
            - "-client.url={{ .Values.loki.serviceScheme }}://{{ .Values.loki.user }}:{{ .Values.loki.password }}@{{ include "loki.serviceName" . }}:{{ .Values.loki.servicePort }}/loki/api/v1/push"
            {{- else }}
            - "-client.url={{ .Values.loki.serviceScheme }}://{{ include "loki.serviceName" . }}:{{ .Values.loki.servicePort }}/loki/api/v1/push"
            {{- end }}
            {{- end }}
            {{- if .Values.extraCommandlineArgs }}
            {{- range .Values.extraCommandlineArgs }}
            - {{ . | quote }}
            {{- end }}
            {{- end }}
          volumeMounts:
            - name: config
              mountPath: /etc/promtail
            - name: run
              mountPath: /run/promtail
            {{- with .Values.volumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with .Values.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          env:
            {{- with .Values.env }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          ports:
            - containerPort: {{ .Values.config.server.http_listen_port }}
              name: http-metrics
            {{- if .Values.syslogService.enabled }}
            - containerPort: {{ .Values.syslogService.port }}
              name: syslog
            {{- end }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      nodeSelector:
        {{- toYaml .Values.nodeSelector | nindent 8 }}
      affinity:
        {{- toYaml .Values.affinity | nindent 8 }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      volumes:
        - name: config
          configMap:
            name: {{ template "promtail.fullname" . }}
        - name: run
          hostPath:
            path: /run/promtail
        {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
