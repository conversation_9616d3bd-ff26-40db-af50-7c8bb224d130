// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/scheduler/schedulerpb/scheduler.proto

package schedulerpb

import (
	context "context"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	httpgrpc "github.com/weaveworks/common/httpgrpc"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type FrontendToSchedulerType int32

const (
	INIT    FrontendToSchedulerType = 0
	ENQUEUE FrontendToSchedulerType = 1
	CANCEL  FrontendToSchedulerType = 2
)

var FrontendToSchedulerType_name = map[int32]string{
	0: "INIT",
	1: "ENQUEUE",
	2: "CANCEL",
}

var FrontendToSchedulerType_value = map[string]int32{
	"INIT":    0,
	"ENQUEUE": 1,
	"CANCEL":  2,
}

func (FrontendToSchedulerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{0}
}

type SchedulerToFrontendStatus int32

const (
	OK                           SchedulerToFrontendStatus = 0
	TOO_MANY_REQUESTS_PER_TENANT SchedulerToFrontendStatus = 1
	ERROR                        SchedulerToFrontendStatus = 2
	SHUTTING_DOWN                SchedulerToFrontendStatus = 3
)

var SchedulerToFrontendStatus_name = map[int32]string{
	0: "OK",
	1: "TOO_MANY_REQUESTS_PER_TENANT",
	2: "ERROR",
	3: "SHUTTING_DOWN",
}

var SchedulerToFrontendStatus_value = map[string]int32{
	"OK":                           0,
	"TOO_MANY_REQUESTS_PER_TENANT": 1,
	"ERROR":                        2,
	"SHUTTING_DOWN":                3,
}

func (SchedulerToFrontendStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{1}
}

// Querier reports its own clientID when it connects, so that scheduler knows how many *different* queriers are connected.
// To signal that querier is ready to accept another request, querier sends empty message.
type QuerierToScheduler struct {
	QuerierID string `protobuf:"bytes,1,opt,name=querierID,proto3" json:"querierID,omitempty"`
}

func (m *QuerierToScheduler) Reset()      { *m = QuerierToScheduler{} }
func (*QuerierToScheduler) ProtoMessage() {}
func (*QuerierToScheduler) Descriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{0}
}
func (m *QuerierToScheduler) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QuerierToScheduler) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QuerierToScheduler.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QuerierToScheduler) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerierToScheduler.Merge(m, src)
}
func (m *QuerierToScheduler) XXX_Size() int {
	return m.Size()
}
func (m *QuerierToScheduler) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerierToScheduler.DiscardUnknown(m)
}

var xxx_messageInfo_QuerierToScheduler proto.InternalMessageInfo

func (m *QuerierToScheduler) GetQuerierID() string {
	if m != nil {
		return m.QuerierID
	}
	return ""
}

type SchedulerToQuerier struct {
	// Query ID as reported by frontend. When querier sends the response back to frontend (using frontendAddress),
	// it identifies the query by using this ID.
	QueryID     uint64                `protobuf:"varint,1,opt,name=queryID,proto3" json:"queryID,omitempty"`
	HttpRequest *httpgrpc.HTTPRequest `protobuf:"bytes,2,opt,name=httpRequest,proto3" json:"httpRequest,omitempty"`
	// Where should querier send HTTP Response to (using FrontendForQuerier interface).
	FrontendAddress string `protobuf:"bytes,3,opt,name=frontendAddress,proto3" json:"frontendAddress,omitempty"`
	// User who initiated the request. Needed to send reply back to frontend.
	UserID string `protobuf:"bytes,4,opt,name=userID,proto3" json:"userID,omitempty"`
	// Whether query statistics tracking should be enabled. The response will include
	// statistics only when this option is enabled.
	StatsEnabled bool `protobuf:"varint,5,opt,name=statsEnabled,proto3" json:"statsEnabled,omitempty"`
}

func (m *SchedulerToQuerier) Reset()      { *m = SchedulerToQuerier{} }
func (*SchedulerToQuerier) ProtoMessage() {}
func (*SchedulerToQuerier) Descriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{1}
}
func (m *SchedulerToQuerier) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SchedulerToQuerier) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SchedulerToQuerier.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SchedulerToQuerier) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SchedulerToQuerier.Merge(m, src)
}
func (m *SchedulerToQuerier) XXX_Size() int {
	return m.Size()
}
func (m *SchedulerToQuerier) XXX_DiscardUnknown() {
	xxx_messageInfo_SchedulerToQuerier.DiscardUnknown(m)
}

var xxx_messageInfo_SchedulerToQuerier proto.InternalMessageInfo

func (m *SchedulerToQuerier) GetQueryID() uint64 {
	if m != nil {
		return m.QueryID
	}
	return 0
}

func (m *SchedulerToQuerier) GetHttpRequest() *httpgrpc.HTTPRequest {
	if m != nil {
		return m.HttpRequest
	}
	return nil
}

func (m *SchedulerToQuerier) GetFrontendAddress() string {
	if m != nil {
		return m.FrontendAddress
	}
	return ""
}

func (m *SchedulerToQuerier) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *SchedulerToQuerier) GetStatsEnabled() bool {
	if m != nil {
		return m.StatsEnabled
	}
	return false
}

type FrontendToScheduler struct {
	Type FrontendToSchedulerType `protobuf:"varint,1,opt,name=type,proto3,enum=schedulerpb.FrontendToSchedulerType" json:"type,omitempty"`
	// Used by INIT message. Will be put into all requests passed to querier.
	FrontendAddress string `protobuf:"bytes,2,opt,name=frontendAddress,proto3" json:"frontendAddress,omitempty"`
	// Used by ENQUEUE and CANCEL.
	// Each frontend manages its own queryIDs. Different frontends may use same set of query IDs.
	QueryID uint64 `protobuf:"varint,3,opt,name=queryID,proto3" json:"queryID,omitempty"`
	// Following are used by ENQUEUE only.
	UserID       string                `protobuf:"bytes,4,opt,name=userID,proto3" json:"userID,omitempty"`
	HttpRequest  *httpgrpc.HTTPRequest `protobuf:"bytes,5,opt,name=httpRequest,proto3" json:"httpRequest,omitempty"`
	StatsEnabled bool                  `protobuf:"varint,6,opt,name=statsEnabled,proto3" json:"statsEnabled,omitempty"`
}

func (m *FrontendToScheduler) Reset()      { *m = FrontendToScheduler{} }
func (*FrontendToScheduler) ProtoMessage() {}
func (*FrontendToScheduler) Descriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{2}
}
func (m *FrontendToScheduler) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FrontendToScheduler) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FrontendToScheduler.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FrontendToScheduler) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FrontendToScheduler.Merge(m, src)
}
func (m *FrontendToScheduler) XXX_Size() int {
	return m.Size()
}
func (m *FrontendToScheduler) XXX_DiscardUnknown() {
	xxx_messageInfo_FrontendToScheduler.DiscardUnknown(m)
}

var xxx_messageInfo_FrontendToScheduler proto.InternalMessageInfo

func (m *FrontendToScheduler) GetType() FrontendToSchedulerType {
	if m != nil {
		return m.Type
	}
	return INIT
}

func (m *FrontendToScheduler) GetFrontendAddress() string {
	if m != nil {
		return m.FrontendAddress
	}
	return ""
}

func (m *FrontendToScheduler) GetQueryID() uint64 {
	if m != nil {
		return m.QueryID
	}
	return 0
}

func (m *FrontendToScheduler) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *FrontendToScheduler) GetHttpRequest() *httpgrpc.HTTPRequest {
	if m != nil {
		return m.HttpRequest
	}
	return nil
}

func (m *FrontendToScheduler) GetStatsEnabled() bool {
	if m != nil {
		return m.StatsEnabled
	}
	return false
}

type SchedulerToFrontend struct {
	Status SchedulerToFrontendStatus `protobuf:"varint,1,opt,name=status,proto3,enum=schedulerpb.SchedulerToFrontendStatus" json:"status,omitempty"`
	Error  string                    `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *SchedulerToFrontend) Reset()      { *m = SchedulerToFrontend{} }
func (*SchedulerToFrontend) ProtoMessage() {}
func (*SchedulerToFrontend) Descriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{3}
}
func (m *SchedulerToFrontend) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SchedulerToFrontend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SchedulerToFrontend.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SchedulerToFrontend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SchedulerToFrontend.Merge(m, src)
}
func (m *SchedulerToFrontend) XXX_Size() int {
	return m.Size()
}
func (m *SchedulerToFrontend) XXX_DiscardUnknown() {
	xxx_messageInfo_SchedulerToFrontend.DiscardUnknown(m)
}

var xxx_messageInfo_SchedulerToFrontend proto.InternalMessageInfo

func (m *SchedulerToFrontend) GetStatus() SchedulerToFrontendStatus {
	if m != nil {
		return m.Status
	}
	return OK
}

func (m *SchedulerToFrontend) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type NotifyQuerierShutdownRequest struct {
	QuerierID string `protobuf:"bytes,1,opt,name=querierID,proto3" json:"querierID,omitempty"`
}

func (m *NotifyQuerierShutdownRequest) Reset()      { *m = NotifyQuerierShutdownRequest{} }
func (*NotifyQuerierShutdownRequest) ProtoMessage() {}
func (*NotifyQuerierShutdownRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{4}
}
func (m *NotifyQuerierShutdownRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NotifyQuerierShutdownRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NotifyQuerierShutdownRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NotifyQuerierShutdownRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyQuerierShutdownRequest.Merge(m, src)
}
func (m *NotifyQuerierShutdownRequest) XXX_Size() int {
	return m.Size()
}
func (m *NotifyQuerierShutdownRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyQuerierShutdownRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyQuerierShutdownRequest proto.InternalMessageInfo

func (m *NotifyQuerierShutdownRequest) GetQuerierID() string {
	if m != nil {
		return m.QuerierID
	}
	return ""
}

type NotifyQuerierShutdownResponse struct {
}

func (m *NotifyQuerierShutdownResponse) Reset()      { *m = NotifyQuerierShutdownResponse{} }
func (*NotifyQuerierShutdownResponse) ProtoMessage() {}
func (*NotifyQuerierShutdownResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c3657184e8d38989, []int{5}
}
func (m *NotifyQuerierShutdownResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NotifyQuerierShutdownResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NotifyQuerierShutdownResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NotifyQuerierShutdownResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyQuerierShutdownResponse.Merge(m, src)
}
func (m *NotifyQuerierShutdownResponse) XXX_Size() int {
	return m.Size()
}
func (m *NotifyQuerierShutdownResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyQuerierShutdownResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyQuerierShutdownResponse proto.InternalMessageInfo

func init() {
	proto.RegisterEnum("schedulerpb.FrontendToSchedulerType", FrontendToSchedulerType_name, FrontendToSchedulerType_value)
	proto.RegisterEnum("schedulerpb.SchedulerToFrontendStatus", SchedulerToFrontendStatus_name, SchedulerToFrontendStatus_value)
	proto.RegisterType((*QuerierToScheduler)(nil), "schedulerpb.QuerierToScheduler")
	proto.RegisterType((*SchedulerToQuerier)(nil), "schedulerpb.SchedulerToQuerier")
	proto.RegisterType((*FrontendToScheduler)(nil), "schedulerpb.FrontendToScheduler")
	proto.RegisterType((*SchedulerToFrontend)(nil), "schedulerpb.SchedulerToFrontend")
	proto.RegisterType((*NotifyQuerierShutdownRequest)(nil), "schedulerpb.NotifyQuerierShutdownRequest")
	proto.RegisterType((*NotifyQuerierShutdownResponse)(nil), "schedulerpb.NotifyQuerierShutdownResponse")
}

func init() {
	proto.RegisterFile("pkg/scheduler/schedulerpb/scheduler.proto", fileDescriptor_c3657184e8d38989)
}

var fileDescriptor_c3657184e8d38989 = []byte{
	// 659 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0xcf, 0x4f, 0x13, 0x4f,
	0x14, 0xdf, 0x29, 0x6d, 0x81, 0x57, 0xbe, 0x5f, 0xd7, 0x01, 0xb4, 0x36, 0x38, 0x34, 0x8d, 0x31,
	0x85, 0xc4, 0xd6, 0x54, 0x13, 0x3d, 0x10, 0x93, 0x0a, 0x8b, 0x34, 0xe2, 0x16, 0xa6, 0xd3, 0xf8,
	0xe3, 0xd2, 0xd0, 0x76, 0x68, 0x09, 0xb0, 0xb3, 0xcc, 0xee, 0x4a, 0x7a, 0xf3, 0xe8, 0xd1, 0x3f,
	0xc3, 0x3f, 0xc5, 0x8b, 0x09, 0x47, 0x0e, 0x1e, 0x64, 0xb9, 0x78, 0xe4, 0x4f, 0x30, 0x4c, 0xb7,
	0x65, 0x5b, 0x5b, 0xe0, 0xf6, 0xde, 0xdb, 0xcf, 0x67, 0xe7, 0x7d, 0x3e, 0xef, 0xcd, 0xc0, 0x92,
	0xbd, 0xdf, 0xca, 0x3b, 0x8d, 0x36, 0x6f, 0x7a, 0x07, 0x5c, 0x5e, 0x45, 0x76, 0xfd, 0x2a, 0xce,
	0xd9, 0x52, 0xb8, 0x02, 0x27, 0x42, 0x1f, 0x53, 0x4f, 0x5a, 0x7b, 0x6e, 0xdb, 0xab, 0xe7, 0x1a,
	0xe2, 0x30, 0xdf, 0x12, 0x2d, 0x91, 0x57, 0x98, 0xba, 0xb7, 0xab, 0x32, 0x95, 0xa8, 0xa8, 0xcb,
	0x4d, 0x3d, 0x0f, 0xc1, 0x8f, 0xf9, 0xce, 0x67, 0x7e, 0x2c, 0xe4, 0xbe, 0x93, 0x6f, 0x88, 0xc3,
	0x43, 0x61, 0xe5, 0xdb, 0xae, 0x6b, 0xb7, 0xa4, 0xdd, 0xe8, 0x07, 0x5d, 0x56, 0xa6, 0x00, 0x78,
	0xdb, 0xe3, 0x72, 0x8f, 0x4b, 0x26, 0x2a, 0xbd, 0xc3, 0xf1, 0x02, 0x4c, 0x1f, 0x75, 0xab, 0xa5,
	0xb5, 0x24, 0x4a, 0xa3, 0xec, 0x34, 0xbd, 0x2a, 0x64, 0x7e, 0x22, 0xc0, 0x7d, 0x2c, 0x13, 0x01,
	0x1f, 0x27, 0x61, 0xf2, 0x12, 0xd3, 0x09, 0x28, 0x51, 0xda, 0x4b, 0xf1, 0x0b, 0x48, 0x5c, 0x1e,
	0x4b, 0xf9, 0x91, 0xc7, 0x1d, 0x37, 0x19, 0x49, 0xa3, 0x6c, 0xa2, 0x30, 0x9f, 0xeb, 0xb7, 0xb2,
	0xc1, 0xd8, 0x56, 0xf0, 0x91, 0x86, 0x91, 0x38, 0x0b, 0x77, 0x76, 0xa5, 0xb0, 0x5c, 0x6e, 0x35,
	0x8b, 0xcd, 0xa6, 0xe4, 0x8e, 0x93, 0x9c, 0x50, 0xdd, 0x0c, 0x97, 0xf1, 0x3d, 0x88, 0x7b, 0x8e,
	0x6a, 0x37, 0xaa, 0x00, 0x41, 0x86, 0x33, 0x30, 0xe3, 0xb8, 0x3b, 0xae, 0x63, 0x58, 0x3b, 0xf5,
	0x03, 0xde, 0x4c, 0xc6, 0xd2, 0x28, 0x3b, 0x45, 0x07, 0x6a, 0x99, 0xaf, 0x11, 0x98, 0x5d, 0x0f,
	0xfe, 0x17, 0x76, 0xe1, 0x25, 0x44, 0xdd, 0x8e, 0xcd, 0x95, 0x9a, 0xff, 0x0b, 0x8f, 0x72, 0xa1,
	0xe1, 0xe4, 0x46, 0xe0, 0x59, 0xc7, 0xe6, 0x54, 0x31, 0x46, 0xf5, 0x1d, 0x19, 0xdd, 0x77, 0xc8,
	0xb4, 0x89, 0x41, 0xd3, 0xc6, 0x29, 0x1a, 0x32, 0x33, 0x76, 0x6b, 0x33, 0x87, 0xad, 0x88, 0x8f,
	0xb0, 0x62, 0x1f, 0x66, 0x43, 0x93, 0xed, 0x89, 0xc4, 0xaf, 0x20, 0x7e, 0x09, 0xf3, 0x9c, 0xc0,
	0x8b, 0xc7, 0x03, 0x5e, 0x8c, 0x60, 0x54, 0x14, 0x9a, 0x06, 0x2c, 0x3c, 0x07, 0x31, 0x2e, 0xa5,
	0x90, 0x81, 0x0b, 0xdd, 0x24, 0xb3, 0x02, 0x0b, 0xa6, 0x70, 0xf7, 0x76, 0x3b, 0xc1, 0x06, 0x55,
	0xda, 0x9e, 0xdb, 0x14, 0xc7, 0x56, 0xaf, 0xe1, 0xeb, 0xb7, 0x70, 0x11, 0x1e, 0x8e, 0x61, 0x3b,
	0xb6, 0xb0, 0x1c, 0xbe, 0xbc, 0x02, 0xf7, 0xc7, 0x4c, 0x09, 0x4f, 0x41, 0xb4, 0x64, 0x96, 0x98,
	0xae, 0xe1, 0x04, 0x4c, 0x1a, 0xe6, 0x76, 0xd5, 0xa8, 0x1a, 0x3a, 0xc2, 0x00, 0xf1, 0xd5, 0xa2,
	0xb9, 0x6a, 0x6c, 0xea, 0x91, 0xe5, 0x06, 0x3c, 0x18, 0xab, 0x0b, 0xc7, 0x21, 0x52, 0x7e, 0xab,
	0x6b, 0x38, 0x0d, 0x0b, 0xac, 0x5c, 0xae, 0xbd, 0x2b, 0x9a, 0x1f, 0x6b, 0xd4, 0xd8, 0xae, 0x1a,
	0x15, 0x56, 0xa9, 0x6d, 0x19, 0xb4, 0xc6, 0x0c, 0xb3, 0x68, 0x32, 0x1d, 0xe1, 0x69, 0x88, 0x19,
	0x94, 0x96, 0xa9, 0x1e, 0xc1, 0x77, 0xe1, 0xbf, 0xca, 0x46, 0x95, 0xb1, 0x92, 0xf9, 0xa6, 0xb6,
	0x56, 0x7e, 0x6f, 0xea, 0x13, 0x85, 0x5f, 0x28, 0xe4, 0xf7, 0xba, 0x90, 0xbd, 0xab, 0x54, 0x85,
	0x44, 0x10, 0x6e, 0x0a, 0x61, 0xe3, 0xc5, 0x01, 0xbb, 0xff, 0xbd, 0xaf, 0xa9, 0xc5, 0x71, 0xf3,
	0x08, 0xb0, 0x19, 0x2d, 0x8b, 0x9e, 0x22, 0x6c, 0xc1, 0xfc, 0x48, 0xcb, 0xf0, 0xd2, 0x00, 0xff,
	0xba, 0xa1, 0xa4, 0x96, 0x6f, 0x03, 0xed, 0x4e, 0xa0, 0x60, 0xc3, 0x5c, 0x58, 0x5d, 0x7f, 0x9d,
	0x3e, 0xc0, 0x4c, 0x2f, 0x56, 0xfa, 0xd2, 0x37, 0x5d, 0xad, 0x54, 0xfa, 0xa6, 0x85, 0xeb, 0x2a,
	0x7c, 0x5d, 0x3c, 0x39, 0x23, 0xda, 0xe9, 0x19, 0xd1, 0x2e, 0xce, 0x08, 0xfa, 0xe2, 0x13, 0xf4,
	0xdd, 0x27, 0xe8, 0x87, 0x4f, 0xd0, 0x89, 0x4f, 0xd0, 0x6f, 0x9f, 0xa0, 0x3f, 0x3e, 0xd1, 0x2e,
	0x7c, 0x82, 0xbe, 0x9d, 0x13, 0xed, 0xe4, 0x9c, 0x68, 0xa7, 0xe7, 0x44, 0xfb, 0x14, 0x7e, 0x76,
	0xeb, 0x71, 0xf5, 0x30, 0x3e, 0xfb, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x10, 0x20, 0x45, 0xfc, 0xb7,
	0x05, 0x00, 0x00,
}

func (x FrontendToSchedulerType) String() string {
	s, ok := FrontendToSchedulerType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x SchedulerToFrontendStatus) String() string {
	s, ok := SchedulerToFrontendStatus_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *QuerierToScheduler) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QuerierToScheduler)
	if !ok {
		that2, ok := that.(QuerierToScheduler)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.QuerierID != that1.QuerierID {
		return false
	}
	return true
}
func (this *SchedulerToQuerier) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SchedulerToQuerier)
	if !ok {
		that2, ok := that.(SchedulerToQuerier)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.QueryID != that1.QueryID {
		return false
	}
	if !this.HttpRequest.Equal(that1.HttpRequest) {
		return false
	}
	if this.FrontendAddress != that1.FrontendAddress {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if this.StatsEnabled != that1.StatsEnabled {
		return false
	}
	return true
}
func (this *FrontendToScheduler) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*FrontendToScheduler)
	if !ok {
		that2, ok := that.(FrontendToScheduler)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.FrontendAddress != that1.FrontendAddress {
		return false
	}
	if this.QueryID != that1.QueryID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.HttpRequest.Equal(that1.HttpRequest) {
		return false
	}
	if this.StatsEnabled != that1.StatsEnabled {
		return false
	}
	return true
}
func (this *SchedulerToFrontend) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SchedulerToFrontend)
	if !ok {
		that2, ok := that.(SchedulerToFrontend)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Status != that1.Status {
		return false
	}
	if this.Error != that1.Error {
		return false
	}
	return true
}
func (this *NotifyQuerierShutdownRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*NotifyQuerierShutdownRequest)
	if !ok {
		that2, ok := that.(NotifyQuerierShutdownRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.QuerierID != that1.QuerierID {
		return false
	}
	return true
}
func (this *NotifyQuerierShutdownResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*NotifyQuerierShutdownResponse)
	if !ok {
		that2, ok := that.(NotifyQuerierShutdownResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *QuerierToScheduler) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&schedulerpb.QuerierToScheduler{")
	s = append(s, "QuerierID: "+fmt.Sprintf("%#v", this.QuerierID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SchedulerToQuerier) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&schedulerpb.SchedulerToQuerier{")
	s = append(s, "QueryID: "+fmt.Sprintf("%#v", this.QueryID)+",\n")
	if this.HttpRequest != nil {
		s = append(s, "HttpRequest: "+fmt.Sprintf("%#v", this.HttpRequest)+",\n")
	}
	s = append(s, "FrontendAddress: "+fmt.Sprintf("%#v", this.FrontendAddress)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	s = append(s, "StatsEnabled: "+fmt.Sprintf("%#v", this.StatsEnabled)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *FrontendToScheduler) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 10)
	s = append(s, "&schedulerpb.FrontendToScheduler{")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "FrontendAddress: "+fmt.Sprintf("%#v", this.FrontendAddress)+",\n")
	s = append(s, "QueryID: "+fmt.Sprintf("%#v", this.QueryID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	if this.HttpRequest != nil {
		s = append(s, "HttpRequest: "+fmt.Sprintf("%#v", this.HttpRequest)+",\n")
	}
	s = append(s, "StatsEnabled: "+fmt.Sprintf("%#v", this.StatsEnabled)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SchedulerToFrontend) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&schedulerpb.SchedulerToFrontend{")
	s = append(s, "Status: "+fmt.Sprintf("%#v", this.Status)+",\n")
	s = append(s, "Error: "+fmt.Sprintf("%#v", this.Error)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *NotifyQuerierShutdownRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&schedulerpb.NotifyQuerierShutdownRequest{")
	s = append(s, "QuerierID: "+fmt.Sprintf("%#v", this.QuerierID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *NotifyQuerierShutdownResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&schedulerpb.NotifyQuerierShutdownResponse{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringScheduler(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SchedulerForQuerierClient is the client API for SchedulerForQuerier service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SchedulerForQuerierClient interface {
	// After calling this method, both Querier and Scheduler enter a loop, in which querier waits for
	// "SchedulerToQuerier" messages containing HTTP requests and processes them. After processing the request,
	// querier signals that it is ready to accept another one by sending empty QuerierToScheduler message.
	//
	// Long-running loop is used to detect broken connection between scheduler and querier. This is important
	// for scheduler to keep a list of connected queriers up-to-date.
	QuerierLoop(ctx context.Context, opts ...grpc.CallOption) (SchedulerForQuerier_QuerierLoopClient, error)
	// The querier notifies the query-scheduler that it started a graceful shutdown.
	NotifyQuerierShutdown(ctx context.Context, in *NotifyQuerierShutdownRequest, opts ...grpc.CallOption) (*NotifyQuerierShutdownResponse, error)
}

type schedulerForQuerierClient struct {
	cc *grpc.ClientConn
}

func NewSchedulerForQuerierClient(cc *grpc.ClientConn) SchedulerForQuerierClient {
	return &schedulerForQuerierClient{cc}
}

func (c *schedulerForQuerierClient) QuerierLoop(ctx context.Context, opts ...grpc.CallOption) (SchedulerForQuerier_QuerierLoopClient, error) {
	stream, err := c.cc.NewStream(ctx, &_SchedulerForQuerier_serviceDesc.Streams[0], "/schedulerpb.SchedulerForQuerier/QuerierLoop", opts...)
	if err != nil {
		return nil, err
	}
	x := &schedulerForQuerierQuerierLoopClient{stream}
	return x, nil
}

type SchedulerForQuerier_QuerierLoopClient interface {
	Send(*QuerierToScheduler) error
	Recv() (*SchedulerToQuerier, error)
	grpc.ClientStream
}

type schedulerForQuerierQuerierLoopClient struct {
	grpc.ClientStream
}

func (x *schedulerForQuerierQuerierLoopClient) Send(m *QuerierToScheduler) error {
	return x.ClientStream.SendMsg(m)
}

func (x *schedulerForQuerierQuerierLoopClient) Recv() (*SchedulerToQuerier, error) {
	m := new(SchedulerToQuerier)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *schedulerForQuerierClient) NotifyQuerierShutdown(ctx context.Context, in *NotifyQuerierShutdownRequest, opts ...grpc.CallOption) (*NotifyQuerierShutdownResponse, error) {
	out := new(NotifyQuerierShutdownResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.SchedulerForQuerier/NotifyQuerierShutdown", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SchedulerForQuerierServer is the server API for SchedulerForQuerier service.
type SchedulerForQuerierServer interface {
	// After calling this method, both Querier and Scheduler enter a loop, in which querier waits for
	// "SchedulerToQuerier" messages containing HTTP requests and processes them. After processing the request,
	// querier signals that it is ready to accept another one by sending empty QuerierToScheduler message.
	//
	// Long-running loop is used to detect broken connection between scheduler and querier. This is important
	// for scheduler to keep a list of connected queriers up-to-date.
	QuerierLoop(SchedulerForQuerier_QuerierLoopServer) error
	// The querier notifies the query-scheduler that it started a graceful shutdown.
	NotifyQuerierShutdown(context.Context, *NotifyQuerierShutdownRequest) (*NotifyQuerierShutdownResponse, error)
}

// UnimplementedSchedulerForQuerierServer can be embedded to have forward compatible implementations.
type UnimplementedSchedulerForQuerierServer struct {
}

func (*UnimplementedSchedulerForQuerierServer) QuerierLoop(srv SchedulerForQuerier_QuerierLoopServer) error {
	return status.Errorf(codes.Unimplemented, "method QuerierLoop not implemented")
}
func (*UnimplementedSchedulerForQuerierServer) NotifyQuerierShutdown(ctx context.Context, req *NotifyQuerierShutdownRequest) (*NotifyQuerierShutdownResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyQuerierShutdown not implemented")
}

func RegisterSchedulerForQuerierServer(s *grpc.Server, srv SchedulerForQuerierServer) {
	s.RegisterService(&_SchedulerForQuerier_serviceDesc, srv)
}

func _SchedulerForQuerier_QuerierLoop_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SchedulerForQuerierServer).QuerierLoop(&schedulerForQuerierQuerierLoopServer{stream})
}

type SchedulerForQuerier_QuerierLoopServer interface {
	Send(*SchedulerToQuerier) error
	Recv() (*QuerierToScheduler, error)
	grpc.ServerStream
}

type schedulerForQuerierQuerierLoopServer struct {
	grpc.ServerStream
}

func (x *schedulerForQuerierQuerierLoopServer) Send(m *SchedulerToQuerier) error {
	return x.ServerStream.SendMsg(m)
}

func (x *schedulerForQuerierQuerierLoopServer) Recv() (*QuerierToScheduler, error) {
	m := new(QuerierToScheduler)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _SchedulerForQuerier_NotifyQuerierShutdown_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyQuerierShutdownRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerForQuerierServer).NotifyQuerierShutdown(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.SchedulerForQuerier/NotifyQuerierShutdown",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerForQuerierServer).NotifyQuerierShutdown(ctx, req.(*NotifyQuerierShutdownRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SchedulerForQuerier_serviceDesc = grpc.ServiceDesc{
	ServiceName: "schedulerpb.SchedulerForQuerier",
	HandlerType: (*SchedulerForQuerierServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NotifyQuerierShutdown",
			Handler:    _SchedulerForQuerier_NotifyQuerierShutdown_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "QuerierLoop",
			Handler:       _SchedulerForQuerier_QuerierLoop_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "pkg/scheduler/schedulerpb/scheduler.proto",
}

// SchedulerForFrontendClient is the client API for SchedulerForFrontend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SchedulerForFrontendClient interface {
	// After calling this method, both Frontend and Scheduler enter a loop. Frontend will keep sending ENQUEUE and
	// CANCEL requests, and scheduler is expected to process them. Scheduler returns one response for each request.
	//
	// Long-running loop is used to detect broken connection between frontend and scheduler. This is important for both
	// parties... if connection breaks, frontend can cancel (and possibly retry on different scheduler) all pending
	// requests sent to this scheduler, while scheduler can cancel queued requests from given frontend.
	FrontendLoop(ctx context.Context, opts ...grpc.CallOption) (SchedulerForFrontend_FrontendLoopClient, error)
}

type schedulerForFrontendClient struct {
	cc *grpc.ClientConn
}

func NewSchedulerForFrontendClient(cc *grpc.ClientConn) SchedulerForFrontendClient {
	return &schedulerForFrontendClient{cc}
}

func (c *schedulerForFrontendClient) FrontendLoop(ctx context.Context, opts ...grpc.CallOption) (SchedulerForFrontend_FrontendLoopClient, error) {
	stream, err := c.cc.NewStream(ctx, &_SchedulerForFrontend_serviceDesc.Streams[0], "/schedulerpb.SchedulerForFrontend/FrontendLoop", opts...)
	if err != nil {
		return nil, err
	}
	x := &schedulerForFrontendFrontendLoopClient{stream}
	return x, nil
}

type SchedulerForFrontend_FrontendLoopClient interface {
	Send(*FrontendToScheduler) error
	Recv() (*SchedulerToFrontend, error)
	grpc.ClientStream
}

type schedulerForFrontendFrontendLoopClient struct {
	grpc.ClientStream
}

func (x *schedulerForFrontendFrontendLoopClient) Send(m *FrontendToScheduler) error {
	return x.ClientStream.SendMsg(m)
}

func (x *schedulerForFrontendFrontendLoopClient) Recv() (*SchedulerToFrontend, error) {
	m := new(SchedulerToFrontend)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// SchedulerForFrontendServer is the server API for SchedulerForFrontend service.
type SchedulerForFrontendServer interface {
	// After calling this method, both Frontend and Scheduler enter a loop. Frontend will keep sending ENQUEUE and
	// CANCEL requests, and scheduler is expected to process them. Scheduler returns one response for each request.
	//
	// Long-running loop is used to detect broken connection between frontend and scheduler. This is important for both
	// parties... if connection breaks, frontend can cancel (and possibly retry on different scheduler) all pending
	// requests sent to this scheduler, while scheduler can cancel queued requests from given frontend.
	FrontendLoop(SchedulerForFrontend_FrontendLoopServer) error
}

// UnimplementedSchedulerForFrontendServer can be embedded to have forward compatible implementations.
type UnimplementedSchedulerForFrontendServer struct {
}

func (*UnimplementedSchedulerForFrontendServer) FrontendLoop(srv SchedulerForFrontend_FrontendLoopServer) error {
	return status.Errorf(codes.Unimplemented, "method FrontendLoop not implemented")
}

func RegisterSchedulerForFrontendServer(s *grpc.Server, srv SchedulerForFrontendServer) {
	s.RegisterService(&_SchedulerForFrontend_serviceDesc, srv)
}

func _SchedulerForFrontend_FrontendLoop_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SchedulerForFrontendServer).FrontendLoop(&schedulerForFrontendFrontendLoopServer{stream})
}

type SchedulerForFrontend_FrontendLoopServer interface {
	Send(*SchedulerToFrontend) error
	Recv() (*FrontendToScheduler, error)
	grpc.ServerStream
}

type schedulerForFrontendFrontendLoopServer struct {
	grpc.ServerStream
}

func (x *schedulerForFrontendFrontendLoopServer) Send(m *SchedulerToFrontend) error {
	return x.ServerStream.SendMsg(m)
}

func (x *schedulerForFrontendFrontendLoopServer) Recv() (*FrontendToScheduler, error) {
	m := new(FrontendToScheduler)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _SchedulerForFrontend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "schedulerpb.SchedulerForFrontend",
	HandlerType: (*SchedulerForFrontendServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "FrontendLoop",
			Handler:       _SchedulerForFrontend_FrontendLoop_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "pkg/scheduler/schedulerpb/scheduler.proto",
}

func (m *QuerierToScheduler) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuerierToScheduler) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QuerierToScheduler) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.QuerierID) > 0 {
		i -= len(m.QuerierID)
		copy(dAtA[i:], m.QuerierID)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.QuerierID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SchedulerToQuerier) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SchedulerToQuerier) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SchedulerToQuerier) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StatsEnabled {
		i--
		if m.StatsEnabled {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.FrontendAddress) > 0 {
		i -= len(m.FrontendAddress)
		copy(dAtA[i:], m.FrontendAddress)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.FrontendAddress)))
		i--
		dAtA[i] = 0x1a
	}
	if m.HttpRequest != nil {
		{
			size, err := m.HttpRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintScheduler(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.QueryID != 0 {
		i = encodeVarintScheduler(dAtA, i, uint64(m.QueryID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *FrontendToScheduler) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FrontendToScheduler) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FrontendToScheduler) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StatsEnabled {
		i--
		if m.StatsEnabled {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.HttpRequest != nil {
		{
			size, err := m.HttpRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintScheduler(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x22
	}
	if m.QueryID != 0 {
		i = encodeVarintScheduler(dAtA, i, uint64(m.QueryID))
		i--
		dAtA[i] = 0x18
	}
	if len(m.FrontendAddress) > 0 {
		i -= len(m.FrontendAddress)
		copy(dAtA[i:], m.FrontendAddress)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.FrontendAddress)))
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintScheduler(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SchedulerToFrontend) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SchedulerToFrontend) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SchedulerToFrontend) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0x12
	}
	if m.Status != 0 {
		i = encodeVarintScheduler(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *NotifyQuerierShutdownRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyQuerierShutdownRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NotifyQuerierShutdownRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.QuerierID) > 0 {
		i -= len(m.QuerierID)
		copy(dAtA[i:], m.QuerierID)
		i = encodeVarintScheduler(dAtA, i, uint64(len(m.QuerierID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *NotifyQuerierShutdownResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyQuerierShutdownResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NotifyQuerierShutdownResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func encodeVarintScheduler(dAtA []byte, offset int, v uint64) int {
	offset -= sovScheduler(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *QuerierToScheduler) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.QuerierID)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	return n
}

func (m *SchedulerToQuerier) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.QueryID != 0 {
		n += 1 + sovScheduler(uint64(m.QueryID))
	}
	if m.HttpRequest != nil {
		l = m.HttpRequest.Size()
		n += 1 + l + sovScheduler(uint64(l))
	}
	l = len(m.FrontendAddress)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	if m.StatsEnabled {
		n += 2
	}
	return n
}

func (m *FrontendToScheduler) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovScheduler(uint64(m.Type))
	}
	l = len(m.FrontendAddress)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	if m.QueryID != 0 {
		n += 1 + sovScheduler(uint64(m.QueryID))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	if m.HttpRequest != nil {
		l = m.HttpRequest.Size()
		n += 1 + l + sovScheduler(uint64(l))
	}
	if m.StatsEnabled {
		n += 2
	}
	return n
}

func (m *SchedulerToFrontend) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Status != 0 {
		n += 1 + sovScheduler(uint64(m.Status))
	}
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	return n
}

func (m *NotifyQuerierShutdownRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.QuerierID)
	if l > 0 {
		n += 1 + l + sovScheduler(uint64(l))
	}
	return n
}

func (m *NotifyQuerierShutdownResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func sovScheduler(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozScheduler(x uint64) (n int) {
	return sovScheduler(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *QuerierToScheduler) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&QuerierToScheduler{`,
		`QuerierID:` + fmt.Sprintf("%v", this.QuerierID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SchedulerToQuerier) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SchedulerToQuerier{`,
		`QueryID:` + fmt.Sprintf("%v", this.QueryID) + `,`,
		`HttpRequest:` + strings.Replace(fmt.Sprintf("%v", this.HttpRequest), "HTTPRequest", "httpgrpc.HTTPRequest", 1) + `,`,
		`FrontendAddress:` + fmt.Sprintf("%v", this.FrontendAddress) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`StatsEnabled:` + fmt.Sprintf("%v", this.StatsEnabled) + `,`,
		`}`,
	}, "")
	return s
}
func (this *FrontendToScheduler) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&FrontendToScheduler{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`FrontendAddress:` + fmt.Sprintf("%v", this.FrontendAddress) + `,`,
		`QueryID:` + fmt.Sprintf("%v", this.QueryID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`HttpRequest:` + strings.Replace(fmt.Sprintf("%v", this.HttpRequest), "HTTPRequest", "httpgrpc.HTTPRequest", 1) + `,`,
		`StatsEnabled:` + fmt.Sprintf("%v", this.StatsEnabled) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SchedulerToFrontend) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SchedulerToFrontend{`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`Error:` + fmt.Sprintf("%v", this.Error) + `,`,
		`}`,
	}, "")
	return s
}
func (this *NotifyQuerierShutdownRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&NotifyQuerierShutdownRequest{`,
		`QuerierID:` + fmt.Sprintf("%v", this.QuerierID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *NotifyQuerierShutdownResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&NotifyQuerierShutdownResponse{`,
		`}`,
	}, "")
	return s
}
func valueToStringScheduler(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *QuerierToScheduler) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QuerierToScheduler: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QuerierToScheduler: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field QuerierID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.QuerierID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipScheduler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SchedulerToQuerier) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SchedulerToQuerier: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SchedulerToQuerier: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryID", wireType)
			}
			m.QueryID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QueryID |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HttpRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HttpRequest == nil {
				m.HttpRequest = &httpgrpc.HTTPRequest{}
			}
			if err := m.HttpRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FrontendAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FrontendAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StatsEnabled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StatsEnabled = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipScheduler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FrontendToScheduler) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FrontendToScheduler: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FrontendToScheduler: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= FrontendToSchedulerType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FrontendAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FrontendAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryID", wireType)
			}
			m.QueryID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QueryID |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HttpRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HttpRequest == nil {
				m.HttpRequest = &httpgrpc.HTTPRequest{}
			}
			if err := m.HttpRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StatsEnabled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StatsEnabled = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipScheduler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SchedulerToFrontend) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SchedulerToFrontend: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SchedulerToFrontend: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= SchedulerToFrontendStatus(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipScheduler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyQuerierShutdownRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotifyQuerierShutdownRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotifyQuerierShutdownRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field QuerierID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthScheduler
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthScheduler
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.QuerierID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipScheduler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyQuerierShutdownResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotifyQuerierShutdownResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotifyQuerierShutdownResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipScheduler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthScheduler
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipScheduler(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowScheduler
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowScheduler
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthScheduler
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthScheduler
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowScheduler
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipScheduler(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthScheduler
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthScheduler = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowScheduler   = fmt.Errorf("proto: integer overflow")
)
