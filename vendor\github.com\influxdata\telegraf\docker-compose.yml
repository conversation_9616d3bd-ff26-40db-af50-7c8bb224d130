version: '3'

services:
  aerospike:
    image: aerospike/aerospike-server:********
    ports:
      - "3000:3000"
      - "3001:3001"
      - "3002:3002"
      - "3003:3003"
  zookeeper:
    image: wurstmeister/zookeeper
    environment:
      - JAVA_OPTS="-Xms256m -Xmx256m"
    ports:
      - "2181:2181"
  kafka:
    image: wurstmeister/kafka
    environment:
      - KAFKA_ADVERTISED_HOST_NAME=localhost
      - KAFKA_ADVERTISED_PORT=9092
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - JAVA_OPTS="-Xms256m -Xmx256m"
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.2.0
    environment:
    - "ES_JAVA_OPTS=-Xms256m -Xmx256m"
    - discovery.type=single-node
    - xpack.security.enabled=false
    ports:
      - "9200:9200"
  mysql:
    image: mysql
    environment:
      - MYSQL_ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "3306:3306"
  memcached:
    image: memcached
    ports:
      - "11211:11211"
  pgbouncer:
    image: mbentley/ubuntu-pgbouncer
    environment:
      - PG_ENV_POSTGRESQL_USER=pgbouncer
      - PG_ENV_POSTGRESQL_PASS=pgbouncer
    ports:
      - "6432:6432"
  postgres:
    image: postgres:alpine
    environment: 
      - POSTGRES_HOST_AUTH_METHOD=trust
    ports:
      - "5432:5432"
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "15672:15672"
      - "5672:5672"
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  nsq:
    image: nsqio/nsq
    ports:
      - "4150:4150"
    command: "/nsqd"
  mqtt:
    image: ncarlier/mqtt
    ports:
      - "1883:1883"
  riemann:
    image: stealthly/docker-riemann
    ports:
      - "5555:5555"
  nats:
    image: nats
    ports:
      - "4222:4222"
  openldap:
    image: cobaugh/openldap-alpine
    environment:
      - SLAPD_CONFIG_ROOTDN="cn=manager,cn=config"
      - SLAPD_CONFIG_ROOTPW="secret"
    ports:
      - "389:389"
      - "636:636"
  crate:
    image: crate/crate
    ports:
      - "4200:4200"
      - "4230:4230"
      - "6543:5432"
    command:
      - crate
      - -Cnetwork.host=0.0.0.0
      - -Ctransport.host=localhost
    environment:
      - CRATE_HEAP_SIZE=128m
