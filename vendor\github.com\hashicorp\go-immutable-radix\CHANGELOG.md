# UNRELEASED

# 1.3.0 (September 17th, 2020)

FEATURES

* Add reverse tree traversal [[GH-30](https://github.com/hashicorp/go-immutable-radix/pull/30)]

# 1.2.0 (March 18th, 2020)

FEATURES

* Adds a `Clone` method to `Txn` allowing transactions to be split either into two independently mutable trees. [[GH-26](https://github.com/hashicorp/go-immutable-radix/pull/26)]

# 1.1.0 (May 22nd, 2019)

FEATURES

* Add `SeekLowerBound` to allow for range scans. [[GH-24](https://github.com/hashicorp/go-immutable-radix/pull/24)]

# 1.0.0 (August 30th, 2018)

* go mod adopted
