language: go
sudo: false

go:
  - 1.2.x
  - 1.3.x
  - 1.4.x
  - 1.5.x
  - 1.6.x
  - 1.7.x
  - 1.8.x
  - 1.9.x
  - 1.10.x
  - 1.11.x
  - 1.12.x
  - 1.13.x
  - 1.14.x
  - master

matrix:
  include:
  - go: 1.14.x
    env: TEST_REAL_SERVER=rackspace
  - go: 1.14.x
    env: TEST_REAL_SERVER=memset
  allow_failures:
  - go: 1.14.x
    env: TEST_REAL_SERVER=rackspace
  - go: 1.14.x
    env: TEST_REAL_SERVER=memset
install: go test -i ./...
script:
  - test -z "$(go fmt ./...)"
  - go test
  - ./travis_realserver.sh
