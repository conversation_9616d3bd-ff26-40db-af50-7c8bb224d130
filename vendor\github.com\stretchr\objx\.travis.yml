language: go
go:
  - "1.10.x"
  - "1.11.x"
  - "1.12.x"
  - master

matrix:
  allow_failures:
    - go: master
fast_finish: true

env:
  global:
    - CC_TEST_REPORTER_ID=68feaa3410049ce73e145287acbcdacc525087a30627f96f04e579e75bd71c00

before_script:
  - curl -L https://codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ./cc-test-reporter
  - chmod +x ./cc-test-reporter
  - ./cc-test-reporter before-build

install:
  - curl -sL https://taskfile.dev/install.sh | sh

script:
  - diff -u <(echo -n) <(./bin/task lint)
  - ./bin/task test-coverage

after_script:
  - ./cc-test-reporter after-build --exit-code $TRAVIS_TEST_RESULT
