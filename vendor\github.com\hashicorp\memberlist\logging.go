package memberlist

import (
	"fmt"
	"net"
)

func LogAddress(addr net.Addr) string {
	if addr == nil {
		return "from=<unknown address>"
	}

	return fmt.Sprintf("from=%s", addr.String())
}

func LogStringAddress(addr string) string {
	if addr == "" {
		return "from=<unknown address>"
	}

	return fmt.Sprintf("from=%s", addr)
}

func LogConn(conn net.Conn) string {
	if conn == nil {
		return LogAddress(nil)
	}

	return LogAddress(conn.RemoteAddr())
}
