{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "fluent-bit-loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "fluent-bit-loki.name" . }}
    chart: {{ template "fluent-bit-loki.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
{{- if .Values.rbac.pspEnabled }}
rules:
- apiGroups:      ['extensions']
  resources:      ['podsecuritypolicies']
  verbs:          ['use']
  resourceNames:  [{{ template "fluent-bit-loki.fullname" . }}]
{{- end }}
{{- end }}
