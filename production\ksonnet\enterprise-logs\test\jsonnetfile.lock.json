{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "consul"}}, "version": "7e3954ca9459cad6508ce3675876dbe4f4b33a8f", "sum": "Po3c1Ic96ngrJCtOazic/7OsLkoILOKZWXWyZWl+od8="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "jaeger-agent-mixin"}}, "version": "7e3954ca9459cad6508ce3675876dbe4f4b33a8f", "sum": "DsdBoqgx5kE3zc6fMYnfiGjW2+9Mx2OXFieWm1oFHgY="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "ksonnet-util"}}, "version": "7e3954ca9459cad6508ce3675876dbe4f4b33a8f", "sum": "OxgtIWL4hjvG0xkMwUzZ7Yjs52zUhLhaVQpwHCbqf8A="}, {"source": {"git": {"remote": "https://github.com/grafana/jsonnet-libs.git", "subdir": "memcached"}}, "version": "7e3954ca9459cad6508ce3675876dbe4f4b33a8f", "sum": "dTOeEux3t9bYSqP2L/uCuLo/wUDpCKH4w+4OD9fePUk="}, {"source": {"git": {"remote": "https://github.com/grafana/loki.git", "subdir": "production/ksonnet/loki"}}, "version": "cd79adc8ab0c80c55a0f39b43322839c6ffb05d8", "sum": "ihooLlOemtJlNiFzAPYM06ug4kLKtOMTJ0SWRXWNCuA="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/k8s-libsonnet.git", "subdir": "1.18"}}, "version": "91008dbd2ea5734288467d6dcafef7c285c3f7e6", "sum": "x881PM+6ARMsa9OSJcxO6L+4GOBy91clZipjeYbbzpw="}], "legacyImports": false}