// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/querier/stats/stats.proto

package stats

import (
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	_ "github.com/golang/protobuf/ptypes/duration"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Stats struct {
	// The sum of all wall time spent in the querier to execute the query.
	WallTime time.Duration `protobuf:"bytes,1,opt,name=wall_time,json=wallTime,proto3,stdduration" json:"wall_time"`
	// The number of series fetched for the query
	FetchedSeriesCount uint64 `protobuf:"varint,2,opt,name=fetched_series_count,json=fetchedSeriesCount,proto3" json:"fetched_series_count,omitempty"`
	// The number of bytes of the chunks fetched for the query
	FetchedChunkBytes uint64 `protobuf:"varint,3,opt,name=fetched_chunk_bytes,json=fetchedChunkBytes,proto3" json:"fetched_chunk_bytes,omitempty"`
}

func (m *Stats) Reset()      { *m = Stats{} }
func (*Stats) ProtoMessage() {}
func (*Stats) Descriptor() ([]byte, []int) {
	return fileDescriptor_8ca2404f80bab2e8, []int{0}
}
func (m *Stats) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Stats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Stats.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Stats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Stats.Merge(m, src)
}
func (m *Stats) XXX_Size() int {
	return m.Size()
}
func (m *Stats) XXX_DiscardUnknown() {
	xxx_messageInfo_Stats.DiscardUnknown(m)
}

var xxx_messageInfo_Stats proto.InternalMessageInfo

func (m *Stats) GetWallTime() time.Duration {
	if m != nil {
		return m.WallTime
	}
	return 0
}

func (m *Stats) GetFetchedSeriesCount() uint64 {
	if m != nil {
		return m.FetchedSeriesCount
	}
	return 0
}

func (m *Stats) GetFetchedChunkBytes() uint64 {
	if m != nil {
		return m.FetchedChunkBytes
	}
	return 0
}

func init() {
	proto.RegisterType((*Stats)(nil), "stats.Stats")
}

func init() { proto.RegisterFile("pkg/querier/stats/stats.proto", fileDescriptor_8ca2404f80bab2e8) }

var fileDescriptor_8ca2404f80bab2e8 = []byte{
	// 308 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x90, 0x31, 0x4e, 0xf3, 0x30,
	0x18, 0x86, 0xfd, 0xfd, 0x3f, 0x45, 0x25, 0x4c, 0x04, 0x86, 0x52, 0x89, 0xaf, 0x15, 0x53, 0x19,
	0x48, 0x10, 0x5c, 0x00, 0xb5, 0x9c, 0xa0, 0x65, 0x62, 0x89, 0x9c, 0xd4, 0x75, 0xad, 0xa6, 0x71,
	0x49, 0x6c, 0x21, 0x36, 0x8e, 0xc0, 0xc8, 0x11, 0x90, 0xb8, 0x48, 0xc7, 0x8e, 0x9d, 0x80, 0xba,
	0x0b, 0x63, 0x8f, 0x80, 0xec, 0xa4, 0x02, 0x89, 0xc5, 0xf2, 0xab, 0xc7, 0x8f, 0xa5, 0xf7, 0xf5,
	0x4e, 0x66, 0x13, 0x1e, 0xde, 0x6b, 0x96, 0x0b, 0x96, 0x87, 0x85, 0xa2, 0xaa, 0x28, 0xcf, 0x60,
	0x96, 0x4b, 0x25, 0xfd, 0x9a, 0x0b, 0xcd, 0x73, 0x2e, 0xd4, 0x58, 0xc7, 0x41, 0x22, 0xa7, 0x21,
	0x97, 0x5c, 0x86, 0x8e, 0xc6, 0x7a, 0xe4, 0x92, 0x0b, 0xee, 0x56, 0x5a, 0x4d, 0xe4, 0x52, 0xf2,
	0x94, 0xfd, 0xbc, 0x1a, 0xea, 0x9c, 0x2a, 0x21, 0xb3, 0x92, 0x9f, 0xbe, 0x81, 0x57, 0x1b, 0xd8,
	0x8f, 0xfd, 0x6b, 0x6f, 0xef, 0x81, 0xa6, 0x69, 0xa4, 0xc4, 0x94, 0x35, 0xa0, 0x0d, 0x9d, 0xfd,
	0xcb, 0xe3, 0xa0, 0xb4, 0x83, 0xad, 0x1d, 0xdc, 0x54, 0x76, 0xb7, 0x3e, 0x7f, 0x6f, 0x91, 0x97,
	0x8f, 0x16, 0xf4, 0xeb, 0xd6, 0xba, 0x15, 0x53, 0xe6, 0x5f, 0x78, 0x47, 0x23, 0xa6, 0x92, 0x31,
	0x1b, 0x46, 0x85, 0x6d, 0x51, 0x44, 0x89, 0xd4, 0x99, 0x6a, 0xfc, 0x6b, 0x43, 0x67, 0xa7, 0xef,
	0x57, 0x6c, 0xe0, 0x50, 0xcf, 0x12, 0x3f, 0xf0, 0x0e, 0xb7, 0x46, 0x32, 0xd6, 0xd9, 0x24, 0x8a,
	0x1f, 0x15, 0x2b, 0x1a, 0xff, 0x9d, 0x70, 0x50, 0xa1, 0x9e, 0x25, 0x5d, 0x0b, 0xba, 0xd1, 0x62,
	0x85, 0x64, 0xb9, 0x42, 0xb2, 0x59, 0x21, 0x3c, 0x19, 0x84, 0x57, 0x83, 0x30, 0x37, 0x08, 0x0b,
	0x83, 0xf0, 0x69, 0x10, 0xbe, 0x0c, 0x92, 0x8d, 0x41, 0x78, 0x5e, 0x23, 0x59, 0xac, 0x91, 0x2c,
	0xd7, 0x48, 0xee, 0xce, 0x7e, 0x4f, 0x96, 0xd3, 0x11, 0xcd, 0x68, 0x98, 0xca, 0x89, 0x08, 0xff,
	0x0c, 0x1e, 0xef, 0xba, 0xa6, 0x57, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x7f, 0xcb, 0x18, 0x2b,
	0x8c, 0x01, 0x00, 0x00,
}

func (this *Stats) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Stats)
	if !ok {
		that2, ok := that.(Stats)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.WallTime != that1.WallTime {
		return false
	}
	if this.FetchedSeriesCount != that1.FetchedSeriesCount {
		return false
	}
	if this.FetchedChunkBytes != that1.FetchedChunkBytes {
		return false
	}
	return true
}
func (this *Stats) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&stats.Stats{")
	s = append(s, "WallTime: "+fmt.Sprintf("%#v", this.WallTime)+",\n")
	s = append(s, "FetchedSeriesCount: "+fmt.Sprintf("%#v", this.FetchedSeriesCount)+",\n")
	s = append(s, "FetchedChunkBytes: "+fmt.Sprintf("%#v", this.FetchedChunkBytes)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringStats(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *Stats) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Stats) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Stats) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FetchedChunkBytes != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.FetchedChunkBytes))
		i--
		dAtA[i] = 0x18
	}
	if m.FetchedSeriesCount != 0 {
		i = encodeVarintStats(dAtA, i, uint64(m.FetchedSeriesCount))
		i--
		dAtA[i] = 0x10
	}
	n1, err1 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.WallTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.WallTime):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintStats(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func encodeVarintStats(dAtA []byte, offset int, v uint64) int {
	offset -= sovStats(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Stats) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.WallTime)
	n += 1 + l + sovStats(uint64(l))
	if m.FetchedSeriesCount != 0 {
		n += 1 + sovStats(uint64(m.FetchedSeriesCount))
	}
	if m.FetchedChunkBytes != 0 {
		n += 1 + sovStats(uint64(m.FetchedChunkBytes))
	}
	return n
}

func sovStats(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozStats(x uint64) (n int) {
	return sovStats(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *Stats) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Stats{`,
		`WallTime:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.WallTime), "Duration", "duration.Duration", 1), `&`, ``, 1) + `,`,
		`FetchedSeriesCount:` + fmt.Sprintf("%v", this.FetchedSeriesCount) + `,`,
		`FetchedChunkBytes:` + fmt.Sprintf("%v", this.FetchedChunkBytes) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringStats(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *Stats) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStats
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Stats: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Stats: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WallTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStats
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStats
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.WallTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FetchedSeriesCount", wireType)
			}
			m.FetchedSeriesCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchedSeriesCount |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FetchedChunkBytes", wireType)
			}
			m.FetchedChunkBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStats
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchedChunkBytes |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStats(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthStats
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipStats(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowStats
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStats
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStats
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthStats
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthStats
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowStats
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipStats(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthStats
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthStats = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowStats   = fmt.Errorf("proto: integer overflow")
)
