/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// RuntimeClassSpecApplyConfiguration represents an declarative configuration of the RuntimeClassSpec type for use
// with apply.
type RuntimeClassSpecApplyConfiguration struct {
	RuntimeHandler *string                       `json:"runtimeHandler,omitempty"`
	Overhead       *OverheadApplyConfiguration   `json:"overhead,omitempty"`
	Scheduling     *SchedulingApplyConfiguration `json:"scheduling,omitempty"`
}

// RuntimeClassSpecApplyConfiguration constructs an declarative configuration of the RuntimeClassSpec type for use with
// apply.
func RuntimeClassSpec() *RuntimeClassSpecApplyConfiguration {
	return &RuntimeClassSpecApplyConfiguration{}
}

// WithRuntimeHandler sets the RuntimeHandler field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RuntimeHandler field is set to the value of the last call.
func (b *RuntimeClassSpecApplyConfiguration) WithRuntimeHandler(value string) *RuntimeClassSpecApplyConfiguration {
	b.RuntimeHandler = &value
	return b
}

// WithOverhead sets the Overhead field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Overhead field is set to the value of the last call.
func (b *RuntimeClassSpecApplyConfiguration) WithOverhead(value *OverheadApplyConfiguration) *RuntimeClassSpecApplyConfiguration {
	b.Overhead = value
	return b
}

// WithScheduling sets the Scheduling field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Scheduling field is set to the value of the last call.
func (b *RuntimeClassSpecApplyConfiguration) WithScheduling(value *SchedulingApplyConfiguration) *RuntimeClassSpecApplyConfiguration {
	b.Scheduling = value
	return b
}
