// Copyright 2019, OpenCensus Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package trace

type evictedQueue struct {
	queue        []interface{}
	capacity     int
	droppedCount int
}

func newEvictedQueue(capacity int) *evictedQueue {
	eq := &evictedQueue{
		capacity: capacity,
		queue:    make([]interface{}, 0),
	}

	return eq
}

func (eq *evictedQueue) add(value interface{}) {
	if len(eq.queue) == eq.capacity {
		eq.queue = eq.queue[1:]
		eq.droppedCount++
	}
	eq.queue = append(eq.queue, value)
}
