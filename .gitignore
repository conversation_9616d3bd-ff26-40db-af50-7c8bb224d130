.uptodate
.uptodate-debug
.pkg
.cache
*.output
*.tgz
*.exe
__debug_bin
requirements.lock
mixin/vendor/
cmd/loki/loki
cmd/logcli/logcli
clients/cmd/promtail/promtail
cmd/loki/loki-debug
clients/cmd/promtail/promtail-debug
clients/cmd/docker-driver/docker-driver
cmd/loki-canary/loki-canary
clients/cmd/fluent-bit/out_grafana_loki.so
clients/cmd/fluent-bit/out_grafana_loki.h
cmd/migrate/migrate
cmd/querytee/querytee
/loki
/promtail
/logcli
/loki-canary
dlv
rootfs/
dist
coverage.txt
test_results.txt
.DS_Store
.aws-sam
.idea

# emacs
.#*

# terraform
.terraform*
*.tfstate*
*.tfvars

# vscode
.vscode
