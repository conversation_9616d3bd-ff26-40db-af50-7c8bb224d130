syntax = "proto3";

// Protobuf package should not be changed when moving around go packages
// in order to not break backward compatibility.
package frontend;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "github.com/weaveworks/common/httpgrpc/httpgrpc.proto";
import "pkg/querier/stats/stats.proto";

option go_package = "frontendv1pb";
option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;

service Frontend {
  // After calling this method, client enters a loop, in which it waits for
  // a "FrontendToClient" message and replies with single "ClientToFrontend" message.
  rpc Process(stream ClientToFrontend) returns (stream FrontendToClient) {}

  // The client notifies the query-frontend that it started a graceful shutdown.
  rpc NotifyClientShutdown(NotifyClientShutdownRequest) returns (NotifyClientShutdownResponse);
}

enum Type {
  HTTP_REQUEST = 0;
  GET_ID = 1;
}

message FrontendToClient {
  httpgrpc.HTTPRequest httpRequest = 1;
  Type type = 2;

  // Whether query statistics tracking should be enabled. The response will include
  // statistics only when this option is enabled.
  bool statsEnabled = 3;
}

message ClientToFrontend {
  httpgrpc.HTTPResponse httpResponse = 1;
  string clientID = 2;
  stats.Stats stats = 3;
}

message NotifyClientShutdownRequest {
  string clientID = 1;
}

message NotifyClientShutdownResponse {}
