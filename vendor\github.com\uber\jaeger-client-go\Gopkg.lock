# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  digest = "1:4c4c33075b704791d6a7f09dfb55c66769e8a1dc6adf87026292d274fe8ad113"
  name = "github.com/HdrHistogram/hdrhistogram-go"
  packages = ["."]
  pruneopts = "UT"
  revision = "3a0bb77429bd3a61596f5e8a3172445844342120"
  version = "0.9.0"

[[projects]]
  digest = "1:d6afaeed1502aa28e80a4ed0981d570ad91b2579193404256ce672ed0a609e0d"
  name = "github.com/beorn7/perks"
  packages = ["quantile"]
  pruneopts = "UT"
  revision = "37c8de3658fcb183f997c4e13e8337516ab753e6"
  version = "v1.0.1"

[[projects]]
  branch = "master"
  digest = "1:a382acd6150713655ded76ab5fbcbc7924a7808dab4312dda5d1f23dd8ce5277"
  name = "github.com/crossdock/crossdock-go"
  packages = [
    ".",
    "assert",
    "require",
  ]
  pruneopts = "UT"
  revision = "049aabb0122b03bc9bd30cab8f3f91fb60166361"

[[projects]]
  digest = "1:ffe9824d294da03b391f44e1ae8281281b4afc1bdaa9588c9097785e3af10cec"
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  pruneopts = "UT"
  revision = "8991bc29aa16c548c550c7ff78260e27b9ab7c73"
  version = "v1.1.1"

[[projects]]
  digest = "1:7ae311278f7ccaa724de8f2cdec0a507ba3ee6dea8c77237e8157bcf64b0f28b"
  name = "github.com/golang/mock"
  packages = ["gomock"]
  pruneopts = "UT"
  revision = "f7b1909c82a8958747e5c87c6a5c3b2eaed8a33d"
  version = "v1.4.4"

[[projects]]
  digest = "1:4a32eb57407190eced21a21abee9ce4d4ab6f0bf113ca61cb1cb2d549a65c985"
  name = "github.com/golang/protobuf"
  packages = [
    "proto",
    "ptypes",
    "ptypes/any",
    "ptypes/duration",
    "ptypes/timestamp",
  ]
  pruneopts = "UT"
  revision = "d04d7b157bb510b1e0c10132224b616ac0e26b17"
  version = "v1.4.2"

[[projects]]
  digest = "1:ff5ebae34cfbf047d505ee150de27e60570e8c394b3b8fdbb720ff6ac71985fc"
  name = "github.com/matttproud/golang_protobuf_extensions"
  packages = ["pbutil"]
  pruneopts = "UT"
  revision = "c12348ce28de40eed0136aa2b644d0ee0650e56c"
  version = "v1.0.1"

[[projects]]
  digest = "1:fe5217d44ae8fb84f711968816fe50077cea9dfa8f44425b8e44e7e3de896d01"
  name = "github.com/opentracing/opentracing-go"
  packages = [
    ".",
    "ext",
    "harness",
    "log",
  ]
  pruneopts = "UT"
  revision = "d34af3eaa63c4d08ab54863a4bdd0daa45212e12"
  version = "v1.2.0"

[[projects]]
  digest = "1:9e1d37b58d17113ec3cb5608ac0382313c5b59470b94ed97d0976e69c7022314"
  name = "github.com/pkg/errors"
  packages = ["."]
  pruneopts = "UT"
  revision = "614d223910a179a466c1767a985424175c39b465"
  version = "v0.9.1"

[[projects]]
  digest = "1:0028cb19b2e4c3112225cd871870f2d9cf49b9b4276531f03438a88e94be86fe"
  name = "github.com/pmezard/go-difflib"
  packages = ["difflib"]
  pruneopts = "UT"
  revision = "792786c7400a136282c1664665ae0a8db921c6c2"
  version = "v1.0.0"

[[projects]]
  digest = "1:7097829edd12fd7211fca0d29496b44f94ef9e6d72f88fb64f3d7b06315818ad"
  name = "github.com/prometheus/client_golang"
  packages = [
    "prometheus",
    "prometheus/internal",
  ]
  pruneopts = "UT"
  revision = "170205fb58decfd011f1550d4cfb737230d7ae4f"
  version = "v1.1.0"

[[projects]]
  digest = "1:0db23933b8052702d980a3f029149b3f175f7c0eea0cff85b175017d0f2722c0"
  name = "github.com/prometheus/client_model"
  packages = ["go"]
  pruneopts = "UT"
  revision = "7bc5445566f0fe75b15de23e6b93886e982d7bf9"
  version = "v0.2.0"

[[projects]]
  digest = "1:4407525bde4e6ad9c1f60113d38cbb255d769e0ea506c8cf877db39da7753b3a"
  name = "github.com/prometheus/common"
  packages = [
    "expfmt",
    "internal/bitbucket.org/ww/goautoneg",
    "model",
  ]
  pruneopts = "UT"
  revision = "317b7b125e8fddda956d0c9574e5f03f438ed5bc"
  version = "v0.14.0"

[[projects]]
  digest = "1:b2268435af85ee1a0fca0e37de4225f78e2d9d8b0b66acde3a29f127634efa87"
  name = "github.com/prometheus/procfs"
  packages = [
    ".",
    "internal/fs",
    "internal/util",
  ]
  pruneopts = "UT"
  revision = "9dece15c53cd5e9fbfbd72d5108adcf526a3f486"
  version = "v0.2.0"

[[projects]]
  digest = "1:86ff4af7b6bb3d27c2e89b5ef8c139678acff1cad74a3c5235fc5af6b94fcc9e"
  name = "github.com/stretchr/objx"
  packages = ["."]
  pruneopts = "UT"
  revision = "****************************************"
  version = "v0.3.0"

[[projects]]
  digest = "1:5201127841a78d84d0ca68a2e564c08e3882c0fb9321a75997ce87926e0d63ea"
  name = "github.com/stretchr/testify"
  packages = [
    "assert",
    "mock",
    "require",
    "suite",
  ]
  pruneopts = "UT"
  revision = "f654a9112bbeac49ca2cd45bfbe11533c4666cf8"
  version = "v1.6.1"

[[projects]]
  digest = "1:4af46f2faea30e52c96ec9ec32bb654d2729579a80d242b0acfa193ad321eb61"
  name = "github.com/uber/jaeger-lib"
  packages = [
    "metrics",
    "metrics/metricstest",
    "metrics/prometheus",
  ]
  pruneopts = "UT"
  revision = "48cc1df63e6be0d63b95677f0d22beb880bce1e4"
  version = "v2.3.0"

[[projects]]
  digest = "1:7a3de4371d6b68c6f37a0df2c09905664d9de59026c91cbe275aae55f4fe760f"
  name = "go.uber.org/atomic"
  packages = ["."]
  pruneopts = "UT"
  revision = "12f27ba2637fa0e13772a4f05fa46a5d18d53182"
  version = "v1.7.0"

[[projects]]
  digest = "1:e9eeeabfd025a5e69b9c8e2857d3517ea67e747ae913bcb0a9e1e7bafdb9c298"
  name = "go.uber.org/multierr"
  packages = ["."]
  pruneopts = "UT"
  revision = "3114a8b704d2d28dbacda34a872690aaef66aeed"
  version = "v1.6.0"

[[projects]]
  digest = "1:0398f5f0e2e9233f25fad702f3b323241daf9f876cc869ab259238cf1bced236"
  name = "go.uber.org/zap"
  packages = [
    ".",
    "buffer",
    "internal/bufferpool",
    "internal/color",
    "internal/exit",
    "zapcore",
    "zaptest/observer",
  ]
  pruneopts = "UT"
  revision = "404189cf44aea95b0cd9bddcb0242dd4cf88c510"
  version = "v1.16.0"

[[projects]]
  branch = "master"
  digest = "1:f8b491a7c25030a895a0e579742d07136e6958e77ef2d46e769db8eec4e58fcd"
  name = "golang.org/x/net"
  packages = [
    "context",
    "context/ctxhttp",
  ]
  pruneopts = "UT"
  revision = "328152dc79b1547da63f950cd4cdd9afd50b2774"

[[projects]]
  branch = "master"
  digest = "1:1e581fa394685ef0d84008ae04cf3414390c1a700c04846853869cb4ac2fec86"
  name = "golang.org/x/sys"
  packages = [
    "internal/unsafeheader",
    "unix",
    "windows",
  ]
  pruneopts = "UT"
  revision = "d9f96fdee20d1e5115ee34ba4016eae6cfb66eb9"

[[projects]]
  digest = "1:fd328c5b52e433ea3ffc891bcc4f94469a82bf478558208db2b386aad8a304a1"
  name = "google.golang.org/protobuf"
  packages = [
    "encoding/prototext",
    "encoding/protowire",
    "internal/descfmt",
    "internal/descopts",
    "internal/detrand",
    "internal/encoding/defval",
    "internal/encoding/messageset",
    "internal/encoding/tag",
    "internal/encoding/text",
    "internal/errors",
    "internal/fieldsort",
    "internal/filedesc",
    "internal/filetype",
    "internal/flags",
    "internal/genid",
    "internal/impl",
    "internal/mapsort",
    "internal/pragma",
    "internal/set",
    "internal/strs",
    "internal/version",
    "proto",
    "reflect/protoreflect",
    "reflect/protoregistry",
    "runtime/protoiface",
    "runtime/protoimpl",
    "types/known/anypb",
    "types/known/durationpb",
    "types/known/timestamppb",
  ]
  pruneopts = "UT"
  revision = "3f7a61f89bb6813f89d981d1870ed68da0b3c3f1"
  version = "v1.25.0"

[[projects]]
  branch = "v3"
  digest = "1:229cb0f6192914f518cc1241ede6d6f1f458b31debfa18bf3a5c9e4f7b01e24b"
  name = "gopkg.in/yaml.v3"
  packages = ["."]
  pruneopts = "UT"
  revision = "eeeca48fe7764f320e4870d231902bf9c1be2c08"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  input-imports = [
    "github.com/crossdock/crossdock-go",
    "github.com/golang/mock/gomock",
    "github.com/opentracing/opentracing-go",
    "github.com/opentracing/opentracing-go/ext",
    "github.com/opentracing/opentracing-go/harness",
    "github.com/opentracing/opentracing-go/log",
    "github.com/pkg/errors",
    "github.com/prometheus/client_golang/prometheus",
    "github.com/stretchr/testify/assert",
    "github.com/stretchr/testify/mock",
    "github.com/stretchr/testify/require",
    "github.com/stretchr/testify/suite",
    "github.com/uber/jaeger-lib/metrics",
    "github.com/uber/jaeger-lib/metrics/metricstest",
    "github.com/uber/jaeger-lib/metrics/prometheus",
    "go.uber.org/atomic",
    "go.uber.org/zap",
    "go.uber.org/zap/zapcore",
    "go.uber.org/zap/zaptest/observer",
  ]
  solver-name = "gps-cdcl"
  solver-version = 1
