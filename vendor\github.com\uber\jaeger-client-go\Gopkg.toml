[[constraint]]
  name = "github.com/crossdock/crossdock-go"
  branch = "master"

[[constraint]]
  name = "github.com/opentracing/opentracing-go"
  version = "^1.2"

[[constraint]]
  name = "github.com/prometheus/client_golang"
  version = "^1"

[[constraint]]
  name = "github.com/stretchr/testify"
  version = "^1.1.3"

[[constraint]]
  name = "go.uber.org/atomic"
  version = "^1"

[[constraint]]
  name = "github.com/uber/jaeger-lib"
  version = "^2.3"

[[constraint]]
  name = "go.uber.org/zap"
  version = "^1"

[prune]
  go-tests = true
  unused-packages = true
