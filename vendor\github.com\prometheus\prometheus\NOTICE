The Prometheus systems and service monitoring server
Copyright 2012-2015 The Prometheus Authors

This product includes software developed at
SoundCloud Ltd. (https://soundcloud.com/).


The following components are included in this product:

Bootstrap
https://getbootstrap.com
Copyright 2011-2014 Twitter, Inc.
Licensed under the MIT License

bootstrap3-typeahead.js
https://github.com/bassjobsen/Bootstrap-3-Typeahead
Original written by @mdo and @fat
Copyright 2014 <PERSON> @bassjobsen
Licensed under the Apache License, Version 2.0

fuzzy
https://github.com/mattyork/fuzzy
Original written by @mattyork
Copyright 2012 Matt York
Licensed under the MIT License

bootstrap-datetimepicker.js
https://github.com/Eonasdan/bootstrap-datetimepicker
Copyright 2015 <PERSON> (@Eonasdan)
Licensed under the MIT License

moment.js
https://github.com/moment/moment/
Copyright JS Foundation and other contributors
Licensed under the MIT License

Rickshaw
https://github.com/shutterstock/rickshaw
Copyright 2011-2014 by Shutterstock Images, LLC
See https://github.com/shutterstock/rickshaw/blob/master/LICENSE for license details

mustache.js
https://github.com/janl/mustache.js
Copyright 2009 <PERSON> (Ruby)
Copyright 2010-2014 <PERSON> (JavaScript)
Copyright 2010-2015 The mustache.js community
Licensed under the MIT License

jQuery
https://jquery.org
Copyright jQuery Foundation and other contributors
Licensed under the MIT License

Protocol Buffers for Go with Gadgets
https://github.com/gogo/protobuf/
Copyright (c) 2013, The GoGo Authors.
See source code for license details.

Go support for leveled logs, analogous to
https://code.google.com/p/google-glog/
Copyright 2013 Google Inc.
Licensed under the Apache License, Version 2.0

Support for streaming Protocol Buffer messages for the Go language (golang).
https://github.com/matttproud/golang_protobuf_extensions
Copyright 2013 Matt T. Proud
Licensed under the Apache License, Version 2.0

DNS library in Go
https://miek.nl/2014/august/16/go-dns-package/
Copyright 2009 The Go Authors, 2011 Miek Gieben
See https://github.com/miekg/dns/blob/master/LICENSE for license details.

LevelDB key/value database in Go
https://github.com/syndtr/goleveldb
Copyright 2012 Suryandaru Triandana
See https://github.com/syndtr/goleveldb/blob/master/LICENSE for license details.

gosnappy - a fork of code.google.com/p/snappy-go
https://github.com/syndtr/gosnappy
Copyright 2011 The Snappy-Go Authors
See https://github.com/syndtr/gosnappy/blob/master/LICENSE for license details.

go-zookeeper - Native ZooKeeper client for Go
https://github.com/samuel/go-zookeeper
Copyright (c) 2013, Samuel Stauffer <<EMAIL>>
See https://github.com/samuel/go-zookeeper/blob/master/LICENSE for license details.

Time series compression algorithm from Facebook's Gorilla paper
https://github.com/dgryski/go-tsz
Copyright (c) 2015,2016 Damian Gryski <<EMAIL>>
See https://github.com/dgryski/go-tsz/blob/master/LICENSE for license details.

The Go programming language
https://go.dev/
Copyright (c) 2009 The Go Authors
See https://go.dev/LICENSE for license details.

The Codicon icon font from Microsoft
https://github.com/microsoft/vscode-codicons
Copyright (c) Microsoft Corporation and other contributors
See https://github.com/microsoft/vscode-codicons/blob/main/LICENSE for license details.

We also use code from a large number of npm packages. For details, see:
- https://github.com/prometheus/prometheus/blob/main/web/ui/react-app/package.json
- https://github.com/prometheus/prometheus/blob/main/web/ui/react-app/package-lock.json
- The individual package licenses as copied from the node_modules directory can be found in
  the npm_licenses.tar.bz2 archive in release tarballs and Docker images.
