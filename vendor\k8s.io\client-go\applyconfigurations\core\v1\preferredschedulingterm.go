/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// PreferredSchedulingTermApplyConfiguration represents an declarative configuration of the PreferredSchedulingTerm type for use
// with apply.
type PreferredSchedulingTermApplyConfiguration struct {
	Weight     *int32                              `json:"weight,omitempty"`
	Preference *NodeSelectorTermApplyConfiguration `json:"preference,omitempty"`
}

// PreferredSchedulingTermApplyConfiguration constructs an declarative configuration of the PreferredSchedulingTerm type for use with
// apply.
func PreferredSchedulingTerm() *PreferredSchedulingTermApplyConfiguration {
	return &PreferredSchedulingTermApplyConfiguration{}
}

// WithWeight sets the Weight field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Weight field is set to the value of the last call.
func (b *PreferredSchedulingTermApplyConfiguration) WithWeight(value int32) *PreferredSchedulingTermApplyConfiguration {
	b.Weight = &value
	return b
}

// WithPreference sets the Preference field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Preference field is set to the value of the last call.
func (b *PreferredSchedulingTermApplyConfiguration) WithPreference(value *NodeSelectorTermApplyConfiguration) *PreferredSchedulingTermApplyConfiguration {
	b.Preference = value
	return b
}
