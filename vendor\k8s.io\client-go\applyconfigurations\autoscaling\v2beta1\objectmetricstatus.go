/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	resource "k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// ObjectMetricStatusApplyConfiguration represents an declarative configuration of the ObjectMetricStatus type for use
// with apply.
type ObjectMetricStatusApplyConfiguration struct {
	Target       *CrossVersionObjectReferenceApplyConfiguration `json:"target,omitempty"`
	MetricName   *string                                        `json:"metricName,omitempty"`
	CurrentValue *resource.Quantity                             `json:"currentValue,omitempty"`
	Selector     *v1.LabelSelectorApplyConfiguration            `json:"selector,omitempty"`
	AverageValue *resource.Quantity                             `json:"averageValue,omitempty"`
}

// ObjectMetricStatusApplyConfiguration constructs an declarative configuration of the ObjectMetricStatus type for use with
// apply.
func ObjectMetricStatus() *ObjectMetricStatusApplyConfiguration {
	return &ObjectMetricStatusApplyConfiguration{}
}

// WithTarget sets the Target field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Target field is set to the value of the last call.
func (b *ObjectMetricStatusApplyConfiguration) WithTarget(value *CrossVersionObjectReferenceApplyConfiguration) *ObjectMetricStatusApplyConfiguration {
	b.Target = value
	return b
}

// WithMetricName sets the MetricName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MetricName field is set to the value of the last call.
func (b *ObjectMetricStatusApplyConfiguration) WithMetricName(value string) *ObjectMetricStatusApplyConfiguration {
	b.MetricName = &value
	return b
}

// WithCurrentValue sets the CurrentValue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CurrentValue field is set to the value of the last call.
func (b *ObjectMetricStatusApplyConfiguration) WithCurrentValue(value resource.Quantity) *ObjectMetricStatusApplyConfiguration {
	b.CurrentValue = &value
	return b
}

// WithSelector sets the Selector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Selector field is set to the value of the last call.
func (b *ObjectMetricStatusApplyConfiguration) WithSelector(value *v1.LabelSelectorApplyConfiguration) *ObjectMetricStatusApplyConfiguration {
	b.Selector = value
	return b
}

// WithAverageValue sets the AverageValue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AverageValue field is set to the value of the last call.
func (b *ObjectMetricStatusApplyConfiguration) WithAverageValue(value resource.Quantity) *ObjectMetricStatusApplyConfiguration {
	b.AverageValue = &value
	return b
}
