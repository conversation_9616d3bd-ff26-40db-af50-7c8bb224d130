{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "promtail.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "promtail.name" . }}
    chart: {{ template "promtail.chart" . }}
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "promtail.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "promtail.serviceAccountName" . }}
{{- end }}

