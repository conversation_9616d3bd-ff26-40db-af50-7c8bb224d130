auth_enabled: false

server:
  log_level: info
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}
  # grpc_tls_config:
  #   client_auth_type: "RequireAndVerifyClientCert"
  #   client_ca_file: "/secrets/certs/CA.pem"
  #   cert_file: "/secrets/certs/cert.pem"
  #   key_file: "/secrets/certs/key.pem"

common:
  replication_factor: 2
  # Tell Loki which address to advertise
  instance_addr: {{ env "NOMAD_IP_grpc" }}
  # Failure domain
  # Must be the same as specified in job constraints
  instance_availability_zone: {{ env "node.unique.name" }}
  zone_awareness_enabled: true
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

# ingester_client:
#   grpc_client_config:
#     grpc_compression: snappy
#     tls_enabled: true
#     tls_ca_path: "/secrets/certs/CA.pem"
#     tls_cert_path: "/secrets/certs/cert.pem"
#     tls_key_path: "/secrets/certs/key.pem"

ingester:
  wal:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/wal
    flush_on_shutdown: true
    replay_memory_ceiling: "1G"

# query_scheduler:
#   grpc_client_config:
#     grpc_compression: snappy
#     tls_enabled: true
#     tls_ca_path: "/secrets/certs/CA.pem"
#     tls_cert_path: "/secrets/certs/cert.pem"
#     tls_key_path: "/secrets/certs/key.pem"

frontend:
  scheduler_address: loki-query-scheduler.service.consul:9096
  compress_responses: true
  log_queries_longer_than: 5s
  # grpc_client_config:
  #   grpc_compression: snappy
  #   tls_enabled: true
  #   tls_ca_path: "/secrets/certs/CA.pem"
  #   tls_cert_path: "/secrets/certs/cert.pem"
  #   tls_key_path: "/secrets/certs/key.pem"

frontend_worker:
  scheduler_address: loki-query-scheduler.service.consul:9096
  # grpc_client_config:
  #   grpc_compression: snappy
  #   tls_enabled: true
  #   tls_ca_path: "/secrets/certs/CA.pem"
  #   tls_cert_path: "/secrets/certs/cert.pem"
  #   tls_key_path: "/secrets/certs/key.pem"

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v12
    index:
      prefix: index_
      period: 24h

storage_config:
  boltdb_shipper:
    # Nomad ephemeral disk is used to store index and cache
    # it will try to preserve /alloc/data between job updates
    active_index_directory: {{ env "NOMAD_ALLOC_DIR" }}/data/index
    cache_location: {{ env "NOMAD_ALLOC_DIR" }}/data/index-cache
    shared_store: s3
    index_gateway_client:
      server_address: loki-index-gateway.service.consul:9097
      # grpc_client_config:
      #   grpc_compression: snappy
      #   tls_enabled: true
      #   tls_ca_path: "/secrets/certs/CA.pem"
      #   tls_cert_path: "/secrets/certs/cert.pem"
      #   tls_key_path: "/secrets/certs/key.pem"

  aws:
    endpoint: https://minio.service.consul
    bucketnames: loki
    region: us-west-1
    access_key_id: ${S3_ACCESS_KEY_ID}
    secret_access_key: ${S3_SECRET_ACCESS_KEY}
    s3forcepathstyle: true

compactor:
  working_directory: {{ env "NOMAD_ALLOC_DIR" }}/compactor
  shared_store: s3
  compaction_interval: 24h
  retention_enabled: true

ruler:
  alertmanager_url: https://alertmanager.service.consul
  enable_alertmanager_v2: true
  enable_api: true
  external_url: https://loki-ruler.service.consul
  rule_path: {{ env "NOMAD_ALLOC_DIR" }}/tmp/rules
  storage:
    type: local
    local:
      directory: {{ env "NOMAD_TASK_DIR" }}/rules
  wal:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/ruler

limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h
