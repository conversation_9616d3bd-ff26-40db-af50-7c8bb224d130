// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/storage/chunk/client/grpc/grpc.proto

package grpc

import (
	bytes "bytes"
	context "context"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type PutChunksRequest struct {
	Chunks []*Chunk `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
}

func (m *PutChunksRequest) Reset()      { *m = PutChunksRequest{} }
func (*PutChunksRequest) ProtoMessage() {}
func (*PutChunksRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{0}
}
func (m *PutChunksRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutChunksRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutChunksRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PutChunksRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutChunksRequest.Merge(m, src)
}
func (m *PutChunksRequest) XXX_Size() int {
	return m.Size()
}
func (m *PutChunksRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutChunksRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutChunksRequest proto.InternalMessageInfo

func (m *PutChunksRequest) GetChunks() []*Chunk {
	if m != nil {
		return m.Chunks
	}
	return nil
}

type GetChunksRequest struct {
	Chunks []*Chunk `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
}

func (m *GetChunksRequest) Reset()      { *m = GetChunksRequest{} }
func (*GetChunksRequest) ProtoMessage() {}
func (*GetChunksRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{1}
}
func (m *GetChunksRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetChunksRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetChunksRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetChunksRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChunksRequest.Merge(m, src)
}
func (m *GetChunksRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetChunksRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChunksRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChunksRequest proto.InternalMessageInfo

func (m *GetChunksRequest) GetChunks() []*Chunk {
	if m != nil {
		return m.Chunks
	}
	return nil
}

type GetChunksResponse struct {
	Chunks []*Chunk `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
}

func (m *GetChunksResponse) Reset()      { *m = GetChunksResponse{} }
func (*GetChunksResponse) ProtoMessage() {}
func (*GetChunksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{2}
}
func (m *GetChunksResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetChunksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetChunksResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetChunksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChunksResponse.Merge(m, src)
}
func (m *GetChunksResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetChunksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChunksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChunksResponse proto.InternalMessageInfo

func (m *GetChunksResponse) GetChunks() []*Chunk {
	if m != nil {
		return m.Chunks
	}
	return nil
}

type Chunk struct {
	Encoded   []byte `protobuf:"bytes,1,opt,name=encoded,proto3" json:"encoded,omitempty"`
	Key       string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	TableName string `protobuf:"bytes,3,opt,name=tableName,proto3" json:"tableName,omitempty"`
}

func (m *Chunk) Reset()      { *m = Chunk{} }
func (*Chunk) ProtoMessage() {}
func (*Chunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{3}
}
func (m *Chunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Chunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Chunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Chunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Chunk.Merge(m, src)
}
func (m *Chunk) XXX_Size() int {
	return m.Size()
}
func (m *Chunk) XXX_DiscardUnknown() {
	xxx_messageInfo_Chunk.DiscardUnknown(m)
}

var xxx_messageInfo_Chunk proto.InternalMessageInfo

func (m *Chunk) GetEncoded() []byte {
	if m != nil {
		return m.Encoded
	}
	return nil
}

func (m *Chunk) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Chunk) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

type ChunkID struct {
	ChunkID string `protobuf:"bytes,1,opt,name=chunkID,proto3" json:"chunkID,omitempty"`
}

func (m *ChunkID) Reset()      { *m = ChunkID{} }
func (*ChunkID) ProtoMessage() {}
func (*ChunkID) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{4}
}
func (m *ChunkID) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChunkID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChunkID.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChunkID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChunkID.Merge(m, src)
}
func (m *ChunkID) XXX_Size() int {
	return m.Size()
}
func (m *ChunkID) XXX_DiscardUnknown() {
	xxx_messageInfo_ChunkID.DiscardUnknown(m)
}

var xxx_messageInfo_ChunkID proto.InternalMessageInfo

func (m *ChunkID) GetChunkID() string {
	if m != nil {
		return m.ChunkID
	}
	return ""
}

type DeleteTableRequest struct {
	TableName string `protobuf:"bytes,1,opt,name=tableName,proto3" json:"tableName,omitempty"`
}

func (m *DeleteTableRequest) Reset()      { *m = DeleteTableRequest{} }
func (*DeleteTableRequest) ProtoMessage() {}
func (*DeleteTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{5}
}
func (m *DeleteTableRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteTableRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTableRequest.Merge(m, src)
}
func (m *DeleteTableRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTableRequest proto.InternalMessageInfo

func (m *DeleteTableRequest) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

type DescribeTableRequest struct {
	TableName string `protobuf:"bytes,1,opt,name=tableName,proto3" json:"tableName,omitempty"`
}

func (m *DescribeTableRequest) Reset()      { *m = DescribeTableRequest{} }
func (*DescribeTableRequest) ProtoMessage() {}
func (*DescribeTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{6}
}
func (m *DescribeTableRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeTableRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeTableRequest.Merge(m, src)
}
func (m *DescribeTableRequest) XXX_Size() int {
	return m.Size()
}
func (m *DescribeTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeTableRequest proto.InternalMessageInfo

func (m *DescribeTableRequest) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

type WriteBatch struct {
	Writes  []*IndexEntry `protobuf:"bytes,1,rep,name=writes,proto3" json:"writes,omitempty"`
	Deletes []*IndexEntry `protobuf:"bytes,2,rep,name=deletes,proto3" json:"deletes,omitempty"`
}

func (m *WriteBatch) Reset()      { *m = WriteBatch{} }
func (*WriteBatch) ProtoMessage() {}
func (*WriteBatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{7}
}
func (m *WriteBatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteBatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteBatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteBatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteBatch.Merge(m, src)
}
func (m *WriteBatch) XXX_Size() int {
	return m.Size()
}
func (m *WriteBatch) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteBatch.DiscardUnknown(m)
}

var xxx_messageInfo_WriteBatch proto.InternalMessageInfo

func (m *WriteBatch) GetWrites() []*IndexEntry {
	if m != nil {
		return m.Writes
	}
	return nil
}

func (m *WriteBatch) GetDeletes() []*IndexEntry {
	if m != nil {
		return m.Deletes
	}
	return nil
}

type WriteIndexRequest struct {
	Writes []*IndexEntry `protobuf:"bytes,1,rep,name=writes,proto3" json:"writes,omitempty"`
}

func (m *WriteIndexRequest) Reset()      { *m = WriteIndexRequest{} }
func (*WriteIndexRequest) ProtoMessage() {}
func (*WriteIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{8}
}
func (m *WriteIndexRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteIndexRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteIndexRequest.Merge(m, src)
}
func (m *WriteIndexRequest) XXX_Size() int {
	return m.Size()
}
func (m *WriteIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WriteIndexRequest proto.InternalMessageInfo

func (m *WriteIndexRequest) GetWrites() []*IndexEntry {
	if m != nil {
		return m.Writes
	}
	return nil
}

type DeleteIndexRequest struct {
	Deletes []*IndexEntry `protobuf:"bytes,1,rep,name=deletes,proto3" json:"deletes,omitempty"`
}

func (m *DeleteIndexRequest) Reset()      { *m = DeleteIndexRequest{} }
func (*DeleteIndexRequest) ProtoMessage() {}
func (*DeleteIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{9}
}
func (m *DeleteIndexRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteIndexRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIndexRequest.Merge(m, src)
}
func (m *DeleteIndexRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIndexRequest proto.InternalMessageInfo

func (m *DeleteIndexRequest) GetDeletes() []*IndexEntry {
	if m != nil {
		return m.Deletes
	}
	return nil
}

type QueryIndexResponse struct {
	Rows []*Row `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *QueryIndexResponse) Reset()      { *m = QueryIndexResponse{} }
func (*QueryIndexResponse) ProtoMessage() {}
func (*QueryIndexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{10}
}
func (m *QueryIndexResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryIndexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryIndexResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryIndexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryIndexResponse.Merge(m, src)
}
func (m *QueryIndexResponse) XXX_Size() int {
	return m.Size()
}
func (m *QueryIndexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryIndexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryIndexResponse proto.InternalMessageInfo

func (m *QueryIndexResponse) GetRows() []*Row {
	if m != nil {
		return m.Rows
	}
	return nil
}

type Row struct {
	RangeValue []byte `protobuf:"bytes,1,opt,name=rangeValue,proto3" json:"rangeValue,omitempty"`
	Value      []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Row) Reset()      { *m = Row{} }
func (*Row) ProtoMessage() {}
func (*Row) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{11}
}
func (m *Row) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Row) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Row.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Row) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Row.Merge(m, src)
}
func (m *Row) XXX_Size() int {
	return m.Size()
}
func (m *Row) XXX_DiscardUnknown() {
	xxx_messageInfo_Row.DiscardUnknown(m)
}

var xxx_messageInfo_Row proto.InternalMessageInfo

func (m *Row) GetRangeValue() []byte {
	if m != nil {
		return m.RangeValue
	}
	return nil
}

func (m *Row) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type IndexEntry struct {
	TableName  string `protobuf:"bytes,1,opt,name=tableName,proto3" json:"tableName,omitempty"`
	HashValue  string `protobuf:"bytes,2,opt,name=hashValue,proto3" json:"hashValue,omitempty"`
	RangeValue []byte `protobuf:"bytes,3,opt,name=rangeValue,proto3" json:"rangeValue,omitempty"`
	Value      []byte `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *IndexEntry) Reset()      { *m = IndexEntry{} }
func (*IndexEntry) ProtoMessage() {}
func (*IndexEntry) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{12}
}
func (m *IndexEntry) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IndexEntry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IndexEntry.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IndexEntry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexEntry.Merge(m, src)
}
func (m *IndexEntry) XXX_Size() int {
	return m.Size()
}
func (m *IndexEntry) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexEntry.DiscardUnknown(m)
}

var xxx_messageInfo_IndexEntry proto.InternalMessageInfo

func (m *IndexEntry) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

func (m *IndexEntry) GetHashValue() string {
	if m != nil {
		return m.HashValue
	}
	return ""
}

func (m *IndexEntry) GetRangeValue() []byte {
	if m != nil {
		return m.RangeValue
	}
	return nil
}

func (m *IndexEntry) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type QueryIndexRequest struct {
	TableName        string `protobuf:"bytes,1,opt,name=tableName,proto3" json:"tableName,omitempty"`
	HashValue        string `protobuf:"bytes,2,opt,name=hashValue,proto3" json:"hashValue,omitempty"`
	RangeValuePrefix []byte `protobuf:"bytes,3,opt,name=rangeValuePrefix,proto3" json:"rangeValuePrefix,omitempty"`
	RangeValueStart  []byte `protobuf:"bytes,4,opt,name=rangeValueStart,proto3" json:"rangeValueStart,omitempty"`
	ValueEqual       []byte `protobuf:"bytes,5,opt,name=valueEqual,proto3" json:"valueEqual,omitempty"`
	Immutable        bool   `protobuf:"varint,6,opt,name=immutable,proto3" json:"immutable,omitempty"`
}

func (m *QueryIndexRequest) Reset()      { *m = QueryIndexRequest{} }
func (*QueryIndexRequest) ProtoMessage() {}
func (*QueryIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{13}
}
func (m *QueryIndexRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryIndexRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryIndexRequest.Merge(m, src)
}
func (m *QueryIndexRequest) XXX_Size() int {
	return m.Size()
}
func (m *QueryIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryIndexRequest proto.InternalMessageInfo

func (m *QueryIndexRequest) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

func (m *QueryIndexRequest) GetHashValue() string {
	if m != nil {
		return m.HashValue
	}
	return ""
}

func (m *QueryIndexRequest) GetRangeValuePrefix() []byte {
	if m != nil {
		return m.RangeValuePrefix
	}
	return nil
}

func (m *QueryIndexRequest) GetRangeValueStart() []byte {
	if m != nil {
		return m.RangeValueStart
	}
	return nil
}

func (m *QueryIndexRequest) GetValueEqual() []byte {
	if m != nil {
		return m.ValueEqual
	}
	return nil
}

func (m *QueryIndexRequest) GetImmutable() bool {
	if m != nil {
		return m.Immutable
	}
	return false
}

type UpdateTableRequest struct {
	Current  *TableDesc `protobuf:"bytes,1,opt,name=current,proto3" json:"current,omitempty"`
	Expected *TableDesc `protobuf:"bytes,2,opt,name=expected,proto3" json:"expected,omitempty"`
}

func (m *UpdateTableRequest) Reset()      { *m = UpdateTableRequest{} }
func (*UpdateTableRequest) ProtoMessage() {}
func (*UpdateTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{14}
}
func (m *UpdateTableRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateTableRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTableRequest.Merge(m, src)
}
func (m *UpdateTableRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTableRequest proto.InternalMessageInfo

func (m *UpdateTableRequest) GetCurrent() *TableDesc {
	if m != nil {
		return m.Current
	}
	return nil
}

func (m *UpdateTableRequest) GetExpected() *TableDesc {
	if m != nil {
		return m.Expected
	}
	return nil
}

type DescribeTableResponse struct {
	Desc     *TableDesc `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	IsActive bool       `protobuf:"varint,2,opt,name=isActive,proto3" json:"isActive,omitempty"`
}

func (m *DescribeTableResponse) Reset()      { *m = DescribeTableResponse{} }
func (*DescribeTableResponse) ProtoMessage() {}
func (*DescribeTableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{15}
}
func (m *DescribeTableResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeTableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeTableResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeTableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeTableResponse.Merge(m, src)
}
func (m *DescribeTableResponse) XXX_Size() int {
	return m.Size()
}
func (m *DescribeTableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeTableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeTableResponse proto.InternalMessageInfo

func (m *DescribeTableResponse) GetDesc() *TableDesc {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *DescribeTableResponse) GetIsActive() bool {
	if m != nil {
		return m.IsActive
	}
	return false
}

type CreateTableRequest struct {
	Desc *TableDesc `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (m *CreateTableRequest) Reset()      { *m = CreateTableRequest{} }
func (*CreateTableRequest) ProtoMessage() {}
func (*CreateTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{16}
}
func (m *CreateTableRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateTableRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTableRequest.Merge(m, src)
}
func (m *CreateTableRequest) XXX_Size() int {
	return m.Size()
}
func (m *CreateTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTableRequest proto.InternalMessageInfo

func (m *CreateTableRequest) GetDesc() *TableDesc {
	if m != nil {
		return m.Desc
	}
	return nil
}

type TableDesc struct {
	Name              string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	UseOnDemandIOMode bool              `protobuf:"varint,2,opt,name=useOnDemandIOMode,proto3" json:"useOnDemandIOMode,omitempty"`
	ProvisionedRead   int64             `protobuf:"varint,3,opt,name=provisionedRead,proto3" json:"provisionedRead,omitempty"`
	ProvisionedWrite  int64             `protobuf:"varint,4,opt,name=provisionedWrite,proto3" json:"provisionedWrite,omitempty"`
	Tags              map[string]string `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *TableDesc) Reset()      { *m = TableDesc{} }
func (*TableDesc) ProtoMessage() {}
func (*TableDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{17}
}
func (m *TableDesc) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableDesc.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableDesc.Merge(m, src)
}
func (m *TableDesc) XXX_Size() int {
	return m.Size()
}
func (m *TableDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_TableDesc.DiscardUnknown(m)
}

var xxx_messageInfo_TableDesc proto.InternalMessageInfo

func (m *TableDesc) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TableDesc) GetUseOnDemandIOMode() bool {
	if m != nil {
		return m.UseOnDemandIOMode
	}
	return false
}

func (m *TableDesc) GetProvisionedRead() int64 {
	if m != nil {
		return m.ProvisionedRead
	}
	return 0
}

func (m *TableDesc) GetProvisionedWrite() int64 {
	if m != nil {
		return m.ProvisionedWrite
	}
	return 0
}

func (m *TableDesc) GetTags() map[string]string {
	if m != nil {
		return m.Tags
	}
	return nil
}

type ListTablesResponse struct {
	TableNames []string `protobuf:"bytes,1,rep,name=tableNames,proto3" json:"tableNames,omitempty"`
}

func (m *ListTablesResponse) Reset()      { *m = ListTablesResponse{} }
func (*ListTablesResponse) ProtoMessage() {}
func (*ListTablesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{18}
}
func (m *ListTablesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListTablesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListTablesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListTablesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTablesResponse.Merge(m, src)
}
func (m *ListTablesResponse) XXX_Size() int {
	return m.Size()
}
func (m *ListTablesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTablesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListTablesResponse proto.InternalMessageInfo

func (m *ListTablesResponse) GetTableNames() []string {
	if m != nil {
		return m.TableNames
	}
	return nil
}

type Labels struct {
	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Labels) Reset()      { *m = Labels{} }
func (*Labels) ProtoMessage() {}
func (*Labels) Descriptor() ([]byte, []int) {
	return fileDescriptor_70cfe0ce419fcf8f, []int{19}
}
func (m *Labels) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Labels) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Labels.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Labels) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Labels.Merge(m, src)
}
func (m *Labels) XXX_Size() int {
	return m.Size()
}
func (m *Labels) XXX_DiscardUnknown() {
	xxx_messageInfo_Labels.DiscardUnknown(m)
}

var xxx_messageInfo_Labels proto.InternalMessageInfo

func (m *Labels) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Labels) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func init() {
	proto.RegisterType((*PutChunksRequest)(nil), "grpc.PutChunksRequest")
	proto.RegisterType((*GetChunksRequest)(nil), "grpc.GetChunksRequest")
	proto.RegisterType((*GetChunksResponse)(nil), "grpc.GetChunksResponse")
	proto.RegisterType((*Chunk)(nil), "grpc.Chunk")
	proto.RegisterType((*ChunkID)(nil), "grpc.ChunkID")
	proto.RegisterType((*DeleteTableRequest)(nil), "grpc.DeleteTableRequest")
	proto.RegisterType((*DescribeTableRequest)(nil), "grpc.DescribeTableRequest")
	proto.RegisterType((*WriteBatch)(nil), "grpc.WriteBatch")
	proto.RegisterType((*WriteIndexRequest)(nil), "grpc.WriteIndexRequest")
	proto.RegisterType((*DeleteIndexRequest)(nil), "grpc.DeleteIndexRequest")
	proto.RegisterType((*QueryIndexResponse)(nil), "grpc.QueryIndexResponse")
	proto.RegisterType((*Row)(nil), "grpc.Row")
	proto.RegisterType((*IndexEntry)(nil), "grpc.IndexEntry")
	proto.RegisterType((*QueryIndexRequest)(nil), "grpc.QueryIndexRequest")
	proto.RegisterType((*UpdateTableRequest)(nil), "grpc.UpdateTableRequest")
	proto.RegisterType((*DescribeTableResponse)(nil), "grpc.DescribeTableResponse")
	proto.RegisterType((*CreateTableRequest)(nil), "grpc.CreateTableRequest")
	proto.RegisterType((*TableDesc)(nil), "grpc.TableDesc")
	proto.RegisterMapType((map[string]string)(nil), "grpc.TableDesc.TagsEntry")
	proto.RegisterType((*ListTablesResponse)(nil), "grpc.ListTablesResponse")
	proto.RegisterType((*Labels)(nil), "grpc.Labels")
}

func init() {
	proto.RegisterFile("pkg/storage/chunk/client/grpc/grpc.proto", fileDescriptor_70cfe0ce419fcf8f)
}

var fileDescriptor_70cfe0ce419fcf8f = []byte{
	// 944 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x55, 0xcd, 0x92, 0xdb, 0x44,
	0x10, 0xf6, 0xf8, 0x6f, 0x57, 0xed, 0xa4, 0xb2, 0x3b, 0x15, 0x12, 0xe1, 0x80, 0x6a, 0x4b, 0xb9,
	0x98, 0x00, 0x36, 0xe5, 0x2c, 0x95, 0x40, 0x2a, 0x84, 0x24, 0xde, 0x82, 0xad, 0x0a, 0x24, 0x11,
	0x21, 0x70, 0xa3, 0x64, 0xa9, 0xe3, 0x55, 0xad, 0x2d, 0x39, 0x9a, 0xd1, 0xfe, 0x5c, 0x28, 0xee,
	0x5c, 0x78, 0x0c, 0x1e, 0x85, 0xe3, 0x1e, 0x73, 0x64, 0xbd, 0x17, 0x8e, 0x79, 0x04, 0x6a, 0x46,
	0x1a, 0x49, 0x96, 0xac, 0xec, 0xc2, 0x45, 0xa5, 0xf9, 0xba, 0xfb, 0x9b, 0xaf, 0x67, 0xa6, 0xbb,
	0xa1, 0x37, 0xdf, 0x9f, 0x0c, 0x18, 0x0f, 0x42, 0x7b, 0x82, 0x03, 0x67, 0x2f, 0xf2, 0xf7, 0x07,
	0xce, 0xd4, 0x43, 0x9f, 0x0f, 0x26, 0xe1, 0xdc, 0x91, 0x9f, 0xfe, 0x3c, 0x0c, 0x78, 0x40, 0x9b,
	0xe2, 0xbf, 0x7b, 0x63, 0x12, 0x04, 0x93, 0x29, 0x0e, 0x24, 0x36, 0x8e, 0x5e, 0x0d, 0x70, 0x36,
	0xe7, 0xc7, 0xb1, 0x8b, 0x79, 0x07, 0x36, 0x9e, 0x45, 0xfc, 0xb1, 0x60, 0x61, 0x16, 0xbe, 0x8e,
	0x90, 0x71, 0x7a, 0x13, 0xda, 0x92, 0x96, 0xe9, 0x64, 0xab, 0xd1, 0xeb, 0x0c, 0x3b, 0x7d, 0xc9,
	0x29, 0x9d, 0xac, 0xc4, 0x24, 0x02, 0xbf, 0xc1, 0xff, 0x13, 0x78, 0x17, 0x36, 0x73, 0x81, 0x6c,
	0x1e, 0xf8, 0x0c, 0x2f, 0x16, 0xf9, 0x1c, 0x5a, 0x12, 0xa0, 0x3a, 0xac, 0xa1, 0xef, 0x04, 0x2e,
	0xba, 0x3a, 0xd9, 0x22, 0xbd, 0x4b, 0x96, 0x5a, 0xd2, 0x0d, 0x68, 0xec, 0xe3, 0xb1, 0x5e, 0xdf,
	0x22, 0x3d, 0xcd, 0x12, 0xbf, 0xf4, 0x03, 0xd0, 0xb8, 0x3d, 0x9e, 0xe2, 0xf7, 0xf6, 0x0c, 0xf5,
	0x86, 0xc4, 0x33, 0xc0, 0xbc, 0x09, 0x6b, 0x92, 0x72, 0x77, 0x24, 0x48, 0x9d, 0xf8, 0x57, 0x92,
	0x6a, 0x96, 0x5a, 0x9a, 0x43, 0xa0, 0x23, 0x9c, 0x22, 0xc7, 0x17, 0x22, 0x4e, 0x25, 0xbb, 0x44,
	0x4c, 0x8a, 0xc4, 0xdb, 0x70, 0x75, 0x84, 0xcc, 0x09, 0xbd, 0xf1, 0x7f, 0x89, 0x1a, 0x03, 0xfc,
	0x14, 0x7a, 0x1c, 0x1f, 0xd9, 0xdc, 0xd9, 0xa3, 0x3d, 0x68, 0x1f, 0x8a, 0x95, 0x3a, 0x94, 0x8d,
	0xf8, 0x50, 0x76, 0x7d, 0x17, 0x8f, 0x76, 0x7c, 0x1e, 0x1e, 0x5b, 0x89, 0x9d, 0xde, 0x82, 0x35,
	0x57, 0x2a, 0x64, 0x7a, 0xbd, 0xc2, 0x55, 0x39, 0x98, 0xf7, 0x61, 0x53, 0xee, 0x21, 0x6d, 0x4a,
	0xd6, 0x85, 0xb7, 0x32, 0xbf, 0x56, 0x87, 0xb1, 0x14, 0x9f, 0x13, 0x40, 0xce, 0x13, 0x70, 0x1b,
	0xe8, 0xf3, 0x08, 0xc3, 0xe3, 0x84, 0x20, 0x79, 0x01, 0x1f, 0x42, 0x33, 0x0c, 0x0e, 0x55, 0xb8,
	0x16, 0x87, 0x5b, 0xc1, 0xa1, 0x25, 0x61, 0xf3, 0x1e, 0x34, 0xac, 0xe0, 0x90, 0x1a, 0x00, 0xa1,
	0xed, 0x4f, 0xf0, 0xa5, 0x3d, 0x8d, 0x30, 0xb9, 0xfc, 0x1c, 0x42, 0xaf, 0x42, 0xeb, 0x40, 0x9a,
	0xea, 0xd2, 0x14, 0x2f, 0xcc, 0x5f, 0x01, 0x32, 0x21, 0xef, 0xbe, 0x02, 0x61, 0xdd, 0xb3, 0xd9,
	0xde, 0xcb, 0x94, 0x45, 0xb3, 0x32, 0xa0, 0xb0, 0x7f, 0xa3, 0x7a, 0xff, 0x66, 0x7e, 0xff, 0x33,
	0x02, 0x9b, 0xf9, 0x94, 0x2f, 0xf0, 0x14, 0xce, 0xd1, 0x71, 0x0b, 0x36, 0xb2, 0x5d, 0x9f, 0x85,
	0xf8, 0xca, 0x3b, 0x4a, 0xd4, 0x94, 0x70, 0xda, 0x83, 0x2b, 0x19, 0xf6, 0x03, 0xb7, 0x43, 0x9e,
	0xa8, 0x2b, 0xc2, 0x22, 0x3b, 0x29, 0x78, 0xe7, 0x75, 0x64, 0x4f, 0xf5, 0x56, 0x9c, 0x5d, 0x86,
	0x08, 0x4d, 0xde, 0x6c, 0x16, 0x49, 0x91, 0x7a, 0x7b, 0x8b, 0xf4, 0xd6, 0xad, 0x0c, 0x30, 0xa7,
	0x40, 0x7f, 0x9c, 0xbb, 0x76, 0xa1, 0x4c, 0x3e, 0x82, 0x35, 0x27, 0x0a, 0x43, 0xf4, 0xb9, 0xcc,
	0xb1, 0x33, 0xbc, 0x12, 0x5f, 0xad, 0x74, 0x12, 0x25, 0x62, 0x29, 0x3b, 0xfd, 0x18, 0xd6, 0xf1,
	0x68, 0x8e, 0x0e, 0x47, 0x57, 0x66, 0xbc, 0xc2, 0x37, 0x75, 0x30, 0x7f, 0x86, 0xf7, 0x0a, 0x05,
	0x96, 0xb6, 0x92, 0xa6, 0x8b, 0xcc, 0xa9, 0xda, 0x4d, 0x1a, 0x69, 0x17, 0xd6, 0x3d, 0xf6, 0xd0,
	0xe1, 0xde, 0x41, 0x7c, 0xb8, 0xeb, 0x56, 0xba, 0x36, 0xbf, 0x00, 0xfa, 0x38, 0xc4, 0x62, 0x1e,
	0x17, 0xa1, 0x35, 0x7f, 0xaf, 0x83, 0x96, 0x62, 0x94, 0x42, 0xd3, 0xcf, 0xee, 0x56, 0xfe, 0xd3,
	0x4f, 0x60, 0x33, 0x62, 0xf8, 0xd4, 0x1f, 0xe1, 0xcc, 0xf6, 0xdd, 0xdd, 0xa7, 0xdf, 0x05, 0xae,
	0x52, 0x50, 0x36, 0x88, 0xab, 0x9b, 0x87, 0xc1, 0x81, 0xc7, 0xbc, 0xc0, 0x47, 0xd7, 0x42, 0xdb,
	0x95, 0xb7, 0xdc, 0xb0, 0x8a, 0xb0, 0x78, 0x10, 0x39, 0x48, 0x16, 0xb8, 0xbc, 0xe5, 0x86, 0x55,
	0xc2, 0xe9, 0xa7, 0xd0, 0xe4, 0xf6, 0x84, 0xe9, 0x2d, 0x59, 0x6a, 0xef, 0x17, 0x52, 0xe9, 0xbf,
	0xb0, 0x27, 0x2c, 0x2e, 0x59, 0xe9, 0xd6, 0xbd, 0x23, 0x72, 0x4a, 0x20, 0xd5, 0x60, 0x49, 0xd6,
	0x60, 0x97, 0x4a, 0x4e, 0x4b, 0x9e, 0xfc, 0x97, 0xf5, 0xbb, 0xc4, 0xdc, 0x06, 0xfa, 0xc4, 0x63,
	0x5c, 0x32, 0x67, 0xad, 0xde, 0x00, 0x48, 0x5f, 0x79, 0x5c, 0xee, 0x9a, 0x95, 0x43, 0xcc, 0x21,
	0xb4, 0x9f, 0xd8, 0x63, 0x9c, 0xb2, 0x95, 0xe7, 0xb7, 0x72, 0xb7, 0xe1, 0x49, 0x0b, 0x40, 0x64,
	0xf1, 0x8b, 0x18, 0x8b, 0x48, 0xef, 0x27, 0x6d, 0x54, 0x96, 0x1b, 0xbd, 0x1e, 0x27, 0x58, 0x6a,
	0x7a, 0xdd, 0x6b, 0xfd, 0x78, 0x32, 0xf6, 0xd5, 0x64, 0xec, 0xef, 0x88, 0xc9, 0x48, 0x1f, 0x02,
	0x64, 0xd5, 0xaa, 0xc2, 0x4b, 0xf5, 0xdb, 0xd5, 0xcb, 0x86, 0x38, 0xc5, 0xcf, 0x08, 0x7d, 0x00,
	0x9d, 0x5c, 0x97, 0xa4, 0x89, 0x6b, 0xb9, 0x71, 0x56, 0x6a, 0xb8, 0x07, 0x5a, 0x3a, 0x97, 0xe9,
	0xb5, 0x38, 0xbc, 0x38, 0xa8, 0x2b, 0x83, 0xbf, 0x02, 0x2d, 0x1d, 0xb1, 0x2a, 0xb8, 0x38, 0xac,
	0xbb, 0xd7, 0x4b, 0x78, 0xaa, 0xfe, 0x73, 0xb8, 0x14, 0x4b, 0x4d, 0x28, 0x2e, 0xe7, 0xa6, 0xf1,
	0xee, 0xe8, 0x1d, 0xdb, 0x42, 0x76, 0xdf, 0xb4, 0xc2, 0x4b, 0x1d, 0xdb, 0x8a, 0x97, 0xf1, 0x00,
	0x3a, 0xb9, 0xc2, 0x53, 0x87, 0x56, 0xae, 0xc5, 0x4a, 0x01, 0xe9, 0xa9, 0x2f, 0x11, 0x94, 0x67,
	0x77, 0x25, 0xc1, 0xb7, 0x70, 0x79, 0xa9, 0xa9, 0xd0, 0xae, 0xa2, 0x28, 0x8f, 0xf2, 0xee, 0x8d,
	0x95, 0xb6, 0x2c, 0x97, 0x5c, 0x33, 0x54, 0x52, 0xca, 0xfd, 0xb1, 0x4a, 0xca, 0xa3, 0xed, 0x93,
	0x53, 0xa3, 0xf6, 0xe6, 0xd4, 0xa8, 0xbd, 0x3d, 0x35, 0xc8, 0x6f, 0x0b, 0x83, 0xfc, 0xb9, 0x30,
	0xc8, 0x5f, 0x0b, 0x83, 0x9c, 0x2c, 0x0c, 0xf2, 0xf7, 0xc2, 0x20, 0xff, 0x2c, 0x8c, 0xda, 0xdb,
	0x85, 0x41, 0xfe, 0x38, 0x33, 0x6a, 0x27, 0x67, 0x46, 0xed, 0xcd, 0x99, 0x51, 0x1b, 0xb7, 0x25,
	0xcb, 0xed, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x5d, 0xba, 0x8d, 0xe6, 0x24, 0x0a, 0x00, 0x00,
}

func (this *PutChunksRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PutChunksRequest)
	if !ok {
		that2, ok := that.(PutChunksRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Chunks) != len(that1.Chunks) {
		return false
	}
	for i := range this.Chunks {
		if !this.Chunks[i].Equal(that1.Chunks[i]) {
			return false
		}
	}
	return true
}
func (this *GetChunksRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetChunksRequest)
	if !ok {
		that2, ok := that.(GetChunksRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Chunks) != len(that1.Chunks) {
		return false
	}
	for i := range this.Chunks {
		if !this.Chunks[i].Equal(that1.Chunks[i]) {
			return false
		}
	}
	return true
}
func (this *GetChunksResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetChunksResponse)
	if !ok {
		that2, ok := that.(GetChunksResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Chunks) != len(that1.Chunks) {
		return false
	}
	for i := range this.Chunks {
		if !this.Chunks[i].Equal(that1.Chunks[i]) {
			return false
		}
	}
	return true
}
func (this *Chunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Chunk)
	if !ok {
		that2, ok := that.(Chunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !bytes.Equal(this.Encoded, that1.Encoded) {
		return false
	}
	if this.Key != that1.Key {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	return true
}
func (this *ChunkID) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ChunkID)
	if !ok {
		that2, ok := that.(ChunkID)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.ChunkID != that1.ChunkID {
		return false
	}
	return true
}
func (this *DeleteTableRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeleteTableRequest)
	if !ok {
		that2, ok := that.(DeleteTableRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	return true
}
func (this *DescribeTableRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeTableRequest)
	if !ok {
		that2, ok := that.(DescribeTableRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	return true
}
func (this *WriteBatch) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*WriteBatch)
	if !ok {
		that2, ok := that.(WriteBatch)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Writes) != len(that1.Writes) {
		return false
	}
	for i := range this.Writes {
		if !this.Writes[i].Equal(that1.Writes[i]) {
			return false
		}
	}
	if len(this.Deletes) != len(that1.Deletes) {
		return false
	}
	for i := range this.Deletes {
		if !this.Deletes[i].Equal(that1.Deletes[i]) {
			return false
		}
	}
	return true
}
func (this *WriteIndexRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*WriteIndexRequest)
	if !ok {
		that2, ok := that.(WriteIndexRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Writes) != len(that1.Writes) {
		return false
	}
	for i := range this.Writes {
		if !this.Writes[i].Equal(that1.Writes[i]) {
			return false
		}
	}
	return true
}
func (this *DeleteIndexRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeleteIndexRequest)
	if !ok {
		that2, ok := that.(DeleteIndexRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Deletes) != len(that1.Deletes) {
		return false
	}
	for i := range this.Deletes {
		if !this.Deletes[i].Equal(that1.Deletes[i]) {
			return false
		}
	}
	return true
}
func (this *QueryIndexResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryIndexResponse)
	if !ok {
		that2, ok := that.(QueryIndexResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Rows) != len(that1.Rows) {
		return false
	}
	for i := range this.Rows {
		if !this.Rows[i].Equal(that1.Rows[i]) {
			return false
		}
	}
	return true
}
func (this *Row) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Row)
	if !ok {
		that2, ok := that.(Row)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !bytes.Equal(this.RangeValue, that1.RangeValue) {
		return false
	}
	if !bytes.Equal(this.Value, that1.Value) {
		return false
	}
	return true
}
func (this *IndexEntry) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*IndexEntry)
	if !ok {
		that2, ok := that.(IndexEntry)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	if this.HashValue != that1.HashValue {
		return false
	}
	if !bytes.Equal(this.RangeValue, that1.RangeValue) {
		return false
	}
	if !bytes.Equal(this.Value, that1.Value) {
		return false
	}
	return true
}
func (this *QueryIndexRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryIndexRequest)
	if !ok {
		that2, ok := that.(QueryIndexRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	if this.HashValue != that1.HashValue {
		return false
	}
	if !bytes.Equal(this.RangeValuePrefix, that1.RangeValuePrefix) {
		return false
	}
	if !bytes.Equal(this.RangeValueStart, that1.RangeValueStart) {
		return false
	}
	if !bytes.Equal(this.ValueEqual, that1.ValueEqual) {
		return false
	}
	if this.Immutable != that1.Immutable {
		return false
	}
	return true
}
func (this *UpdateTableRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*UpdateTableRequest)
	if !ok {
		that2, ok := that.(UpdateTableRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Current.Equal(that1.Current) {
		return false
	}
	if !this.Expected.Equal(that1.Expected) {
		return false
	}
	return true
}
func (this *DescribeTableResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeTableResponse)
	if !ok {
		that2, ok := that.(DescribeTableResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Desc.Equal(that1.Desc) {
		return false
	}
	if this.IsActive != that1.IsActive {
		return false
	}
	return true
}
func (this *CreateTableRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateTableRequest)
	if !ok {
		that2, ok := that.(CreateTableRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Desc.Equal(that1.Desc) {
		return false
	}
	return true
}
func (this *TableDesc) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TableDesc)
	if !ok {
		that2, ok := that.(TableDesc)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.UseOnDemandIOMode != that1.UseOnDemandIOMode {
		return false
	}
	if this.ProvisionedRead != that1.ProvisionedRead {
		return false
	}
	if this.ProvisionedWrite != that1.ProvisionedWrite {
		return false
	}
	if len(this.Tags) != len(that1.Tags) {
		return false
	}
	for i := range this.Tags {
		if this.Tags[i] != that1.Tags[i] {
			return false
		}
	}
	return true
}
func (this *ListTablesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ListTablesResponse)
	if !ok {
		that2, ok := that.(ListTablesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.TableNames) != len(that1.TableNames) {
		return false
	}
	for i := range this.TableNames {
		if this.TableNames[i] != that1.TableNames[i] {
			return false
		}
	}
	return true
}
func (this *Labels) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Labels)
	if !ok {
		that2, ok := that.(Labels)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.Value != that1.Value {
		return false
	}
	return true
}
func (this *PutChunksRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.PutChunksRequest{")
	if this.Chunks != nil {
		s = append(s, "Chunks: "+fmt.Sprintf("%#v", this.Chunks)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetChunksRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.GetChunksRequest{")
	if this.Chunks != nil {
		s = append(s, "Chunks: "+fmt.Sprintf("%#v", this.Chunks)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetChunksResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.GetChunksResponse{")
	if this.Chunks != nil {
		s = append(s, "Chunks: "+fmt.Sprintf("%#v", this.Chunks)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Chunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&grpc.Chunk{")
	s = append(s, "Encoded: "+fmt.Sprintf("%#v", this.Encoded)+",\n")
	s = append(s, "Key: "+fmt.Sprintf("%#v", this.Key)+",\n")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ChunkID) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.ChunkID{")
	s = append(s, "ChunkID: "+fmt.Sprintf("%#v", this.ChunkID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeleteTableRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.DeleteTableRequest{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeTableRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.DescribeTableRequest{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *WriteBatch) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&grpc.WriteBatch{")
	if this.Writes != nil {
		s = append(s, "Writes: "+fmt.Sprintf("%#v", this.Writes)+",\n")
	}
	if this.Deletes != nil {
		s = append(s, "Deletes: "+fmt.Sprintf("%#v", this.Deletes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *WriteIndexRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.WriteIndexRequest{")
	if this.Writes != nil {
		s = append(s, "Writes: "+fmt.Sprintf("%#v", this.Writes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeleteIndexRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.DeleteIndexRequest{")
	if this.Deletes != nil {
		s = append(s, "Deletes: "+fmt.Sprintf("%#v", this.Deletes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryIndexResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.QueryIndexResponse{")
	if this.Rows != nil {
		s = append(s, "Rows: "+fmt.Sprintf("%#v", this.Rows)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Row) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&grpc.Row{")
	s = append(s, "RangeValue: "+fmt.Sprintf("%#v", this.RangeValue)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *IndexEntry) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&grpc.IndexEntry{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "HashValue: "+fmt.Sprintf("%#v", this.HashValue)+",\n")
	s = append(s, "RangeValue: "+fmt.Sprintf("%#v", this.RangeValue)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryIndexRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 10)
	s = append(s, "&grpc.QueryIndexRequest{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "HashValue: "+fmt.Sprintf("%#v", this.HashValue)+",\n")
	s = append(s, "RangeValuePrefix: "+fmt.Sprintf("%#v", this.RangeValuePrefix)+",\n")
	s = append(s, "RangeValueStart: "+fmt.Sprintf("%#v", this.RangeValueStart)+",\n")
	s = append(s, "ValueEqual: "+fmt.Sprintf("%#v", this.ValueEqual)+",\n")
	s = append(s, "Immutable: "+fmt.Sprintf("%#v", this.Immutable)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *UpdateTableRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&grpc.UpdateTableRequest{")
	if this.Current != nil {
		s = append(s, "Current: "+fmt.Sprintf("%#v", this.Current)+",\n")
	}
	if this.Expected != nil {
		s = append(s, "Expected: "+fmt.Sprintf("%#v", this.Expected)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeTableResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&grpc.DescribeTableResponse{")
	if this.Desc != nil {
		s = append(s, "Desc: "+fmt.Sprintf("%#v", this.Desc)+",\n")
	}
	s = append(s, "IsActive: "+fmt.Sprintf("%#v", this.IsActive)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CreateTableRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.CreateTableRequest{")
	if this.Desc != nil {
		s = append(s, "Desc: "+fmt.Sprintf("%#v", this.Desc)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TableDesc) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&grpc.TableDesc{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "UseOnDemandIOMode: "+fmt.Sprintf("%#v", this.UseOnDemandIOMode)+",\n")
	s = append(s, "ProvisionedRead: "+fmt.Sprintf("%#v", this.ProvisionedRead)+",\n")
	s = append(s, "ProvisionedWrite: "+fmt.Sprintf("%#v", this.ProvisionedWrite)+",\n")
	keysForTags := make([]string, 0, len(this.Tags))
	for k, _ := range this.Tags {
		keysForTags = append(keysForTags, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForTags)
	mapStringForTags := "map[string]string{"
	for _, k := range keysForTags {
		mapStringForTags += fmt.Sprintf("%#v: %#v,", k, this.Tags[k])
	}
	mapStringForTags += "}"
	if this.Tags != nil {
		s = append(s, "Tags: "+mapStringForTags+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ListTablesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&grpc.ListTablesResponse{")
	s = append(s, "TableNames: "+fmt.Sprintf("%#v", this.TableNames)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Labels) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&grpc.Labels{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringGrpc(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GrpcStoreClient is the client API for GrpcStore service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GrpcStoreClient interface {
	/// WriteIndex writes batch of indexes to the index tables.
	WriteIndex(ctx context.Context, in *WriteIndexRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	/// QueryIndex reads the indexes required for given query & sends back the batch of rows
	/// in rpc streams
	QueryIndex(ctx context.Context, in *QueryIndexRequest, opts ...grpc.CallOption) (GrpcStore_QueryIndexClient, error)
	/// DeleteIndex deletes the batch of index entries from the index tables
	DeleteIndex(ctx context.Context, in *DeleteIndexRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	/// PutChunks saves the batch of chunks into the chunk tables.
	PutChunks(ctx context.Context, in *PutChunksRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	/// GetChunks requests for batch of chunks and the batch of chunks are sent back in rpc streams
	/// batching needs to be performed at server level as per requirement instead of sending single chunk per stream.
	/// In GetChunks rpc request send buf as nil
	GetChunks(ctx context.Context, in *GetChunksRequest, opts ...grpc.CallOption) (GrpcStore_GetChunksClient, error)
	/// DeleteChunks deletes the chunks based on chunkID.
	DeleteChunks(ctx context.Context, in *ChunkID, opts ...grpc.CallOption) (*empty.Empty, error)
	/// Lists all the tables that exists in the database.
	ListTables(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*ListTablesResponse, error)
	/// Creates a table with provided name & attributes.
	CreateTable(ctx context.Context, in *CreateTableRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	// Deletes a table using table name provided.
	DeleteTable(ctx context.Context, in *DeleteTableRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	// Describes a table information for the provided table.
	DescribeTable(ctx context.Context, in *DescribeTableRequest, opts ...grpc.CallOption) (*DescribeTableResponse, error)
	// Update a table with newly provided table information.
	UpdateTable(ctx context.Context, in *UpdateTableRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type grpcStoreClient struct {
	cc *grpc.ClientConn
}

func NewGrpcStoreClient(cc *grpc.ClientConn) GrpcStoreClient {
	return &grpcStoreClient{cc}
}

func (c *grpcStoreClient) WriteIndex(ctx context.Context, in *WriteIndexRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/WriteIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) QueryIndex(ctx context.Context, in *QueryIndexRequest, opts ...grpc.CallOption) (GrpcStore_QueryIndexClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcStore_serviceDesc.Streams[0], "/grpc.grpc_store/QueryIndex", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcStoreQueryIndexClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type GrpcStore_QueryIndexClient interface {
	Recv() (*QueryIndexResponse, error)
	grpc.ClientStream
}

type grpcStoreQueryIndexClient struct {
	grpc.ClientStream
}

func (x *grpcStoreQueryIndexClient) Recv() (*QueryIndexResponse, error) {
	m := new(QueryIndexResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcStoreClient) DeleteIndex(ctx context.Context, in *DeleteIndexRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/DeleteIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) PutChunks(ctx context.Context, in *PutChunksRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/PutChunks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) GetChunks(ctx context.Context, in *GetChunksRequest, opts ...grpc.CallOption) (GrpcStore_GetChunksClient, error) {
	stream, err := c.cc.NewStream(ctx, &_GrpcStore_serviceDesc.Streams[1], "/grpc.grpc_store/GetChunks", opts...)
	if err != nil {
		return nil, err
	}
	x := &grpcStoreGetChunksClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type GrpcStore_GetChunksClient interface {
	Recv() (*GetChunksResponse, error)
	grpc.ClientStream
}

type grpcStoreGetChunksClient struct {
	grpc.ClientStream
}

func (x *grpcStoreGetChunksClient) Recv() (*GetChunksResponse, error) {
	m := new(GetChunksResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *grpcStoreClient) DeleteChunks(ctx context.Context, in *ChunkID, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/DeleteChunks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) ListTables(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*ListTablesResponse, error) {
	out := new(ListTablesResponse)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/ListTables", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) CreateTable(ctx context.Context, in *CreateTableRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/CreateTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) DeleteTable(ctx context.Context, in *DeleteTableRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/DeleteTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) DescribeTable(ctx context.Context, in *DescribeTableRequest, opts ...grpc.CallOption) (*DescribeTableResponse, error) {
	out := new(DescribeTableResponse)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/DescribeTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *grpcStoreClient) UpdateTable(ctx context.Context, in *UpdateTableRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpc.grpc_store/UpdateTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GrpcStoreServer is the server API for GrpcStore service.
type GrpcStoreServer interface {
	/// WriteIndex writes batch of indexes to the index tables.
	WriteIndex(context.Context, *WriteIndexRequest) (*empty.Empty, error)
	/// QueryIndex reads the indexes required for given query & sends back the batch of rows
	/// in rpc streams
	QueryIndex(*QueryIndexRequest, GrpcStore_QueryIndexServer) error
	/// DeleteIndex deletes the batch of index entries from the index tables
	DeleteIndex(context.Context, *DeleteIndexRequest) (*empty.Empty, error)
	/// PutChunks saves the batch of chunks into the chunk tables.
	PutChunks(context.Context, *PutChunksRequest) (*empty.Empty, error)
	/// GetChunks requests for batch of chunks and the batch of chunks are sent back in rpc streams
	/// batching needs to be performed at server level as per requirement instead of sending single chunk per stream.
	/// In GetChunks rpc request send buf as nil
	GetChunks(*GetChunksRequest, GrpcStore_GetChunksServer) error
	/// DeleteChunks deletes the chunks based on chunkID.
	DeleteChunks(context.Context, *ChunkID) (*empty.Empty, error)
	/// Lists all the tables that exists in the database.
	ListTables(context.Context, *empty.Empty) (*ListTablesResponse, error)
	/// Creates a table with provided name & attributes.
	CreateTable(context.Context, *CreateTableRequest) (*empty.Empty, error)
	// Deletes a table using table name provided.
	DeleteTable(context.Context, *DeleteTableRequest) (*empty.Empty, error)
	// Describes a table information for the provided table.
	DescribeTable(context.Context, *DescribeTableRequest) (*DescribeTableResponse, error)
	// Update a table with newly provided table information.
	UpdateTable(context.Context, *UpdateTableRequest) (*empty.Empty, error)
}

// UnimplementedGrpcStoreServer can be embedded to have forward compatible implementations.
type UnimplementedGrpcStoreServer struct {
}

func (*UnimplementedGrpcStoreServer) WriteIndex(ctx context.Context, req *WriteIndexRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteIndex not implemented")
}
func (*UnimplementedGrpcStoreServer) QueryIndex(req *QueryIndexRequest, srv GrpcStore_QueryIndexServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryIndex not implemented")
}
func (*UnimplementedGrpcStoreServer) DeleteIndex(ctx context.Context, req *DeleteIndexRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIndex not implemented")
}
func (*UnimplementedGrpcStoreServer) PutChunks(ctx context.Context, req *PutChunksRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PutChunks not implemented")
}
func (*UnimplementedGrpcStoreServer) GetChunks(req *GetChunksRequest, srv GrpcStore_GetChunksServer) error {
	return status.Errorf(codes.Unimplemented, "method GetChunks not implemented")
}
func (*UnimplementedGrpcStoreServer) DeleteChunks(ctx context.Context, req *ChunkID) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteChunks not implemented")
}
func (*UnimplementedGrpcStoreServer) ListTables(ctx context.Context, req *empty.Empty) (*ListTablesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTables not implemented")
}
func (*UnimplementedGrpcStoreServer) CreateTable(ctx context.Context, req *CreateTableRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTable not implemented")
}
func (*UnimplementedGrpcStoreServer) DeleteTable(ctx context.Context, req *DeleteTableRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTable not implemented")
}
func (*UnimplementedGrpcStoreServer) DescribeTable(ctx context.Context, req *DescribeTableRequest) (*DescribeTableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeTable not implemented")
}
func (*UnimplementedGrpcStoreServer) UpdateTable(ctx context.Context, req *UpdateTableRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTable not implemented")
}

func RegisterGrpcStoreServer(s *grpc.Server, srv GrpcStoreServer) {
	s.RegisterService(&_GrpcStore_serviceDesc, srv)
}

func _GrpcStore_WriteIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteIndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).WriteIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/WriteIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).WriteIndex(ctx, req.(*WriteIndexRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_QueryIndex_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(QueryIndexRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(GrpcStoreServer).QueryIndex(m, &grpcStoreQueryIndexServer{stream})
}

type GrpcStore_QueryIndexServer interface {
	Send(*QueryIndexResponse) error
	grpc.ServerStream
}

type grpcStoreQueryIndexServer struct {
	grpc.ServerStream
}

func (x *grpcStoreQueryIndexServer) Send(m *QueryIndexResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _GrpcStore_DeleteIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).DeleteIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/DeleteIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).DeleteIndex(ctx, req.(*DeleteIndexRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_PutChunks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutChunksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).PutChunks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/PutChunks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).PutChunks(ctx, req.(*PutChunksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_GetChunks_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetChunksRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(GrpcStoreServer).GetChunks(m, &grpcStoreGetChunksServer{stream})
}

type GrpcStore_GetChunksServer interface {
	Send(*GetChunksResponse) error
	grpc.ServerStream
}

type grpcStoreGetChunksServer struct {
	grpc.ServerStream
}

func (x *grpcStoreGetChunksServer) Send(m *GetChunksResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _GrpcStore_DeleteChunks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChunkID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).DeleteChunks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/DeleteChunks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).DeleteChunks(ctx, req.(*ChunkID))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_ListTables_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(empty.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).ListTables(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/ListTables",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).ListTables(ctx, req.(*empty.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_CreateTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).CreateTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/CreateTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).CreateTable(ctx, req.(*CreateTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_DeleteTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).DeleteTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/DeleteTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).DeleteTable(ctx, req.(*DeleteTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_DescribeTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).DescribeTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/DescribeTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).DescribeTable(ctx, req.(*DescribeTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GrpcStore_UpdateTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcStoreServer).UpdateTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.grpc_store/UpdateTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcStoreServer).UpdateTable(ctx, req.(*UpdateTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GrpcStore_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpc.grpc_store",
	HandlerType: (*GrpcStoreServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WriteIndex",
			Handler:    _GrpcStore_WriteIndex_Handler,
		},
		{
			MethodName: "DeleteIndex",
			Handler:    _GrpcStore_DeleteIndex_Handler,
		},
		{
			MethodName: "PutChunks",
			Handler:    _GrpcStore_PutChunks_Handler,
		},
		{
			MethodName: "DeleteChunks",
			Handler:    _GrpcStore_DeleteChunks_Handler,
		},
		{
			MethodName: "ListTables",
			Handler:    _GrpcStore_ListTables_Handler,
		},
		{
			MethodName: "CreateTable",
			Handler:    _GrpcStore_CreateTable_Handler,
		},
		{
			MethodName: "DeleteTable",
			Handler:    _GrpcStore_DeleteTable_Handler,
		},
		{
			MethodName: "DescribeTable",
			Handler:    _GrpcStore_DescribeTable_Handler,
		},
		{
			MethodName: "UpdateTable",
			Handler:    _GrpcStore_UpdateTable_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "QueryIndex",
			Handler:       _GrpcStore_QueryIndex_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetChunks",
			Handler:       _GrpcStore_GetChunks_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "pkg/storage/chunk/client/grpc/grpc.proto",
}

func (m *PutChunksRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutChunksRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PutChunksRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetChunksRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChunksRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetChunksRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetChunksResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChunksResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetChunksResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Chunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Chunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Chunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Encoded) > 0 {
		i -= len(m.Encoded)
		copy(dAtA[i:], m.Encoded)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Encoded)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ChunkID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChunkID) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChunkID) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ChunkID) > 0 {
		i -= len(m.ChunkID)
		copy(dAtA[i:], m.ChunkID)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.ChunkID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteTableRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTableRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteTableRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeTableRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeTableRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeTableRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WriteBatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteBatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteBatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Deletes) > 0 {
		for iNdEx := len(m.Deletes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Deletes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Writes) > 0 {
		for iNdEx := len(m.Writes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Writes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *WriteIndexRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteIndexRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteIndexRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Writes) > 0 {
		for iNdEx := len(m.Writes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Writes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DeleteIndexRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteIndexRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteIndexRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Deletes) > 0 {
		for iNdEx := len(m.Deletes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Deletes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *QueryIndexResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryIndexResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryIndexResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGrpc(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Row) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Row) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Row) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RangeValue) > 0 {
		i -= len(m.RangeValue)
		copy(dAtA[i:], m.RangeValue)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.RangeValue)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IndexEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexEntry) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IndexEntry) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RangeValue) > 0 {
		i -= len(m.RangeValue)
		copy(dAtA[i:], m.RangeValue)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.RangeValue)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.HashValue) > 0 {
		i -= len(m.HashValue)
		copy(dAtA[i:], m.HashValue)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.HashValue)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *QueryIndexRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryIndexRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryIndexRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Immutable {
		i--
		if m.Immutable {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if len(m.ValueEqual) > 0 {
		i -= len(m.ValueEqual)
		copy(dAtA[i:], m.ValueEqual)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.ValueEqual)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.RangeValueStart) > 0 {
		i -= len(m.RangeValueStart)
		copy(dAtA[i:], m.RangeValueStart)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.RangeValueStart)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RangeValuePrefix) > 0 {
		i -= len(m.RangeValuePrefix)
		copy(dAtA[i:], m.RangeValuePrefix)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.RangeValuePrefix)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.HashValue) > 0 {
		i -= len(m.HashValue)
		copy(dAtA[i:], m.HashValue)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.HashValue)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateTableRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateTableRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateTableRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Expected != nil {
		{
			size, err := m.Expected.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGrpc(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Current != nil {
		{
			size, err := m.Current.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGrpc(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeTableResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeTableResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeTableResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsActive {
		i--
		if m.IsActive {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Desc != nil {
		{
			size, err := m.Desc.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGrpc(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateTableRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTableRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateTableRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Desc != nil {
		{
			size, err := m.Desc.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGrpc(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TableDesc) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableDesc) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableDesc) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Tags) > 0 {
		for k := range m.Tags {
			v := m.Tags[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintGrpc(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintGrpc(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGrpc(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.ProvisionedWrite != 0 {
		i = encodeVarintGrpc(dAtA, i, uint64(m.ProvisionedWrite))
		i--
		dAtA[i] = 0x20
	}
	if m.ProvisionedRead != 0 {
		i = encodeVarintGrpc(dAtA, i, uint64(m.ProvisionedRead))
		i--
		dAtA[i] = 0x18
	}
	if m.UseOnDemandIOMode {
		i--
		if m.UseOnDemandIOMode {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ListTablesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListTablesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListTablesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableNames) > 0 {
		for iNdEx := len(m.TableNames) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.TableNames[iNdEx])
			copy(dAtA[i:], m.TableNames[iNdEx])
			i = encodeVarintGrpc(dAtA, i, uint64(len(m.TableNames[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Labels) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Labels) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Labels) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintGrpc(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintGrpc(dAtA []byte, offset int, v uint64) int {
	offset -= sovGrpc(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *PutChunksRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for _, e := range m.Chunks {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *GetChunksRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for _, e := range m.Chunks {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *GetChunksResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for _, e := range m.Chunks {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *Chunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Encoded)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *ChunkID) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ChunkID)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *DeleteTableRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *DescribeTableRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *WriteBatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Writes) > 0 {
		for _, e := range m.Writes {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	if len(m.Deletes) > 0 {
		for _, e := range m.Deletes {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *WriteIndexRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Writes) > 0 {
		for _, e := range m.Writes {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *DeleteIndexRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Deletes) > 0 {
		for _, e := range m.Deletes {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *QueryIndexResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *Row) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RangeValue)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *IndexEntry) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.HashValue)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.RangeValue)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *QueryIndexRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.HashValue)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.RangeValuePrefix)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.RangeValueStart)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.ValueEqual)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	if m.Immutable {
		n += 2
	}
	return n
}

func (m *UpdateTableRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Current != nil {
		l = m.Current.Size()
		n += 1 + l + sovGrpc(uint64(l))
	}
	if m.Expected != nil {
		l = m.Expected.Size()
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *DescribeTableResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Desc != nil {
		l = m.Desc.Size()
		n += 1 + l + sovGrpc(uint64(l))
	}
	if m.IsActive {
		n += 2
	}
	return n
}

func (m *CreateTableRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Desc != nil {
		l = m.Desc.Size()
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func (m *TableDesc) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	if m.UseOnDemandIOMode {
		n += 2
	}
	if m.ProvisionedRead != 0 {
		n += 1 + sovGrpc(uint64(m.ProvisionedRead))
	}
	if m.ProvisionedWrite != 0 {
		n += 1 + sovGrpc(uint64(m.ProvisionedWrite))
	}
	if len(m.Tags) > 0 {
		for k, v := range m.Tags {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovGrpc(uint64(len(k))) + 1 + len(v) + sovGrpc(uint64(len(v)))
			n += mapEntrySize + 1 + sovGrpc(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *ListTablesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TableNames) > 0 {
		for _, s := range m.TableNames {
			l = len(s)
			n += 1 + l + sovGrpc(uint64(l))
		}
	}
	return n
}

func (m *Labels) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovGrpc(uint64(l))
	}
	return n
}

func sovGrpc(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGrpc(x uint64) (n int) {
	return sovGrpc(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *PutChunksRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForChunks := "[]*Chunk{"
	for _, f := range this.Chunks {
		repeatedStringForChunks += strings.Replace(f.String(), "Chunk", "Chunk", 1) + ","
	}
	repeatedStringForChunks += "}"
	s := strings.Join([]string{`&PutChunksRequest{`,
		`Chunks:` + repeatedStringForChunks + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetChunksRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForChunks := "[]*Chunk{"
	for _, f := range this.Chunks {
		repeatedStringForChunks += strings.Replace(f.String(), "Chunk", "Chunk", 1) + ","
	}
	repeatedStringForChunks += "}"
	s := strings.Join([]string{`&GetChunksRequest{`,
		`Chunks:` + repeatedStringForChunks + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetChunksResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForChunks := "[]*Chunk{"
	for _, f := range this.Chunks {
		repeatedStringForChunks += strings.Replace(f.String(), "Chunk", "Chunk", 1) + ","
	}
	repeatedStringForChunks += "}"
	s := strings.Join([]string{`&GetChunksResponse{`,
		`Chunks:` + repeatedStringForChunks + `,`,
		`}`,
	}, "")
	return s
}
func (this *Chunk) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Chunk{`,
		`Encoded:` + fmt.Sprintf("%v", this.Encoded) + `,`,
		`Key:` + fmt.Sprintf("%v", this.Key) + `,`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ChunkID) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ChunkID{`,
		`ChunkID:` + fmt.Sprintf("%v", this.ChunkID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DeleteTableRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DeleteTableRequest{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeTableRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeTableRequest{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`}`,
	}, "")
	return s
}
func (this *WriteBatch) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForWrites := "[]*IndexEntry{"
	for _, f := range this.Writes {
		repeatedStringForWrites += strings.Replace(f.String(), "IndexEntry", "IndexEntry", 1) + ","
	}
	repeatedStringForWrites += "}"
	repeatedStringForDeletes := "[]*IndexEntry{"
	for _, f := range this.Deletes {
		repeatedStringForDeletes += strings.Replace(f.String(), "IndexEntry", "IndexEntry", 1) + ","
	}
	repeatedStringForDeletes += "}"
	s := strings.Join([]string{`&WriteBatch{`,
		`Writes:` + repeatedStringForWrites + `,`,
		`Deletes:` + repeatedStringForDeletes + `,`,
		`}`,
	}, "")
	return s
}
func (this *WriteIndexRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForWrites := "[]*IndexEntry{"
	for _, f := range this.Writes {
		repeatedStringForWrites += strings.Replace(f.String(), "IndexEntry", "IndexEntry", 1) + ","
	}
	repeatedStringForWrites += "}"
	s := strings.Join([]string{`&WriteIndexRequest{`,
		`Writes:` + repeatedStringForWrites + `,`,
		`}`,
	}, "")
	return s
}
func (this *DeleteIndexRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForDeletes := "[]*IndexEntry{"
	for _, f := range this.Deletes {
		repeatedStringForDeletes += strings.Replace(f.String(), "IndexEntry", "IndexEntry", 1) + ","
	}
	repeatedStringForDeletes += "}"
	s := strings.Join([]string{`&DeleteIndexRequest{`,
		`Deletes:` + repeatedStringForDeletes + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryIndexResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForRows := "[]*Row{"
	for _, f := range this.Rows {
		repeatedStringForRows += strings.Replace(f.String(), "Row", "Row", 1) + ","
	}
	repeatedStringForRows += "}"
	s := strings.Join([]string{`&QueryIndexResponse{`,
		`Rows:` + repeatedStringForRows + `,`,
		`}`,
	}, "")
	return s
}
func (this *Row) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Row{`,
		`RangeValue:` + fmt.Sprintf("%v", this.RangeValue) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func (this *IndexEntry) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&IndexEntry{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`HashValue:` + fmt.Sprintf("%v", this.HashValue) + `,`,
		`RangeValue:` + fmt.Sprintf("%v", this.RangeValue) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryIndexRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&QueryIndexRequest{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`HashValue:` + fmt.Sprintf("%v", this.HashValue) + `,`,
		`RangeValuePrefix:` + fmt.Sprintf("%v", this.RangeValuePrefix) + `,`,
		`RangeValueStart:` + fmt.Sprintf("%v", this.RangeValueStart) + `,`,
		`ValueEqual:` + fmt.Sprintf("%v", this.ValueEqual) + `,`,
		`Immutable:` + fmt.Sprintf("%v", this.Immutable) + `,`,
		`}`,
	}, "")
	return s
}
func (this *UpdateTableRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&UpdateTableRequest{`,
		`Current:` + strings.Replace(this.Current.String(), "TableDesc", "TableDesc", 1) + `,`,
		`Expected:` + strings.Replace(this.Expected.String(), "TableDesc", "TableDesc", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeTableResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeTableResponse{`,
		`Desc:` + strings.Replace(this.Desc.String(), "TableDesc", "TableDesc", 1) + `,`,
		`IsActive:` + fmt.Sprintf("%v", this.IsActive) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CreateTableRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateTableRequest{`,
		`Desc:` + strings.Replace(this.Desc.String(), "TableDesc", "TableDesc", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TableDesc) String() string {
	if this == nil {
		return "nil"
	}
	keysForTags := make([]string, 0, len(this.Tags))
	for k, _ := range this.Tags {
		keysForTags = append(keysForTags, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForTags)
	mapStringForTags := "map[string]string{"
	for _, k := range keysForTags {
		mapStringForTags += fmt.Sprintf("%v: %v,", k, this.Tags[k])
	}
	mapStringForTags += "}"
	s := strings.Join([]string{`&TableDesc{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`UseOnDemandIOMode:` + fmt.Sprintf("%v", this.UseOnDemandIOMode) + `,`,
		`ProvisionedRead:` + fmt.Sprintf("%v", this.ProvisionedRead) + `,`,
		`ProvisionedWrite:` + fmt.Sprintf("%v", this.ProvisionedWrite) + `,`,
		`Tags:` + mapStringForTags + `,`,
		`}`,
	}, "")
	return s
}
func (this *ListTablesResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ListTablesResponse{`,
		`TableNames:` + fmt.Sprintf("%v", this.TableNames) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Labels) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Labels{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGrpc(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *PutChunksRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutChunksRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutChunksRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, &Chunk{})
			if err := m.Chunks[len(m.Chunks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChunksRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetChunksRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetChunksRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, &Chunk{})
			if err := m.Chunks[len(m.Chunks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChunksResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetChunksResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetChunksResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, &Chunk{})
			if err := m.Chunks[len(m.Chunks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Chunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Chunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Chunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Encoded", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Encoded = append(m.Encoded[:0], dAtA[iNdEx:postIndex]...)
			if m.Encoded == nil {
				m.Encoded = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChunkID) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChunkID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChunkID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChunkID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChunkID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTableRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteTableRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteTableRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeTableRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeTableRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeTableRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteBatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteBatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteBatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Writes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Writes = append(m.Writes, &IndexEntry{})
			if err := m.Writes[len(m.Writes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deletes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Deletes = append(m.Deletes, &IndexEntry{})
			if err := m.Deletes[len(m.Deletes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteIndexRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteIndexRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteIndexRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Writes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Writes = append(m.Writes, &IndexEntry{})
			if err := m.Writes[len(m.Writes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteIndexRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteIndexRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteIndexRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deletes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Deletes = append(m.Deletes, &IndexEntry{})
			if err := m.Deletes[len(m.Deletes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryIndexResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryIndexResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryIndexResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &Row{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Row) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Row: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Row: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValue", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValue = append(m.RangeValue[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValue == nil {
				m.RangeValue = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IndexEntry) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IndexEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IndexEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HashValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HashValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValue", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValue = append(m.RangeValue[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValue == nil {
				m.RangeValue = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryIndexRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryIndexRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryIndexRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HashValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HashValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValuePrefix", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValuePrefix = append(m.RangeValuePrefix[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValuePrefix == nil {
				m.RangeValuePrefix = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeValueStart", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RangeValueStart = append(m.RangeValueStart[:0], dAtA[iNdEx:postIndex]...)
			if m.RangeValueStart == nil {
				m.RangeValueStart = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValueEqual", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ValueEqual = append(m.ValueEqual[:0], dAtA[iNdEx:postIndex]...)
			if m.ValueEqual == nil {
				m.ValueEqual = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Immutable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Immutable = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateTableRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateTableRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateTableRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Current", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Current == nil {
				m.Current = &TableDesc{}
			}
			if err := m.Current.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Expected", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Expected == nil {
				m.Expected = &TableDesc{}
			}
			if err := m.Expected.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeTableResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeTableResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeTableResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Desc == nil {
				m.Desc = &TableDesc{}
			}
			if err := m.Desc.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsActive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsActive = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTableRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateTableRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateTableRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Desc == nil {
				m.Desc = &TableDesc{}
			}
			if err := m.Desc.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableDesc) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableDesc: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableDesc: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UseOnDemandIOMode", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UseOnDemandIOMode = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProvisionedRead", wireType)
			}
			m.ProvisionedRead = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProvisionedRead |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProvisionedWrite", wireType)
			}
			m.ProvisionedWrite = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProvisionedWrite |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tags == nil {
				m.Tags = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGrpc
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGrpc
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGrpc
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGrpc
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGrpc
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthGrpc
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthGrpc
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGrpc(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if skippy < 0 {
						return ErrInvalidLengthGrpc
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Tags[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListTablesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListTablesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListTablesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableNames = append(m.TableNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Labels) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Labels: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Labels: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGrpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGrpc
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGrpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthGrpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGrpc(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGrpc
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGrpc
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGrpc
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthGrpc
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGrpc
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGrpc(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthGrpc
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGrpc = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGrpc   = fmt.Errorf("proto: integer overflow")
)
